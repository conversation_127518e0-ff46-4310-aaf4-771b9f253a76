-- Stored Procedure to calculate VAT of Payment via VisaExpensePaymentTypeDetails
-- This replicates the vatOfPayment calculation from calculateVatViaVisaExpensePaymentTypeDetails method

DELIMITER $$

CREATE PROCEDURE CalculateVatOfPayment(
    IN p_paymentType VARCHAR(50),
    IN p_expensePurpose VARCHAR(50),
    IN p_actualVisaPaymentAmount DOUBLE,
    OUT o_vatOfPayment DOUBLE,
    OUT o_vatOfExpense DOUBLE,
    OUT o_totalVatAmount DOUBLE,
    OUT o_serviceChargeAmount DOUBLE,
    OUT o_recordId BIGINT
)
BEGIN
    DECLARE v_charge DOUBLE DEFAULT 0.0;
    DECLARE v_vatChargePercentage DOUBLE DEFAULT 0.0;
    DECLARE v_serviceChargeOfExpense DOUBLE DEFAULT 0.0;
    DECLARE v_vatChargeOfExpense DOUBLE DEFAULT 0.0;
    DECLARE v_typeOfServiceChargeOfExpense VARCHAR(50);
    DECLARE v_recordId BIGINT;
    DECLARE v_serviceChargeAmount DOUBLE DEFAULT 0.0;
    
    -- Initialize output parameters
    SET o_vatOfPayment = 0.0;
    SET o_vatOfExpense = 0.0;
    SET o_totalVatAmount = 0.0;
    SET o_serviceChargeAmount = 0.0;
    SET o_recordId = NULL;
    
    -- Get the visa expense payment type details
    SELECT 
        ID,
        COALESCE(CHARGE, 0.0),
        COALESCE(VAT_CHARGE_PERCENTAGE, 0.0),
        COALESCE(SERVICE_CHARGE_OF_EXPENSE, 0.0),
        COALESCE(VAT_CHARGE_OF_EXPENSE, 0.0),
        COALESCE(TYPE_OF_SERVICE_CHARGE_OF_EXPENSE, 'STATIC_AMOUNT')
    INTO 
        v_recordId,
        v_charge,
        v_vatChargePercentage,
        v_serviceChargeOfExpense,
        v_vatChargeOfExpense,
        v_typeOfServiceChargeOfExpense
    FROM VISAEXPENSEPAYMENTTYPEDETAILS
    WHERE PAYMENT_TYPE = p_paymentType 
      AND EXPENSE_PURPOSE = p_expensePurpose
    LIMIT 1;
    
    -- Check if record was found
    IF v_recordId IS NOT NULL THEN
        
        -- Calculate service charge amount based on type
        IF v_typeOfServiceChargeOfExpense = 'PERCENTAGE' AND p_actualVisaPaymentAmount IS NOT NULL THEN
            SET v_serviceChargeAmount = ROUND((p_actualVisaPaymentAmount * (v_serviceChargeOfExpense / 100)), 2);
        ELSE
            SET v_serviceChargeAmount = v_serviceChargeOfExpense;
        END IF;
        
        -- Calculate VAT of Payment = charge * (vatChargePercentage / 100)
        SET o_vatOfPayment = ROUND((v_charge * (v_vatChargePercentage / 100)), 2);
        
        -- Calculate VAT of Expense = serviceChargeAmount * (vatChargeOfExpense / 100)
        SET o_vatOfExpense = ROUND((v_serviceChargeAmount * (v_vatChargeOfExpense / 100)), 2);
        
        -- Calculate Total VAT Amount = VAT of Payment + VAT of Expense
        SET o_totalVatAmount = ROUND((o_vatOfPayment + o_vatOfExpense), 2);
        
        -- Set output parameters
        SET o_serviceChargeAmount = v_serviceChargeAmount;
        SET o_recordId = v_recordId;
        
    END IF;
    
END$$

DELIMITER ;

-- Example usage procedure calls:

-- Example 1: Calculate VAT for Noqoodi payment with VISA_FEE purpose
-- CALL CalculateVatOfPayment('Noqoodi', 'VISA_FEE', 1000.0, @vatOfPayment, @vatOfExpense, @totalVat, @serviceCharge, @recordId);
-- SELECT @vatOfPayment AS VAT_OF_PAYMENT, @vatOfExpense AS VAT_OF_EXPENSE, @totalVat AS TOTAL_VAT, @serviceCharge AS SERVICE_CHARGE, @recordId AS RECORD_ID;

-- Example 2: Calculate VAT for Credit Card payment with SERVICE_CHARGE purpose
-- CALL CalculateVatOfPayment('Credit_Card', 'SERVICE_CHARGE', 500.0, @vatOfPayment, @vatOfExpense, @totalVat, @serviceCharge, @recordId);
-- SELECT @vatOfPayment AS VAT_OF_PAYMENT, @vatOfExpense AS VAT_OF_EXPENSE, @totalVat AS TOTAL_VAT, @serviceCharge AS SERVICE_CHARGE, @recordId AS RECORD_ID;

-- Example 3: Calculate VAT for static service charge (actualVisaPaymentAmount not needed)
-- CALL CalculateVatOfPayment('CASH', 'VISA_FEE', NULL, @vatOfPayment, @vatOfExpense, @totalVat, @serviceCharge, @recordId);
-- SELECT @vatOfPayment AS VAT_OF_PAYMENT, @vatOfExpense AS VAT_OF_EXPENSE, @totalVat AS TOTAL_VAT, @serviceCharge AS SERVICE_CHARGE, @recordId AS RECORD_ID;
