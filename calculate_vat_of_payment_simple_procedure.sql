-- Simplified Stored Procedure to calculate VAT of Payment only
-- Focuses specifically on vatOfPayment calculation

DELIMITER $$

CREATE PROCEDURE CalculateVatOfPaymentSimple(
    IN p_paymentType VARCHAR(50),
    IN p_expensePurpose VARCHAR(50),
    IN p_actualVisaPaymentAmount DOUBLE,
    OUT o_vatOfPayment DOUBLE
)
BEGIN
    DECLARE v_charge DOUBLE DEFAULT 0.0;
    DECLARE v_vatChargePercentage DOUBLE DEFAULT 0.0;
    DECLARE v_recordFound INT DEFAULT 0;
    
    -- Initialize output parameter
    SET o_vatOfPayment = 0.0;
    
    -- Get the charge and VAT charge percentage
    SELECT 
        COALESCE(CHARGE, 0.0),
        COALESCE(VAT_CHARGE_PERCENTAGE, 0.0),
        1
    INTO 
        v_charge,
        v_vatChargePercentage,
        v_recordFound
    FROM VISAEXPENSEPAYMENTTYPEDETAILS
    WHERE PAYMENT_TYPE = p_paymentType 
      AND EXPENSE_PURPOSE = p_expensePurpose
    LIMIT 1;
    
    -- Calculate VAT of Payment = charge * (vatChargePercentage / 100)
    IF v_recordFound = 1 THEN
        SET o_vatOfPayment = ROUND((v_charge * (v_vatChargePercentage / 100)), 2);
    END IF;
    
END$$

DELIMITER ;

-- Function version for easier usage in SELECT statements
DELIMITER $$

CREATE FUNCTION GetVatOfPayment(
    p_paymentType VARCHAR(50),
    p_expensePurpose VARCHAR(50),
    p_actualVisaPaymentAmount DOUBLE
) RETURNS DOUBLE
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_charge DOUBLE DEFAULT 0.0;
    DECLARE v_vatChargePercentage DOUBLE DEFAULT 0.0;
    DECLARE v_vatOfPayment DOUBLE DEFAULT 0.0;
    
    -- Get the charge and VAT charge percentage
    SELECT 
        COALESCE(CHARGE, 0.0),
        COALESCE(VAT_CHARGE_PERCENTAGE, 0.0)
    INTO 
        v_charge,
        v_vatChargePercentage
    FROM VISAEXPENSEPAYMENTTYPEDETAILS
    WHERE PAYMENT_TYPE = p_paymentType 
      AND EXPENSE_PURPOSE = p_expensePurpose
    LIMIT 1;
    
    -- Calculate VAT of Payment = charge * (vatChargePercentage / 100)
    SET v_vatOfPayment = ROUND((v_charge * (v_vatChargePercentage / 100)), 2);
    
    RETURN v_vatOfPayment;
    
END$$

DELIMITER ;

-- Usage Examples:

-- Example 1: Using the procedure
-- CALL CalculateVatOfPaymentSimple('Noqoodi', 'VISA_FEE', 1000.0, @vatOfPayment);
-- SELECT @vatOfPayment AS VAT_OF_PAYMENT;

-- Example 2: Using the function in a SELECT statement
-- SELECT GetVatOfPayment('Credit_Card', 'SERVICE_CHARGE', 500.0) AS VAT_OF_PAYMENT;

-- Example 3: Using the function with multiple payment types
-- SELECT 
--     'Noqoodi' AS PAYMENT_TYPE,
--     'VISA_FEE' AS EXPENSE_PURPOSE,
--     GetVatOfPayment('Noqoodi', 'VISA_FEE', 1000.0) AS VAT_OF_PAYMENT
-- UNION ALL
-- SELECT 
--     'Credit_Card' AS PAYMENT_TYPE,
--     'SERVICE_CHARGE' AS EXPENSE_PURPOSE,
--     GetVatOfPayment('Credit_Card', 'SERVICE_CHARGE', 500.0) AS VAT_OF_PAYMENT;

-- Example 4: Get VAT of Payment for all configured payment types and purposes
-- SELECT 
--     PAYMENT_TYPE,
--     EXPENSE_PURPOSE,
--     CHARGE,
--     VAT_CHARGE_PERCENTAGE,
--     GetVatOfPayment(PAYMENT_TYPE, EXPENSE_PURPOSE, 1000.0) AS VAT_OF_PAYMENT
-- FROM VISAEXPENSEPAYMENTTYPEDETAILS
-- ORDER BY PAYMENT_TYPE, EXPENSE_PURPOSE;
