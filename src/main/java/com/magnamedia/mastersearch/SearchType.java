package com.magnamedia.mastersearch;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Mar 9, 2018
 */

public class SearchType{

    private int order;
    private String url;
    private Class<?> type;
    private String shownName;
    private String permissionCode; 
    private List<String> joins; 
    private Map<String,SearchField> searchFields = new HashMap();

    public SearchType(
            int order, String url, Class<?> type,
            String shownName, String permissionCode) {
        this.order = order;
        this.url = url;
        this.type = type;
        this.shownName = shownName;
        this.permissionCode = permissionCode;
        this.joins = new ArrayList<>();
        this.searchFields = new HashMap();
    }
    
    public void addJoinField(String field){
        this.joins.add(field);
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Class<?> getType() {
        return type;
    }

    public void setType(Class<?> type) {
        this.type = type;
    }

    public String getShownName() {
        return shownName;
    }

    public void setShownName(String shownName) {
        this.shownName = shownName;
    }

    public String getPermissionCode() {
        return permissionCode;
    }

    public void setPermissionCode(String permissionCode) {
        this.permissionCode = permissionCode;
    }

    public List<String> getJoins() {
        return joins;
    }

    public void setJoins(List<String> joins) {
        this.joins = joins;
    }

    public Map<String, SearchField> getSearchFields() {
        return searchFields;
    }

    public void setSearchFields(Map<String, SearchField> searchFields) {
        this.searchFields = searchFields;
    }

}