package com.magnamedia.mastersearch;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.FrontendPage;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.PageRepository;
import com.magnamedia.core.type.PermissionType;
import com.magnamedia.entity.Bucket;
import com.magnamedia.extra.FilterItem;
import java.lang.reflect.Field;
import java.util.AbstractList;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.reflections.Reflections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR> Kanaan <<EMAIL>>
 * Created on Mar 9, 2018
 */
@Component
public class SearchMap {

    @Autowired
    PageRepository pageRepository;
    
    private final String packageName = "com.magnamedia.entity";
    private final int maxResultCount = 10;
    
    private final Set<Class<?>> searchableTypes;
    private Map<String,SearchType> map = new HashMap();

    public SearchMap() {
        Reflections reflections = new Reflections(packageName);
        this.searchableTypes =
                reflections.getTypesAnnotatedWith(Searchable.class);
        buildMap();
    } 
    
    private void buildMap(){
        this.searchableTypes.forEach((cls) -> {
            Searchable searchableAnnotationClass =
                    (Searchable) cls.getAnnotation(Searchable.class);
            
            SearchInheritedFields searchInheritedFields;
            List<String> inheritedFieldsName = new ArrayList<String>();
            if (cls.isAnnotationPresent(SearchInheritedFields.class)){
                searchInheritedFields =
                    (SearchInheritedFields) cls.getAnnotation(SearchInheritedFields.class);
                inheritedFieldsName =
                        new LinkedList<String>(Arrays.asList(searchInheritedFields.fields()));
            }
            
            List<Field> inheritedFields = Arrays.asList(cls.getSuperclass().getDeclaredFields());
            
            SearchType searchType =
                    new SearchType(searchableAnnotationClass.order(),
                            searchableAnnotationClass.url(),
                            cls,
                            searchableAnnotationClass.showunName().isEmpty() ?
                                    cls.getSimpleName() :
                                    searchableAnnotationClass.showunName(),
                            searchableAnnotationClass.permissionCode());
            
            try {
                searchType.getSearchFields().put("id",
                        new SearchField(-1000,
                                "id",
                                "ID",
                                inheritedFieldsName.contains("id"),
                                true,
                                BaseEntity.class.getDeclaredField("id")));
            } catch (NoSuchFieldException | SecurityException ex) {
                Logger.getLogger(SearchMap.class.getName()).log(Level.SEVERE, null, ex);
            } catch (Exception ex) {
                Logger.getLogger(SearchMap.class.getName()).log(Level.SEVERE, null, ex);
            }
            
            inheritedFieldsName.remove("id");
            int order = 999;
            for (Field field : cls.getSuperclass().getDeclaredFields()) {
                if (inheritedFieldsName.contains(field.getName())){
                    searchType.getSearchFields().put(field.getName(),
                            new SearchField(order,
                                    field.getName(),
                                    field.getName(),
                                    true,
                                    true,
                                    field));
                    order--;
                }
            }
                
            for (Field field : cls.getDeclaredFields()) {
                if ((field.isAnnotationPresent(SearchableField.class))){
                    
                    SearchableField annotationField =
                            (SearchableField) field.getAnnotation(SearchableField.class);
                    
                    int innerOrder = annotationField.order();
                    
                    if ((field.isAnnotationPresent(SearchInnerFields.class))){
                    
                        searchType.addJoinField(field.getName());
                        SearchInnerFields searchInnerFields =
                                (SearchInnerFields) field.getAnnotation(SearchInnerFields.class);
                        List<String> searchInnerFieldsNames =
                                Arrays.asList(searchInnerFields.fields());
                        String parentHeaderName = annotationField.headerName().isEmpty() ?
                                field.getName() : annotationField.headerName();
                        for (Field innerField : field.getType().getDeclaredFields()){
                            if (searchInnerFieldsNames.contains(innerField.getName())){
                                String fieldName = field.getName()+"."+innerField.getName();
                                searchType.getSearchFields().put(fieldName,
                                        new SearchField(innerOrder,
                                                fieldName,
                                                parentHeaderName + " "
                                                        + innerField.getName().substring(0, 1).toUpperCase()
                                                        + innerField.getName().substring(1),
                                                annotationField.searched(),
                                                annotationField.returned(),
                                                innerField));
                                innerOrder++;
                            }
                        }
                    }
                    else
                        searchType.getSearchFields().put(field.getName(),
                                new SearchField(annotationField.order(),
                                        field.getName(),
                                        annotationField.headerName().isEmpty() ? 
                                                field.getName() :
                                                annotationField.headerName(),
                                        annotationField.searched(),
                                        annotationField.returned(),
                                        field));
                }
            }
            this.map.put( cls.getSimpleName(), searchType);
        });
    }
    
    public List<MasterSearchResultModel> getResult(String queryString){
        List<MasterSearchResultModel> resultModels =
                new ArrayList<>();
        for(Class<?> type : searchableTypes){
            SearchType searchType = this.map.get(type.getSimpleName());
            if (searchType != null){
                FrontendPage transactionManage =
                        pageRepository.findByCode(
                                searchType.getPermissionCode());
                if (hasPermission(CurrentRequest.getUser(), transactionManage)){
                    List<SearchField> fields = 
                            new ArrayList (searchType.getSearchFields().values());
                    fields.sort(Comparator.comparingInt(SearchField::getOrder));
                    List<SearchField> headersFields = fields.stream()
                            .filter(obj-> {return obj.isReturned();})
                            .collect(Collectors.toList());
                    List<String> headers = headersFields.stream().map(obj->
                            obj.getFieldHeader()).collect(Collectors.toList());
                    
                    List<String> stringQyeryFields;
                    
                    stringQyeryFields = fields.stream()
                            .filter(obj-> {return (obj.isSearched() &&
                                    (String.class.isAssignableFrom(
                                            obj.getField().getType())));})
                            .map(obj -> obj.getFieldName())
                            .collect(Collectors.toList());
                    
                    List<String> notStringQyeryFields;
                    
                    notStringQyeryFields = fields.stream()
                            .filter(obj-> {return (obj.isSearched() &&
                                    (!String.class.isAssignableFrom(
                                            obj.getField().getType())));})
                            .map(obj -> obj.getFieldName())
                            .collect(Collectors.toList());
                    
                    if ((headers != null) && (headers.size()>0) &&
                            (stringQyeryFields != null) && (stringQyeryFields.size()>0)){
                        
                        SelectQuery<?> query = new SelectQuery<>(type);
                        
                        for (String join : searchType.getJoins()){
                            query.leftJoin(join);
                        }
                        
                        SelectFilter filter = new SelectFilter();
                        if (stringQyeryFields.size()>0)
                            filter.or(new FilterItem(
                                    stringQyeryFields.get(0),stringQyeryFields,
                                    "like", queryString).getSelectFilter(type));
                        
                        if ((notStringQyeryFields.size()>0) && (StringUtils.isNumeric(queryString)))
                            filter.or(new FilterItem(
                                    notStringQyeryFields.get(0),notStringQyeryFields,
                                    "=", queryString)
                                    .getSelectFilter(type));
                        
                        query.filterBy(filter);
                        
                        PageImpl s = (PageImpl) query
                                .execute(PageRequest.of(0, maxResultCount))
                                .map(obj ->
                                        resultProjection(obj, type, headersFields));
                        MasterSearchResultModel e =
                                new MasterSearchResultModel(
                                        searchType.getOrder(),
                                        searchType.getUrl(),
                                        searchType.getPermissionCode(),
                                        searchType.getShownName(),
                                        (String[]) Arrays.copyOf(
                                                headers.toArray(),
                                                headers.toArray().length,
                                                String[].class),
                                        s.getContent(),
                                        s.getTotalElements());
                        resultModels.add(e);
                    }
                }
            }
        }
        resultModels.sort(
                Comparator.comparingInt(MasterSearchResultModel::getOrder));
        return resultModels;
    }
            
    private static final Logger logger = Logger.getLogger(
            BaseRepositoryController.class.getName());
    private String[] resultProjection(
            Object obj, Class<?> cls, List<SearchField> headersFields){
        String[] result = new String[headersFields.size()];
        int i =0; 
        for (SearchField field : headersFields){
            try {
                if (field.getFieldName().contains(".")){
                    Field masterField = cls.getDeclaredField(
                            field.getFieldName()
                                    .substring(0, field.getFieldName().indexOf(".")));
                    masterField.setAccessible(true);
                    
                    Object obj2 = masterField.get(obj);
                    if (obj2 != null){                        
                        Class<?> clazz = masterField.getType();
                        Field innerField = clazz.getDeclaredField(field.getFieldName()
                                    .substring(field.getFieldName().indexOf(".")+1));
                        logger.log(Level.SEVERE,innerField.getName());
                        innerField.setAccessible(true);
                        if (innerField.get(obj2) != null)
                            result[i] = innerField.get(obj2).toString();
                        else
                            result[i] = "";
                    }
                    else
                        result[i] = "";
                        
                }
                else{    
                    Field f = field.getField();
                    logger.log(Level.SEVERE,f.getName());
                    f.setAccessible(true);
                    if ((obj != null) && (f.get(obj) != null))
                        result[i] = f.get(obj).toString();
                    else
                        result[i] = "";
                }
            } catch (IllegalArgumentException | IllegalAccessException ex) {
                result[i] = "";
                Logger.getLogger(SearchMap.class.getName()).log(Level.SEVERE, null, ex);
            } catch (NoSuchFieldException | SecurityException ex) {
                Logger.getLogger(SearchMap.class.getName()).log(Level.SEVERE, null, ex);
            }
            i++;
        }
        return result;
    }
    
    Boolean hasPermission(User user,
            FrontendPage page) {
        PermissionType transPermType = user.getPagePermission(page);
        return !transPermType.equals(PermissionType.NO);

    }
}
    //*** Getters && Setters ***//
//    public Set<Class<?>> getSearchableTypes() {
//        return searchableTypes;
//    }
//
//    public Map<String, SearchType> getMap() {
//        return map;
//    }
//
//    public void setMap(Map<String, SearchType> map) {
//        this.map = map;
//    }
