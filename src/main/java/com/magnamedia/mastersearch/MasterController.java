package com.magnamedia.mastersearch;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Mar 9, 2018
 */
@RequestMapping("/master")
@RestController
public class MasterController {

    @Autowired
    private SearchMap SearchMap;
    
    @PreAuthorize("hasPermission('master','search')")
    @RequestMapping(path = "/search", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> masterSearch(
            @RequestParam(name = "search",
                    required = false) String queryString) {
        //get classes
        if ((queryString != null) && (!queryString.isEmpty())){
            return new ResponseEntity<>(
                SearchMap.getResult(queryString),
                HttpStatus.OK);
        }
        
        
        return new ResponseEntity<>(
                "",
                HttpStatus.BAD_REQUEST);
        //Set<Field> ids = reflections.getFieldsAnnotatedWith(SearchableField.class);
        
    }
}
