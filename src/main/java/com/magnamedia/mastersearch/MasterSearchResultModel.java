package com.magnamedia.mastersearch;

import java.util.List;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Mar 10, 2018
 */
public class MasterSearchResultModel {

    private final int order;
    private final String url;
    private final String pageCode;
    private final String shownName;
    private final String[] headers;
    private final List<String[]> content;
    private final long allResultCount;

    public MasterSearchResultModel(
            int order, String url, String pageCode, String shownName,
            String[] headers, List<String[]> content, long allResultCount) {
        this.order = order;
        this.url = url;
        this.pageCode = pageCode;
        this.shownName = shownName;
        this.headers = headers;
        this.content = content;
        this.allResultCount = allResultCount;
    }

    public int getOrder() {
        return order;
    }

    public String getUrl() {
        return url;
    }

    public String getPageCode() {
        return pageCode;
    }

    public String getShownName() {
        return shownName;
    }

    public String[] getHeaders() {
        return headers;
    }

    public List<String[]> getContent() {
        return content;
    }

    public long getAllResultCount() {
        return allResultCount;
    }  
}