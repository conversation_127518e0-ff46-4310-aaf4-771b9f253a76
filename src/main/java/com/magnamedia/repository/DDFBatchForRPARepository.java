package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.DDFBatchForRPA;
import com.magnamedia.module.type.DDFBatchStatus;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Sep 19, 2020
 *         Jirra ACC-2571
 */

@Repository
public interface DDFBatchForRPARepository extends BaseRepository<DDFBatchForRPA> {

    @Query("select d from DDFBatchForRPA d " +
            "where (concat(', ', d.ids, ',') like concat('%, ', ?1, ',%')) and d.status not in ?2")
    List<DDFBatchForRPA> findByIdsContainsAndStatusNotIn(String id, List<DDFBatchStatus> statuses);

    @Query("select count(d) > 0 FROM DDFBatchForRPA d " +
            "where (concat(', ', d.ids, ',') like concat('%, ', ?1, ',%')) and d.status not in ?2")
    boolean existsByIdsContainsAndStatusNotIn(String id, List<DDFBatchStatus> statuses);

    @Query("select count(d) > 0 FROM DDFBatchForRPA d " +
            "where (concat(', ', d.ids, ',') like concat('%, ', ?1, ',%')) and d.status = ?2")
    boolean existsByIdsContainsAndStatus(String id, DDFBatchStatus s);
}
