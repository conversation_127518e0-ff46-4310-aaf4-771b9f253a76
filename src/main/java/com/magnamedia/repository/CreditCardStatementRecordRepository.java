package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.CreditCardStatementRecord;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 4/21/2020
 **/

@Repository
public interface CreditCardStatementRecordRepository extends BaseRepository<CreditCardStatementRecord> {

    public List<CreditCardStatementRecord> findByBankStatementFileId(Long fileId);

    @Query(value = "select min(TRANSACTION_DATE) " +
            "from CREDITCARDSTATEMENTRECORDS " +
            "where BANK_STATEMENT_FILE_ID = :fileId " +
            "and   TERMINAL_ID IN :terminalId ",nativeQuery = true)
    public Timestamp findMinDate(@Param("fileId") Long fileId,@Param("terminalId") List<String> terminalIds);

}
