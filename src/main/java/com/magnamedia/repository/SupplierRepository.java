package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Supplier;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <PERSON> (Jan 25, 2021)
 */
@Repository
public interface SupplierRepository extends BaseRepository<Supplier> {
    Supplier findBySupplierId(String supplierId);
    Supplier findFirstByName(String name);

    List<Supplier> findBySupplierIdNotNull();
}
