package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.BankStatementRecord;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface BankStatementRecordRepository extends BaseRepository<BankStatementRecord> {


    @Query("select r from BankStatementRecord r " +
            "where r.bankStatementFile.id = ?1 and (r.paymentDetail is null or r.paymentDetail not like '500%') " +
            "order by r.date")
    List<BankStatementRecord> findByBankStatementFileIdOrderByDateAsc(Long fileId);

    @Query("select r from BankStatementRecord r " +
            "where r.bankStatementFile.id = ?1 and r.processed = false and r.paymentDetail is not null and " +
            "lower(r.paymentDetail) like 'sal%-%' and r.debitAmount > 0 " +
            "order by r.date")
    List<BankStatementRecord> findByBankStatementFileIdOrderByDateAscForPayroll(Long fileId);

    @Query("select r " +
            "from BankStatementRecord r " +
            "where r.bankStatementFile.id = ?1 and r.processed = false and r.paymentDetail like '500%' and length(r.paymentDetail) > 20 " +
            "order by r.date")
    List<BankStatementRecord> findAllBankRecordsOfDdByBankStatementFileId(Long fileId);

    @Query("select new map(r.id as rId, ddf as directDebitFile) " +
            "from BankStatementRecord r " +
            "left join DirectDebitFile ddf " +
                "on ddf.applicationId = substring(r.paymentDetail, length(r.paymentDetail) - locate('_', reverse(r.paymentDetail)) + 2) and " +
                    "ddf.ddStatus = 'CONFIRMED' " +
            "where r.id in ?1 " +
            "group by r.id")
    List<Map<String, Object>> findAllBankRecordsOfDirectDebitByBankStatementFileId(List<Long> bankRecords);
}
