package com.magnamedia.repository;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Expense;
import com.magnamedia.entity.TransactionPostingRule;
import com.magnamedia.entity.workflow.PaymentRequestPurpose;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.module.type.PaymentOrderStatus;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.workflow.type.PaymentRequestMethod;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> masod <<EMAIL>>
 *         Created on Dec 21, 2019
 *         ACC-1230
 */
@Repository
public interface TransactionPostingRuleRepository extends BaseRepository<TransactionPostingRule> {

    List<TransactionPostingRule> findByCompanyAndTypeOfPaymentAndMethodOfPaymentAndInitialPaymentAndPaymentStatusAndActiveTrue(
            PicklistItem company, PicklistItem typeOfPayment, PaymentMethod methodOfPayment, boolean initialPayment, PaymentStatus paymentStatus);

    Long countByCompanyAndTypeOfPaymentAndMethodOfPaymentAndInitialPaymentAndPaymentStatusAndActive(PicklistItem company, PicklistItem typeOfPayment,
                                                                                                    PaymentMethod methodOfPayment, boolean initialPayment,
                                                                                                    PaymentStatus paymentStatus, boolean active);

    List<TransactionPostingRule> findByPaymentOrderStatusAndPurposeOfPaymentAndPaymentOrderMethodOfPayment(
            PaymentOrderStatus paymentOrderStatus, PaymentRequestPurpose paymentRequestPurpose, PaymentRequestMethod paymentMethod
    );

    Long countByPaymentOrderStatusAndPurposeOfPaymentAndPaymentOrderMethodOfPaymentAndActive(
            PaymentOrderStatus paymentOrderStatus, PaymentRequestPurpose paymentRequestPurpose, PaymentRequestMethod paymentMethod, boolean active
    );

    //for Background task
    @Query(nativeQuery = true, value = "SELECT TBR.FROM_BUCKET_ID FROM TRANSACTIONPOSTINGRULES AS TBR where TBR.ID = ?1")
    Long findFromBucketId(Long transactionPostingRuleId);

    //for Background task
    @Query(nativeQuery = true, value = "SELECT TBR.TO_BUCKET_ID FROM TRANSACTIONPOSTINGRULES AS TBR where TBR.ID = ?1")
    Long findToBucketId(Long transactionPostingRuleId);

    boolean existsByExpenseAndActive(Expense expense, boolean active);

    @Query("select r from TransactionPostingRule r " +
            "where r.methodOfPayment = 'DIRECT_DEBIT' and " +
            "r.paymentStatus = 'RECEIVED' and r.typeOfPayment.code in ?1")
    List<TransactionPostingRule> findPostingRulesOfPaymentsDirectDebitAndReceivedByType(Set<String> types);
}