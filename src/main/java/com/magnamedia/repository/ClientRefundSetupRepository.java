package com.magnamedia.repository;

import com.magnamedia.core.entity.User;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.ClientRefundSetup;
import com.magnamedia.entity.workflow.PaymentRequestPurpose;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface ClientRefundSetupRepository extends BaseRepository<ClientRefundSetup> {

    @Query("select count(s) > 0 from ClientRefundSetup s " +
            "WHERE s.paymentRequestPurpose = :p and " +
                "(:o is null or s.partialRefundForCancellationPaymentMethod.id = :o)")
    Boolean existsBeforeByPaymentRequestPurpose(
            @Param("p") PaymentRequestPurpose p,
            @Param("o") Long partialRefundForCancellationPaymentMethodId);


    @Query("select count(s) > 0 from ClientRefundSetup s " +
            "WHERE s.paymentRequestPurpose = :p AND s.id != :id and " +
            "(:o is null or s.partialRefundForCancellationPaymentMethod.id = :o)")
    Boolean existsBeforeByPaymentRequestPurpose(
            @Param("id") Long id,
            @Param("p") PaymentRequestPurpose p,
            @Param("o") Long partialRefundForCancellationPaymentMethodId);

    @Query("select DISTINCT setup.paymentRequestPurpose.id from ClientRefundSetup setup where setup.hidden = false ")
    List<Long> getClientPurposeIds();
    
    List<ClientRefundSetup> findByApprovedBy(User u);

    List<ClientRefundSetup> findByAutoApprovedTrueAndApprovedByIsNullOrApprovedByIn(List<User> approvedBy);
}
