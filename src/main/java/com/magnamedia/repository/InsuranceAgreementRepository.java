package com.magnamedia.repository;

import com.magnamedia.core.entity.workflow.WorkFlowTaskHistory;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.InsuranceAgreement;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;

import org.springframework.data.repository.query.Param;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Sep 20, 2018
 */
@Repository
public interface InsuranceAgreementRepository extends BaseRepository<InsuranceAgreement> {

    @Query("select ia from InsuranceAgreement ia " +
            "where ia.startDate <= ?1 and ia.endDate >= ?2 " +
            "order by ia.endDate desc")
    List<InsuranceAgreement> findTopByStartDateLessThanEqualAndEndDateGreaterThanEqual(Date startDate, Date endDate, Pageable pageable);

    InsuranceAgreement findTopByOrderByEndDateDesc();

    // ACC-818 ACC-1208
    @Query("select h.id, h.name, i.eventDate, 'Housemaid', r.newEidNumber, h.passportNumber, h.landedInDubaiDate " +
            "from InsuranceDetails i " +
            "inner join NewRequest r on r.id = i.newRequest.id " +
            "inner join Housemaid h on r.housemaid.id = h.id " +
            "where i.type in ('INSURANCE_STARTED', 'INSURANCE_REACTIVATED') and " +
                "(:maidName is null or (:maidName is not null and " +
                    "((h.name is not null and h.name like :maidName) or " +
                    "(h.passportNumber is not null and h.passportNumber like :maidName)))) and " +
                "(:t1 is null or (:t1 is not null and  i.eventDate >= :t1)) and " +
                "(:t2 is null or (:t2 is not null and  i.eventDate < :t2))")
    List<Object[]> findMaidDebitNoteList(@Param("t1") Timestamp t1, @Param("t2") Timestamp t2, @Param("maidName") String maidName);

    @Query("select h.id, h.name, i.eventDate, 'Housemaid', r.newEidNumber, h.passportNumber, h.dateOfTermination  " +
            "from InsuranceDetails i " +
            "inner join NewRequest r on r.id = i.newRequest.id " +
            "inner join Housemaid h on r.housemaid.id = h.id " +
            "where i.type = 'INSURANCE_CANCELED' and " +
                "(:maidName is null or (:maidName is not null and " +
                    "((h.name is not null and h.name like :maidName) or " +
                    "(h.passportNumber is not null and h.passportNumber like :maidName)))) and " +
                "(:t1 is null or (:t1 is not null and  i.eventDate >= :t1)) and " +
                "(:t2 is null or (:t2 is not null and  i.eventDate < :t2))")
    List<Object[]> findMaidCreditNoteList(@Param("t1") Timestamp t1, @Param("t2") Timestamp t2, @Param("maidName") String maidName);

    // ACC-1208
    @Query("select os.id, os.name, i.eventDate, 'office staff', r.newEidNumber, os.passportId, os.startingDate " +
            "from InsuranceDetails i " +
            "inner join NewRequest r on r.id = i.newRequest.id " +
            "inner join OfficeStaff os on r.officeStaff.id = os.id " +
            "where i.type in ('INSURANCE_STARTED', 'INSURANCE_REACTIVATED') and " +
                "(:maidName is null or (:maidName is not null and " +
                    "((os.name is not null and os.name like :maidName) or " +
                    "(os.passportId is not null and os.passportId like :maidName)))) and " +
                "(:t1 is null or (:t1 is not null and  i.eventDate >= :t1)) and " +
                "(:t2 is null or (:t2 is not null and  i.eventDate < :t2))")
    List<Object[]> findOfficeStaffDebitNoteList(@Param("t1") Timestamp t1, @Param("t2") Timestamp t2, @Param("maidName") String maidName);

    @Query("select os.id, os.name, i.eventDate, 'office staff', r.newEidNumber, os.passportId, os.terminationDate " +
            "from InsuranceDetails i " +
            "inner join NewRequest r on r.id = i.newRequest.id " +
            "inner join OfficeStaff os on r.officeStaff.id = os.id " +
            "where i.type = 'INSURANCE_CANCELED' and " +
                "(:maidName is null or (:maidName is not null and " +
                    "((os.name is not null and os.name like :maidName) or " +
                    "(os.passportId is not null and os.passportId like :maidName)))) and " +
                "(:t1 is null or (:t1 is not null and  i.eventDate >= :t1)) and " +
                "(:t2 is null or (:t2 is not null and  i.eventDate < :t2))")
    List<Object[]> findOfficeStaffCreditNoteList(@Param("t1") Timestamp t1, @Param("t2") Timestamp t2, @Param("maidName") String maidName);


    // ACC-1862
    @Query("select W " +
            "from WorkFlowTaskHistory W " +
            "INNER JOIN RenewRequest NR ON NR.id = W.taskId  " +
            "WHERE W.taskMoveOutDate IS NOT NULL " +
            "AND W.type like 'com.magnamedia.entity.RenewRequest' " +
            "AND W.taskName LIKE 'Upload Contract to Tasheel' "+
            "AND NR.id =  ?1 ")
    List<WorkFlowTaskHistory> findCompleteRenewRequest(Long renewRequestId);
}