package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Category;
import com.magnamedia.entity.Item;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <PERSON> (Jan 25, 2021)
 */
@Repository
public interface ItemRepository extends BaseRepository<Item> {
    Item findByItemId(String itemId);

    Item findFirstByNameAndCategory(String name, Category category);

    //    List<Item> findByCategoryIdStr(String categoryId);
    List<Item> findByCategory(Category category);

    Page<Item> findByCategoryOrderByCreationDateDesc(Category category, Pageable pageable);

    Page<Item> findAllByOrderByCreationDateDesc(Pageable pageable);
}
