package com.magnamedia.repository;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.sms.SmsTemplate;
import com.magnamedia.entity.sms.SmsTranslation;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR> <<EMAIL>> 
 * Created at Jun 20, 2019
 * <AUTHOR> kanaan <<EMAIL>>
 * Jirra ACC-1092
 */
@Repository
public interface SmsTranslationRepository extends BaseRepository<SmsTranslation> {
    SmsTranslation findFirst1ByTemplateAndNationality(SmsTemplate template,PicklistItem nationality);
}
