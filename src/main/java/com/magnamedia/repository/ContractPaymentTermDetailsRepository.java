package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.ContractPaymentTermDetails;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.sql.Date;
import java.util.List;

/**
 * <AUTHOR> <PERSON>ud
 *         ACC-6150
 */

@Repository
public interface ContractPaymentTermDetailsRepository extends BaseRepository<ContractPaymentTermDetails> {

    @Query("select d from ContractPaymentTermDetails d " +
            "where ((d.detailLevel = 'CONTRACT' and d.contractPaymentTerm.contract = ?1) or (d.contractPaymentTerm = ?2)) and " +
                    "d.startDate <= ?3 and (d.endDate is null or d.endDate >= ?3)")
    List<ContractPaymentTermDetails> findByCptAndDate(Contract c, ContractPaymentTerm cpt, Date s);

    @Query("select d from ContractPaymentTermDetails d " +
            "where ((d.detailLevel = 'CONTRACT' and d.contractPaymentTerm.contract = ?1) or (d.contractPaymentTerm = ?2))")
    List<ContractPaymentTermDetails> findByDetailLevel(Contract c, ContractPaymentTerm cpt);

    List<ContractPaymentTermDetails> findByContractPaymentTerm_ContractAndSource(Contract c, ContractPaymentTermDetails.Source source);
}