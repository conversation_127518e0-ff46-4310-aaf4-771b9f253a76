package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.HousemaidAttendanceLog;
import org.springframework.stereotype.Repository;

import java.util.Date;


@Repository
public interface HousemaidAttendanceLogRepository extends BaseRepository<HousemaidAttendanceLog> {

    Boolean existsHousemaidAttendanceLogByHousemaidAndAttendanceStatusAndCreationDateGreaterThan(Housemaid housemaid, HousemaidAttendanceLog.AttendanceStatus status, Date logDate);

}
