package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.VisaExpensePaymentTypeDetails;
import com.magnamedia.module.type.PaymentType;
import com.magnamedia.workflow.visa.ExpensePurpose;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> Created at Nov 24, 2017
 */
@Repository
public interface VisaExpensePaymentTypeDetailsRepository extends BaseRepository<VisaExpensePaymentTypeDetails> {

    boolean existsByPaymentTypeAndExpensePurposeAndIdNot(PaymentType paymentType, ExpensePurpose expensePurpose, Long id);

    VisaExpensePaymentTypeDetails findFirstByPaymentTypeAndExpensePurpose(PaymentType paymentType, ExpensePurpose expensePurpose);
}
