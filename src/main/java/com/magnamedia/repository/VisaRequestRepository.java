package com.magnamedia.repository;

import com.magnamedia.core.repository.workflow.WorkflowRepository;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.VisaRequest;
import java.util.List;
import org.springframework.data.repository.NoRepositoryBean;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Sep 16, 2017
 */
@NoRepositoryBean
public interface VisaRequestRepository<T extends VisaRequest> extends WorkflowRepository<T>  {

	public List<T> findByHousemaid(Housemaid housemaid);
	public List<T> findByOfficeStaff(OfficeStaff officeStaff);

}
