package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.DDMessagingToDo;
import com.magnamedia.module.type.DDMessagingType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.sql.Time;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Created on Apr 13, 2020
 * ACC-1611
 */
@Repository
public interface DDMessagingToDoRepository extends BaseRepository<DDMessagingToDo> {

    //ACC-3880
    @Query("SELECT t FROM DDMessagingToDo t " + 
            "WHERE t.id > ?1 and ((t.creationDate >= ?2 AND t.sendDate IS NULL) " +
                "OR (t.sendDate IS NOT NULL AND t.sendDate >= ?2)) " +
                "AND t.active = 1 AND t.isSent = 0")
    Page<DDMessagingToDo> findByDdMessagingToDosToSend(Long id, Date yesterday, Pageable pageable);

    List<DDMessagingToDo> findByContractUuidAndIsSentAndEventIn(String contractUUID, Boolean isSent, List<DDMessagingType> ddMessagingTypes);

    List<DDMessagingToDo> findByPaymentIdAndIsSentAndEvent(Long paymentId, Boolean isSent, DDMessagingType ddMessagingTypes);

    //jirra SD-15039
    List<DDMessagingToDo> findByContractUuidAndIsSent(String contractUUID, Boolean isSent);

    Boolean existsByClientIdAndSendTimeAndSendDateAndEvent(Long clientId, Time sendTime, java.sql.Date sendDate, DDMessagingType event);

    @Query("select todo from DDMessagingToDo todo " +
            "where todo.contractUuid = ?1 and todo.event = ?2 and todo.active = true and todo.isSent = false")
    List<DDMessagingToDo> findActiveDDMessagingToDoWithTerminateDDMessaging(String uuid, DDMessagingType event);

    @Query("select todo from DDMessagingToDo todo " +
            "where todo.contractUuid = ?1 and todo.event = ?2 and todo.isTerminationMessage = true and todo.active = true")
    List<DDMessagingToDo> findActiveDDMessagingToDoWithTerminateDDMessagingAndTerminationContract(String uuid, DDMessagingType event);
}
