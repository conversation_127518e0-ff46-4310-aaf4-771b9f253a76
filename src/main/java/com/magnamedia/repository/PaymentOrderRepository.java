package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.PaymentOrder;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 6-10-2020
 * Jirra ACC-1962
 */
@Repository
public interface PaymentOrderRepository extends BaseRepository<PaymentOrder> {

//    Integer countByHousemaidAndCountedTrue(Housemaid housemaid);

}
