package com.magnamedia.repository;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.ScheduledAnnualVacation;
import java.util.List;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */

@Repository
public interface ScheduledAnnualVacationRepository extends BaseRepository<ScheduledAnnualVacation>{
    
    List<ScheduledAnnualVacation> findByHousemaid(Housemaid housemaid);
    List<ScheduledAnnualVacation> findByHousemaidAndType(Housemaid housemaid,PicklistItem type);
}
