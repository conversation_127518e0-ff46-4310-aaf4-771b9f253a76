package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.VisaStatement;
import com.magnamedia.entity.VisaStatementTransaction;
import com.magnamedia.extra.VisaStatementTransactionType;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Repository
public interface VisaStatementTransactionRepository extends BaseRepository<VisaStatementTransaction> {
    VisaStatementTransaction findTopByStatementAndFinished(VisaStatement statement, Boolean finished);
    List<VisaStatementTransaction> findByStatement(VisaStatement statement);

    VisaStatementTransaction findTopByStatementAndTypeNotAndFinishedFalse(
            VisaStatement statement, VisaStatementTransactionType t);

    List<VisaStatementTransaction> findByStatementAndTypeAndFinishedFalse(
            VisaStatement statement, VisaStatementTransactionType t);

    @Query("select new map(v.id as visaStatementTransactionId) " +
            "from VisaStatementTransaction v " +
            "where v.statement = ?1 and v.type = ?2 and v.finished = false and v.fromBucket is not null and " +
                "v.expense is not null and v.description is not null")
    List<Map<String, Object>> findRecordsToBeConfirmed(
            VisaStatement statement, VisaStatementTransactionType type);

    @Query("select distinct st.id from VisaStatementTransaction t " +
            "join t.statement st " +
            "where st.creationDate >= ?1 and st.id > ?2 and t.transaction is null and " +
            "t.type = ?3 and t.finished = false")
    Page<Long> findByTypeAndFinishedFalse(Date d, Long id, VisaStatementTransactionType type, Pageable page);

    @Query("select t from VisaStatementTransaction t " +
            "where t.type = ?1 and t.referenceNumber = ?2 and t.finished = false")
    List<VisaStatementTransaction> findByTypeAndReferenceNumberAfterAndFinishedFalse(
            VisaStatementTransactionType type, String referenceNumber);

    @Query("select new map (t.id as id, t.creationDate as creationDate, t.rowRecordDate as rowRecordDate," +
            " t.referenceNumber as referenceNumber, t.amount as amount, t.type as type, st.creationDate as fileUploadDate) " +
            "from VisaStatementTransaction t " +
            "join t.statement st " +
            "where st.id = ?1 and t.type in ?2")
    List<Map> findByTypeAndAmountsInAndFinishedFalse(
            Long statementId, List<VisaStatementTransactionType> type);
}