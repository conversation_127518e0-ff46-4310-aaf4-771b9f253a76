package com.magnamedia.repository;

import com.magnamedia.core.repository.workflow.WorkflowRepository;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.workflow.VoiceResolverToDo;
import com.magnamedia.module.type.VoiceResolverToDoReason;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */

public interface VoiceResolverToDoRepository extends WorkflowRepository<VoiceResolverToDo> {

    @Query("SELECT vr from VoiceResolverToDo vr "
            + "WHERE ( vr.completed IS NULL "
            + "OR vr.completed = false ) "
            + "AND vr.reason = ?2 "
            + "AND vr.contract = ?1")
    List<VoiceResolverToDo> findOpenByContractAndReason(Contract contract, VoiceResolverToDoReason reason);

    @Query("SELECT vr from VoiceResolverToDo vr "
            + "WHERE ( vr.completed IS NULL "
            + "OR vr.completed = false ) "
            + "AND vr.client = ?1 ")
    List<VoiceResolverToDo> findByClient(Client client);
}