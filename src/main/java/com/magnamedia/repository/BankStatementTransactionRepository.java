package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.BankStatementTransaction;
import com.magnamedia.entity.Payment;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created At Apr 18, 2020
 *
 **/


@Repository
public interface BankStatementTransactionRepository extends BaseRepository<BankStatementTransaction> {

    Long countByFileIdAndResolved(long fileId, boolean resolved);

    boolean existsByUniqueId(String uniqueId);

    @Query("select t.uniqueId from BankStatementTransaction t where t.uniqueId in ?1")
    List<String> findUniqueIdByUniqueIdIn(List<String> uniqueId);

    List<BankStatementTransaction> findByFileId(Long fileId);

    List<BankStatementTransaction> findByPayment(Payment payment);

    BankStatementTransaction findFirstByClientRefundToDo(ClientRefundToDo clientRefundToDo);

    @Query("select bst, t " +
            "from BankStatementTransaction bst " +
            "inner join Transaction t on t.payrollUniqueCode like concat(substring(bst.paymentDetail, 1, locate('-', bst.paymentDetail) - 1), '%') " +
            "where bst.file.id = :fileId and bst.bankTransactionType = 'PAYROLL_TRANSFER' and " +
                "bst.resolved = false and bst.date >= :minimumDate and bst.paymentDetail is not null and " +
                "bst.paymentDetail <> '' and locate('-', bst.paymentDetail) > 0")
    List<Object[]> findRecordsShouldBeAlreadyMatched(@Param("fileId") Long fileId, @Param("minimumDate") java.sql.Date minimumDate);

    @Query("select bst, l, e " +
            "from BankStatementTransaction bst " +
            "inner join OfficeStaffPayrollLog l on l.bankTransferUniqueCode like concat(substring(bst.paymentDetail, 1, locate('-', bst.paymentDetail) - 1), '%') " +
            "inner join l.payrollAccountantTodo " +
            "left join Expense e on e.code = substring(bst.paymentDetail, length(bst.paymentDetail) - locate('-', reverse(bst.paymentDetail)) + 2) and " +
                "e.deleted = false " +
            "where bst.file.id = :fileId and " +
                "bst.bankTransactionType = 'PAYROLL_TRANSFER' and bst.bankTransactionStatus = 'UNMATCHED_PAYROLL_EXPENSE' and " +
                "bst.reason = :reason and bst.date >= :minimumDate and " +
                "bst.paymentDetail is not null and bst.paymentDetail <> '' and locate('-', bst.paymentDetail) > 0")
    List<Object[]> findUnMatchedRecordsByReason(
            @Param("fileId") Long fileId, @Param("reason") String reason,
            @Param("minimumDate") java.sql.Date minimumDate);
    @Query("select count (b.id) > 0 from BankStatementTransaction b " +
            "join b.bankStatementRecord.bankStatementFile f " +
            "join b.payment p " +
            "where f.deleted = false and b.creationDate >= ?1 and " +
                "b.bankTransactionType = 'DIRECT_DEBIT' and b.resolved = false")
    boolean existsUnResolvedBankStatementTransaction(Date creationDate);
}
