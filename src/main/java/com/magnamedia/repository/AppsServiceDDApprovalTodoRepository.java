package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.AppsServiceDDApprovalTodo;
import com.magnamedia.entity.AppsServiceDDApprovalTodo.Result;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */

@Repository
public interface AppsServiceDDApprovalTodoRepository extends BaseRepository<AppsServiceDDApprovalTodo> {

    @Query("select count(ddc.id) > 0 from AppsServiceDDApprovalTodo ddc " +
            "where ddc.id = ?1 and ddc.result = ?2 ")
    boolean existsIfDdcIsClosedByDdcIdAndResult(Long ddcId, Result result);

    @Query("select count(ddc.id) > 0 from AppsServiceDDApprovalTodo ddc " +
            "where ddc.id = ?1 and ddc.isClosed = true and ddc.result = 'CLOSED_WITH_CONFIRMATION'")
    boolean existsClosedDdcWithConfirmation(Long ddcId);
}