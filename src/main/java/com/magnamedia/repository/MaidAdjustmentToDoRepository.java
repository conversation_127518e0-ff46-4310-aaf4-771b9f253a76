package com.magnamedia.repository;

import com.magnamedia.core.repository.workflow.WorkflowRepository;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.maidAdjustment.MaidAdjustmentToDo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 *
 * <AUTHOR>
 */

@Repository
public interface MaidAdjustmentToDoRepository extends WorkflowRepository<MaidAdjustmentToDo> {
    
    List<MaidAdjustmentToDo> findByHousemaidAndCompleted(Housemaid housemaid, Boolean isCompleted);
    
    List<MaidAdjustmentToDo> findByHousemaidAndTaskNameAndStoppedFalseAndCompletedFalse(Housemaid housemaid, String taskName);
    List<MaidAdjustmentToDo> findByHousemaidAndTaskNameIgnoreCaseContainingAndStoppedFalseAndCompletedFalse(Housemaid housemaid, String taskName);
    
    List<MaidAdjustmentToDo> findByHousemaidOrderByCreationDateDesc(Housemaid housemaid);
}
