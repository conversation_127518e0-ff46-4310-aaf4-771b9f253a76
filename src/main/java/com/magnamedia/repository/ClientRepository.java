package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Client;
import java.util.List;

import com.magnamedia.entity.ContractPaymentTerm;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Aug 29, 2019
 * ACC-837
 */
@Repository
public interface ClientRepository extends BaseRepository<Client> {
    
    public List<Client> findByMobileNumber(String mobileNumber);

    public List<Client> findByNameContaining(String name, Pageable pageable);

    Client findFirstOneByDiscountCode(String discountCode);

    @Query("select distinct cl from ContractPaymentTerm cpt inner join cpt.contract c inner join c.client cl where (cpt.eid = ?1 or cpt.ibanNumber = ?2) and cl <> ?3")
    List<Client> findClientByEidAndIban(String eid, String iban, Client currentClient);

    @Query(nativeQuery = true, 
            value = "SELECT V.CONTRACT_ID FROM VISASERVICEAPPLICATIONS V " + 
                    "LEFT JOIN CONTRACTS CO ON V.CONTRACT_ID = CO.ID " + 
                    "WHERE V.EXISTING_CLIENT_FLOW = 1 AND V.DD_GENERATION_REQUESTED = 1 AND CO.STATUS = 'POSTPONED'")
    List<Long> findPostponedExistingMvClients();
}
