package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.CollectionFlowJobHistory;
import com.magnamedia.module.type.CollectionJobStatus;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 3/12/2022
 **/

@Repository
public interface CollectionFlowJobHistoryRepository extends BaseRepository<CollectionFlowJobHistory> {
    CollectionFlowJobHistory findTopByStatusOrderByIdDesc(CollectionJobStatus status);

    CollectionFlowJobHistory findTopByOrderByIdDesc();
}
