package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.GraphicDesignerToDo;
import com.magnamedia.workflow.newversion.services.graphicdesigner.GraphicToDoType;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
/**
 * <AUTHOR> <<EMAIL>>
 * Created At 4/18/2022
 **/

@Repository
public interface GraphicDesignerTodoRepository extends BaseRepository <GraphicDesignerToDo> {

    boolean existsByContractIdAndToDoTypeAndCompletedFalse(Long contractId, GraphicToDoType toDoType);

    GraphicDesignerToDo findTopByContractIdAndToDoTypeAndCompletedFalseOrderByIdDesc(Long contractId, GraphicToDoType toDoType);

    @Query("select count(g.id) > 0 from GraphicDesignerToDo g " +
            "where g.contractId = ?1 and g.toDoType = 'ACCOUNTING_DIGITALIZE_CLIENT_SIGNATURE' and " +
                "g.creationDate > ?2 and g.completed = false")
    boolean existsByContractIdAndToDoTypeAndCreationDateGreaterThan(Long contractId, Date d);
}
