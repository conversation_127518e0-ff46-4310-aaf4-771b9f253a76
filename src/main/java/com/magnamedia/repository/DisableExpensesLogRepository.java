package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.DisableExpensesLog;
import com.magnamedia.entity.Expense;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 10/8/2020
 **/
@Repository
public interface DisableExpensesLogRepository extends BaseRepository<DisableExpensesLog> {

    DisableExpensesLog findTopByExpenseOrderByCreationDateDesc(Expense expense);

    @Query("SELECT d FROM DisableExpensesLog d where d.expense = ?1 and d.disableDate < ?2 and (d.enableDate is null or d.enableDate > ?2) order by creationDate desc")
    List<DisableExpensesLog> getDisabledExpenseByExpenseAndTransactionDate(Expense expense, Date transactionCreationDate);

    @Query("SELECT distinct e.id from DisableExpensesLog d inner join d.expense e where d.disableDate < ?1 and (d.enableDate is null or d.enableDate > ?1)")
    List<Long> getDisabledExpenses(Date transactionCreationDate);
}
