package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.payroll.logging.OfficeStaffPayrollLog;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.sql.Date;
import java.util.List;

/**
 * Created by hp on 2/20/2021.
 */
@Repository
public interface OfficeStaffPayrollLogRepository extends BaseRepository<OfficeStaffPayrollLog> {

    List<OfficeStaffPayrollLog> findByBankTransferUniqueCodeStartingWith(String bankTransferUniqueCodePrefix);

    @Query("SELECT DISTINCT o.payrollMonth " +
            "FROM OfficeStaffPayrollLog o " +
            "WHERE o.payrollAccountantTodo.id = :todoId")
    List<Date> findDistinctPayrollMonthsByTodoId(@Param("todoId") Long todoId);
}
