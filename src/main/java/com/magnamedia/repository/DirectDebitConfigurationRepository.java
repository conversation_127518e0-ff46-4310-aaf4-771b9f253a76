package com.magnamedia.repository;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.DirectDebitConfiguration;
import org.springframework.stereotype.Repository;

/**
 * Created by Mamon.Masod on 5/2/2021.
 */
@Repository
public interface DirectDebitConfigurationRepository extends BaseRepository<DirectDebitConfiguration> {
    DirectDebitConfiguration findFirstByBank(PicklistItem bank);
}