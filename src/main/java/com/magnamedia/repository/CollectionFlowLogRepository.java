package com.magnamedia.repository;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.CollectionFlowLog;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.FlowProcessorEntity;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.*;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.workflow.type.ClientRefundStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 3/5/2022
 **/

@Repository
public interface CollectionFlowLogRepository extends BaseRepository<CollectionFlowLog> {

    List<CollectionFlowLog> findByEndedFalseOrderByIdDesc();

    List<CollectionFlowLog> findByEndedTrueAndRejectionTodoIsRescheduledWhenEndedTrue();

    @Query("select count(c) from CollectionFlowLog c where c.contract = ?1 and c.ended = false")
    Integer countCollectionFlowLogsByContract(Contract contract);

    @Query("select count(c) from CollectionFlowLog c inner join c.contract co where co.client = ?1 and c.ended = false")
    Integer countCollectionFlowLogsByClient(Client client);
    CollectionFlowLog findTopByFlowType_CodeAndContractAndEndedFalse(String flowType, Contract contract);

    @Query(value=
            "select new map (l.id as id, l.flowType.name as flowType, l.triggerDate as triggerDate, " +
                    "l.idleSinceDate as idleSinceDate, l.status as status, l.notes as notes, " +
                    "(case " +
                        "when l.flowType.code = 'bounced_payment_flow' then " +
                            "(select p.lastMessageDate from Payment p " +
                                "where p.id = l.relatedToId and l.relatedToEntity = 'Payment') " +
                        "when l.flowType.code = 'dd_rejection_flow' then " +
                            "(select d.lastMessageDate from DirectDebitRejectionToDo d " +
                                "where d.id = l.relatedToId  and l.relatedToEntity = 'DirectDebitRejectionToDo') " +
                        "when l.flowType.code in ('client_paying_via_credit_card_flow', 'one_month_agreement_flow') then " +
                            "(select f.lastMessageDate from FlowProcessorEntity f " +
                                "where f.id = l.currentRunningFlowId  and l.currentRunningFlowEntity = 'FlowProcessorEntity') " +
                        "when l.flowType.code = 'missing/wrong_document_flow' then " +
                            "(select max(d.lastMessageDate) from DirectDebit d " +
                                "where d.contractPaymentTerm.id = l.relatedToId  and l.relatedToEntity = 'ContractPaymentTerm') " +
                        "when l.flowType.code in ('initial_payment_by_alternative_method_flow', 'online_payments_reminder_flow', " +
                                "'incomplete_flow_missing_bank_info', 'extension_flow') then " +
                            "(select f.lastMessageDate from FlowProcessorEntity f " +
                                "where f.id = l.relatedToId  and l.relatedToEntity = 'FlowProcessorEntity') " +
                        "else null end) as lastMessageDate) " +
                    "from CollectionFlowLog l " +
                    "where l.contract.id = ?1 and l.ended = false " +
                    "group by l.id",
            countQuery = "select count(l.id) from CollectionFlowLog l where l.contract.id = ?1 and l.ended = false")
    Page<Map<String,Object>> findByContractAndEndedFalse(Long contractId, Pageable pageable);

    CollectionFlowLog findTopByFlowTypeAndRelatedToIdAndRelatedToEntityAndContractAndEndedFalse(PicklistItem flowType, Long relatedToId, String relatedToEntity, Contract contract);

    boolean existsByFlowTypeAndRelatedToIdAndRelatedToEntityAndContractAndEndedFalse(PicklistItem flowType, Long relatedToId, String relatedToEntity, Contract contract);

    boolean existsByFlowTypeAndContractAndEndedFalse(PicklistItem flowType, Contract contract);

    @Query("select c from Contract c " +
            "where c.id > ?1 and c.isOneMonthAgreement = true and c.status = 'ACTIVE' " +
            "and not exists (select 1 from CollectionFlowLog l where l.contract = c " +
                "and l.flowType.code ='one_month_agreement_flow' and l.ended = false)")
    Page<Contract> findContractFlaggedOneMonthAgreementWithoutLogs(Long lastId, Pageable pageable);

    @Query(value =
            "SELECT l.ID, (CASE WHEN ddf.ddf_id IS NULL THEN 'PENDING_CLIENT_RESPONSE' " +
                "WHEN ddf.ddf_status = 'SENT' AND (ddf.dd_status = 'PENDING' OR ddf.dd_m_status = 'PENDING') THEN 'PENDING_BANK_RESPONSE' " +
                "ELSE 'PENDING_BUSINESS_USER_ACTION' END) AS log_tatus " +
            "FROM COLLECTIONFLOWLOGS l " +
            "INNER JOIN PICKLISTS_ITEMS i ON i.ID = l.FLOW_TYPE_ID " +
            "INNER JOIN CONTRACTS c ON l.CONTRACT_ID = c.ID AND c.PAYING_VIA_CREDIT_CARD " +
            "INNER JOIN CONTRACTPAYMENTTERMS t ON t.CONTRACT_ID = c.ID AND t.IS_ACTIVE " +
            "LEFT JOIN (" +
                "SELECT  " +
                    "ddf.ID AS ddf_id, " +
                    "ddf.STATUS AS ddf_status, " +
                    "d.STATUS AS dd_status, " +
                    "d.M_STATUS AS dd_m_status, " +
                    "d.CONTRACT_PAYMENT_TERM_ID as dd_cpt " +
                "FROM DIRECTDEBITFILES ddf " +
                    "INNER JOIN DIRECTDEBITS d ON ddf.DIRECT_DEBIT_ID = d.ID AND (d.STATUS IN ('PENDING', 'PENDING_DATA_ENTRY')  " +
                        "OR d.M_STATUS IN ('PENDING', 'PENDING_DATA_ENTRY')) " +
            ") ddf ON ddf.dd_cpt = t.ID " +
            "WHERE l.ID > ?1 and i.CODE ='one_month_agreement_flow' AND l.ENDED = false AND " +
                    "l.STATUS != (CASE WHEN ddf.ddf_id IS NULL THEN 'PENDING_CLIENT_RESPONSE' " +
                                        "WHEN ddf.ddf_status = 'SENT' AND (ddf.dd_status = 'PENDING' OR ddf.dd_m_status = 'PENDING') " +
                                        "THEN 'PENDING_BANK_RESPONSE' " +
                                        "ELSE 'PENDING_BUSINESS_USER_ACTION' END) " +
            "GROUP BY c.ID",
        nativeQuery = true)
    Page<Object[]> findOMALogsNeedToUpdate(Long lastId, Date d, Pageable pageable);

    @Query("select distinct c.flowType.name " +
            "from CollectionFlowLog c " +
            "where c.contract = ?1 and c.ended = false")
    List<String> findByContractAndEndedFalse(Contract contract);

    @Query("select c from Contract c " +
            "where c.id > ?1 and c.payingViaCreditCard = true and c.isOneMonthAgreement = false and c.status = 'ACTIVE' " +
            "and not exists (select 1 from CollectionFlowLog l where l.contract = c " +
                 "and l.flowType.code in ('CLIENTS_PAYING_VIA_Credit_Card', 'EXTENSION_FLOW') and l.ended = false) and " +
            "not exists (select 1 from FlowProcessorEntity f where f.contractPaymentTerm.contract = c and " +
                "f.stopped = false and f.completed = false and f.flowEventConfig.name = 'EXTENSION_FLOW')")
    Page<Contract> findContractFlaggedPayingViaCreditCardWithoutLogs(Long lastId, Pageable pageable);

    /*@Query(value = "SELECT f.ID as flow_id, f.ENTITY_TYPE, c.ID as contract_id, REPLACE(g.NAME, 'collection_log_type:', '') as log_type " +
            "FROM FLOWPROCESSORENTITYS f " +
            "INNER JOIN CONTRACTPAYMENTTERMS cpt ON f.CONTRACT_PAYMENT_TERM_ID = cpt.ID " +
            "INNER JOIN CONTRACTS c ON c.ID = cpt.CONTRACT_ID " +
            "INNER JOIN FLOWEVENTCONFIGS fc ON fc.ID = f.FLOW_EVENT_CONFIG_ID " +
            "INNER JOIN FLOWEVENTCONFIGTAGS fg ON fg.ITEM = fc.ID " +
            "INNER JOIN TAGS g ON g.ID = fg.TAG AND g.NAME LIKE 'collection_log_type:%' " +
            "WHERE f.ID > ?1 AND c.STATUS NOT IN ('CANCELLED', 'EXPIRED') AND f.STOPPED = false AND " +
                "f.IS_COMPLETED = false AND fc.NAME IN ?2 AND NOT EXISTS (" +
                    "SELECT 1 FROM COLLECTIONFLOWLOGS l INNER JOIN PICKLISTS_ITEMS li ON li.ID = l.FLOW_TYPE_ID " +
                                                "WHERE l.RELATED_TO_ENTITY = f.ENTITY_TYPE AND l.RELATED_TO_ID = f.ID AND " +
                                                        "l.ENDED = false AND li.CODE = REPLACE(g.NAME, 'collection_log_type:', ''))",
            nativeQuery = true)
    Page<Object[]> findFlowProcessorEntityWithoutLogs(
            Long lastId, List<String> flowEventNames, Pageable pageable);*/

    @Query(value = "SELECT `f`.ID as flow_id, `f`.ENTITY_TYPE as entity_type, " +
                          "c.ID as contract_id, REPLACE(g.NAME, 'collection_log_type:', '') as log_type " +
            "FROM FLOWPROCESSORENTITYS `f` " +
            "INNER JOIN CONTRACTPAYMENTTERMS cpt ON cpt.ID = `f`.CONTRACT_PAYMENT_TERM_ID " +
            "INNER JOIN CONTRACTS c ON c.ID = cpt.CONTRACT_ID " +
            "INNER JOIN FLOWEVENTCONFIGS fc ON fc.ID = `f`.FLOW_EVENT_CONFIG_ID " +
            "INNER JOIN FLOWEVENTCONFIGTAGS fg ON fg.ITEM = fc.ID " +
            "INNER JOIN TAGS g ON g.ID = fg.TAG AND g.NAME LIKE 'collection_log_type:%' " +
            "LEFT JOIN COLLECTIONFLOWLOGS l ON l.RELATED_TO_ENTITY = `f`.ENTITY_TYPE AND " +
                "l.RELATED_TO_ID = `f`.ID AND l.ENDED = false " +
            "LEFT JOIN PICKLISTS_ITEMS li ON li.ID = l.FLOW_TYPE_ID " +
            "WHERE `f`.ID > ?1 AND `f`.STOPPED = false AND `f`.IS_COMPLETED = false AND " +
                "c.STATUS NOT IN ('CANCELLED', 'EXPIRED') AND fc.NAME IN ?2 AND " +
                    "(l.ID IS NULL OR li.CODE <> REPLACE(g.NAME, 'collection_log_type:', ''))",
            nativeQuery = true)
    Page<Object[]> findFlowProcessorEntityWithoutLogs(
            Long lastId, List<String> flowEventNames, Pageable pageable);

    //todo to be discussed for left join with select and if we could reflect it to jpa query
    @Query(value = "SELECT l.ID, (CASE WHEN p.ID IS NOT NULL AND ddf.ID IS NOT NULL THEN 'PENDING_BANK_RESPONSE' " +
            "ELSE 'PENDING_CLIENT_RESPONSE' END) AS log_status " +
            "FROM COLLECTIONFLOWLOGS l " +
            "INNER JOIN PICKLISTS_ITEMS i ON i.ID = l.FLOW_TYPE_ID " +
            "INNER JOIN CONTRACTS c ON l.CONTRACT_ID = c.ID AND c.PAYING_VIA_CREDIT_CARD = true " +
            "INNER JOIN CONTRACTPAYMENTTERMS t ON t.CONTRACT_ID = c.ID AND t.IS_ACTIVE = true " +
            "LEFT JOIN PAYMENTS p ON p.CONTRACT_ID = c.ID AND p.STATUS = 'RECEIVED' AND " +
                "p.TYPE_OF_PAYMENT_ID = 1 AND p.DATE_OF_PAYMENT >= ?2 AND p.DATE_OF_PAYMENT < ?3 " +
            "LEFT JOIN ( " +
                "SELECT  " +
                    "ddf.ID, " +
                    "ddf.LAST_MODIFICATION_DATE, " +
                    "d.CONTRACT_PAYMENT_TERM_ID as dd_cpt " +
                    "FROM DIRECTDEBITFILES ddf " +
                    "INNER JOIN DIRECTDEBITS d ON ddf.DIRECT_DEBIT_ID = d.ID AND d.CATEGORY = 'B' " +
                    "AND (d.STATUS = 'PENDING' OR d.M_STATUS = 'PENDING') " +
                "WHERE ddf.STATUS = 'SENT' " +
            ") ddf ON ddf.dd_cpt = t.ID " +
            "WHERE l.ID > ?1 AND i.CODE ='client_paying_via_credit_card_flow' AND l.ENDED = false " +
                "AND l.STATUS != (CASE WHEN p.ID IS NOT NULL AND ddf.ID IS NOT NULL THEN 'PENDING_BANK_RESPONSE' " +
                "ELSE 'PENDING_CLIENT_RESPONSE' END) " +
            "GROUP BY c.ID",
        nativeQuery = true)
    Page<Object[]> findPayingViaCcLogsNeedToUpdate(Long lastId, Date nextMonth, Date nextMonthPlusOne, Pageable pageable);

    @Query("select l from CollectionFlowLog l " +
            "join l.contract c " +
            "where l.id > ?1 and l.flowType.code = ?2 and l.ended = false and " +
                "(c.payingViaCreditCard = false or c.isOneMonthAgreement = true or c.status in ('CANCELLED', 'EXPIRED') or " +
                "exists (select 1 from FlowProcessorEntity f " +
                        "where f.flowEventConfig.name = 'EXTENSION_FLOW' and f.stopped = false and f.completed = false))")
    Page<CollectionFlowLog> findLogsNeedToStopByFlowTypeCode(Long lastId, String flowTypeCode, Pageable pageable);

    @Query("select l from CollectionFlowLog l " +
            "join l.contract c " +
            "where l.id > ?1 and l.flowType.code = ?2 and l.ended = false and " +
                "(c.isOneMonthAgreement = false or c.status in ('CANCELLED', 'EXPIRED') or " +
                "exists (select 1 from FlowProcessorEntity f " +
                    "where f.flowEventConfig.name = 'EXTENSION_FLOW' and f.stopped = false and f.completed = false))")
    Page<CollectionFlowLog> findLogsNeedToStopByFlowTypeCodeForOMA(Long lastId, String flowTypeCode, Pageable pageable);

    @Query("select l from CollectionFlowLog l " +
            "inner join FlowProcessorEntity f on l.relatedToId = f.id and l.relatedToEntity = f.entityType " +
            "where l.id > ?1 and l.ended = false and l.flowType.code in ?2 and (f.stopped = true or f.completed = true)")
    Page<CollectionFlowLog> findFlowProcessorEntityStoopedWithLogs(Long lastId, List<String> logTypes, Pageable pageable);

    @Query("select c from Contract c " +
            "where c.status not in ('CANCELLED', 'EXPIRED') and " +
            "exists (select 1 from FlowProcessorEntity f where f.contractPaymentTerm.contract = c and " +
            "f.stopped = false and f.completed = false and f.flowEventConfig.name = ?2) and " +
            "not exists (select 1 from CollectionFlowLog l where l.contract = c and " +
            "l.flowType.code = ?1 and l.ended = false) ")
    List<Contract> findFlowProcessorEntityWithoutLogs(String logType, FlowEventConfig.FlowEventName flowEventName);

    @Query("select f from FlowProcessorEntity f " +
            "join f.contractPaymentTerm.contract c " +
            "where c.status not in ('CANCELLED', 'EXPIRED') and " +
            "f.stopped = false and f.completed = false and f.flowEventConfig.name = ?2 and " +
            "not exists (select 1 from CollectionFlowLog l where l.contract = c and l.relatedToId = f.id and " +
            "l.relatedToEntity = 'FlowProcessorEntity' and l.flowType.code = ?1 and l.ended = false) ")
    List<FlowProcessorEntity> findAllFlowProcessorEntityWithoutLogs(String logType, FlowEventConfig.FlowEventName flowEventName);

    // ACC-8457
    @Query("select l from CollectionFlowLog l " +
            "join l.contract c " +
            "where l.id > ?1 and l.ended = false and l.flowType.code = 'incomplete_flow_missing_bank_info' and " +
                "l.status <> (case when c.signingPaperMode = true and " +
                                "exists (select 1 from GraphicDesignerToDo g where g.contractId = c.id and " +
                                        "g.toDoType = 'ACCOUNTING_DIGITALIZE_CLIENT_SIGNATURE' and g.completed = false) " +
                             "then 'PENDING_BUSINESS_USER_ACTION' " +
                             "else 'PENDING_CLIENT_RESPONSE' " +
                            "end)")
    Page<CollectionFlowLog> findInCompleteFlowMissingBankInfoToUpdate(Long lastId, Pageable pageable);

    @Query("select l from CollectionFlowLog l " +
            "inner join ClientRefundToDo c on l.relatedToId = c.id and l.relatedToEntity = 'ClientRefundToDo' " +
            "where l.id > ?1 and l.flowType.code = 'refund_flow' and l.ended = false and " +
            "c.status in ?2")
    Page<CollectionFlowLog> findRefundLogsNeedToStopped(Long lastId, List<ClientRefundStatus> statuses, Pageable pageable);

    @Query("select l from CollectionFlowLog l " +
            "inner join ClientRefundToDo c on l.relatedToId = c.id and l.relatedToEntity = 'ClientRefundToDo' " +
            "where l.id > ?1 and l.flowType.code = 'refund_flow' and l.ended = false and c.taskModifiedDate > l.idleSinceDate")
    Page<CollectionFlowLog> findRefundLogsNeedToUpdate(Long lastId, Pageable pageable);

    //todo could it be optimized?
    @Query("select l from CollectionFlowLog l " +
            "inner join ContractPaymentTerm cpt on cpt.id = l.relatedToId and l.relatedToEntity = 'ContractPaymentTerm' " +
            "inner join ContractPaymentTerm oldCpt on oldCpt.id = (select max(c.id) from ContractPaymentTerm c where cpt.contract = c.contract and c.id < cpt.id) " +
            "where l.id > ?1 and l.flowType.code = 'switching_bank_account_flow' and l.ended = false and " +
                "(cpt.isActive = false or " +
                    "exists (select 1 from DirectDebit d where d.contractPaymentTerm = oldCpt and d.lastModificationDate > (" +
                            "select max(c.idleSinceDate) as maxIdleSinceDate from CollectionFlowLog c " +
                                    "where c.flowType.code = 'switching_bank_flow')) or " +
                    "exists (select 1 from DirectDebitFile d where d.directDebit.contractPaymentTerm = oldCpt and d.lastModificationDate > (" +
                            "select max(c.idleSinceDate) as maxIdleSinceDate from CollectionFlowLog c " +
                                    "where c.flowType.code = 'switching_bank_flow')))")
    Page<CollectionFlowLog> findSwitchingBankLogsNeedToUpdate(Long lastId, Pageable pageable);

    @Query("select l from CollectionFlowLog l " +
            "join l.contract c " +
            "where l.id > ?1 and l.flowType.code = 'switching_nationality_flow' and l.ended = false and " +
                "(c.status <> 'Active' or " +
                "not exists (select 1 from AccountingEntityProperty a " +
                            "where l.relatedToId = a.id and l.relatedToEntity = 'AccountingEntityProperty' and a.isDeleted = false))")
    Page<CollectionFlowLog> findSwitchingNationalityLogsNeedToUpdate(Long lastId, Pageable pageable);

    @Query("select l from CollectionFlowLog l " +
            "join l.contract c " +
            "where l.id > ?1 and l.flowType.code ='missing/wrong_document_flow' and l.ended = false and " +
            "(c.status <> 'Active' or " +
                "exists (select 1 from ContractPaymentTerm cpt " +
                        "inner join DirectDebit d on d.contractPaymentTerm = cpt " +
                        "where cpt.id = l.relatedToId and (cpt.lastModificationDate > ?2 or d.lastModificationDate > ?2)) or " +
                "exists (select 1 from GraphicDesignerToDo g where g.contractId = c.id and g.toDoType = 'ACCOUNTING_DIGITALIZE_CLIENT_SIGNATURE' and g.lastModificationDate > ?2))")
    Page<CollectionFlowLog> findIncompleteFlowDataEntryRejectionLog(Long lastId, Date d, Pageable pageable);

    @Query("select new map(l as log, todo as ddrTodo) from CollectionFlowLog l " +
            "inner join DirectDebitRejectionToDo todo on todo.id = l.relatedToId and l.relatedToEntity = 'DirectDebitRejectionToDo' " +
            "where l.id > ?1 and l.flowType.code ='dd_rejection_flow' and (l.ended = false or l.rejectionTodoIsRescheduledWhenEnded = true) and " +
            "(todo.lastModificationDate > ?2 or " +
                "exists (select 1 from DirectDebit d where d.directDebitRejectionToDo = todo and d.lastModificationDate > ?2) or " +
                "exists (select 1 from DirectDebit d where d.directDebitBouncingRejectionToDo = todo and d.lastModificationDate > ?2) or " +
                "exists (select 1 from GraphicDesignerToDo g where g.contractId = l.contract.id and g.toDoType = 'ACCOUNTING_DIGITALIZE_CLIENT_SIGNATURE' and g.lastModificationDate > ?2))")
    Page<Map> findDDRejectionLogsNeedToUpdate(Long lastId, Date d, Pageable pageable);

    @Query("select new map(l as log, p as payment) from CollectionFlowLog l " +
            "join l.contract c " +
            "inner join Payment p on p.id = l.relatedToId and l.relatedToEntity = 'Payment' " +
            "where l.id > ?1 and l.flowType.code ='bounced_payment_flow' and l.ended = false and " +
            "(p.lastModificationDate > ?2 or c.lastModificationDate > ?2 or " +
                "exists (select 1 from DirectDebitFile d where d.directDebit.id = p.directDebitId and d.lastModificationDate > ?2) or " +
                "exists (select 1 from GraphicDesignerToDo g where g.contractId = l.contract.id and g.toDoType = 'ACCOUNTING_DIGITALIZE_CLIENT_SIGNATURE' and g.lastModificationDate > ?2))")
    Page<Map> findBouncedPaymentLogsNeedToUpdate(Long lastId, Date d, Pageable pageable);
}
