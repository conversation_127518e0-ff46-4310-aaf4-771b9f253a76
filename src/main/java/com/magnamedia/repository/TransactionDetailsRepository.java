package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Transaction;
import com.magnamedia.entity.TransactionDetails;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Feb 25, 2020
 *         Jirra ACC-1389
 */

@Repository
public interface TransactionDetailsRepository extends BaseRepository<TransactionDetails> {

    @Query("SELECT td FROM TransactionDetails td "
            + "WHERE td.transaction = ?1 AND td.accrualDate >= ?2 AND td.accrualDate <= ?3")
    List<TransactionDetails> findByTransactionAndAccrualDateAfterAndAccrualDateBefore(Transaction transaction, Date fromDate, Date toDate);
}
