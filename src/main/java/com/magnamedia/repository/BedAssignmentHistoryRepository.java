package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.BedAssignmentHistory;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface BedAssignmentHistoryRepository extends BaseRepository<BedAssignmentHistory> {
    @Query("select b from BedAssignmentHistory b where b.assignmentDate>=?1 and b.assignmentDate<=?2")
    List<BedAssignmentHistory> findByAssignmentDateGreaterThanAndLessThan(Date begin, Date end);

    @Query("select b from BedAssignmentHistory b where b.assignmentDate>=?1 and b.assignmentDate<=?2 and b.oldHousemaidStatus=?3")
    List<BedAssignmentHistory> findByAssignmentDateGreaterThanAndLessThanAndMaidStatus(Date begin, Date end,String oldMaidStatus);
}

