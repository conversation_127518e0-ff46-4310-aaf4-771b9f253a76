package com.magnamedia.repository;

import com.magnamedia.core.entity.User;
import com.magnamedia.core.repository.BaseRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CashierRepository extends BaseRepository<User> {

    /**
     * Find users with active cash boxes
     */
    @Query(value = "SELECT u.* FROM USERS u " +
            "INNER JOIN BUCKETS b ON b.HOLDER_ID = u.ID " +
            "WHERE b.BUCKET_TYPE = 'CASH_BOX' AND b.IS_ACTIVE = 1", 
            nativeQuery = true)
    Page<User> findUsersWithActiveCashBoxes(Pageable pageable);
}
