package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.DirectDebitSignatureApprovalLink;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;


@Repository
public interface DirectDebitSignatureApprovalLinkRepository extends BaseRepository<DirectDebitSignatureApprovalLink> {

    @Query("select c.uuid " +
            "from DirectDebitSignatureApprovalLink l " +
            "join l.contract c " +
            "where l.uuid = ?1 and l.expired = false")
    String findContractUuidByLinkUuidAndExpiredFalse(String uuid);
}