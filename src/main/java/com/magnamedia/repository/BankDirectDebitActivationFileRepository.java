package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.BankDirectDebitActivationFile;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;


/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Dec 10, 2018
 * Jirra ACC-329
 */
@Repository
public interface BankDirectDebitActivationFileRepository
        extends BaseRepository<BankDirectDebitActivationFile> {

    @Query("select f from BankDirectDebitActivationFile f " +
            "where f.addedByRPA = true and f.fileParsed = true " +
            "order by f.creationDate desc")
    Page<BankDirectDebitActivationFile> getAllBankDirectDebitActivationFiles(Pageable pageable);
}