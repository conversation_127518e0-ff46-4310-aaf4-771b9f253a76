package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.BaseReportCompany;
import com.magnamedia.entity.BasePLNode;
import org.springframework.data.repository.NoRepositoryBean;

import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jul 20, 2020
 *         Jirra ACC-644
 */

@NoRepositoryBean
public interface BasePLNodeRepository<T extends BasePLNode> extends BaseRepository<T> {
    List<BasePLNode> findByPLCompanyAndParentIsNull(BaseReportCompany pLCompany);

    List<BasePLNode> findByParent(BasePLNode parent);

    List<BasePLNode> findByNameAndPLCompanyAndParentIsNull(
            String name, Long pLCompanyId);

    List<BasePLNode> findByNameAndParent(
            String name, BasePLNode parent);

    List<BasePLNode> findByNodeOrderGreaterThanAndPLCompanyAndParentIsNull(
            Integer nodeOrder, BaseReportCompany pLCompany);

    List<BasePLNode> findByNodeOrderLessThanAndPLCompanyAndParentIsNull(
            Integer nodeOrder, BaseReportCompany pLCompany);

    List<BasePLNode> findByNodeOrderGreaterThanAndParent(
            Integer nodeOrder, BasePLNode parent);

    List<BasePLNode> findByNodeOrderLessThanAndParent(
            Integer nodeOrder, BasePLNode parent);
}
