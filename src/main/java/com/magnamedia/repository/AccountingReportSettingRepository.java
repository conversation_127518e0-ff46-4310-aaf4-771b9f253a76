package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.AccountingReportSetting;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AccountingReportSettingRepository extends BaseRepository<AccountingReportSetting> {

    List<AccountingReportSetting> findByReportCategoryAndActiveTrue(AccountingReportSetting.ReportCategory reportCategory);

    AccountingReportSetting findFirstByReportCategoryAndQueryCategory(AccountingReportSetting.ReportCategory reportCategory, AccountingReportSetting.QueryCategory queryCategory);

}
