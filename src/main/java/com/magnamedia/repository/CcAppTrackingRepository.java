package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.ccapp.CcAppTracking;
import java.util.Date;
import java.util.List;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 */

@Repository
public interface CcAppTrackingRepository extends BaseRepository<CcAppTracking> {
    
    @Query("SELECT t.ccAction, COUNT(DISTINCT(t.contractUuid)) " +
            "FROM CcAppTracking t " + 
            "WHERE t.creationDate >= ?1 AND t.creationDate <= ?2 " + 
            "GROUP BY t.ccAction")
    List<Object[]> getCcAppTrackingStats(Date from, Date to);
}
