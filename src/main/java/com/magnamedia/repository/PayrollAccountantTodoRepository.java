package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.maidsatv2.actions.employeragreement.SendRequestForApprovalAction;
import com.magnamedia.entity.projection.AccountantToDoProjection;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.entity.workflow.PayrollAccountantTodo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;

@Repository
public interface PayrollAccountantTodoRepository extends BaseRepository<PayrollAccountantTodo> {

    @Query("select ac from PayrollAccountantTodo ac " +
            "where ac.completed = false and ac.stopped = false " +
            "order by ac.creationDate desc, ac.id desc")
    Page<PayrollAccountantTodo> findOpenToDos(Pageable pageable);

    @Query(nativeQuery = true,
            value = "SELECT * from " +
                    "(select ac.ID as ID, 'PAYROLL_ACCOUNTANT_TODO' as TODO_TYPE, ac.LAST_MODIFICATION_DATE as LAST_MODIFICATION_DATE from PAYROLLACCOUNTANTTODOS ac " +
                    "where ac.TASK_NAME not in ('BANK_TRANSFER', 'PENSION_AUTHORITY') and " +
                    "ac.IS_COMPLETED = true and ac.ITEM_TYPE_ID is not null " +
                    "and ac.CEO_ACTION = 'PENDING' " +
                    "UNION " +
                    "select pl.ID as ID, 'OFFICE_STAFF_PAYROLL_LOG' as TODO_TYPE, pl.LAST_MODIFICATION_DATE as LAST_MODIFICATION_DATE from OFFICESTAFFPAYROLLLOGS pl INNER JOIN PAYROLLACCOUNTANTTODOS patodo on pl.PAYROLL_ACCOUNTANT_TODO_ID = patodo.ID " +
                    "where patodo.TASK_NAME in ('BANK_TRANSFER', 'PENSION_AUTHORITY') and " +
                    "pl.TRANSFERRED = 1 and pl.CEO_ACTION = 'PENDING' ) as t " +
                    "ORDER BY LAST_MODIFICATION_DATE, ID DESC",
            countQuery = "SELECT count(ID) FROM " +
                    "(select ac.ID, 'PAYROLL_ACCOUNTANT_TODO' from PAYROLLACCOUNTANTTODOS ac " +
                    "where ac.TASK_NAME not in ('BANK_TRANSFER', 'PENSION_AUTHORITY') and " +
                    "ac.IS_COMPLETED = true and ac.ITEM_TYPE_ID is not null " +
                    "and ac.CEO_ACTION = 'PENDING' " +
                    "UNION " +
                    "select pl.ID, 'OFFICE_STAFF_PAYROLL_LOG' from OFFICESTAFFPAYROLLLOGS pl INNER JOIN PAYROLLACCOUNTANTTODOS patodo on pl.PAYROLL_ACCOUNTANT_TODO_ID = patodo.ID " +
                    "where patodo.TASK_NAME in ('BANK_TRANSFER', 'PENSION_AUTHORITY') and " +
                    "pl.TRANSFERRED = 1 and pl.CEO_ACTION = 'PENDING' ) as t")
    Page<Object[]> findPendingConfirmationForCoo(Pageable pageable);

    @Query(nativeQuery = true,
            value = "SELECT SUM(t.amount) FROM " +
                        "(select SUM(" +
                                    "ROUND(case when ac.TOTAL is null or ac.TOTAL = 0.0 " +
                                    "then (ac.AMOUNT + ac.VAT + ac.CHARGES) " +
                                        "else (ac.TOTAL) end,2)) as amount " +
                        "from PAYROLLACCOUNTANTTODOS ac " +
                        "where ac.TASK_NAME not in ('BANK_TRANSFER', 'PENSION_AUTHORITY') and " +
                            "ac.IS_COMPLETED = true and ac.ITEM_TYPE_ID is not null " +
                            "and ac.CEO_ACTION = 'PENDING' " +
                    "UNION " +
                        "select SUM(ROUND(pl.CONTRIBUTION_AMOUNT, 2)) as amount from OFFICESTAFFPAYROLLLOGS pl " +
                        "INNER JOIN PAYROLLACCOUNTANTTODOS patodo on pl.PAYROLL_ACCOUNTANT_TODO_ID = patodo.ID " +
                        "where patodo.TASK_NAME = 'PENSION_AUTHORITY' and " +
                            "pl.TRANSFERRED = 1 and pl.CEO_ACTION = 'PENDING' " +
                    "UNION " +
                        "select SUM(ROUND(pl.TOTAL_SALARY, 2)) as amount from OFFICESTAFFPAYROLLLOGS pl " +
                        "INNER JOIN PAYROLLACCOUNTANTTODOS patodo on pl.PAYROLL_ACCOUNTANT_TODO_ID = patodo.ID " +
                        "where patodo.TASK_NAME = 'BANK_TRANSFER' and " +
                            "pl.TRANSFERRED = 1 and pl.CEO_ACTION = 'PENDING' ) as t")
    Double sumPendingConfirmationForCoo();

    @Query(nativeQuery = true,
            value = "SELECT * from " +
                    "(select ac.ID, 'PAYROLL_ACCOUNTANT_TODO' from PAYROLLACCOUNTANTTODOS ac INNER JOIN PAYROLLACCOUNTANTTODOS_REVISIONS ac_rev on ac.ID = ac_rev.ID " +
                    "where ac.TASK_NAME not in ('BANK_TRANSFER', 'PENSION_AUTHORITY') and " +
                    "ac.IS_COMPLETED = true and ac.ITEM_TYPE_ID is not null " +
                    "and ac_rev.CEO_ACTION = 'APPROVED' " +
                    "and ac_rev.CEO_ACTION_MODIFIED = true and ac_rev.LAST_MODIFICATION_DATE >= :#{#fromDate} and ac_rev.LAST_MODIFICATION_DATE <= :#{#toDate} " +
                    "and (:#{#archive == true} = true or (:#{#archive == true} = false and ac.DONE_BY_COO = false)) " +
                    "GROUP BY ac_rev.ID " +
                    "UNION " +
                    "select ospl_rev.ID, 'OFFICE_STAFF_PAYROLL_LOG' from OFFICESTAFFPAYROLLLOGS_REVISIONS AS ospl_rev INNER JOIN OFFICESTAFFPAYROLLLOGS AS ospl on ospl.ID = ospl_rev.ID INNER JOIN PAYROLLACCOUNTANTTODOS AS patodo on ospl_rev.PAYROLL_ACCOUNTANT_TODO_ID = patodo.ID " +
                    "where patodo.TASK_NAME in ('BANK_TRANSFER', 'PENSION_AUTHORITY') and " +
                    "ospl_rev.TRANSFERRED = 1 and ospl_rev.CEO_ACTION = 'APPROVED' " +
                    "and ospl_rev.CEO_ACTION_MODIFIED = true and ospl_rev.LAST_MODIFICATION_DATE >= :#{#fromDate} and ospl_rev.LAST_MODIFICATION_DATE <= :#{#toDate} " +
                    "and (:#{#archive == true} = true or (:#{#archive == true} = false and ospl.DONE_BY_COO = false)) " +
                    "GROUP BY ospl_rev.ID " +
                    ") as t",
            countQuery = "SELECT count(ID) FROM " +
                    "(select ac.ID, 'PAYROLL_ACCOUNTANT_TODO' from PAYROLLACCOUNTANTTODOS ac INNER JOIN PAYROLLACCOUNTANTTODOS_REVISIONS ac_rev on ac.ID = ac_rev.ID " +
                    "where ac.TASK_NAME not in ('BANK_TRANSFER', 'PENSION_AUTHORITY') and " +
                    "ac.IS_COMPLETED = true and ac.ITEM_TYPE_ID is not null " +
                    "and ac_rev.CEO_ACTION = 'APPROVED' " +
                    "and ac_rev.CEO_ACTION_MODIFIED = true and ac_rev.LAST_MODIFICATION_DATE >= :#{#fromDate} and ac_rev.LAST_MODIFICATION_DATE <= :#{#toDate} " +
                    "and (:#{#archive == true} = true or (:#{#archive == true} = false and ac.DONE_BY_COO = false)) " +
                    "GROUP BY ac_rev.ID " +
                    "UNION " +
                    "select ospl_rev.ID, 'OFFICE_STAFF_PAYROLL_LOG' from OFFICESTAFFPAYROLLLOGS_REVISIONS AS ospl_rev INNER JOIN OFFICESTAFFPAYROLLLOGS AS ospl on ospl.ID = ospl_rev.ID INNER JOIN PAYROLLACCOUNTANTTODOS AS patodo on ospl_rev.PAYROLL_ACCOUNTANT_TODO_ID = patodo.ID " +
                    "where patodo.TASK_NAME in ('BANK_TRANSFER', 'PENSION_AUTHORITY') and " +
                    "ospl_rev.TRANSFERRED = 1 and ospl_rev.CEO_ACTION = 'APPROVED' " +
                    "and ospl_rev.CEO_ACTION_MODIFIED = true and ospl_rev.LAST_MODIFICATION_DATE >= :#{#fromDate} and ospl_rev.LAST_MODIFICATION_DATE <= :#{#toDate} " +
                    "and (:#{#archive == true} = true or (:#{#archive == true} = false and ospl.DONE_BY_COO = false)) " +
                    "GROUP BY ospl_rev.ID " +
                    ") as t")
    Page<Object[]> findConfirmedModifiedToDosForCoo(@Param("fromDate") Date date, @Param("toDate") Date toDate, @Param("archive") boolean archive, Pageable pageable);

    @Query(nativeQuery = true,
            value = "SELECT * from " +
                    "(select ac.ID, 'PAYROLL_ACCOUNTANT_TODO' from PAYROLLACCOUNTANTTODOS ac INNER JOIN PAYROLLACCOUNTANTTODOS_REVISIONS ac_rev on ac.ID = ac_rev.ID " +
                    "where ac.TASK_NAME not in ('BANK_TRANSFER', 'PENSION_AUTHORITY') and " +
                    "ac.IS_COMPLETED = true and ac.ITEM_TYPE_ID is not null " +
                    "and ac_rev.CEO_ACTION = 'APPROVED' " +
                    "and ac_rev.CEO_ACTION_MODIFIED = true and ac_rev.LAST_MODIFICATION_DATE >= :#{#fromDate} and ac_rev.LAST_MODIFICATION_DATE <= :#{#toDate} " +
                    "and (:#{#archive == true} = true or (:#{#archive == true} = false and ac.DONE_BY_COO = false)) and " +
                    "(EXISTS (SELECT * FROM COOQUESTIONS AS cooQ where cooQ.RELATED_ENTITY_ID = ac.ID and cooQ.RELATED_ENTITY_TYPE = ac.ENTITY_TYPE)) AND " +
                    "(NOT EXISTS (SELECT * FROM COOQUESTIONS AS cooQ where cooQ.RELATED_ENTITY_ID = ac.ID and cooQ.RELATED_ENTITY_TYPE = ac.ENTITY_TYPE AND (cooQ.ANSWER IS NULL OR cooQ.ANSWER = ''))) " +
                    "GROUP BY ac_rev.ID " +
                    "UNION " +
                    "select ospl_rev.ID, 'OFFICE_STAFF_PAYROLL_LOG' from OFFICESTAFFPAYROLLLOGS_REVISIONS AS ospl_rev INNER JOIN OFFICESTAFFPAYROLLLOGS AS ospl on ospl.ID = ospl_rev.ID INNER JOIN PAYROLLACCOUNTANTTODOS AS patodo on ospl_rev.PAYROLL_ACCOUNTANT_TODO_ID = patodo.ID " +
                    "where patodo.TASK_NAME in ('BANK_TRANSFER', 'PENSION_AUTHORITY') and " +
                    "ospl_rev.TRANSFERRED = 1 and ospl_rev.CEO_ACTION = 'APPROVED' " +
                    "and ospl_rev.CEO_ACTION_MODIFIED = true and ospl_rev.LAST_MODIFICATION_DATE >= :#{#fromDate} and ospl_rev.LAST_MODIFICATION_DATE <= :#{#toDate} " +
                    "and (:#{#archive == true} = true or (:#{#archive == true} = false and ospl.DONE_BY_COO = false)) and " +
                    "(EXISTS (SELECT * FROM COOQUESTIONS AS cooQ where cooQ.RELATED_ENTITY_ID = ospl.ID and cooQ.RELATED_ENTITY_TYPE = ospl.ENTITY_TYPE)) AND " +
                    "(NOT EXISTS (SELECT * FROM COOQUESTIONS AS cooQ where cooQ.RELATED_ENTITY_ID = ospl.ID and cooQ.RELATED_ENTITY_TYPE = ospl.ENTITY_TYPE AND (cooQ.ANSWER IS NULL OR cooQ.ANSWER = ''))) " +
                    "GROUP BY ospl_rev.ID " +
                    ") as t",
            countQuery = "SELECT count(ID) FROM " +
                    "(select ac.ID, 'PAYROLL_ACCOUNTANT_TODO' from PAYROLLACCOUNTANTTODOS ac INNER JOIN PAYROLLACCOUNTANTTODOS_REVISIONS ac_rev on ac.ID = ac_rev.ID " +
                    "where ac.TASK_NAME not in ('BANK_TRANSFER', 'PENSION_AUTHORITY') and " +
                    "ac.IS_COMPLETED = true and ac.ITEM_TYPE_ID is not null " +
                    "and ac_rev.CEO_ACTION = 'APPROVED' " +
                    "and ac_rev.CEO_ACTION_MODIFIED = true and ac_rev.LAST_MODIFICATION_DATE >= :#{#fromDate} and ac_rev.LAST_MODIFICATION_DATE <= :#{#toDate} " +
                    "and ospl.DONE_BY_COO = false and " +
                    "(EXISTS (SELECT * FROM COOQUESTIONS AS cooQ where cooQ.RELATED_ENTITY_ID = ac.ID and cooQ.RELATED_ENTITY_TYPE = ac.ENTITY_TYPE)) AND " +
                    "(NOT EXISTS (SELECT * FROM COOQUESTIONS AS cooQ where cooQ.RELATED_ENTITY_ID = ac.ID and cooQ.RELATED_ENTITY_TYPE = ac.ENTITY_TYPE AND (cooQ.ANSWER IS NULL OR cooQ.ANSWER = ''))) " +
                    "GROUP BY ac_rev.ID " +
                    "UNION " +
                    "select ospl_rev.ID, 'OFFICE_STAFF_PAYROLL_LOG' from OFFICESTAFFPAYROLLLOGS_REVISIONS AS ospl_rev INNER JOIN OFFICESTAFFPAYROLLLOGS AS ospl on ospl.ID = ospl_rev.ID INNER JOIN PAYROLLACCOUNTANTTODOS AS patodo on ospl_rev.PAYROLL_ACCOUNTANT_TODO_ID = patodo.ID " +
                    "where patodo.TASK_NAME in ('BANK_TRANSFER', 'PENSION_AUTHORITY') and " +
                    "ospl_rev.TRANSFERRED = 1 and ospl_rev.CEO_ACTION = 'APPROVED' " +
                    "and ospl_rev.CEO_ACTION_MODIFIED = true and ospl_rev.LAST_MODIFICATION_DATE >= :#{#fromDate} and ospl_rev.LAST_MODIFICATION_DATE <= :#{#toDate} " +
                    "and ospl.DONE_BY_COO = false and " +
                    "(EXISTS (SELECT * FROM COOQUESTIONS AS cooQ where cooQ.RELATED_ENTITY_ID = ospl.ID and cooQ.RELATED_ENTITY_TYPE = ospl.ENTITY_TYPE)) AND " +
                    "(NOT EXISTS (SELECT * FROM COOQUESTIONS AS cooQ where cooQ.RELATED_ENTITY_ID = ospl.ID and cooQ.RELATED_ENTITY_TYPE = ospl.ENTITY_TYPE AND (cooQ.ANSWER IS NULL OR cooQ.ANSWER = ''))) " +
                    "GROUP BY ospl_rev.ID " +
                    ") as t ")
    Page<Object[]> findQuestionedConfirmedModifiedToDosForCoo(@Param("fromDate") Date date, @Param("toDate") Date toDate, Pageable pageable);

    @Query("select ac from PayrollAccountantTodo ac " +
            " left  join  ac.itemType " +
            " left  join  ac.itemType.tags as tag " +
            " where ac.completed=true and ac.managerAction =com.magnamedia.workflow.type.PayrollAccountantTodoManagerAction.PENDING " +
            " and tag.name = ?1 " +
            "and  ac.ceoAction =com.magnamedia.workflow.type.PayrollAccountantTodoManagerAction.PENDING " +
            "group by ac.id order by ac.lastModificationDate desc")
    Page<AccountantToDoProjection> findPendingConfirmationForManager(Pageable pageable, String managerName);

    PayrollAccountantTodo findFirstByExpensePayment(ExpensePayment expensePayment);

    PayrollAccountantTodo findFirstBySendRequestForApprovalAction(SendRequestForApprovalAction sendRequestForApprovalAction);

    boolean existsByClientRefundToDo(ClientRefundToDo clientRefundToDo);
    boolean existsByExpensePayment(ExpensePayment expensePayment);
    boolean existsBySendRequestForApprovalAction(SendRequestForApprovalAction action);

    @Query(nativeQuery = true,
            value = "SELECT IF(count(ID) > 0, 'true', 'false') FROM " +
                        "(select t.ID from PAYROLLACCOUNTANTTODOS t " +
                        "where t.IS_COMPLETED = true AND " +
                            "t.TASK_NAME not in ('BANK_TRANSFER', 'PENSION_AUTHORITY') AND " +
                            "t.ITEM_TYPE_ID IS NOT NULL AND t.CEO_ACTION = 'PENDING' " +
                        "UNION " +
                        "select pl.ID " +
                        "from OFFICESTAFFPAYROLLLOGS pl " +
                        "INNER JOIN PAYROLLACCOUNTANTTODOS t on pl.PAYROLL_ACCOUNTANT_TODO_ID = t.ID " +
                        "where t.TASK_NAME in ('BANK_TRANSFER', 'PENSION_AUTHORITY') and " +
                            "pl.TRANSFERRED = 1 and pl.CEO_ACTION = 'PENDING') as p ")
    String existsBankTransferWaitingCooAction();

    @Query("select count(p.id) from PayrollAccountantTodo p " +
            "left join p.clientRefundToDo c " +
            "left join p.expensePayment e " +
            "where p.taskName = 'EXPENSE_BANK_TRANSFER' and p.stopped = false and p.completed = false and (" +
                "(c is not null and c.client is not null) or " +
                "(e is not null and e.replenishmentTodo is not null) or " +
                "(e is not null and e.beneficiaryType = 'SUPPLIER'))")
    Long countForOverseasSalaryTodos();
}