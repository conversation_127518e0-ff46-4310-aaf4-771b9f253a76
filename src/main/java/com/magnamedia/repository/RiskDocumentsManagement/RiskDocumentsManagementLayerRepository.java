package com.magnamedia.repository.RiskDocumentsManagement;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagement;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagementLayer;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagementRole;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;

import java.sql.Date;
import java.util.List;
import java.util.Map;

public interface RiskDocumentsManagementLayerRepository extends BaseRepository<RiskDocumentsManagementLayer> {

    @Query("select new map (d.id as documentId, l as layer) from RiskDocumentsManagement d " +
            "join d.layers l " +
            "where l.id > ?1 and l.creationRenewalToDoDate = ?2 and d.active = true and d.status = 'ACTIVE' and l.type = ?3 and " +
            "not exists (select 1 from RiskDocumentsManagementTodo todo " +
                        "where todo.riskDocumentsManagementLayer = l and " +
                            "todo.riskDocumentsManagementLayer.creationRenewalToDoDate = ?2)")
    Page<Map> findByCreationRenewalToDoDateAndType(
            Long id, Date d, RiskDocumentsManagementLayer.Type type, Pageable pageable);

    @Query("select l from RiskDocumentsManagementLayer l  " +
            "where l.id > ?1 and ?2 member of l.roleAssignee")
    Page<RiskDocumentsManagementLayer> getRiskDocumentsManagementLayerByRoleIds(Long id, RiskDocumentsManagementRole role, Pageable pageable);

    @Query("select l from RiskDocumentsManagement d " +
            "join d.layers l " +
            "where d.id = ?1 and l.type = 'LAYER_TWO' and l.sendLayerTwoEmailDate = ?2")
    List<RiskDocumentsManagementLayer> findLayer2ByTodoDocument(Long documentId, Date sendLayerTwoEmailDate);

    @Query("select l from RiskDocumentsManagement d " +
            "join d.layers l " +
            "where d = ?1")
    List<RiskDocumentsManagementLayer> findByRiskDocumentsManagement(RiskDocumentsManagement d);
}