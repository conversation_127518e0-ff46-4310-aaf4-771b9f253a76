package com.magnamedia.repository.RiskDocumentsManagement;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagement;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagementTodo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;

import java.sql.Date;
import java.util.List;

public interface RiskDocumentsManagementToDoRepository extends BaseRepository<RiskDocumentsManagementTodo> {

    @Query("select todo from RiskDocumentsManagementTodo todo " +
            "where todo.riskDocumentsManagement = ?1 and todo.status <> 'CLOSED'")
    List<RiskDocumentsManagementTodo> findByRiskDocumentsManagement(RiskDocumentsManagement d);

    @Query("select todo from RiskDocumentsManagementTodo todo " +
            "where todo.type = 'VISIT' and todo.status = 'PENDING_VISIT' and todo.riskDocumentsManagement = ?1")
    List<RiskDocumentsManagementTodo> findPendingVisitToDoByRiskDocumentsManagement(RiskDocumentsManagement d);

    @Query("select todo from RiskDocumentsManagementTodo todo " +
            "join todo.riskDocumentsManagement r " +
            "join r.layers l " +
            "where todo.id > ?1 and l.type = 'LAYER_TWO' and todo.status in ?2 and l.sendLayerTwoEmailDate = ?3 " +
            "and r.active = true")
    Page<RiskDocumentsManagementTodo> findByStatusesWithActiveDocumentsAndLayers(
            Long id, List<RiskDocumentsManagementTodo.Status> statuses, Date sendLayerTwoEmailDate, Pageable pageable);

    @Query("select count(todo.id) > 0 from RiskDocumentsManagementTodo todo " +
            "join todo.riskDocumentsManagementLayer l " +
            "where l.id = ?1 and l.type = 'LAYER_ONE' and todo.status not in ?2 and l.creationRenewalToDoDate = ?3 ")
    boolean existsByStatusNotInAndLayerCreationRenewalDate(
            Long layerId, List<RiskDocumentsManagementTodo.Status> statuses, Date creationRenewalDate);

    @Query("select count(todo.id) > 0 from RiskDocumentsManagementTodo todo " +
            "where todo.riskDocumentsManagement.id = ?1 and todo.type = 'VISIT' and todo.creationDate between ?2 and ?3")
    boolean existsVisitToDosByDocumentAndCreationDateBetween(Long riskDocumentId, java.util.Date stDate, java.util.Date enDate);
}