package com.magnamedia.repository.RiskDocumentsManagement;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagement;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagementRole;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface RiskDocumentsManagementRoleRepository extends BaseRepository<RiskDocumentsManagementRole> {

    @Query("select r from RiskDocumentsManagementRole r " +
            "where r.isDeleted = false")
    Page<RiskDocumentsManagementRole> getActiveRoles(Pageable pageable);

    @Query("select role from RiskDocumentsManagement d " +
            "join d.accountablePerson role " +
            "where d = ?1 and role.isDeleted = true")
    List<RiskDocumentsManagementRole> findDeletedRolesByRiskDocumentManagement(RiskDocumentsManagement document);
}