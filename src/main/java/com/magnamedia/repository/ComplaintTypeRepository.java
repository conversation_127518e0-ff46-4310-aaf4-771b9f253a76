package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.ComplaintCategory;
import com.magnamedia.entity.ComplaintType;
import java.util.List;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Jul 28, 2019
 * Jirra ACC-737
 */
@Repository
public interface ComplaintTypeRepository extends BaseRepository<ComplaintType> {

    public List<ComplaintType> findByCategoryAndDisabled(
            ComplaintCategory category,
            boolean disabled);

    public long countByCategory(ComplaintCategory category);

    public ComplaintType findByCode(String code);

    List<ComplaintType> findByIsReplacementReasonAndDisabled(
            boolean isReplacement,
            boolean disabled);

}

