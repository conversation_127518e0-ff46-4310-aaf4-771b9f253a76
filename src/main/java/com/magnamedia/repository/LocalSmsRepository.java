package com.magnamedia.repository;

import com.magnamedia.core.entity.Sms;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.module.type.DDMessagingType;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface LocalSmsRepository extends BaseRepository<Sms> {

    @Query("SELECT s FROM Sms s " +
            "inner join Template t on LOWER(s.type.code) = LOWER(t.name) " +
            "left join DDMessaging d on t.id = d.clientTemplate.id " +
            "left join DDBankMessaging db on t.id = db.clientTemplate.id " +
            "left join DDMessaging db_dd on db.ddMessaging = db_dd " +
            "WHERE s.receiverType = 'Client' AND s.receiverId = ?1 " +
            "AND ((d is not null and d.event IN (?2)) or (db_dd is not null and db_dd.event IN (?2)) or t.name IN (?3)) " +
            "ORDER BY s.creationDate DESC")
    List<Sms> findLastSmsByType(
            Long clientId, List<DDMessagingType> event, List<String> templateNames, Pageable pageable);

    @Query("SELECT count(s) > 0 FROM Sms s " +
            "inner join Template t on LOWER(s.type.code) = LOWER(t.name) " +
            "WHERE s.receiverType = 'Client' AND s.receiverId = ?1 " +
            "AND t.name = ?2 and s.creationDate <= ?3 and s.creationDate >= ?4 ")
    boolean existsSmsByTemplateNameAndCreationDateLessThanEqual(
            Long clientId, String templateName, Date smsData, Date notificationDate);
}
