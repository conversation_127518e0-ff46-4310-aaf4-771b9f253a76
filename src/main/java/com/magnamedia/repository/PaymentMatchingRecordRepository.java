package com.magnamedia.repository;

import com.magnamedia.core.entity.BackgroundTaskStatus;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.PaymentMatchingRecord;
import com.magnamedia.entity.PaymentsMatchingFile;
import com.magnamedia.module.type.PaymentMatchingRecordStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Mar 12, 2019
 *         Jirra ACC-469
 */
@Repository
public interface PaymentMatchingRecordRepository extends BaseRepository<PaymentMatchingRecord> {

    @Query("select distinct pmr from PaymentMatchingRecord pmr where pmr.paymentsMatchingFile = :file and " +
            "pmr.recordStatus = :recordStatus and ((pmr.payment is not null and pmr.payment.dateOfPayment >= :dateOfPayment) or pmr.payment is null)")
    List<PaymentMatchingRecord> findByPaymentsMatchingFileAndRecordStatus(@Param("file") PaymentsMatchingFile file, @Param("recordStatus") PaymentMatchingRecordStatus status
            , @Param("dateOfPayment") Date dateOfPayment);


    //Jirra ACC-2330
    @Query("select pmr from PaymentMatchingRecord pmr left join pmr.payment p " +
            "left join p.contract con left join con.client cl " +
            "where pmr.paymentsMatchingFile.id = ?1 and pmr.recordStatus= ?2")
    List<PaymentMatchingRecord> findByPaymentsMatchingFileIdAndRecordStatusForCSV(Long fileId, PaymentMatchingRecordStatus status);

    //Jirra ACC-2113
    Page<PaymentMatchingRecord> findByPaymentsMatchingFileIdAndRecordStatus(Long fileId, PaymentMatchingRecordStatus status, Pageable pageable);

    List<PaymentMatchingRecord> findByPaymentsMatchingFileIdAndRecordStatus(Long fileId, PaymentMatchingRecordStatus status);

    @Query("select sum(p.amountOfPayment) from PaymentMatchingRecord pmr left join Payment p on pmr.paymentIdStr = p.id " +
            "where pmr.paymentsMatchingFile.id = :fileId and pmr.recordStatus = :recordStatus")
    Double sumPaymentsAmountByPaymentsMatchingFileIdAndRecordStatus(@Param("fileId") Long fileId, @Param("recordStatus") PaymentMatchingRecordStatus status);

    @Query("select distinct pmr from PaymentMatchingRecord pmr where pmr.paymentsMatchingFile.id = :fileId and " +
            "(pmr.recordStatus = :recordStatus or (exists (select 1 from BackgroundTask bgt where bgt.relatedEntityType = 'PaymentMatchingRecord' " +
            "and bgt.relatedEntityId = pmr.id and bgt.status = :taskStatus))) and " +
            "(pmr.payment.dateOfPayment >= :dateOfPayment)")
    List<PaymentMatchingRecord> findByPaymentsMatchingFileIdAndRecordStatusOrBackgroundTaskStatusEquals(@Param("fileId") Long fileId,
                                                                                                        @Param("recordStatus") PaymentMatchingRecordStatus status,
                                                                                                        @Param("taskStatus") BackgroundTaskStatus taskStatus,
                                                                                                        @Param("dateOfPayment") Date dateOfPayment);

    @Query("select sum(pmr.payment.amountOfPayment) from PaymentMatchingRecord pmr left join pmr.payment p where pmr.paymentsMatchingFile.id = :fileId and " +
            "(pmr.recordStatus = :recordStatus or (exists (select 1 from BackgroundTask bgt where bgt.relatedEntityType = 'PaymentMatchingRecord' " +
            "and bgt.relatedEntityId = pmr.id and bgt.status = :taskStatus))) and " +
            "(pmr.payment.dateOfPayment >= :dateOfPayment)")
    Double sumPaymentsAmountByPaymentsMatchingFileIdAndRecordStatusOrBackgroundTaskStatusEquals(@Param("fileId") Long fileId,
                                                                                                @Param("recordStatus") PaymentMatchingRecordStatus status,
                                                                                                @Param("taskStatus") BackgroundTaskStatus taskStatus,
                                                                                                @Param("dateOfPayment") Date dateOfPayment);

    @Query("select distinct pmr from PaymentMatchingRecord pmr where pmr.paymentsMatchingFile.id = :fileId and " +
            "(pmr.recordStatus = :recordStatus or (exists (select 1 from BackgroundTask bgt where bgt.relatedEntityType = 'PaymentMatchingRecord' " +
            "and bgt.relatedEntityId = pmr.id and bgt.status = :taskStatus))) and " +
            "(pmr.payment.dateOfPayment >= :dateOfPayment)")
    Page<PaymentMatchingRecord> findByPaymentsMatchingFileIdAndRecordStatusOrBackgroundTaskStatusEquals(@Param("fileId") Long fileId,
                                                                                                        @Param("recordStatus") PaymentMatchingRecordStatus status,
                                                                                                        @Param("taskStatus") BackgroundTaskStatus taskStatus,
                                                                                                        @Param("dateOfPayment") Date dateOfPayment,
                                                                                                        Pageable pageable);

    //Jirra ACC-2330
    @Query("select distinct pmr from PaymentMatchingRecord pmr left join pmr.payment p " +
            "left join p.contract con left join con.client cl " +
            "where pmr.paymentsMatchingFile.id = :fileId and " +
            "(pmr.recordStatus = :recordStatus or (exists (select 1 from BackgroundTask bgt where bgt.relatedEntityType = 'PaymentMatchingRecord' " +
            "and bgt.relatedEntityId = pmr.id and bgt.status = :taskStatus)))")
    List<PaymentMatchingRecord> findByPaymentsMatchingFileIdAndRecordStatusOrBackgroundTaskStatusEqualsForCSV(@Param("fileId") Long fileId,
                                                                                                              @Param("recordStatus") PaymentMatchingRecordStatus status,
                                                                                                              @Param("taskStatus") BackgroundTaskStatus taskStatus);

    @Query("select distinct pmr from PaymentMatchingRecord pmr where pmr.paymentsMatchingFile.id = :fileId " +
            "and (exists (select 1 from BackgroundTask bgt where bgt.relatedEntityType = 'PaymentMatchingRecord' " +
            "and bgt.relatedEntityId = pmr.id and bgt.status not in :taskStatuses)) and " +
            "(pmr.payment.dateOfPayment >= :dateOfPayment)")
    List<PaymentMatchingRecord> findByPaymentsMatchingFileIdWithBackgroundTaskStatusNotIn(@Param("fileId") Long fileId,
                                                                                          @Param("taskStatuses") List<BackgroundTaskStatus> taskStatuses,
                                                                                          @Param("dateOfPayment") Date dateOfPayment);

    @Query("select distinct pmr from PaymentMatchingRecord pmr where pmr.paymentsMatchingFile.id = :fileId " +
            "and (exists (select 1 from BackgroundTask bgt where bgt.relatedEntityType = 'PaymentMatchingRecord' " +
            "and bgt.relatedEntityId = pmr.id and bgt.status not in :taskStatuses)) and " +
            "(pmr.payment.dateOfPayment >= :dateOfPayment)")
    Page<PaymentMatchingRecord> findByPaymentsMatchingFileIdWithBackgroundTaskStatusNotIn(@Param("fileId") Long fileId,
                                                                                          @Param("taskStatuses") List<BackgroundTaskStatus> taskStatuses,
                                                                                          @Param("dateOfPayment") Date dateOfPayment,
                                                                                          Pageable pageable);

    @Query("select sum(pmr.payment.amountOfPayment) from PaymentMatchingRecord pmr left join pmr.payment p where pmr.paymentsMatchingFile.id = :fileId " +
            "and (exists (select 1 from BackgroundTask bgt where bgt.relatedEntityType = 'PaymentMatchingRecord' " +
            "and bgt.relatedEntityId = pmr.id and bgt.status not in :taskStatuses)) and " +
            "(pmr.payment.dateOfPayment >= :dateOfPayment)")
    Double sumPaymentsAmountByPaymentsMatchingFileIdWithBackgroundTaskStatusNotIn(@Param("fileId") Long fileId,
                                                                                  @Param("taskStatuses") List<BackgroundTaskStatus> taskStatuses,
                                                                                  @Param("dateOfPayment") Date dateOfPayment);

    //Jirra ACC-2330
    @Query("select distinct pmr from PaymentMatchingRecord pmr left join pmr.payment p " +
            "left join p.contract con left join con.client cl " +
            "where pmr.paymentsMatchingFile.id = :fileId " +
            "and (exists (select 1 from BackgroundTask bgt where bgt.relatedEntityType = 'PaymentMatchingRecord' " +
            "and bgt.relatedEntityId = pmr.id and bgt.status not in :taskStatuses)) and " +
            "(p.dateOfPayment >= :dateOfPayment)")
    List<PaymentMatchingRecord> findByPaymentsMatchingFileIdWithBackgroundTaskStatusNotInForCSV(@Param("fileId") Long fileId,
                                                                                                @Param("taskStatuses") List<BackgroundTaskStatus> taskStatuses,
                                                                                                @Param("dateOfPayment") Date dateOfPayment);

    @Query("select distinct pmr from PaymentMatchingRecord pmr where pmr.paymentsMatchingFile.id = :fileId and " +
            "pmr.recordStatus = :recordStatus and ( not exists (select 1 from BackgroundTask bgt where bgt.relatedEntityType = 'PaymentMatchingRecord' " +
            "and bgt.relatedEntityId = pmr.id)) and " +
            "(pmr.payment.dateOfPayment >= :dateOfPayment)")
    List<PaymentMatchingRecord> findByPaymentsMatchingFileIdAAndRecordStatusWithoutBackgroundTask(@Param("fileId") Long fileId,
                                                                                                  @Param("recordStatus") PaymentMatchingRecordStatus status,
                                                                                                  @Param("dateOfPayment") Date dateOfPayment);

    @Query("select distinct pmr from PaymentMatchingRecord pmr where pmr.paymentsMatchingFile.id = :fileId and " +
            "pmr.recordStatus = :recordStatus and ( not exists (select 1 from BackgroundTask bgt where bgt.relatedEntityType = 'PaymentMatchingRecord' " +
            "and bgt.relatedEntityId = pmr.id)) and " +
            "(pmr.payment.dateOfPayment >= :dateOfPayment)")
    Page<PaymentMatchingRecord> findByPaymentsMatchingFileIdAAndRecordStatusWithoutBackgroundTask(@Param("fileId") Long fileId,
                                                                                                  @Param("recordStatus") PaymentMatchingRecordStatus status,
                                                                                                  @Param("dateOfPayment") Date dateOfPayment,
                                                                                                  Pageable pageable);

    @Query("select sum(pmr.payment.amountOfPayment) from PaymentMatchingRecord pmr where pmr.paymentsMatchingFile.id = :fileId and " +
            "pmr.recordStatus = :recordStatus and ( not exists (select 1 from BackgroundTask bgt where bgt.relatedEntityType = 'PaymentMatchingRecord' " +
            "and bgt.relatedEntityId = pmr.id)) and " +
            "(pmr.payment.dateOfPayment >= :dateOfPayment)")
    Double sumPaymentsAmountByPaymentsMatchingFileIdAAndRecordStatusWithoutBackgroundTask(@Param("fileId") Long fileId,
                                                                                          @Param("recordStatus") PaymentMatchingRecordStatus status,
                                                                                          @Param("dateOfPayment") Date dateOfPayment);

    //Jirra ACC-2230
    @Query("select distinct pmr from PaymentMatchingRecord pmr left join pmr.payment p " +
            "left join p.contract con left join con.client cl " +
            "where pmr.paymentsMatchingFile.id = :fileId and " +
            "pmr.recordStatus = :recordStatus and ( not exists (select 1 from BackgroundTask bgt where bgt.relatedEntityType = 'PaymentMatchingRecord' " +
            "and bgt.relatedEntityId = pmr.id)) and " +
            "(p.dateOfPayment >= :dateOfPayment)")
    List<PaymentMatchingRecord> findByPaymentsMatchingFileIdAAndRecordStatusWithoutBackgroundTaskForCSV(@Param("fileId") Long fileId,
                                                                                                        @Param("recordStatus") PaymentMatchingRecordStatus status,
                                                                                                        @Param("dateOfPayment") Date dateOfPayment);


    Integer countByPaymentsMatchingFileAndRecordStatus(PaymentsMatchingFile file, PaymentMatchingRecordStatus status);
}
