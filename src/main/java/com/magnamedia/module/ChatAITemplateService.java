package com.magnamedia.module;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.GPTTemplatePromptMessage;
import com.magnamedia.core.entity.Picklist;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.helper.chatai.ChatMessageRole;
import com.magnamedia.core.helper.chatai.OpenAiMessageContentType;

import java.util.ArrayList;
import java.util.List;

public class ChatAITemplateService {

    public static void createTemplates() {
        validateExtractedSignatureByGPT();

        extractAmountsFromDdcMessage();

        extractDataFromAnnexureOcrText();
    }

    private static void validateExtractedSignatureByGPT() {

        GPTTemplatePromptMessage msg = new GPTTemplatePromptMessage(
                "Please answer with just 'YES' if this image include valid human signature else please answer with just 'NO'",
                ChatMessageRole.USER);
        msg.setType(OpenAiMessageContentType.text);

        GPTTemplatePromptMessage msg2 = new GPTTemplatePromptMessage("@img@",
                ChatMessageRole.USER);
        msg2.setType(OpenAiMessageContentType.image_file);

        List<GPTTemplatePromptMessage> messages = new ArrayList<>();
        messages.add(msg);
        messages.add(msg2);

        new Template.GPTTemplateBuilder()
                .template("gpt_validate_extracted_signature",
                        "recognize if an attachment contains valid signature",
                        "recognize if an attachment contains valid signature")
                .isConversational()
                .gptTemperature(0.0)
                .gptTopP(0.9)
                .gptModel(Setup.getOrCreateItem(Picklist.GPT_MODELS, "gpt-4-vision-preview"))
                .messages(messages)
                .build();
    }

    private static void extractAmountsFromDdcMessage() {

        GPTTemplatePromptMessage msg = new GPTTemplatePromptMessage(
                "Extract all unique amounts from this image. without including the currency or a thousand separator. " +
                        "Sum the maid salary with the related amount. Sum payments within the same day. " +
                        "List the results as comma-separated values without duplication. If no amounts are found, " +
                        "display \"No_Text\" instead. Do not include any additional text.",
                ChatMessageRole.USER);
        msg.setType(OpenAiMessageContentType.text);

        GPTTemplatePromptMessage msg2 = new GPTTemplatePromptMessage("@img@",
                ChatMessageRole.USER);
        msg2.setType(OpenAiMessageContentType.image_file);

        List<GPTTemplatePromptMessage> messages = new ArrayList<>();
        messages.add(msg);
        messages.add(msg2);

        new Template.GPTTemplateBuilder()
                .template("gpt_extract_amounts_from_ddc_message",
                        "recognize if an attachment contains specific text",
                        "recognize if an attachment contains specific text")
                .isConversational()
                .gptTemperature(0.0)
                .gptTopP(0.0)
                .gptModel(Setup.getOrCreateItem(Picklist.GPT_MODELS, "gpt-4-vision-preview"))
                .messages(messages)
                .build();
    }

    private static void extractDataFromAnnexureOcrText() {

        GPTTemplatePromptMessage msg = new GPTTemplatePromptMessage("Please read the following OCR-extracted text from a PDF file, which contains a " +
                "structured table with patient visit information. Your task is to accurately " +
                "reconstruct this data into a clean, properly formatted CSV text. Each row in " +
                "the CSV should represent a single entry, and the columns should include the " +
                "following fields (if available): No,Visit Date,MRN,Name,Passport No,UID,Type of Service,Amount" +
                " Ensure that:\n" +
                "The table rows are correctly aligned and merged even if the OCR text is broken or spread across lines.\n" +
                "All extracted data is cleaned of any formatting artifacts.\n" +
                "The CSV output is plain text (not a table), ready to be saved as a .csv file.\n" +
                "Begin parsing from the first record and stop at the end of the table (ignore headers, footers, and invoice metadata).",
                ChatMessageRole.USER);

        msg.setType(OpenAiMessageContentType.text);

        GPTTemplatePromptMessage msg2 = new GPTTemplatePromptMessage("@ocr_text@",
                ChatMessageRole.USER);
        msg2.setType(OpenAiMessageContentType.text);

        List<GPTTemplatePromptMessage> messages = new ArrayList<>();
        messages.add(msg);
        messages.add(msg2);

        new Template.GPTTemplateBuilder()
                .template("gpt_parse_annexure_ocr_text",
                        "parse ocr text from annexure",
                        "parse ocr text from annexure")
                .isConversational()
                .gptTemperature(0.0)
                .gptTopP(0.0)
                .gptModel(Setup.getOrCreateItem(Picklist.GPT_MODELS, "gpt-4-vision-preview"))
                .messages(messages)
                .build();
    }
}
