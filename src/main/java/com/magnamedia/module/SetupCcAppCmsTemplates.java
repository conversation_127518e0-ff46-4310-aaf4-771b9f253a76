package com.magnamedia.module;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.extra.CcAppCmsTemplate;
import com.magnamedia.helper.PicklistHelper;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class SetupCcAppCmsTemplates {
    Template template;
    Map<String, String> expressionParameters;

    private Template createTemplate(String name, String subject, String content, String specs, boolean unicode, boolean showInLog) {
        template = new Template(name, subject, content, specs);
        template.setUnicode(unicode);
        template.setStatus(Setup.getItem("template_status", "active"));
        template.setShowInReceiverLog(showInLog);
        return template;
    }

    public void setupTemplates() {
        approvedMonthlyPaymentPendingRefund();
        pendingDdNoApprovedDd();
        paidByCardAndNotSubmitDd();
        incompleteDds();
        bouncedDds();
        bouncedDdsPaid();
        rejectedDds();
        approvedDds();
        approvedDdsMonthlyPaymentIntro();
        payWithCreditCardEarlyCcPayment();
        payWithCreditCardEarlyCcNote();
        noRulesApply();
        cma3318Cms();

        faqBodies();

        cma3750Cms();

        approvedMonthlyPaymentPendingRefundNonConditional();
        approvedMonthlyPaymentPendingRefundConditional();
        approvedMonthlyPaymentPendingRefundProofUploaded();

        paymentSectionDefaultMessages();

        getSwitchMaidTerms();

        //ACC-7105
        updateMonthlyPaymentAndMaidContractBody();
        updateMonthlyPaymentAndMaidContractTitle();
        updateMonthlyPaymentAndMaidContractDDBody();
        updateMonthlyPaymentAndMaidContractDDTitle();
        updateMonthlyPaymentAndMaidContractViaCCBody();
        updateMonthlyPaymentAndMaidContractViaCCTitle();

        // ACC-8949
        createFlowProcessorEntityTerminationMessageDetailsTemplate();
    }

    private void updateMonthlyPaymentAndMaidContractTitle() {
        template = createTemplate(CcAppCmsTemplate.MAID_GO_ON_VACATION_UPDATE_MONTHLY_PAYMENT_AND_MAID_CONTRACT_TITLE.toString(), "",
                "@title@", "", true, true);
        expressionParameters = new HashMap<>();
        expressionParameters.put("title", "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"CCAppContentService\")" +
                ".getTemplateTypeForUpdateMaidMonthlyPaymentAndContractTitle(#root)");
        TemplateUtil.createTemplate(template, expressionParameters);
    }

    private void updateMonthlyPaymentAndMaidContractBody() {
        template = createTemplate(CcAppCmsTemplate.MAID_GO_ON_VACATION_UPDATE_MONTHLY_PAYMENT_AND_MAID_CONTRACT_BODY.toString(), "",
                "@body@", "", true, true);
        expressionParameters = new HashMap<>();
        expressionParameters.put("body", "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"CCAppContentService\")" +
                ".getTemplateTypeForUpdateMaidMonthlyPaymentAndContractBody(#root)");
        TemplateUtil.createTemplate(template, expressionParameters);
    }

    private void updateMonthlyPaymentAndMaidContractViaCCTitle() {
        template = createTemplate(CcAppCmsTemplate.MAID_GO_ON_VACATION_UPDATE_MONTHLY_PAYMENT_AND_MAID_CONTRACT_VIA_CC_TITLE.toString(), "",
                "<b>Permission to amend your monthly payments and update @maid_first_name@’s contract</b>",
                "", true, true);
        TemplateUtil.createTemplate(template, new HashMap<>());
    }

    private void updateMonthlyPaymentAndMaidContractViaCCBody() {
        template = createTemplate(CcAppCmsTemplate.MAID_GO_ON_VACATION_UPDATE_MONTHLY_PAYMENT_AND_MAID_CONTRACT_VIA_CC_BODY.toString(), "",
                "<div>To ensure @maid_first_name@ can go on vacation to Nepal, Nepali government’s @changes@ requirements must be met." +
                            "<br/>" +
                            "@maid_first_name@’s salary must be updated to be " +
                            "<sup style=\"font-size: 10px;\">AED </sup>" +
                            "<b>@nepali_total_salary@/month</b> " +
                            "on @her_his@ contract." +
                            "<br/>" +
                            "Starting <b>@next_month@ 1st</b> and onwards, " +
                            "your monthly payments will be " +
                            "<sup style=\"font-size: 10px;\">AED </sup>" +
                            "<b>@payment@/month + VAT.</b>" +
                            "<br/>" +
                            "Once the contract is updated, we’ll send you @her_his@ updated contract. Please note that this process will take up to " +
                            "<b>@duration@ business days</b>." +
                            "<br/>" +
                            "To authorize the required updates on your monthly payments and to @maid_first_name@’s contract, kindly click the “Confirm” button below." +
                        "</div>",
                "", true, true);
        TemplateUtil.createTemplate(template, new HashMap<>());
    }

    private void updateMonthlyPaymentAndMaidContractDDTitle() {
        template = createTemplate(CcAppCmsTemplate.MAID_GO_ON_VACATION_UPDATE_MONTHLY_PAYMENT_AND_MAID_CONTRACT_DD_TITLE.toString(), "",
                "<b>Permission to amend your monthly bank payment forms and update @maid_first_name@’s contract</b>",
                "", true, true);
        expressionParameters = new HashMap<>();
        TemplateUtil.createTemplate(template, new HashMap<>());
    }

    private void updateMonthlyPaymentAndMaidContractDDBody() {
        template = createTemplate(CcAppCmsTemplate.MAID_GO_ON_VACATION_UPDATE_MONTHLY_PAYMENT_AND_MAID_CONTRACT_DD_BODY.toString(), "",
                "<div>To ensure @maid_first_name@ can go on vacation to Nepal, Nepali government’s @changes@ requirements must be met." +
                            "<br/>" +
                            "@maid_first_name@’s salary must be updated to be " +
                            "<sup style=\"font-size: 10px\">AED </sup>" +
                            "<b>@nepali_total_salary@/month</b> " +
                            "on @her_his@ contract." +
                            "<br/>" +
                            "Starting <b>@next_month@ 1st</b> and onwards, " +
                            "your monthly payments will be " +
                            "<sup style=\"font-size: 10px;\">AED </sup>" +
                            "<b>@payment@/month + VAT.</b>" +
                            "<br/>" +
                            "Once the contract is updated, we’ll send you @her_his@ updated contract. Please note that this process will take up to " +
                            "<b>@duration@ business days</b>." +
                            "<br/>" +
                            "To authorize the required updates on your monthly bank payment forms and to @maid_first_name@’s contract, kindly click the “Confirm” button below."+
                        "</div>"
                , "", true, true);
        expressionParameters = new HashMap<>();
        TemplateUtil.createTemplate(template, new HashMap<>());
    }

    private void approvedMonthlyPaymentPendingRefund() {
        template = createTemplate(CcAppCmsTemplate.CC_PAYMENT_APPROVED_MONTHLY_PAYMENT_PENDING_REFUND.toString(), "",
                "We just sent you AED @amount@ by bank transfer. Please expect to receive the amount to your bank account in the next 2 business days.@Click_here_to_view_the_proof_of_transfer@" ,"", true, true);
        expressionParameters = new HashMap<>();
        expressionParameters.put("amount", "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"CCAppContentService\").approvedMonthlyPaymentGetRefundAmount(#root)");
        expressionParameters.put("Click_here_to_view_the_proof_of_transfer", "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"CCAppContentService\").getViewProofTransferLink(#root)");
        TemplateUtil.createTemplate(template, expressionParameters);
    }

    private void pendingDdNoApprovedDd() {
        template = createTemplate(CcAppCmsTemplate.CC_PAYMENT_PENDING_DD_NO_APPROVED_DD.toString(), "",
                "Some of your Monthly Bank Payment Forms are still not confirmed by your bank.  <br/>" +
                        "<br/>" +
                        "You may receive messages from your bank saying that your Monthly Bank Payment Form has been rejected. Please ignore them, as we'll try to process it again until successful. <b>We'll inform you if you need to do anything, no action or follow up is required from your side.</b>", "", true, true);
        expressionParameters = new HashMap<>();
        TemplateUtil.createTemplate(template, expressionParameters);
    }


    private void paidByCardAndNotSubmitDd() {
        template = createTemplate(CcAppCmsTemplate.CC_PAYMENT_PAID_BY_CARD_AND_NOT_SUBMIT_DD.toString(), "",
                "@content@", "", true, true);
        expressionParameters = new HashMap<>();
        expressionParameters.put("content", "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"CCAppContentService\").resolveContent({\"contract\":#root, \"base_template\":\"CC_PAYMENT_PAID_BY_CARD_AND_NOT_SUBMIT_DD\"})");
        TemplateUtil.createTemplate(template, expressionParameters);
    }

    private void incompleteDds() {
        template = createTemplate(CcAppCmsTemplate.CC_PAYMENT_INCOMPLETE_DDS.toString(), "",
                "@content@", "", true, true);
        expressionParameters = new HashMap<>();
        expressionParameters.put("content", "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"CCAppContentService\").resolveContent({\"contract\":#root, \"base_template\":\"CC_PAYMENT_INCOMPLETE_DDS\"})");
        TemplateUtil.createTemplate(template, expressionParameters);
    }

    private void bouncedDds() {
        template = createTemplate(CcAppCmsTemplate.CC_PAYMENT_BOUNCED_DDS.toString(), "",
                "@content@", "", true, true);
        expressionParameters = new HashMap<>();
        expressionParameters.put("content", "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"CCAppContentService\").resolveContent({\"contract\":#root, \"base_template\":\"CC_PAYMENT_BOUNCED_DDS\"})");
        TemplateUtil.createTemplate(template, expressionParameters);
    }

    private void bouncedDdsPaid() {

        expressionParameters = new HashMap<>();
        expressionParameters.put("content", "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"CCAppContentService\").resolveContent({\"contract\":#root, \"base_template\":\"CC_PAYMENT_BOUNCED_DDS_PAID\"})");

        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.CC_PAYMENT_BOUNCED_DDS_PAID.toString(),
                        "", "")
                .text("@content@")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .expressionParameters(expressionParameters)
                .build();
    }

    private void rejectedDds() {
        template = createTemplate(CcAppCmsTemplate.CC_PAYMENT_REJECTED_DDS.toString(), "",
                "@content@", "", true, true);
        expressionParameters = new HashMap<>();
        expressionParameters.put("content", "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"CCAppContentService\").resolveContent({\"contract\":#root, \"base_template\":\"CC_PAYMENT_REJECTED_DDS\"})");
        TemplateUtil.createTemplate(template, expressionParameters);
    }

    private void approvedDds() {
        template = createTemplate(CcAppCmsTemplate.CC_PAYMENT_APPROVED_DDs.toString(), "",
                "Your Monthly Bank Payment Form has been confirmed. @monthly_payment_intro@", "", true, true);
        expressionParameters = new HashMap<>();
        expressionParameters.put("monthly_payment_intro", "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"CCAppContentService\").approvedDdsMonthlyPaymentIntro(#root)");
        TemplateUtil.createTemplate(template, expressionParameters);
    }

    private void approvedDdsMonthlyPaymentIntro() {
        template = createTemplate(CcAppCmsTemplate.CC_PAYMENT_APPROVED_DDS_MONTHLY_PAYMENT_INTRO.toString(), "",
                " We’ll deduct your next payment of AED @amount@ on @next_deduction_date@.", "", true, true);
        TemplateUtil.createTemplate(template, new HashMap<>());
    }

    private void payWithCreditCardEarlyCcPayment() {
        template = createTemplate(CcAppCmsTemplate.CC_PAYMENT_PAY_WITH_CREDIT_CARD_EARLY_CC_PAYMENT.toString(), "",
                "Please click below to pay your upcoming payment of AED @amount@ via Credit or Debit card.", "", true, true);
        expressionParameters = new HashMap<>();
        expressionParameters.put("amount", "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"CCAppContentService\")" +
                ".payWithCreditCardEarlyCcPaymentAmount({\"contract\":#root,\"todoUuid\":\"@todoUuid@\",\"amount\":\"@amount@\"})");
        TemplateUtil.createTemplate(template, expressionParameters);
    }

    private void payWithCreditCardEarlyCcNote() {

        template = createTemplate(CcAppCmsTemplate.CC_PAYMENT_PAY_WITH_CREDIT_CARD_EARLY_CC_NOTE.toString(), "",
                "@content@", "", true, true);

        TemplateUtil.createTemplate(template, new HashMap<String, String>(){{
            put("content", "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"CCAppContentService\")" +
                    ".payWithCreditCardEarlyCcNote(#root)");
        }});
    }

    private void noRulesApply() {
        template = createTemplate(CcAppCmsTemplate.CC_PAYMENT_NO_RULES_APPLY.toString(), "",
                "Your Monthly Bank Payment Form has been confirmed. @monthly_payment_intro@", "", true, true);
        expressionParameters = new HashMap<>();
        expressionParameters.put("monthly_payment_intro", "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"CCAppContentService\").approvedDdsMonthlyPaymentIntro(#root)");
        TemplateUtil.createTemplate(template, expressionParameters);
    }
    
    private void faqBodies() {
        template = createTemplate(CcAppCmsTemplate.CC_FAQ_BOUNCED_PAY_BY_CARD.toString(), "",
                "If you think your next payment will bounce, you can <a style=\"text-decoration: none; color: rgb(66, 103, 178);\" href=@pay_link@>pay by credit card</a>, just for this month.", "", true, true);
        TemplateUtil.createTemplate(template, new HashMap<>());
    }

    // ACC-4715 CMA-3318
    private void cma3318Cms() {
        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.CC_CLIENT_PAYING_VIA_CREDIT_CARD.toString(),
                        "", "")
                .text("You're currently paying your monthly payments via Credit or Debit Card. " +
                        "If you'd like to submit an automatic monthly bank payment form, please click on the " +
                        "\"Add Automatic Monthly Bank Payment Form\" button below. <br/><br/>" +
                        "Your payment covers your maid's service until @paid_end_date@. Your next payment is due on " +
                        "@due_date@, but don't worry, we will send you a reminder 5 days before your payment is due so you can pay. " +
                        "If you'd like to settle your next payment now, <a style=\"text-decoration: none; color: rgb(66, 103," +
                        " 178);\" href=@pay_link@>click here to pay your upcoming payment via Credit or Debit Card.</a>")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .build();
    }

    private void cma3750Cms() {

        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.CC_CLIENT_PAYING_VIA_CREDIT_CARD_REPLACEMENT_SCREEN.toString(),
                        "", "")
                .text("<div style=\"margin:0px;padding: 8px;border-radius: 8px;color: #4d4d4d;background-color: #fff;\">" +
                        "<span >Pay today by Credit or Debit card to cover the difference between hiring @new_nationality@ maid " +
                        "and @old_nationality@ maid. Your payment will cover your maid's service until @date@.</span>" +
                        "<div style=\"font-size: 14px;margin-top: 40px;\">" +
                        "<span style=\"font-weight: bold;\" >Your Payment Today:</span>" +
                        "<span style=\"font-size: 6px;\">AED</span>" +
                        "<span>@total_amount@</span>" +
                        "</div>" +
                        "<div style=\"text-align:right; opacity:0.5; font-size:10px;margin-top: 2px;\">" +
                        "@payments_info@" +
                        "</div>" +
                        " </div>")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .build();

        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.DD_AMENDMENT_ONE_MONTH_AGREEMENT_DOWNGRADE.toString(),
                        "", "")
                .text("Please click “I Agree“ below to indicate your approval allowing us to submit " +
                        "a Monthly Bank Payment Form of AED @amount@/month + VAT, " +
                        "with your previously obtained electronic signature. <br/>We’ll cancel all your other Monthly Bank Payment Forms")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .build();

        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.DD_AMENDMENT_ONE_MONTH_AGREEMENT_UPGRADE.toString(),
                        "", "")
                .text("Please click “I Agree“ below to indicate your approval allowing us to submit " +
                        "the following Monthly Bank Payment Forms:")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .build();

        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.CC_PAYMENT_IN_ADVANCE_AFTER_REPLACEMENT.toString(),
                        "", "")
                .text("<div style=\"margin:24px 0px;padding: 8px;border-radius: 8px;color: #4d4d4d;background-color: #fff;\">" +
                        "<span >You can pay today by Credit or Debit Card which will cover your service until @date@.</span>" +
                        "<div style=\"font-size: 14px;margin-top: 40px;\">" +
                        "<span style=\"font-weight: bold;\" >Your Payment Today: </span>" +
                        "<span>AED @amount@</span>" +
                        "</div>")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .build();

        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.FLEXIBLE_PACKAGE_NOTE.toString(),
                        "", "")
                .text("This is as per clause 4, paragraph 9 of the government-regulated flexible package")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .build();

        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.LIVE_OUT_ALLOWENCE_NOTE.toString(),
                        "", "")
                .text("Also, As per clause 5, paragraph 7 AED @live_out_amount@/month is added to cover your maids live-out allowance")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .build();

        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.SWITCH_SAME_MAID_LIVE_IN_OUT.toString(),
                        "", "")
                .text("Please note you are currently switching your contract with @maid_first_name@ from a @from_live@ to a @to_live@ contract")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .build();
    }

    // ACC-5663
    private void approvedMonthlyPaymentPendingRefundNonConditional() {

        expressionParameters = new HashMap<>();
        expressionParameters.put("amount", "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"CCAppContentService\").approvedMonthlyPaymentGetRefundAmount(#root)");
        expressionParameters.put("Click_here_to_view_the_proof_of_transfer",
                "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"CCAppContentService\")" +
                        ".getViewProofTransferLink({\"contract\":#root, \"text\":\"Click here to view proof of transfer\"})");

        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.CC_PAYMENT_APPROVED_MONTHLY_PAYMENT_PENDING_REFUND_NON_CONDITIONAL.toString(),
                        "", "")
                .text("We just sent you AED @amount@ by bank transfer. Please expect to receive the amount to " +
                        "your bank account in the next 7 business days.@Click_here_to_view_the_proof_of_transfer@")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .expressionParameters(expressionParameters)
                .build();
    }

    private void approvedMonthlyPaymentPendingRefundConditional() {

        expressionParameters = new HashMap<>();
        expressionParameters.put("amount", "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"CCAppContentService\").approvedMonthlyPaymentGetRefundAmount(#root)");
        expressionParameters.put("payments_amount", "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"CCAppContentService\").getConditionalRefundPayment(#root)");
        expressionParameters.put("Click_here_to_view_the_proof_of_transfer",
                "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"CCAppContentService\")" +
                        ".getViewProofTransferLink({\"contract\":#root, \"text\":\"Click here to view proof of transfer\"})");
        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.CC_PAYMENT_APPROVED_MONTHLY_PAYMENT_PENDING_REFUND_CONDITIONAL.toString(),
                        "", "")
                .text("You're eligible for a refund of AED @amount@. After we receive your @payments_amount@, " +
                        "we’ll refund the amount to your bank account within 2 business days.@Click_here_to_view_the_proof_of_transfer@")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .expressionParameters(expressionParameters)
                .build();
    }

    private void approvedMonthlyPaymentPendingRefundProofUploaded() {

        expressionParameters = new HashMap<>();
        expressionParameters.put("amount", "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"CCAppContentService\").approvedMonthlyPaymentGetRefundAmount(#root)");
        expressionParameters.put("Click_here_to_view_the_proof_of_transfer",
                "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"CCAppContentService\")" +
                        ".getViewProofTransferLink({\"contract\":#root, \"text\":\"Click here to view proof of transfer\"})");

        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.CC_PAYMENT_APPROVED_MONTHLY_PAYMENT_PENDING_REFUND_PROOF_UPLOADED.toString(),
                        "", "")
                .text("We just sent you AED @amount@ by bank transfer. Please expect to receive the amount to your " +
                        "bank account within 3 business days.@Click_here_to_view_the_proof_of_transfer@ Thank you.")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .expressionParameters(expressionParameters)
                .build();
    }

    private void paymentSectionDefaultMessages() {
        expressionParameters = new HashMap<>();
        expressionParameters.put("worker_type", "#root == null ? " +
                "\"maid\" : " +
                "#root instanceof T(com.magnamedia.entity.Contract) ? " +
                "workerType != null ? " +
                "workerType.getCode().equals(\"private_driver\") ? " +
                "\"driver\" : " +
                "\"maid\" : " +
                "\"maid\" : " +
                "\"maid\"");

        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.PAYMENT_SECTION_BOUNCED_PAYMENT_DEFAULT_MESSAGE.toString(),
                        "", "")
                .text("Unfortunately, the bank couldn't deduct your payment of <b>AED @amount@</b> due to insufficient funds.<br/>" +
                        "<br/>" +
                        "We'll try deducting the amount again in <b>3 days.</b> Please make sure to add funds to your bank account. " +
                        "If you can't add funds, please pay using alternative methods by clicking the button below.")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .build();

        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.PAYMENT_SECTION_DD_REJECTION_DEFAULT_MESSAGE.toString(),
                        "", "")
                .text("The bank couldn't deduct your payment because the signature you provided doesn't seem to be correct.<br/>" +
                        "<br/>" +
                        "Banks are really specific when it comes to signatures, so even if the signature your provided had a small mismatch, " +
                        "they would reject it. Fortunately, it's an easy problem to fix. You just have to click the button below and carefully sign again.")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .build();

        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.PAYMENT_SECTION_INCOMPLETE_DDS_ONE_DOC_REJECTED_DEFAULT_MESSAGE.toString(),
                        "", "")
                .text("The photo of the bank account holder's @missing_ban_info@ that you've sent us is not correct.<br/>" +
                        "<br/>" +
                        "Would you be so kind as to send us the correct photo by clicking the button below.")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .build();

        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.PAYMENT_SECTION_INCOMPLETE_DDS_TWO_DOC_REJECTED_DEFAULT_MESSAGE.toString(),
                        "", "")
                .text("The photos of the bank account holder's @missing_ban_info@ that you've sent us are not correct.<br/>" +
                        "<br/>" +
                        "Would you be so kind as to send us the correct photo by clicking the button below.")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .build();

        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.PAYMENT_SECTION_INCOMPLETE_DDS_THREE_DOC_REJECTED_DEFAULT_MESSAGE.toString(),
                        "", "")
                .text("The photos of the bank account holder's name, Emirates ID and IBAN that you've sent us are not correct.<br/>" +
                        "<br/>" +
                        "Would you be so kind as to send us the correct photo by clicking the button below.")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .build();

        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.PAYMENT_SECTION_INCOMPLETE_DDS_MISSING_BANK_INFO_DEFAULT_MESSAGE.toString(),
                        "", "")
                .text("We are still missing your bank information. Would you be so kind as to send us your bank details by clicking the button below.")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .build();

        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.PAYMENT_SECTION_PAYING_VIA_CREDIT_CARD_DEFAULT_MESSAGE.toString(),
                        "", "")
                .text("You're currently paying your monthly payment via Credit or Debit Card. If you'd like to submit an " +
                        "automatic monthly bank payment form, please click on the \"Add Automatic Bank Payment Form\" button below.<br/>" +
                        "<br/>" +
                        "You're payment covers your @worker_type@'s service <b>until @paid_end_date@</b>. " +
                        "Your next payment is due <b>on @due_date@</b>, " +
                        "but don't worry, we'll send you a reminder 5 days before your payment is due so you can pay." +
                        "->Would you like to settle your next payment now?<br/>" +
                        "<a style=\"text-decoration: none; color: rgb(66, 103,178);\" href=@pay_link@>" +
                        "Pay your upcoming payment via Credit ot Debit Card</a>")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .expressionParameters(expressionParameters)
                .build();

        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.PAYMENT_SECTION_PAYING_VIA_CREDIT_CARD_WITH_TOKEN_DEFAULT_MESSAGE.toString(),
                        "", "")
                .text("You're currently paying your monthly payment via Credit or Debit Card. If you'd like to submit an " +
                        "automatic monthly bank payment form, please click on the \"Add Automatic Bank Payment Form\" button below.<br/>" +
                        "<br/>" +
                        "You're payment covers your @worker_type@'s service <b>until @paid_end_date@</b>. " +
                        "Your next payment of <b>AED @amount@<b> is due <b>on @due_date@</b>." )
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .expressionParameters(expressionParameters)
                .build();

        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.PAYMENT_SECTION_INITIAL_PAYMENT_BY_ALTERNATIVE_METHOD_DEFAULT_MESSAGE.toString(),
                        "", "")
                .text("Your payment covers your @worker_type@'s service until <b>@paid_end_date@</b>.<br/>" +
                        "<br/>" +
                        "If we don't receive your payment by <b>@paid_end_date - 1@</b>, your @worker_type@'s service will be " +
                        "automatically canceled and your @worker_type@ will be notified to leave our home." +
                        "->To extend the service for 1 month, we can exceptionally accept your payment of <b>@amount@</b> " +
                        "by debit or credit card. <a style=\"text-decoration: none; color: rgb(66, 103,178);\" href=@pay_link@>" +
                        "Pay your upcoming payment via Credit or Debit card</a>")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .expressionParameters(expressionParameters)
                .build();

        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.PAYMENT_SECTION_ONE_MONTH_AGREEMENT_DEFAULT_MESSAGE.toString(),
                        "", "")
                .text("We hope you're enjoying our services. We'd like to inform you that your current payment covers your maid's service until <b>@paid_end_date@</b>.<br/>" +
                        "<br/>" +
                        "Once you have your Emirate ID and IBAN available, please click the button below to complete your Monthly Bank Payment Form." +
                        "->To extend the service for 1 month, we can exceptionally accept your payment of <b>@amount@</b> by debit or credit card. " +
                        "<a style=\"text-decoration: none; color: rgb(66, 103,178);\" href=@pay_link@>" +
                        "Pay your upcoming payment via Credit or Debit card<a>")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .build();

        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.PAYMENT_SECTION_DD_CONFIRMED_DEFAULT_MESSAGE.toString(),
                        "", "")
                .text("Your Monthly Bank Payment Form has been <b>confirmed</b>.@monthly_payment_intro@")
                .method("CMS")
                .expressionParameters(new HashMap<String, String>() {{
                    put("monthly_payment_intro", "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"CCAppContentService\").approvedDdsPaymentIntro(#root)");

                }})
                .target(PicklistHelper.getItem("template_target", "client"))
                .build();

        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.PAYMENT_SECTION_DD_CONFIRMED_DEFAULT_MESSAGE_INTRO.toString(),
                        "", "")
                .text("We'll deduct your next payment of <b>AED @amount@</b> on <b>@next_deduction_date@</b>.")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .build();

        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.PAYMENT_SECTION_ONLINE_PAYMENTS_REMINDER_CLIENT_HAS_CONFIRMED_DDS_DEFAULT_MESSAGE.toString(),
                        "", "")
                .text("Your Monthly Bank Payment Form has been <b>confirmed</b>." +
                        "@monthly_payment_intro@<br/>" +
                        "<br/>" +
                        "Kindly note that you have an overdue payment of <b>AED @amount@</b>." +
                        "->Please settle your payment to avoid any interruptions in your service.<br/>" +
                        "<a style=\"text-decoration: none; color: rgb(66, 103,178);\" href=@pay_link@>" +
                        "Pay your overdue payment via Credit or Debit Card</a>")
                .expressionParameters(new HashMap<String, String>() {{
                    put("monthly_payment_intro", "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"CCAppContentService\").approvedDdsPaymentIntro(#root)");

                }})
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .build();

        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.PAYMENT_SECTION_ONLINE_PAYMENTS_REMINDER_CLIENT_WITH_NO_CONFIRMED_DDS_DEFAULT_MESSAGE.toString(),
                        "", "")
                .text("The bank is now checking your bank details. We'll notify you as soon as they're approved, or if there's anything you need to do." +
                        "@monthly_payment_intro@<br/>" +
                        "<br/>" +
                        "Kindly note that you have an overdue payment of <b>AED @amount@</b>." +
                        "->Please settle your payment to avoid any interruptions in your service.<br/>" +
                        "<a style=\"text-decoration: none; color: rgb(66, 103,178);\" href=@pay_link@>" +
                        "Pay your overdue payment via Credit or Debit Card</a>")
                .expressionParameters(new HashMap<String, String>() {{
                    put("monthly_payment_intro", "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"CCAppContentService\").approvedDdsPaymentIntro(#root)");

                }})
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .build();

        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.PAYMENT_SECTION_DD_PENDING_DEFAULT_MESSAGE.toString(),
                        "", "")
                .text("The bank is now checking your bank details. We'll notify you as soon as they're approved, or if there's anything you need to do.<br/>" +
                        "<br/>" +
                        "You may receive messages form your bank saying that your Monthly Bank Payment Form has been rejected. " +
                        "Please ignore them, as we'll try to process it again until successful. " +
                        "We'll inform you if you need to do anything, no action or follow up is required from your side.")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .build();
        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.PAYMENT_SECTION_CONTRACT_SCHEDULED_FOR_TERMINATION.toString(),
                        "", "")
                .text("Your contract is scheduled for termination @termination_date@.")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .build();

        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.PAYMENT_SECTION_NO_RULES_APPLY_DEFAULT_MESSAGE.toString(),
                        "", "")
                .text("Your next payment of <b>AED @amount@</b> is due on <b>@next_deduction_date@</b>.")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .build();

        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.PAYMENT_SECTION_RECEIVE_PAYMENT_NOTIFICATIONS_SUB_TITLE.toString(),
                        "", "")
                .text("Enable this feature if you'd like to receive payment notifications whenever your payment is received. " +
                        "You can stop these notifications whenever you wish by turning this option off.")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .build();
    }

    private void getSwitchMaidTerms() {
        new Template.TemplateBuilder().Template(
                CcAppCmsTemplate.DD_AMENDMENT_TO_FILIPINA_PAYMENT_TERMS.toString(), "","")
                .text("<!DOCTYPE html> " +
                        "<html lang=\"en\"> " +
                        "<head> " +
                        "<meta charset=\"UTF-8\" /> " +
                        "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" /> " +
                        "<link href=\" https://cdn.jsdelivr.net/npm/airbnbcereal/stylesheet.min.css  \" rel=\"stylesheet\"/> " +
                        "<link href=\"https://fonts.googleapis.com/css?family=Roboto  \" rel=\"stylesheet\"/> " +
                        "<style> " +
                        ".main-content { --bs-gutter-x: 1.5rem;--bs-gutter-y: 0; width: 100%; margin-right: auto; margin-left: auto; } " +
                        ".row-content { --bs-gutter-x: 1.5rem; --bs-gutter-y: 0; display: flex; flex-wrap: wrap;" +
                        "margin-top: calc(-1 * var(--bs-gutter-y));margin-right: calc(-0.5 * var(--bs-gutter-x));" +
                        " margin-left: calc(-0.5 * var(--bs-gutter-x));  } " +
                        ".content-paragraph-label { color: #4d4d4d; font-family: Airbnb Cereal App; font-size: 14px;font-weight: 500; " +
                        "line-height: 18.23px; text-align: left; margin-bottom: 0 !important; } " +
                        ".label-container { padding: 0 6px !important; flex: 0 0 auto; width: 30%;       } " +
                        ".value-container { flex: 0 0 auto; width: 55%; padding: 0 0.5em;       } " +
                        ".value-content { margin-top: 16px; } " +
                        ".value-paragraph { color: #4d4d4d; font-family: Roboto; font-size: 8px;font-style: italic;font-weight: 400; " +
                        "line-height: 12px; letter-spacing: -0.005em; text-align: left; margin-bottom: 0 !important; " +
                        "margin-top: 0 !important;  } " +
                        ".currency-paragraph { color: #4d4d4d;  font-family: Airbnb Cereal App; font-size: 14px; font-weight: 500; " +
                        "line-height: 18.23px; text-align: left; margin-bottom: 0 !important; } " +
                        ".currency-span { font-family: Airbnb Cereal App;font-size: 7px; font-weight: 400; " +
                        "line-height: 9.11px; text-align: left; position: relative; top: -0.4rem; margin-right: 0.3em;} " +
                        ".currency-span.last{ margin-right: 0 !important; } " +
                        "</style> " +
                        "</head> " +
                        "<body> " +
                        "<div class=\"main-content\">" +
                        " @one_time_terms@ " +
                        " @monthly_terms@ " +
                        "</div>" +
                        "</body>" +
                        "</html>")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .expressionParameters(new HashMap<>())
                .build();

        new Template.TemplateBuilder().Template(
                        CcAppCmsTemplate.DD_AMENDMENT_NOTE_LIVE_OUT.toString(), "","")
                .text("<!DOCTYPE html> " +
                        "<html lang=\"en\"> " +
                            "<head> " +
                                "<meta charset=\"UTF-8\" /> " +
                                "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" /> " +
                                "<link href=\" https://cdn.jsdelivr.net/npm/airbnbcereal/stylesheet.min.css  \" rel=\"stylesheet\"/> " +
                                "<link href=\"https://fonts.googleapis.com/css?family=Roboto  \" rel=\"stylesheet\"/> " +
                                "<style> " +
                                    ".main-content {--bs-gutter-x: 1.5rem;--bs-gutter-y: 0; width: 100%; margin-right: auto; margin-left: auto;padding-left:0} " +
                                    ".row-content {--bs-gutter-x: 1.5rem; --bs-gutter-y: 0; display: flex; flex-wrap: wrap;" +
                                    "margin-top: calc(-1 * var(--bs-gutter-y));margin-right: calc(-0.5 * var(--bs-gutter-x));" +
                                    " margin-left: calc(-0.5 * var(--bs-gutter-x));padding-top:2px;padding-bottom:2px;padding-left:8px} " +
                                    ".note-paragraph {color: #4e4e4e; font-family: Airbnb Cereal App; font-size: 14px; font-weight: 300; " +
                                    "line-height: 16px; letter-spacing: -0.005em; text-align: left; padding-top: 2px; padding-bottom: 2px; " +
                                    "padding-left: 8px; padding-right: 16px; border-left: 1px solid #efa256}" +
                                    ".note-paragraph strong {color: #222; font-weight: 500}" +
                                "</style> " +
                            "</head> " +
                            "<body> " +
                                "<div class=\"main-content\"> " +
                                    "@amend_dd_note_live_out@ " +
                                "</div>" +
                            "</body>" +
                        "</html>")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .build();
     }

     // ACC-8949
    private static void createFlowProcessorEntityTerminationMessageDetailsTemplate() {
        new Template.TemplateBuilder()
                .Template(CcAppCmsTemplate.FLOW_PROCESSOR_ENTITY_TERMINATION_MESSAGE_DETAILS.toString(), "","")
                .text("To ensure uninterrupted service, please settle your payment of AED @total_amount@ today. " +
                        "You can use a credit or debit card by clicking on the button below."
                )
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .expressionParameters(new HashMap<>())
                .build();
    }
}