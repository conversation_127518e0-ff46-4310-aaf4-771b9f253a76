package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 * <AUTHOR>
 * @created 21/05/2025 - 2:09 AM
 * ACC-9286
 */
public enum DDMessagingPaymentStructure implements LabelValueEnum {

    ONE_PAYMENT("One Payment"),
    MULTIPLE_PAYMENTS("Multiple Payments");

    private final String label;

    DDMessagingPaymentStructure(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}