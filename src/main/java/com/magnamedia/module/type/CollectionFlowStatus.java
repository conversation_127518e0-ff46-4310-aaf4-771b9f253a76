package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 3/5/2022
 **/

public enum CollectionFlowStatus implements LabelValueEnum{
    PENDING_CLIENT_RESPONSE("Pending Client Response"),
    PENDING_BANK_RESPONSE("Pending Bank Response"),
    PENDING_BUSINESS_USER_ACTION("Pending Business User Action");

    private final String label;

    CollectionFlowStatus(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }

}
