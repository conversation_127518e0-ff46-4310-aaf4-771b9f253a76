package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 *
 * <AUTHOR> <<EMAIL>>
 *
 */
public enum PaymentMethod implements LabelValueEnum {

    CASH("Cash"),
    WIRE_TRANSFER("Wire Transfer"), 
    BANK_DEPOSIT("Bank Deposit"), 
    CARD("Card"), 
    CHEQUE("Cheque"), 
    ADJUSTMENT("Adjustment"),
    DIRECT_DEBIT("Direct Debit"),
    
    CREDIT_CARD("Credit Card"),
    BANK_TRANSFER("Bank Transfer"),
    MONEY_TRANSFER("Money Transfer");

    private final String label;

    private PaymentMethod(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }

    public static PaymentMethod convertFromExpensePaymentMethodToPaymentMethod(ExpensePaymentMethod expensePaymentMethod) {
        if (expensePaymentMethod == null) return null;
        switch (expensePaymentMethod) {
            case CHEQUE:
                return CHEQUE;
            case CREDIT_CARD:
                return CREDIT_CARD;
            case MONEY_TRANSFER:
                return MONEY_TRANSFER;
            case BANK_TRANSFER:
                return BANK_TRANSFER;
            case CASH:
                return CASH;
        }
        return null;
    }
}
