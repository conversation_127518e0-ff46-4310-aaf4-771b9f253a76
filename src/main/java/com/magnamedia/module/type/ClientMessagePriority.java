package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;


public enum ClientMessagePriority implements LabelValueEnum{
    SEND_SMS_WITH_NOTIFICATION("Send sms with notification"),
    SEND_SMS_AFTER_TWO_HOURS("Send sms after two hours"),
    SEND_SMS_NEXT_DAY("Send sms next day"),
    DO_NOT_SEND_SMS("Do not send sms");

    private final String label;

    ClientMessagePriority(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}

