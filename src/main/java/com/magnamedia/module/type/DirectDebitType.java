package com.magnamedia.module.type;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.magnamedia.extra.LabelValueEnum;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Nov 3, 2019
 *         Jirra ACC-1131
 */

// ACC-1435
public enum DirectDebitType implements LabelValueEnum {
    MONTHLY("Monthly", "contract_direct_debit_activation"),
    ONE_TIME("One Time", "contract_direct_debit_one_time_activation"),
    // ACC-1587
    WEEKLY("Weekly", "contract_direct_debit_weekly_activation"),
    // ACC-2550
    DAILY("Daily", "contract_direct_debit_daily_activation");

    private final String label;

    @JsonIgnore
    private final String templateName;

    DirectDebitType(String label, String templateName) {
        this.label = label;
        this.templateName = templateName;
    }

    @Override
    public String getLabel() {
        return label;
    }

    public String getTemplateName() {
        return templateName;
    }
}
