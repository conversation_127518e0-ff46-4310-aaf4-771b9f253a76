package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Mar 01, 2020
 *         Jirra ACC-1435
 */

public enum ContractPaymentTermReason implements LabelValueEnum {
    REPLACEMENT("Replacement"),
    SHORT_TO_LONG("Short To Long"),
    MANUAL("Manual"),
    AUTO_EXTENSION_WITH_VAT("Auto Extension With VAT"),
    REPLACEMENT_WITH_VAT("Replacement With VAT"),
    SWITCHING("Switching"),
    AMEND_PAYMENTS("Amend Payments"),
    SWITCHING_BANK_ACCOUNT("Switching Bank Account"),
    OEC_AMEND_DDS("Oec Amend DDs"),
    REPLACEMENT_DDC_OPEN("Replacement DDc Open");
    private final String label;

    ContractPaymentTermReason(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
