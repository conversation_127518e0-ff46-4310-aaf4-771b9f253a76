package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created at Nov 4, 2017
 */
public enum OfficeStaffType implements LabelValueEnum {
    QATAR_STAFF("Qatar Staff"),
    STORAGE_STAFF("Storage Staff"),
    FT_PHILIPPINES_STAFF("Ft Philippines Staff"),
    FT_OFFICE_STAFF("FT Office Staff"),
    PT_PHILIPPINES_STAFF("PT Philippines Staff"),
    PT_OFFICE_STAFF("Pt Office Staff"),
    //Jirra ACC-325
//SYR_LEBANON_STAFF,
    INTERNATIONAL_STAFF("International Staff"),
    UAE_NON_H_C("UAE Non H C"),
    //Jirra PAY-2
    DUBAI_STAFF_EMARATI("Dubai Staff Emarati"),
    DUBAI_STAFF_EXPAT("Dubai Staff Expat"),
    OVERSEAS_STAFF("Overseas Staff");

    private final String label;

    private OfficeStaffType(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}