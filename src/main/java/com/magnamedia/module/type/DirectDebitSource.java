package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Jan 15, 2020
 */
public enum DirectDebitSource implements LabelValueEnum {

    MANUAL_EXTENSION("Manual Extension"),
    PAYMENT_EXPIRY_AUTO_EXTENSION("Payment Expiry Auto Extension"),
    //SD-15410
    FILIPINA_SALARY_ADJUSTMENT("Filipina Salary Adjustment"),
    REACTIVATE_CONTRACT_API("Reactivate Contract API");

    private final String label;

    DirectDebitSource(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
