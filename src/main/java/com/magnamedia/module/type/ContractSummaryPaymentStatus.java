package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 * <AUTHOR>
 * MC-28
 * */
public enum ContractSummaryPaymentStatus implements LabelValueEnum {
    PENDING_BANK_APPROVAL("Pending Bank Approval"),
    PENDING_COLLECTION("Pending Collection"),
    REJECTED_PENDING_CLIENT_TO_SIGN("Rejected / Pending Client To Sign"),
    REJECTED_PENDING_BANK_RESPONSE( "Rejected / Pending Bank Response"),
    INCOMPLETE_MISSING_SIGNATURE( "Incomplete / Missing Signature"),
    INCOMPLETE_DOCUMENTS_REJECTED("Incomplete / Documents Rejected (@documents@)"),
    FUTURE_PAYMENT_NOT_CREATED_YET("Future Payment Not Created Yet"),
    BOUNCED("Bounced"),
    RECEIVED("Received"),
    NEW_CONTRACT_PAYMENT_DOESNT_EXIST("New Contract, Payment Doesn't Exist"),
    WAIVED("Waived"),
    PENDING_DATA_ENTRY("Pending Data Entry");


    private final String label;

    ContractSummaryPaymentStatus(String label) { this.label = label; }

    @Override
    public String getLabel() {
        return label;
    }
}
