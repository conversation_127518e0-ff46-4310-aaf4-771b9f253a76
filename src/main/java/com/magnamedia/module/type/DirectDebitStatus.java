package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Dec 9, 2018
 */
public enum DirectDebitStatus implements LabelValueEnum {
    PENDING("Pending"),
    CONFIRMED("Confirmed"),
    CANCELED("Canceled"),
    //Jirra ACC-1135
    PENDING_FOR_CANCELLATION("Pending for cancellation"),
    REJECTED("Rejected"),
    //Jirra ACC-1587
    IN_COMPLETE("In Complete"),
    PENDING_DATA_ENTRY("Pending Data Entry"),
    //ask osamah to be removed
    EXPIRED("Expired"),
    // ACC-5457
    NOT_APPLICABLE("Not Applicable");

    private final String label;

    private DirectDebitStatus(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
