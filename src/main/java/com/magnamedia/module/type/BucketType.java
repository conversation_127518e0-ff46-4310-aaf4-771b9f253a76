package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 *
 * <AUTHOR>
 */
public enum BucketType implements LabelValueEnum{
    
    BANK_ACCOUNT("Bank Account"),
    CASH_BOX("Cash Box"),
    MONEY_TRANSFER("Money Transfer"),
    CREDIT_CARD("Credit Card"),
    OTHER_BANK_ACCOUNTS("Other Bank Accounts");

    private final String label;

    BucketType(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
