package com.magnamedia.module.type;

import com.fasterxml.jackson.annotation.JsonValue;
import com.magnamedia.extra.LabelValueEnum;

/**
 * <AUTHOR>
 * MC-28
 * */
public enum ContractSummaryPaymentMethod implements LabelValueEnum {
    CREDIT_CARD("Credit Card"),
    CREDIT_CARD_FLOW("Credit Card (@FlowName@ Flow)"),
    DIRECT_DEBIT("Direct Debit"),
    CASH("Cash"),
    WIRE_TRANSFER( "Wire Transfer"),
    ADJUSTMENT("Adjustment");


    private final String label;

    ContractSummaryPaymentMethod(String label) { this.label = label; }

    @Override
    @JsonValue
    public String getLabel() {
        return label;
    }
}