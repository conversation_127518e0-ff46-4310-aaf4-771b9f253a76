package com.magnamedia.module.type;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Apr 27, 2019
 * Jirra ACC-580
 */
public enum MaidServiceType {
    ATM_CARD_RELOCATION,
    APPLY_FOR_NEW_EMIRATES_ID,
    RESEND_IMMIGRATION_CANCELLATION_PAPER,
    ARAMEX_PACKAGE_DELIVERED_TO_OLD_CLIENT,
    APPLY_FOR_OEC,
    FORGIVE_MAID_FROM_A_LOAN_AMOUNT,
    PICKUP_LUGGAGE, // SMM-2066
    EXCLUDE_CONTENT_FROM_ARAMEX_SHIPMENT, // VPM-2040
    ADD_ARAMEX_SHIPMENT,
    COVID_19_TESTS,
    RESET_RENEWAL_JOURNEY,
    ANSARI_CARD_REPLACEMENT, // SMM-3379
    DELETE_FINAL_SETTLEMENT, // SMM-4036
    TRIGGER_NON_RENEWAL_FINAL_SETTLEMENT,
    APPLY_FOR_UNPAID_LEAVE_APPLICATION,
    MODIFY_PERSONAL_INFORMATION,
    PAUSE_VISA_PROCESS, // VPM-4988,
    UPDATE_BANK_INFO, // SMM-7707
    GET_ANSARI_SALARY_STATEMENT, //SMM-7575
}