package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 * <AUTHOR>
 * created on 2025-03-01
 * ACC-8771
 */
public enum DirectDebitFileSubStatus implements LabelValueEnum {
    COLLECTED("Collected"),
    NOT_COLLECTED("Not Collected"),
    SENT_FOR_COLLECTION("Sent For Collection");

    private final String label;

    DirectDebitFileSubStatus(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}