package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Mar 25, 2020
 *         Jirra ACC-1435
 */
public enum NeedOfMaidDuration  implements LabelValueEnum {
	LESS_EQUALS_THREE_MONTHS("For 3 months or less"),
	MORE_THAN_THREE_MONTHS("For more than 3 months");

	private final String label;
	
	private NeedOfMaidDuration(String label) {
	  this.label = label;
	}
        
	@Override
	public String getLabel() {
		return label;
	}
}
