package com.magnamedia.module;

import com.magnamedia.core.Setup;
import com.magnamedia.extra.MvHousemaidNotificationTemplateCode;
import com.magnamedia.service.AccountingHousemaidTemplateService;

import java.util.HashMap;
import java.util.Map;

public class MvHousemaidNotificationTemplates {

   private static AccountingHousemaidTemplateService getAccountingTemplateService() {
      return Setup.getApplicationContext()
              .getBean(AccountingHousemaidTemplateService.class);
   }

    public static void createTemplates(){

       createForAcc8384();

       createForAcc8796();
    }

   private static void createForAcc8384() {

      Map<String, Object> m = new HashMap<>();
      m.put("contractType", "maidvisa");
      m.put("translation", new HashMap<String, String>() {{
         put("am", "ሰላም ከ maids.cc ቪዛ አገልግሎቶች። ቪዛዎ እስካሁን ስላላለቀ፡ ደሞዝዎን በአል አንሳሪ በኩል ማስገባት አልቻልንም። ለአሰሪዎ ተመላሽ ተደርጓል እና ክፍያዎን በተመለከተ እነሱን ማናገር ይችላሉ።");
         put("az", "Akkam jirta? Tajaajila Viizaa maids.cc irraa. Viizaan kee hanga ammaa waan hin qophoofneef, " +
                 "miindaa kee karaa Al Ansaariitiin siif erguu hin dandeenye. Gara hojjachiisaa keetii deebisnee " +
                 "waan jirruuf, kaffaltii kee ilaalchisee isaan dubbisuu ni dandeessa.");
      }});

      getAccountingTemplateService().createNewModelTemplate(
              MvHousemaidNotificationTemplateCode.MV_HOUSEMAID_REFUND_SALARY_AMOUNT_E_VISA_NOT_ISSUED_OR_ISSUED_IN_THE_PREVIOUS_MONTH_NOTIFICATION.toString(),
              "Hello from maids.cc Visa Services. Since your visa is not issued yet, " +
                      "we are unable to process your salary through Al Ansari. " +
                      "Your client has been refunded and you can reach out to them regarding your payment.",
              m);
   }

   private static void createForAcc8796() {
      Map<String, Object> m = new HashMap<>();
      m.put("contractType", "maidvisa");

      // First failed medical check template.
      getAccountingTemplateService().createNewModelTemplate(
              MvHousemaidNotificationTemplateCode.MV_HOUSEMAID_FIRST_FAILED_MEDICAL_CHECK_SALARY.toString(),
              "Since your visa is not issued yet, we are unable to process your salary through Al Ansari. " +
                      "You can reach out to your client regarding your @month@ payment", m);

      // Second failed medical check template.
      getAccountingTemplateService().createNewModelTemplate(
              MvHousemaidNotificationTemplateCode.MV_HOUSEMAID_SECOND_FAILED_MEDICAL_CHECK_SALARY.toString(),
              "Since your visa is not issued yet, we are unable to process your salary through Al Ansari. " +
                      "You can reach out to your client regarding your @month_minus_one@ payment", m);

      // Second failed medical check template.
      getAccountingTemplateService().createNewModelTemplate(
              MvHousemaidNotificationTemplateCode.MV_HOUSEMAID_FAILED_MEDICAL_NEXT_PAYMENT_RECEIVED.toString(),
              "Since your visa is not issued yet, we are unable to process your salary through Al Ansari. " +
                      "You can reach out to your client regarding your @month@ payment.", m);

   }
}