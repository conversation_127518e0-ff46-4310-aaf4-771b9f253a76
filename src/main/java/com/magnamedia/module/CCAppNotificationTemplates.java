package com.magnamedia.module;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.Setup;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.entity.template.ChannelSpecificSetting;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.repository.TemplateRepository;
import com.magnamedia.core.type.NotificationLocation;
import com.magnamedia.core.type.template.ChannelSpecificSettingType;
import com.magnamedia.extra.CcNotificationTemplateCode;
import com.magnamedia.extra.CcSmsTemplateCode;
import com.magnamedia.extra.DirectDebitGenerationPlanTemplate;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.service.AccountingTemplateService;
import org.joda.time.DateTime;
import com.magnamedia.service.AccountingTemplateService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public class CCAppNotificationTemplates {

    private static AccountingTemplateService getAccountingTemplateService() {
        return Setup.getApplicationContext()
                .getBean(AccountingTemplateService.class);
    }

    public static void createCCAppNotifications() {

        createDirectDebitGenerationPlanInsurance();
        createDirectDebitGenerationPlanSDR();
        createDirectDebitGenerationPlanOtherTypeDD();

        createPaytabsThankYouMessage();

        ccPaymentExpiry411Notification();
        ccPaymentExpiry412Notification();

        ccAccountingPayViaCreditCardNotification();

        ccDdPendingInfoNotification();

        createClientRefundBankTransferDetails();

        ccAccountingNotOwedMoneyFromClient812Notification();
        ccAccountingWronglyChargedMoneyOnClient813Notification();
        ccAccountingOweMoneyToClient811Notification();

        maidsCcCancellation();

        //ACC-4905
        createPaymentReminderThanksMessage();
        createSigningOfferPaymentReceivedThanksMessage();

//        createCardPaymentsTemplates();

        clientPayingViaCreditCardReplacementSuccessWithPayLink();
        clientPayingViaCreditCardReplacementSuccess();

        // ACC-5663
        refundRejected();
        conditionalRefundRequested();
        nonConditionalRefundRequested();

        // ACC-7611
        refundRejectedCreditCard();
        conditionalRefundRequestedCreditCard();
        nonConditionalRefundRequestedCreditCard();

        createForACC6587();

        // ACC-9011
        informCancelledClientsAboutPaymentDeduction();
    }

    private static void createDirectDebitGenerationPlanInsurance() {
        Template template = new Template();
        template.setName(DirectDebitGenerationPlanTemplate.INSURANCE.toString());
        template.setDescription("In case of a postpone direct debit generate with type insurance, the message will be sent to the client before five days of send date");
        template.setText("Dear @Client_Name@ , Thank you for using our services. It’s time to renew your maid’s insurance." +
                " Your maid’s insurance will be automatically renewed and you’ll be charged AED @insurance_DD_amount@ on @payment_date@." +
                " Please ensure sufficient funds are available in your account to avoid penalties and service interruption. Thank you.");
        template.setNotificationLocation(NotificationLocation.HOME);
        template.setSendSMSIfNotReceived(true);
        template.setNotificationHoursBeforeSendSms(2);
        template.setNotificationSmsTemplateName(DirectDebitGenerationPlanTemplate.INSURANCE.toString());
        template.setNotificationCanClosedByUser(true);
        TemplateUtil.createTemplate(template, new HashMap<>());
    }

    private static void createDirectDebitGenerationPlanSDR() {
        Template template = new Template();
        template.setName(DirectDebitGenerationPlanTemplate.SAME_DAY_RECRUITMENT_FEE.toString());
        template.setDescription("In case of a postpone direct debit generate with type Same day recruitment fee, the message will be sent to the client before five days of send date");
        template.setText("Dear @Client_Name@ , Thank you for using our services. Please note that we’re going to deduct AED @SDR_DD_Amount@" +
                " from your account on @SDR_Payment_date@. This charge is for the 2 year visa fee. Please ensure sufficient funds are available " +
                "in your account to avoid penalties and service interruption. Thank you.");
        template.setNotificationLocation(NotificationLocation.HOME);
        template.setSendSMSIfNotReceived(true);
        template.setNotificationHoursBeforeSendSms(2);
        template.setNotificationSmsTemplateName(DirectDebitGenerationPlanTemplate.SAME_DAY_RECRUITMENT_FEE.toString());
        template.setNotificationCanClosedByUser(true);
        TemplateUtil.createTemplate(template, new HashMap<>());
    }

    private static void createDirectDebitGenerationPlanOtherTypeDD() {
        Template template = new Template();
        template.setName(DirectDebitGenerationPlanTemplate.OTHER_DD_TYPE.toString());
        template.setDescription("In case of a postpone direct debit generate with other types, the message will be sent to the client before five days of send date");
        template.setText("Dear @Client_Name@ , Thank you for using our services. Please note that we’re going to deduct" +
                " AED @payment_amount@ from your account on @payment_date@ for @DD_TYPE@ fee . Please ensure sufficient" +
                " funds are available in your account to avoid penalties and service interruption. Thank you.");
        template.setNotificationLocation(NotificationLocation.HOME);
        template.setSendSMSIfNotReceived(true);
        template.setNotificationHoursBeforeSendSms(2);
        template.setNotificationSmsTemplateName(DirectDebitGenerationPlanTemplate.OTHER_DD_TYPE.toString());
        template.setNotificationCanClosedByUser(true);
        TemplateUtil.createTemplate(template, new HashMap<>());
    }

    // ACC-4591
//    private static void createCardPaymentsTemplates() {
//        Template template = new Template();
//        template.setName(CcNotificationTemplateCode.CC_ACCOUNTING_PAY_ACCOMMODATION_FEE_NOTIFICATION.toString());
//        template.setDescription("Send Notification to client for pay Accommodation Fee");
//        template.setText("Dear @client_name@, Thank you for using maids.cc. " +
//                "To pay your maid’s accommodation fee of AED @amount@ by Credit card, " +
//                "please click on button below:");
//        template.setNotificationLocation(NotificationLocation.HOME);
//        template.setSendSMSIfNotReceived(true);
//        template.setNotificationHoursBeforeSendSms(2);
//        template.setNotificationSmsTemplateName(CcSmsTemplateCode.CC_ACCOUNTING_PAY_ACCOMMODATION_FEE_SMS.toString());
//        template.setNotificationCanClosedByUser(true);
//        TemplateUtil.createTemplate(template, new HashMap<>());
//
//        template = new Template();
//        template.setName(CcNotificationTemplateCode.CC_ACCOUNTING_PAY_CC_TO_MV_NOTIFICATION.toString());
//        template.setDescription("Send Notification to client for pay CC to MV");
//        template.setText("Dear @client_name@ , Thank you for using maids.cc." +
//                " To hire your maid under the new payment plan and to pay the transfer fee of AED @amount@ by Credit card," +
//                " please click on button below:");
//        template.setNotificationLocation(NotificationLocation.HOME);
//        template.setSendSMSIfNotReceived(true);
//        template.setNotificationHoursBeforeSendSms(2);
//        template.setNotificationSmsTemplateName(CcSmsTemplateCode.CC_ACCOUNTING_PAY_CC_TO_MV_SMS.toString());
//        template.setNotificationCanClosedByUser(true);
//        TemplateUtil.createTemplate(template, new HashMap<>());
//
//        template = new Template();
//        template.setName(CcNotificationTemplateCode.CC_ACCOUNTING_PAY_MONTHLY_PAYMENT_NOTIFICATION.toString());
//        template.setDescription("Send Notification to client for pay Monthly Payment");
//        template.setText("Dear @client_name@ , Thank you for using maids.cc." +
//                " To pay your monthly payment of AED @amount@ by Credit card," +
//                " please click on button below:");
//        template.setNotificationLocation(NotificationLocation.HOME);
//        template.setSendSMSIfNotReceived(true);
//        template.setNotificationHoursBeforeSendSms(2);
//        template.setNotificationSmsTemplateName(CcSmsTemplateCode.CC_ACCOUNTING_PAY_MONTHLY_PAYMENT_SMS.toString());
//        template.setNotificationCanClosedByUser(true);
//        TemplateUtil.createTemplate(template, new HashMap<>());
//
//        template = new Template();
//        template.setName(CcNotificationTemplateCode.CC_ACCOUNTING_PAY_OVERSTAY_FEES_NOTIFICATION.toString());
//        template.setDescription("Send Notification to client for pay Overstay fees");
//        template.setText("Dear @client_name@ , Thank you for using maids.cc." +
//                " To pay your maid's overstay fines of AED @amount@ by Credit card," +
//                " please click on button below:");
//        template.setNotificationLocation(NotificationLocation.HOME);
//        template.setSendSMSIfNotReceived(true);
//        template.setNotificationHoursBeforeSendSms(2);
//        template.setNotificationSmsTemplateName(CcSmsTemplateCode.CC_ACCOUNTING_PAY_OVERSTAY_FEES_SMS.toString());
//        template.setNotificationCanClosedByUser(true);
//        TemplateUtil.createTemplate(template, new HashMap<>());
//
//        template = new Template();
//        template.setName(CcNotificationTemplateCode.CC_ACCOUNTING_PAY_PCR_TEST_NOTIFICATION.toString());
//        template.setDescription("Send Notification to client for pay Overstay fees");
//        template.setText("Dear @client_name@ , Thank you for using maids.cc." +
//                " To pay your maid's PCR fees of AED @amount@ by Credit card," +
//                " please click on button below:");
//        template.setNotificationLocation(NotificationLocation.HOME);
//        template.setSendSMSIfNotReceived(true);
//        template.setNotificationHoursBeforeSendSms(2);
//        template.setNotificationSmsTemplateName(CcSmsTemplateCode.CC_ACCOUNTING_PAY_PCR_TEST_SMS.toString());
//        template.setNotificationCanClosedByUser(true);
//        TemplateUtil.createTemplate(template, new HashMap<>());
//
//        template = new Template();
//        template.setName(CcNotificationTemplateCode.CC_ACCOUNTING_PAY_URGENT_VISA_CHARGES_NOTIFICATION.toString());
//        template.setDescription("Send Notification to client for pay Urgent Visa Charges");
//        template.setText("Dear @client_name@ , Thank you for using maids.cc." +
//                " To pay your maid's urgent visa fees of AED @amount@ by Credit card," +
//                " please click on button below:");
//        template.setNotificationLocation(NotificationLocation.HOME);
//        template.setSendSMSIfNotReceived(true);
//        template.setNotificationHoursBeforeSendSms(2);
//        template.setNotificationSmsTemplateName(CcSmsTemplateCode.CC_ACCOUNTING_PAY_URGENT_VISA_CHARGES_SMS.toString());
//        template.setNotificationCanClosedByUser(true);
//        TemplateUtil.createTemplate(template, new HashMap<>());
//
//        template = new Template();
//        template.setName(CcNotificationTemplateCode.CC_ACCOUNTING_PAY_INSURANCE_NOTIFICATION.toString());
//        template.setDescription("Send Notification to client for pay Urgent Insurance");
//        template.setText("Dear @client_name@ , Thank you for using maids.cc." +
//                " To pay your maid's insurance fees of AED @amount@ by Credit card," +
//                " please click on button below:");
//        template.setNotificationLocation(NotificationLocation.HOME);
//        template.setSendSMSIfNotReceived(true);
//        template.setNotificationHoursBeforeSendSms(2);
//        template.setNotificationSmsTemplateName(CcSmsTemplateCode.CC_ACCOUNTING_PAY_INSURANCE_SMS.toString());
//        template.setNotificationCanClosedByUser(true);
//        TemplateUtil.createTemplate(template, new HashMap<>());
//
//        template = new Template();
//        template.setName(CcNotificationTemplateCode.CC_ACCOUNTING_PAY_OTHER_PAYMENTS_TYPES_NOTIFICATION.toString());
//        template.setDescription("Send Notification to client for pay payments types");
//        template.setText("Dear @client_name@ , Thank you for using maids.cc." +
//                " To pay AED @amount@ by Credit card," +
//                " please click on button below:");
//        template.setNotificationLocation(NotificationLocation.HOME);
//        template.setSendSMSIfNotReceived(true);
//        template.setNotificationHoursBeforeSendSms(2);
//        template.setNotificationSmsTemplateName(CcSmsTemplateCode.CC_ACCOUNTING_PAY_OTHER_PAYMENTS_TYPES_SMS.toString());
//        template.setNotificationCanClosedByUser(true);
//        TemplateUtil.createTemplate(template, new HashMap<>());
//    }

    private static void createPaytabsThankYouMessage() {
        Template templateCc = new Template();
        templateCc.setName(CcNotificationTemplateCode.CC_PAYTAB_THANKS_MESSAGE_NOTIFICATION.toString());
        templateCc.setDescription("Thank you message when a client pays online second cash payment");
        templateCc.setText("Thank you for settling your payment for your maid's service. We just want to remind you " +
            "that we can't accept Credit or Debit Card payments anymore.\n" +
            "Once you have your Emirates ID and IBAN ready, would you be so kind as to complete your Monthly Bank " +
            "Payment Form by clicking on “Sign Now” below.");
        templateCc.setNotificationLocation(NotificationLocation.HOME);
        templateCc.setSendSMSIfNotReceived(false);
        templateCc.setNotificationHoursBeforeSendSms(0);
        templateCc.setNotificationAlwaysSendSms(false);
        templateCc.setNotificationSmsTemplateName(CcSmsTemplateCode.CC_PAYTAB_THANKS_MESSAGE_SMS.toString());
        templateCc.setNotificationCanClosedByUser(true);
        TemplateUtil.createTemplate(templateCc, new HashMap<>());
    }

    private static void ccPaymentExpiry411Notification() {
        Template template = new Template();
        template.setName(CcNotificationTemplateCode.CC_PAYMENT_EXPIRY_4_1_1_NOTIFICATION.toString());
        template.setDescription("In case of an unused signature, the message will be sent to the client");
        template.setText("The Bank Payment Form that you’ve previously signed expires soon. To avoid interruptions " +
            "in your service, we'll amend and resubmit your Bank Payment Forms to the bank. Please remember, you can " +
            "stop the service and cancel your Banka Payment Forms at anytime by returning the maid to our center. If " +
            "you have any questions, please WhatsApp us @chat_with_us@ .");
        template.setNotificationLocation(NotificationLocation.HOME);
        template.setSendSMSIfNotReceived(true);
        template.setNotificationHoursBeforeSendSms(2);
        template.setNotificationAlwaysSendSms(false);
        template.setNotificationSmsTemplateName(CcSmsTemplateCode.CC_PAYMENT_EXPIRY_4_1_1_SMS.toString());
        template.setNotificationCanClosedByUser(true);
        TemplateUtil.createTemplate(template, new HashMap<>());
    }

    private static void ccPaymentExpiry412Notification() {
        Template template = new Template();
        template.setName(CcNotificationTemplateCode.CC_PAYMENT_EXPIRY_4_1_2_NOTIFICATION.toString());
        template.setText("The Bank Payment Form that you signed expires on @ExpiryDate@. To continue your service, " +
            "please @link_send_dd_details_click_here@ to sign the new Bank Payment Form. Your monthly payments will remain AED " +
            "@monthly_fee_of_nationality@ per month.");
        template.setNotificationLocation(NotificationLocation.HOME);
        template.setSendSMSIfNotReceived(true);
        template.setNotificationAlwaysSendSms(false);
        template.setNotificationHoursBeforeSendSms(2);
        template.setNotificationSmsTemplateName(CcSmsTemplateCode.CC_PAYMENT_EXPIRY_4_1_2_SMS.toString());
        template.setNotificationCanClosedByUser(false);
        TemplateUtil.createTemplate(template, new HashMap<>());
    }

    private static void ccAccountingPayViaCreditCardNotification() {
        Template template = new Template();
        template.setName(CcNotificationTemplateCode.CC_ACCOUNTING_PAY_VIA_CREDIT_CARD_NOTIFICATION.toString());
        template.setDescription("Notification contains the Credit Card Paying link");
        template.setText("ACTION REQUIRED:\nTo pay your overdue payment of AED @amount@ via credit card, please @paytab_link_click_here@ .");
        template.setNotificationLocation(NotificationLocation.HOME);
        template.setNotificationHoursBeforeSendSms(2);
        template.setNotificationAlwaysSendSms(false);
        template.setSendSMSIfNotReceived(true);
        template.setNotificationSmsTemplateName(CcSmsTemplateCode.CC_ACCOUNTING_PAY_VIA_CREDIT_CARD_SMS.toString());
        template.setNotificationCanClosedByUser(false);
        TemplateUtil.createTemplate(template, new HashMap<>());
    }

    private static void ccDdPendingInfoNotification() {
        Template template = new Template();
        template.setName(CcNotificationTemplateCode.CC_DD_PENDING_INFO_NOTIFICATION.toString());
        template.setText("ACTION REQUIRED: Please @link_send_dd_details_click_here@ , and complete your Bank Payment Form application.");
        template.setNotificationLocation(NotificationLocation.HOME);
        template.setNotificationHoursBeforeSendSms(2);
        template.setNotificationAlwaysSendSms(false);
        template.setSendSMSIfNotReceived(true);
        template.setNotificationSmsTemplateName(CcSmsTemplateCode.CC_DD_PENDING_INFO_SMS.toString());
        template.setNotificationCanClosedByUser(false);
        TemplateUtil.createTemplate(template, new HashMap<>());
    }

    private static void createClientRefundBankTransferDetails() {
        Template template = new Template();
        template.setName(CcNotificationTemplateCode.CC_CLIENT_REFUND_BANK_TRANSFER_DETAILS_NOTIFICATION.toString());
        template.setDescription("Sent when Client's Bank Transfer is completed");
        template.setText("We just sent you AED @amount@ by bank transfer. Please expect to receive the amount to your bank account within 7 business days." +
            " @proof_of_transfer_link_Click_here@ to view the proof of transfer. Thank you.");
        template.setNotificationLocation(NotificationLocation.HOME);
        template.setSendSMSIfNotReceived(false);
        template.setNotificationHoursBeforeSendSms(null);
        template.setNotificationSmsTemplateName(CcSmsTemplateCode.CC_CLIENT_REFUND_BANK_TRANSFER_DETAILS_SMS.toString());
        template.setNotificationCanClosedByUser(true);
        TemplateUtil.createTemplate(template, new HashMap<>());
    }

    private static void ccAccountingNotOwedMoneyFromClient812Notification() {
        Template template = new Template();
        template.setName(CcNotificationTemplateCode.CC_ACCOUNTING_NOT_OWED_MONEY_FROM_CLIENT_8_1_2_NOTIFICATION.toString());
        template.setDescription("In case of an unused signature, the message will be sent to the client");
        template.setText("Unfortunately, the bank didn't process the cancellation of your future payments on time. " +
            "You were charged AED @amount@. Don't worry; we’ll send you that amount within the next 7 business days.");
        template.setNotificationLocation(NotificationLocation.HOME);
        template.setNotificationAlwaysSendSms(false);
        template.setNotificationSmsTemplateName(CcSmsTemplateCode.CC_ACCOUNTING_NOT_OWED_MONEY_FROM_CLIENT_8_1_2_SMS.toString());
        template.setNotificationCanClosedByUser(true);
        TemplateUtil.createTemplate(template, new HashMap<>());
    }

    private static void ccAccountingWronglyChargedMoneyOnClient813Notification() {
        Template template = new Template();
        template.setName(CcNotificationTemplateCode.CC_ACCOUNTING_WRONGLY_CHARGED_MONEY_ON_CLIENT_8_1_3_NOTIFICATION.toString());
        template.setDescription("send to the Client If we receive an amount from the client when he doesn't owe us any amount");
        template.setNotificationLocation(NotificationLocation.HOME);
        template.setText("You were charged AED @amount@ today. But don’t worry, we’ll transfer you the same amount by @scheduled_termination_date@.");
        template.setNotificationAlwaysSendSms(false);
        template.setNotificationSmsTemplateName(CcSmsTemplateCode.CC_ACCOUNTING_WRONGLY_CHARGED_MONEY_ON_CLIENT_8_1_3_SMS.toString());
        template.setNotificationCanClosedByUser(true);
        TemplateUtil.createTemplate(template, new HashMap<>());
    }

    private static void ccAccountingOweMoneyToClient811Notification() {
        Template template = new Template();
        template.setName(CcNotificationTemplateCode.CC_ACCOUNTING_OWE_MONEY_TO_CLIENT_8_1_1_NOTIFICATION.toString());
        template.setDescription("send to the Client if we owe him money, and we need to transfer him the amount");
        template.setNotificationLocation(NotificationLocation.HOME);
        template.setText("We still owe you AED @remaining_balance@. We’ll send you the amount within the next 7 business days.");
        template.setNotificationAlwaysSendSms(false);
        template.setNotificationSmsTemplateName(CcSmsTemplateCode.CC_ACCOUNTING_OWE_MONEY_TO_CLIENT_8_1_1_SMS.toString());
        template.setNotificationCanClosedByUser(false);
        TemplateUtil.createTemplate(template, new HashMap<>());
    }

    // ACC-3843 ACC-5235
    private static void maidsCcCancellation() {

        {
            Template template = new Template();
            template.setName(CcNotificationTemplateCode.CC_PAYMENT_NOT_RECEIVED_OR_BOUNCED_8_1_5_NOTIFICATION.toString());
            template.setDescription("If (the client returned the maid before the end of the month and then he requested " +
                    "to cancel after the 1st of next month. Right after cancelation) AND (payment is not received or bounced)");

            template.setText("We’re sorry you’re disappointed, we won’t charge you for this month. Please tell us " +
                    "if there’s anything more we can do.");
            template.setNotificationLocation(NotificationLocation.HOME);
            template.setSendSMSIfNotReceived(false);
            template.setNotificationSmsTemplateName(CcSmsTemplateCode.CC_PAYMENT_NOT_RECEIVED_OR_BOUNCED_8_1_5_SMS.toString());
            template.setNotificationCanClosedByUser(true);
            TemplateUtil.createTemplate(template, new HashMap<>());
        }
        {
            Template template = new Template();
            template.setName(CcNotificationTemplateCode.CC_PAYMENT_RECEIVED_8_1_5_NOTIFICATION.toString());
            template.setDescription("If (the client returned the maid before the end of the month and then he " +
                    "requested to cancel after the 1st of next month. Right after cancelation) AND (payment is received) " +
                    "AND (we don’t need money from the client)");

            template.setText("We’re sorry you’re disappointed, we won’t charge you for this month. We'll refund the " +
                    "full amount to your bank account within 5 business days. Please tell us if there’s anything more we " +
                    "can do.");
            template.setNotificationLocation(NotificationLocation.HOME);
            template.setSendSMSIfNotReceived(false);
            template.setNotificationSmsTemplateName(CcSmsTemplateCode.CC_PAYMENT_RECEIVED_8_1_5_SMS.toString());
            template.setNotificationCanClosedByUser(true);
            TemplateUtil.createTemplate(template, new HashMap<>());
        }
        {
            Template template = new Template();
            template.setName(CcNotificationTemplateCode.CC_PAYMENT_UNDER_PROCESSING_8_1_5_NOTIFICATION.toString());
            template.setDescription("If (the client returned the maid before the end of the month and then he " +
                    "requested to cancel after the 1st of next month. Right after cancelation) AND (payment is under " +
                    "processing) AND (we don’t need money from the client)");

            template.setText("We’re sorry you’re disappointed, we won’t charge you for this month. Unfortunately, " +
                    "your payment is under process, if you are charged any amount, we'll refund the full amount to your " +
                    "bank account within 5 business days. Please tell us if there’s anything more we can do.");
            template.setNotificationLocation(NotificationLocation.HOME);
            template.setSendSMSIfNotReceived(false);
            template.setNotificationSmsTemplateName(CcSmsTemplateCode.CC_PAYMENT_UNDER_PROCESSING_8_1_5_SMS.toString());
            template.setNotificationCanClosedByUser(true);
            TemplateUtil.createTemplate(template, new HashMap<>());
        }

        {
            Template template = new Template();
            template.setName(CcNotificationTemplateCode.CC_PAYMENT_NOT_RECEIVED_OR_BOUNCED_8_1_6_NOTIFICATION.toString());
            template.setDescription("If (the client cancels within the first 3 days of the month) AND (payment is " +
                    "not received or bounced)");

            template.setText("We’re sorry you’re disappointed, we won’t charge you for the days you hired the maid " +
                    "this month. Please tell us if there’s anything more we can do.");
            template.setNotificationLocation(NotificationLocation.HOME);
            template.setSendSMSIfNotReceived(false);
            template.setNotificationSmsTemplateName(CcSmsTemplateCode.CC_PAYMENT_NOT_RECEIVED_OR_BOUNCED_8_1_6_SMS.toString());
            template.setNotificationCanClosedByUser(true);
            TemplateUtil.createTemplate(template, new HashMap<>());
        }
        {
            Template template = new Template();
            template.setName(CcNotificationTemplateCode.CC_PAYMENT_RECEIVED_8_1_6_NOTIFICATION.toString());
            template.setDescription("If (the client cancels within the first 3 days of the month) AND (payment is " +
                    "received) AND (we don’t need money from the client)");

            template.setText("We’re sorry you’re disappointed, we won’t charge you for the days you hired the maid " +
                    "this month. We'll refund the full amount to your bank account within 5 business days. Please tell us" +
                    " if there’s anything more we can do.");
            template.setNotificationLocation(NotificationLocation.HOME);
            template.setSendSMSIfNotReceived(false);
            template.setNotificationSmsTemplateName(CcSmsTemplateCode.CC_PAYMENT_RECEIVED_8_1_6_SMS.toString());
            template.setNotificationCanClosedByUser(true);
            TemplateUtil.createTemplate(template, new HashMap<>());
        }
        {
            Template template = new Template();
            template.setName(CcNotificationTemplateCode.CC_PAYMENT_UNDER_PROCESSING_8_1_6_NOTIFICATION.toString());
            template.setDescription("If (the client cancels within the first 3 days of the month) AND (payment is " +
                    "under processing) AND (we don’t need money from the client)");

            template.setText("We’re sorry you’re disappointed, we won’t charge you for the days you hired the maid " +
                    "this month. Unfortunately, your payment is under process, if you are charged any amount, we'll " +
                    "refund the full amount to your bank account within 5 business days. Please tell us if there’s " +
                    "anything more we can do.");
            template.setNotificationLocation(NotificationLocation.HOME);
            template.setSendSMSIfNotReceived(false);
            template.setNotificationSmsTemplateName(CcSmsTemplateCode.CC_PAYMENT_UNDER_PROCESSING_8_1_6_SMS.toString());
            template.setNotificationCanClosedByUser(true);
            TemplateUtil.createTemplate(template, new HashMap<>());
        }
    }

    //ACC-4905
    private static void createPaymentReminderThanksMessage() {
        Template template = new Template();
        template.setName(CcNotificationTemplateCode.CC_PAYMENT_REMINDER_THANKS_MESSAGE_NOTIFICATION.toString());
        template.setDescription("On receiving a card payment to extend for one more month");
        template.setText("We've received your payment of AED @amount@. Thank you for using maids.cc");
        template.setNotificationLocation(NotificationLocation.HOME);
        template.setSendSMSIfNotReceived(true);
        template.setNotificationHoursBeforeSendSms(2);
        template.setNotificationAlwaysSendSms(false);
        template.setNotificationSmsTemplateName(CcSmsTemplateCode.CC_PAYMENT_REMINDER_THANKS_MESSAGE_SMS.toString());
        template.setNotificationCanClosedByUser(true);
        TemplateUtil.createTemplate(template, new HashMap<>());
    }

    //ACC-4905
    private static void createSigningOfferPaymentReceivedThanksMessage() {
        Template template = new Template();
        template.setName(CcNotificationTemplateCode.CC_SIGNING_OFFER_PAYMENT_RECEIVED_THANKS_MESSAGE_NOTIFICATION.toString());
        template.setDescription("On receiving a card payment to extend for one more month");
        template.setText("We've received your payment for this month by card. Please note that we're unable to " +
            "accept credit card payments in future.\nPlease whenever your Emirates ID and IBAN are ready, would you be so kind as to complete your Monthly " +
            "Bank Payment Form by clicking on \"Sign Now\" below.");
        template.setNotificationLocation(NotificationLocation.HOME);
        template.setSendSMSIfNotReceived(true);
        template.setNotificationHoursBeforeSendSms(2);
        template.setNotificationAlwaysSendSms(false);
        template.setNotificationSmsTemplateName(CcSmsTemplateCode.CC_SIGNING_OFFER_PAYMENT_RECEIVED_THANKS_MESSAGE_SMS.toString());
        template.setNotificationCanClosedByUser(false);
        TemplateUtil.createTemplate(template, new HashMap<>());
    }

    private static void clientPayingViaCreditCardReplacementSuccessWithPayLink() {
        createNewModelTemplate(CcNotificationTemplateCode.CC_CLIENT_PAYING_VIA_CREDIT_CARD_REPLACEMENT_SUCCESS_WITH_PAY_LINK_NOTIFICATION.toString(),
                "Since you hired @new_nationality@ maid, your monthly payment has been updated to AED @amount@. " +
                        "Your payment covers your maid's service until @paid_end_date@. To extend the service for 1 more month, " +
                        "please settle the payment today by clicking on the following link. @link@",
                "Since you hired @new_nationality@ maid, your monthly payment has been updated to AED @amount@. " +
                        "Your payment covers your maid's service until @paid_end_date@. To extend the service for 1 more month, " +
                        "please settle the payment today by clicking on the following link. @paying_via_credit_card_sms@",
                PicklistHelper.getItem("template_message_delay", "no_sms"));
    }

    private static void clientPayingViaCreditCardReplacementSuccess() {
        createNewModelTemplate(CcNotificationTemplateCode.CC_CLIENT_PAYING_VIA_CREDIT_CARD_REPLACEMENT_SUCCESS_NOTIFICATION.toString(),
                "Since you hired @new_nationality@ maid, your monthly payment has been updated to AED @amount@. " +
                        "Your payment covers your maid's service until @paid_end_date@",
                "Since you hired @new_nationality@ maid, your monthly payment has been updated to AED @amount@. " +
                        "Your payment covers your maid's service until @paid_end_date@" +
                        "Thank you for your understanding.",
                PicklistHelper.getItem("template_message_delay", "no_sms"));
    }

    public static void createNewModelTemplate(
            String name, String notificationText, String smsText, PicklistItem p) {

        Template existingTemplate = Setup.getRepository(TemplateRepository.class).findByNameIgnoreCase(name);
        if (existingTemplate != null) return;

        ChannelSpecificSetting nChannel = new ChannelSpecificSetting();
        nChannel.setType(ChannelSpecificSettingType.Notification);
        nChannel.setActive(true);
        nChannel.setText(notificationText);
        nChannel.setNotificationCanClosedByUser(true);
        nChannel.setControls(new ArrayList<>());
        nChannel.setShowOnHomePage(true);
        nChannel.setOnFailAction(ChannelSpecificSettingType.SMS);
        nChannel.setMessageDelayType(p);

        Template template = TemplateUtil.updateTemplate(
                new Template.TemplateBuilder()
                        .Template(name,"", "")
                        .showInReceiverLog(true)
                        .unicode(true)
                        .smsChannelSetting(smsText, new HashMap<>())
                        .channelSetting(nChannel)
                        .notificationLocation(NotificationLocation.HOME)
                        .target(PicklistHelper.getItem("template_target", "Clients"))
                        .method("Push Notification")
                        .newModel()
                        .build());

        TemplateUtil.createTemplate(template);
    }

    private static void refundRejected() {
        ChannelSpecificSetting nChannel = new ChannelSpecificSetting();
        nChannel.setType(ChannelSpecificSettingType.Notification);
        nChannel.setActive(true);
        nChannel.setText("We’re sorry but after carefully reviewing your case, " +
                "we’ve declined the refund request. If you think this is a mistake, please @whatsApp_us@.");
        nChannel.setNotificationCanClosedByUser(true);
        nChannel.setControls(new ArrayList<>());
        nChannel.setMessageDelayType(Setup.getItem("template_message_delay", "x_hours_after_notification"));
        nChannel.setMessageDelayMinutes(2 * 60);
        nChannel.setShowOnHomePage(true);
        nChannel.setOnFailAction(ChannelSpecificSettingType.SMS);

        new Template.TemplateBuilder()
                .Template(CcNotificationTemplateCode.CC_ACCOUNTING_REFUND_REJECTED_NOTIFICATION.toString(),"", "")
                .showInReceiverLog(true)
                .unicode(true)
                .smsChannelSetting("We’re sorry but after carefully reviewing your case, we’ve declined the refund request. " +
                                "If you think this is a mistake, please WhatsApp us @whatsApp_us_sms@", new HashMap<>())
                .channelSetting(nChannel)
                .notificationLocation(NotificationLocation.HOME)
                .priority("priority_2")
                .target(PicklistHelper.getItem("template_target", "Clients"))
                .method("Push Notification")
                .newModel()
                .build();
    }

    private static void conditionalRefundRequested() {

        ChannelSpecificSetting nChannel = new ChannelSpecificSetting();
        nChannel.setType(ChannelSpecificSettingType.Notification);
        nChannel.setActive(true);
        nChannel.setText("You're eligible for a refund of AED @amount@. After we receive your @payments_amount@ , " +
                            "we’ll refund the amount to your bank account within 2 business days.");
        nChannel.setNotificationCanClosedByUser(true);
        nChannel.setControls(new ArrayList<>());
        nChannel.setMessageDelayType(Setup.getItem("template_message_delay", "specific_time"));
        nChannel.setMessageDelaySpecificDate(Setup.getItem("template_message_delay_specific_date", "@nextMorning@"));
        nChannel.setMessageDelaySpecificTime(new DateTime().withTime(10, 0 ,0, 0).toDate());
        nChannel.setShowOnHomePage(true);
        nChannel.setOnFailAction(ChannelSpecificSettingType.SMS);

        new Template.TemplateBuilder()
                .Template(CcNotificationTemplateCode.CC_ACCOUNTING_CONDITIONAL_REFUND_REQUESTED_NOTIFICATION.toString(),"", "")
                .showInReceiverLog(true)
                .unicode(true)
                .smsChannelSetting(
                        "You're eligible for a refund of AED @amount@. After we receive your @payments_amount@ , " +
                                "we’ll refund the amount to your bank account within 2 business days.", new HashMap<>())
                .channelSetting(nChannel)
                .notificationLocation(NotificationLocation.HOME)
                .priority("priority_3")
                .target(PicklistHelper.getItem("template_target", "Clients"))
                .method("Push Notification")
                .newModel()
                .build();
    }

    private static void nonConditionalRefundRequested() {

        ChannelSpecificSetting nChannel = new ChannelSpecificSetting();
        nChannel.setType(ChannelSpecificSettingType.Notification);
        nChannel.setActive(true);
        nChannel.setText("We just sent you AED @amount@ by bank transfer. Please expect to receive the amount to " +
                "your bank account in the next 7 business days.");
        nChannel.setNotificationCanClosedByUser(true);
        nChannel.setControls(new ArrayList<>());
        nChannel.setMessageDelayType(Setup.getItem("template_message_delay", "specific_time"));
        nChannel.setMessageDelaySpecificDate(Setup.getItem("template_message_delay_specific_date", "@nextMorning@"));
        nChannel.setMessageDelaySpecificTime(new DateTime().withTime(10, 0 ,0, 0).toDate());
        nChannel.setShowOnHomePage(true);
        nChannel.setOnFailAction(ChannelSpecificSettingType.SMS);

        new Template.TemplateBuilder()
                .Template(CcNotificationTemplateCode.CC_ACCOUNTING_NON_CONDITIONAL_REFUND_REQUESTED_NOTIFICATION.toString(),"", "")
                .showInReceiverLog(true)
                .unicode(true)
                .smsChannelSetting(
                        "We just sent you AED @amount@ by bank transfer. Please expect to receive " +
                                "the amount to your bank account in the next 7 business days.", new HashMap<>())
                .channelSetting(nChannel)
                .notificationLocation(NotificationLocation.HOME)
                .priority("priority_3")
                .target(PicklistHelper.getItem("template_target", "Clients"))
                .method("Push Notification")
                .newModel()
                .build();
    }

    private static void refundRejectedCreditCard() {

        getAccountingTemplateService().createNewModelTemplateAndAddAllowedParameters(
                CcNotificationTemplateCode.CC_ACCOUNTING_REFUND_REJECTED_CREDIT_CARD_NOTIFICATION.toString(),
                "We’re sorry but after carefully reviewing your case, " +
                        "we’ve declined the refund request. If you think this is a mistake, please @whatsApp_us@.",
                "We’re sorry but after carefully reviewing your case, we’ve declined the refund request. " +
                        "If you think this is a mistake, please WhatsApp us @whatsApp_us_sms@",
                2,
                new HashMap<>()
        );
    }

    private static void conditionalRefundRequestedCreditCard() {

        getAccountingTemplateService().createNewModelTemplateAndAddAllowedParameters(
                CcNotificationTemplateCode.CC_ACCOUNTING_CONDITIONAL_REFUND_REQUESTED_CREDIT_CARD_NOTIFICATION.toString(),
                "You're eligible for a refund of AED @amount@. After we receive your @payments_amount@ , " +
                        "we’ll refund the amount to your bank account within 2 business days.",
                "You're eligible for a refund of AED @amount@. After we receive your @payments_amount@ , " +
                        "we’ll refund the amount to your bank account within 2 business days.",
                3,
                new HashMap<>()
        );
    }

    private static void nonConditionalRefundRequestedCreditCard() {

        getAccountingTemplateService().createNewModelTemplateAndAddAllowedParameters(
                CcNotificationTemplateCode.CC_ACCOUNTING_NON_CONDITIONAL_REFUND_REQUESTED_CREDIT_CARD_NOTIFICATION.toString(),
                "We just sent you AED @amount@ by bank transfer. Please expect to receive the amount " +
                        "to your bank account in the next 7 business days.",
                "We just sent you AED @amount@ by bank transfer. Please expect to receive the amount " +
                        "to your bank account in the next 7 business days.",
                3,
                new HashMap<>()
        );
    }

    private static void createForACC6587() {
        AccountingTemplateService accountingTemplateService = Setup.getApplicationContext()
                .getBean(AccountingTemplateService.class);

        accountingTemplateService.createNewModelTemplateAndAddAllowedParameters(
                CcNotificationTemplateCode.CC_PAYMENT_RECEIVED_FROM_ONE_TO_X_MINUS_ONE_MONTHS_NOTIFICATION.toString(),
                "Thank you, we have successfully received your payment of AED @total_amount@. This amount covers @payments_description@ " +
                        "@monthly_payment_scheduled_date@\n" +
                        "To manage everything related to your payments, please visit the 'My Payments' section in your app. You can directly access it now by clicking the button below: ",
                "Thank you, we have successfully received your payment of AED @total_amount@. This amount covers @payments_description@ " +
                        "@monthly_payment_scheduled_date@\n" +
                        "To manage everything related to your payments, please download your personalized maids.cc app, by clicking on the following link: " +
                        "@cc_app_download_url@",
                1);

        accountingTemplateService.createNewModelTemplateAndAddAllowedParameters(
                CcNotificationTemplateCode.CC_PAYMENT_RECEIVED_ON_X_MONTH_NOTIFICATION.toString(),
                "Thank you, we have successfully received your payment of AED @total_amount@. This amount covers @payments_description@ " +
                        "@monthly_payment_scheduled_date@\n" +
                        "Because we don't want to bother you, this is the last time you will receive this notification automatically. " +
                        "To continue receiving payment notifications, please click the \"Enable Payment Notifications\" button below. " +
                        "You can always manage your payment notifications from the payment section in your app.",
                "Thank you, we have successfully received your payment of AED @total_amount@. This amount covers @payments_description@ " +
                        "@monthly_payment_scheduled_date@\n" +
                        "Because we don't want to bother you, this is the last time you will receive this notification automatically.\n " +
                        "To manage everything related to your payments, please download your personalized maids.cc app, by clicking on the following link: " +
                        "@cc_app_download_url@",
                1);

        accountingTemplateService.createNewModelTemplateAndAddAllowedParameters(
                CcNotificationTemplateCode.CC_PAYMENT_RECEIVED_FROM_X_PLUS_ONE_ONWARDS_MONTHS_NOTIFICATION.toString(),
                "Thank you, we have successfully received your payment of AED @total_amount@. This amount covers @payments_description@ " +
                        "@monthly_payment_scheduled_date@",
                "Thank you, we have successfully received your payment of AED @total_amount@. This amount covers @payments_description@ " +
                        "@monthly_payment_scheduled_date@",
                4);
    }

    private static void informCancelledClientsAboutPaymentDeduction() {
        Map<String, Object> m = new HashMap<>();
        m.put("method", "whatsapp");
        m.put("priority", "TS");
        m.put("excludeFromWindow", true);

        getAccountingTemplateService().createNewModelTemplateAndAddAllowedParameters(
                CcNotificationTemplateCode.CC_INFORM_CANCELED_CLIENTS_WITH_OVERDUE_PAYMENTS_WA.toString(),
                null,
                "We understand that you've canceled the service. " +
                        "Please note that an overdue payment will be deducted from your bank account. Thank you for your understanding.",
                0,
                m
        );

        getAccountingTemplateService().createNewModelTemplateAndAddAllowedParameters(
                CcNotificationTemplateCode.CC_INFORM_CLIENTS_ABOUT_PENDING_DIRECT_DEBITS_AFTER_CANCELLATION_WA.toString(),
                null,
                "We submitted your direct debits before you canceled with us. " +
                        "You will receive a message from your bank that we have sent your direct debits for approval. " +
                        "Please disregard this message, as they will be canceled once the bank processes them. " +
                        "We apologize for any inconvenience caused and appreciate your patience.",
                0,
                m
        );
    }
}
