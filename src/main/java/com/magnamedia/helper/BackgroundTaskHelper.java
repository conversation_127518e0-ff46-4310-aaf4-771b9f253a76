package com.magnamedia.helper;


/*
 * <AUTHOR>
 * @created 02/07/2024 - 9:15 PM
 * ACC-7640
 */

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.entity.BackgroundTaskStatus;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.entity.BankStatementTransaction;
import com.magnamedia.entity.Contract;
import com.magnamedia.extra.UploadStatementEntityType;
import com.magnamedia.extra.Utils;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.BankStatementTransactionRepository;
import com.magnamedia.service.QueryService;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Component
public class BackgroundTaskHelper {

    private static final Logger logger = Logger.getLogger(BackgroundTaskHelper.class.getSimpleName());

    @Autowired
    BackgroundTaskService backgroundTaskService;
    @Autowired
    BankStatementTransactionRepository bankStatementTransactionRepository;

    public static boolean checkIfFoundAnyBGT(UploadStatementEntityType relatedEntityType) {
        SelectQuery<BackgroundTask> query = new SelectQuery<>(BackgroundTask.class);
        query.filterBy("module", "=", Setup.getCurrentModule().getCode());
        query.filterBy("targetBean", "=", relatedEntityType.getTargetBean());
        query.filterBy("targetMethod", "=", relatedEntityType.getTargetMethod());
        query.filterBy("status", "not in", Arrays.asList(BackgroundTaskStatus.Finished,
                BackgroundTaskStatus.Failed));

        return !query.execute().isEmpty();
    }

    public static boolean checkIfFoundAnyBGT(UploadStatementEntityType type, Long relatedEntityId, String relatedEntityType) {
        SelectQuery<BackgroundTask> query = new SelectQuery<>(BackgroundTask.class);
        query.filterBy("module", "=", Setup.getCurrentModule().getCode());
        query.filterBy("targetBean", "=", type.getTargetBean());
        query.filterBy("targetMethod", "=", type.getTargetMethod());
        query.filterBy("relatedEntityId", "=", relatedEntityId);
        query.filterBy("relatedEntityType", "=", relatedEntityType);
        query.filterBy("status", "not in", Arrays.asList(BackgroundTaskStatus.Finished,
                BackgroundTaskStatus.Failed));

        return !query.execute().isEmpty();
    }

    public static void createBGTParsingStatementUploaded(
            UploadStatementEntityType relatedEntityType, String nameBGT,
            Map<String, Object> payload) {

        if(checkIfFoundAnyBGT(relatedEntityType)) {
            throw new BusinessException("There’s another file under parsing, please wait …");
        }
        Long parsedEntityId = Utils.parseValue(payload.getOrDefault("entityId", null), Long.class);

        String queue = Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_QUEUE_BGT_OF_STATEMENT_PARSING_PROCESSES);

        Setup.getApplicationContext()
                .getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        nameBGT,
                        "accounting",
                        relatedEntityType.getTargetBean(),
                        relatedEntityType.getTargetMethod())
                        .withRelatedEntity(relatedEntityType.name(), parsedEntityId)
                        .withPreventDuplicateExecution()
                        .withParameters(
                                new Class[] { Map.class },
                                new Object[] { payload })
                        .withQueue(BackgroundTaskQueues.valueOf(queue))
                        .build());
    }

    public static void createBackGroundTask(String jobName, String targetedBean, String targetedMethod, BaseEntity entity, BackgroundTaskQueues queue) {

        Setup.getApplicationContext()
                .getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "AccountingJob_" + jobName + "_" + entity.getId() + "_" + new Date().getTime(),
                        "accounting",
                        targetedBean,
                        targetedMethod)
                        .withRelatedEntity(entity.getEntityType(), entity.getId())
                        .withParameters(
                                new Class[] {Long.class},
                                new Object[] {entity.getId()})
                        .withQueue(queue)
                        .build());
    }

    public static void createBGTSendEmailForMissingTransactionPostingRule(
            Contract contract, PicklistItem typeOfPayment, PaymentMethod methodOfPayment,
            Boolean isInitial, PaymentStatus paymentStatus, Long paymentId) {
        String bgtName = "send_email_for_transaction_posting_rule_" + contract.getContractProspectType().getCode() +
                "_" + typeOfPayment.getCode() + "_" + methodOfPayment.getValue() + "_isInitial:" + isInitial +
                "_" + paymentStatus.getValue() + "_" + new LocalDate().toString("yyyy_MM_dd");
        logger.info("bgtName :  " + bgtName);

        synchronized (bgtName.intern()) {
            if (ConcurrentModificationHelper.isExistInGlobalData(bgtName) ||
                    QueryService.existsEntity(BackgroundTask.class, "e.name = :p0 and e.status <> 'Failed'", new Object[]{bgtName})) {
                logger.info("there is a running BGT for payment type : " + typeOfPayment.getCode());
                return;
            }
            logger.info("new bgt will be created with name : " + bgtName);
            ConcurrentModificationHelper.lockGlobalDataNewKey(bgtName, "");
            Setup.getApplicationContext()
                    .getBean(BackgroundTaskService.class)
                    .create(new BackgroundTask.builder(
                            bgtName,
                            "accounting",
                            "officeStaffMessagingService",
                            "sendEmailForTransactionPostingRules")
                            .withPreventDuplicateExecution()
                            .withParameters(
                                    new Class[] { Map.class },
                                    new Object[] { new HashMap<String, String>() {{
                                        put("type_of_payment", typeOfPayment.getName());
                                        put("method", methodOfPayment.getLabel());
                                        put("contract_type", contract.isMaidVisa() ? "Maid Visa" : "Maid CC");
                                        put("is_initial", isInitial? "Yes" : "No");
                                        put("payment_id", String.valueOf(paymentId));
                                        put("contract_id", String.valueOf(contract.getId()));
                                        put("client_id", String.valueOf(contract.getClient().getId()));
                                        put("bgtName", bgtName);
                                    }}})
                            .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                            .build());
        }
    }

    public void createBGTForBulkAccountantAction(List<Long> ids) {
        backgroundTaskService.create(new BackgroundTask.builder(
                "bulkAccountantActionOnPayrollAccountantTodo_" + new Date().getTime(),
                "accounting",
                "accountantTodoService",
                "bulkAccountantAction")
                .withRelatedEntity( "PayrollAccountantTodo", null)
                .withParameters( new Class<?>[] {List.class},
                        new Object[] { ids != null ? ids.stream().map(String::valueOf).collect(Collectors.toList()) : null })
                .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                .withDelay(30L * 1000L)
                .build());
    }

    public void createBGTPreConfirmAllVisaStatementRecords(Long visaStatementId) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("entityId", visaStatementId.toString());

        Setup.getApplicationContext()
                .getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        UploadStatementEntityType.ConfirmVisaStatementTransactions.toString(),
                        "accounting",
                        UploadStatementEntityType.ConfirmVisaStatementTransactions.getTargetBean(),
                        UploadStatementEntityType.ConfirmVisaStatementTransactions.getTargetMethod())
                        .withRelatedEntity("VisaStatement", visaStatementId)
                        .withPreventDuplicateExecution()
                        .withParameters(
                                new Class[] { Map.class },
                                new Object[] { payload })
                        .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                        .build());
    }

    public static void createBGTForPreHandledPayrollLogRecords(Long fileId, String source) {
        Setup.getApplicationContext()
                .getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "preHandlePayrollTransferRecords_" + fileId,
                        "accounting",
                        "bankStatementFileService",
                        "preHandleRecords")
                        .withRelatedEntity("BankStatementFile", fileId)
                        .withParameters(
                                new Class[]{Long.class, String.class},
                                new Object[]{fileId, source})
                        .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                        .build());
    }

    @Async
    public void createBackgroundTask(Long bankTransactionId, HashMap parameters, long time, Long userId) {

        SecurityContext sc = SecurityContextHolder.getContext();
        try {
            UsernamePasswordAuthenticationToken authReq = new UsernamePasswordAuthenticationToken(
                    Setup.getRepository(UserRepository.class).findOne(userId), null, null);
            sc.setAuthentication(authReq);
            BankStatementTransaction bankTransaction = bankStatementTransactionRepository.findOne(bankTransactionId);

            String queue = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_SPECIFY_BGT_QUEUE_OF_CONFIRM_BANK_STATEMENT_TRANSACTION);
            backgroundTaskService.create(new BackgroundTask.builder(
                    "BankStatementFile_" + bankTransaction.getFile().getId() + "_" + time,
                    "accounting",
                    "bankStatementFileController",
                    "confirmBankStatementTransaction")
                    .withRelatedEntity("BankStatementTransaction", bankTransaction.getId())
                    .withParameters(
                            new Class[] { Long.class, HashMap.class },
                            new Object[] { bankTransaction.getId(), parameters })
                    .withQueue(BackgroundTaskQueues.valueOf(queue))
                    .build());
        } finally {
            sc.setAuthentication(null);
        }
    }

    @Async
    public void createBGTForConfirmOneDD(Map<String, Object> record, boolean fromRPA, Long userId) {
        SecurityContext sc = SecurityContextHolder.getContext();
        try {
            UsernamePasswordAuthenticationToken authReq = new UsernamePasswordAuthenticationToken(
                    Setup.getRepository(UserRepository.class).findOne(userId), null, null);
            sc.setAuthentication(authReq);

            String queue = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_SPECIFY_BACKGROUND_TASK_QUEUE_OF_BANK_DDS_CANCELLATION_RECORD);
            backgroundTaskService.create(new BackgroundTask.builder(
                    "BankDirectDebitCancellationFile_Confirm_DD_" + record.get("fileId"),
                    "accounting",
                    "bankDirectDebitCancellationRecordService",
                    "confirmOneDD")
                    .withRelatedEntity("BankDirectDebitCancelationRecord", Utils.parseValue(record.get("id"), Long.class))
                    .withParameters( new Class<?>[] { Long.class, Boolean.class },
                            new Object[] { record.get("id"), fromRPA })
                    .withQueue(BackgroundTaskQueues.valueOf(queue))
                    .build());
        } finally {
            sc.setAuthentication(null);
        }
    }

    @Async
    public void createBGTForApprovalOnRejectionBank(Map<String, Object> record, boolean fromRPA, Long userId) {
        SecurityContext sc = SecurityContextHolder.getContext();
        try {
            UsernamePasswordAuthenticationToken authReq = new UsernamePasswordAuthenticationToken(
                    Setup.getRepository(UserRepository.class).findOne(userId), null, null);
            sc.setAuthentication(authReq);

            String queue = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_SPECIFY_BACKGROUND_TASK_QUEUE_OF_BANK_DDS_CANCELLATION_RECORD);
            backgroundTaskService.create(new BackgroundTask.builder(
                    "BankDirectDebitCancellationFile_Confirm_DD_" + record.get("fileId"),
                    "accounting",
                    "bankDirectDebitCancellationRecordService",
                    "approveRejectionByBankForOneDD")
                    .withRelatedEntity("BankDirectDebitCancelationRecord", Utils.parseValue(record.get("id"), Long.class))
                    .withParameters( new Class<?>[] { Long.class, Boolean.class },
                            new Object[] { record.get("id"), fromRPA })
                    .withQueue(BackgroundTaskQueues.valueOf(queue))
                    .build());
        } finally {
            sc.setAuthentication(null);
        }
    }
}