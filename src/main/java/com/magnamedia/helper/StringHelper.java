package com.magnamedia.helper;

import com.magnamedia.module.type.DDMessagingContractType;

import java.util.Arrays;
import java.util.Random;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Sep 3, 2019
 * ACC-837
 */
public class StringHelper {

    static final String AB = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    static Random rnd = new Random();

    public static String randomString(int len) {
        StringBuilder sb = new StringBuilder(len);
        for (int i = 0; i < len; i++) {
            sb.append(AB.charAt(rnd.nextInt(AB.length())));
        }
        return sb.toString();
    }

    public static String NormalizePhoneNumber(String number) {

        String NormalizedNumber = number;
        NormalizedNumber = NormalizedNumber.replace("-", "");
        NormalizedNumber = NormalizedNumber.replace("+", "");
        NormalizedNumber = NormalizedNumber.replace(")", "");
        NormalizedNumber = NormalizedNumber.replace("(", "");
        NormalizedNumber = NormalizedNumber.replace(" ", "");

        NormalizedNumber = removeFirst(NormalizedNumber, "00");
        
        if (NormalizedNumber.startsWith("0"))
        {
          NormalizedNumber = removeFirst(NormalizedNumber, "0");
          NormalizedNumber = "971" + NormalizedNumber;
        }
        if (NormalizedNumber.startsWith("9710"))
        {
          NormalizedNumber = removeFirst(NormalizedNumber, "9710");
          NormalizedNumber = "971" + NormalizedNumber;
        }
        return NormalizedNumber;
    }

    public static String removeFirst(String s,
            String toRemove) {
        int x = 0;
        if (s.startsWith(toRemove)) {
            s = s.substring(toRemove.length());
        }
        return s;
    }

    /**
     * Validates if the contract prospect types string contains valid values from DDMessagingContractType enum
     * @param contractProspectTypes String containing comma-separated contract types
     * @return true if valid, false otherwise
     */
    public static boolean isValidContractProspectTypes(String contractProspectTypes) {
        if (contractProspectTypes == null || contractProspectTypes.isEmpty()) {
            return false;
        }

        // Get all valid labels from the enum
        Set<String> validLabels = Arrays.stream(DDMessagingContractType.values())
                .map(DDMessagingContractType::getLabel)
                .collect(Collectors.toSet());

        // Split the input string by comma
        String[] types = contractProspectTypes.split(",");

        // Check if all parts are valid labels
        for (String type : types) {
            if (!validLabels.contains(type.trim())) {
                return false;
            }
        }

        return true;
    }
}
