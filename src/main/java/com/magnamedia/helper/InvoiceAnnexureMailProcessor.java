package com.magnamedia.helper;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.helper.chatai.ChatAIRequestBuilder;
import com.magnamedia.core.helper.ocr.GenericOCRService;
import com.magnamedia.core.mail.MailProcessor;
import com.magnamedia.core.repository.TemplateRepository;
import com.magnamedia.core.services.chatai.ChatAIService;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.InvoiceStatement;
import com.magnamedia.entity.InvoiceStatementRecord;
import com.magnamedia.entity.Supplier;
import com.magnamedia.repository.*;
import com.magnamedia.service.InvoiceStatementService;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Logger;


@Component(value = "invoiceAnnexureMailProcessor")
public class InvoiceAnnexureMailProcessor implements MailProcessor {

    private static final Logger logger = Logger.getLogger(InvoiceAnnexureMailProcessor.class.getName());

    private static final String EMAIL_SOURCE = "<EMAIL>";
    private static final String ANNEXURE_FILE_PREFIX = "Copy of Annexure";
    private static final String INVOICE_FILE_TITLE = "AD Medical Invoice";


    @Autowired
    private InvoiceStatementRecordRepository invoiceStatementRecordRepository;

    @Autowired
    private HousemaidRepository housemaidRepository;

    @Autowired
    private InvoiceStatementRepository invoiceStatementRepository;

    @Autowired
    private InvoiceStatementTransactionRepository invoiceStatementTransactionRepository;

    @Autowired
    private ExpenseRequestTodoRepository expenseRequestTodoRepository;

    @Autowired
    private SupplierRepository supplierRepository;

    @Autowired
    private GenericOCRService genericOCRService;

    @Autowired
    private ChatAIService chatAIService;

    @Autowired
    private TemplateRepository templateRepository;

    private static final String MEDICAL_SUPPLIER_NAME = "Capital Medical Centre for Health";

    @Override
    public void process(String emailSender, String title, String body, List<File> attachments) {

        logger.info("Processing email from: " + emailSender + " with title: " + title);

        if (!isTargetEmail(emailSender, title)) {
            logger.info("Not a target email");
            return;
        }

        File annexureFile = findAnnexureFile(attachments);
        File invoiceFile = findInvoiceFile(attachments);

        if (annexureFile == null) {
            logger.info("No annexure file found in email from: " + emailSender + " with title: " + title);
            return;
        }

        try {
            logger.info("Processing annexure file: " + annexureFile.getName());
            InvoiceStatement statement = new InvoiceStatement();
            InputStream annexureFileStream = new FileInputStream(annexureFile);
            InputStream invoiceFileStream = new FileInputStream(invoiceFile);

            String annexureExtension = getFileExtension(annexureFile.getName());

            Attachment  annexureAttachment = Storage.storeTemporary(
                    "annexure"+ DateTime.now().getMillis() + "." + annexureExtension,
                    annexureFileStream,
                    "annexure_file",
                    false);

            Attachment  invoiceAttachment = Storage.storeTemporary(
                    "invoice"+ DateTime.now().getMillis() +".pdf",
                    invoiceFileStream,
                    "invoice_file",
                    false);

            invoiceFileStream.close();
            annexureFileStream.close();

            statement.addAttachment(annexureAttachment);
            statement.addAttachment(invoiceAttachment);
            invoiceStatementRepository.save(statement);

            if (isPdfFile(annexureFile.getName())) {
                logger.info("Processing PDF annexure file: " + annexureFile.getName());
                processAnnexureFilePdf(annexureFile, statement, annexureAttachment);
            } else {
                logger.info("Processing Excel annexure file: " + annexureFile.getName());
                processAnnexureFile(annexureFile, statement);
            }
        } catch (Exception ex) {
            logger.info("Error in processing " + ex.getMessage());
        }
    }

    private boolean isTargetEmail(String emailSender, String title) {
        String targetEmailTitle = "CHSC Al Jazira Clinic - MAIDS CC DOMESTIC WORKERSS SERVICES - Invoice";
        return (title.toLowerCase().contains(targetEmailTitle.toLowerCase()) && emailSender.equalsIgnoreCase(EMAIL_SOURCE));
    }

    private File findAnnexureFile(List<File> attachments) {
        if (attachments == null || attachments.isEmpty()) {
            return null;
        }
        for (File file : attachments) {
            if ((file.getName().contains(".xls") || file.getName().contains(".xlsx") ||
                 file.getName().contains(".pdf") || file.getName().contains(".PDF")) &&
                    file.getName().toLowerCase().contains(ANNEXURE_FILE_PREFIX.toLowerCase())) {
                return file;
            }
        }
        return null;
    }

    private File findInvoiceFile(List<File> attachments) {
        if (attachments == null || attachments.isEmpty()) {
            return null;
        }

        for (File file : attachments) {
            if ((file.getName().contains(".pdf") || file.getName().contains(".PDF")) &&
                    file.getName().toLowerCase().contains(INVOICE_FILE_TITLE.toLowerCase())) {
                return file;
            }
        }

        return null;
    }

    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.lastIndexOf('.') == -1) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
    }

    private boolean isPdfFile(String fileName) {
        String extension = getFileExtension(fileName);
        return "pdf".equals(extension);
    }

    private void processAnnexureFilePdf(
            File annexureFile,
            InvoiceStatement statement,
            Attachment annexureAttachment) throws IOException {

        logger.info("Processing PDF annexure file: " + annexureFile.getName());
        List<InvoiceStatementRecord> records = new ArrayList<>();

        try {
            if (annexureAttachment == null) {
                logger.warning("No annexure attachment found for OCR processing");
                return;
            }

            String ocrText = genericOCRService.getText(annexureAttachment);
            if (ocrText == null || ocrText.trim().isEmpty()) {
                logger.warning("OCR failed to extract text from PDF: " + annexureFile.getName());
                return;
            }
            logger.info("OCR extracted text: " + ocrText);
            extractTaxInvoiceInfoFromText(ocrText, statement);
            records = parseRecordsFromOcrText(ocrText, statement);

            if (!records.isEmpty()) {
                invoiceStatementRecordRepository.saveAll(records);
                logger.info("Processed " + records.size() + " records from PDF annexure file: " + annexureFile.getName());

                Setup.getApplicationContext().getBean(InvoiceStatementService.class)
                        .createInvoiceStatementTransactions(statement);
            }

        } catch (Exception e) {
            logger.severe("Error processing PDF annexure file: " + e.getMessage());
            throw new IOException("Failed to process PDF annexure file", e);
        }
    }

    private void processAnnexureFile(File annexureFile, InvoiceStatement statement) throws IOException {
        Workbook workbook;

        List<InvoiceStatementRecord> records = new ArrayList<>();

        try {
            workbook = WorkbookFactory.create(annexureFile);
            Sheet sheet = workbook.getSheetAt(0);

            extractTaxInvoiceInfo(sheet, statement);

            String[] requiredColumnNames = {"Visit Date", "MRN", "Name", "Passport No", "UID", "Type of Service", "Amount"};

            Map<String, Integer> columnIndices = findColumnIndices(sheet, requiredColumnNames);

            if (columnIndices.size() != requiredColumnNames.length) {
                logger.warning("Not all required columns found in annexure file: " + annexureFile.getName());
                return;
            }

            Iterator<Row> rowIterator = sheet.iterator();
            boolean headerRowProcessed = false;

            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                if (!headerRowProcessed) {
                    headerRowProcessed = true;
                    continue;
                }
                InvoiceStatementRecord record = extractRecordFromRow(row, columnIndices);
                record.setStatement(statement);

                List<Housemaid> housemaids = housemaidRepository.findByPassportNumber(record.getPassportNo());
                if (housemaids != null && !housemaids.isEmpty()) {
                    record.setHousemaid(housemaids.get(0));
                }
                records.add(record);
            }

            if (!records.isEmpty()) {
                invoiceStatementRecordRepository.save(records);
                logger.info("Processed " + records.size() + " records from annexure file: " + annexureFile.getName());

                Setup.getApplicationContext().getBean(InvoiceStatementService.class)
                        .createInvoiceStatementTransactions(statement);
            }

        } catch (InvalidFormatException e) {
            logger.info("error in processing annexure file: " + e.getMessage());
        }
    }

    private void extractTaxInvoiceInfo(Sheet sheet, InvoiceStatement statement) {
        DataFormatter formatter = new DataFormatter();

        for (int rowIndex = 0; rowIndex < 10; rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row == null) continue;

            for (int colIndex = 0; colIndex < 10; colIndex++) {
                Cell cell = row.getCell(colIndex);
                if (cell == null) continue;

                String cellValue = formatter.formatCellValue(cell).trim();

                if (cellValue.equals("Tax Invoice No.") && colIndex + 1 < row.getLastCellNum()) {
                    Cell valueCell = row.getCell(colIndex + 2); // Value is 2 cells to the right
                    if (valueCell != null) {
                        String invoiceNumber = formatter.formatCellValue(valueCell).trim();
                        statement.setTaxInvoiceNumber(invoiceNumber);
                        logger.info("Found Tax Invoice No.: " + invoiceNumber);
                    }
                }

                if (cellValue.equals("Tax Invoice Date") && colIndex + 1 < row.getLastCellNum()) {
                    Cell valueCell = row.getCell(colIndex + 2); // Value is 2 cells to the right
                    if (valueCell != null) {
                        String dateStr = formatter.formatCellValue(valueCell).trim();
                        try {
                            SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MMM-yyyy");
                            java.util.Date invoiceDate = dateFormat.parse(dateStr);
                            statement.setTaxInvoiceDate(new java.sql.Date(invoiceDate.getTime()));
                            logger.info("Found Tax Invoice Date: " + dateStr);
                        } catch (ParseException e) {
                            logger.warning("Error parsing Tax Invoice Date: " + e.getMessage());
                        }
                    }
                }

                if (cellValue.equals("Due Date") && colIndex + 1 < row.getLastCellNum()) {
                    Cell valueCell = row.getCell(colIndex + 2); // Value is 2 cells to the right
                    if (valueCell != null) {
                        String dateStr = formatter.formatCellValue(valueCell).trim();
                        try {
                            SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MMM-yyyy");
                            java.util.Date dueDate = dateFormat.parse(dateStr);
                            statement.setDueDate(new java.sql.Date(dueDate.getTime()));
                            logger.info("Found Due Date: " + dateStr);
                        } catch (ParseException e) {
                            logger.warning("Error parsing Due Date: " + e.getMessage());
                        }
                    }
                }

            }
        }

        invoiceStatementRepository.save(statement);
    }


    private Supplier findMedicalSupplier() {
        return supplierRepository.findFirstByName(MEDICAL_SUPPLIER_NAME);
    }

    private Map<String, Integer> findColumnIndices(Sheet sheet, String[] requiredColumnNames) {
        Map<String, Integer> columnIndices = new HashMap<>();

        Row headerRow = sheet.getRow(0);
        if (headerRow == null) {
            return columnIndices;
        }

        Iterator<Cell> cellIterator = headerRow.cellIterator();
        int columnIndex = 0;

        while (cellIterator.hasNext()) {
            Cell cell = cellIterator.next();
            String cellValue = cell.getStringCellValue();

            for (String columnName : requiredColumnNames) {
                if (columnName.equalsIgnoreCase(cellValue)) {
                    columnIndices.put(columnName, columnIndex);
                    break;
                }
            }

            columnIndex++;
        }

        return columnIndices;
    }

    private InvoiceStatementRecord extractRecordFromRow(Row row, Map<String, Integer> columnIndices) {
        if (row == null) {
            return null;
        }

        DataFormatter formatter = new DataFormatter();

        boolean hasData = false;
        for (int i = 0; i < row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i);
            if (cell != null && !formatter.formatCellValue(cell).trim().isEmpty()) {
                hasData = true;
                break;
            }
        }

        if (!hasData) {
            return null;
        }

        InvoiceStatementRecord record = new InvoiceStatementRecord();

        Cell visitDateCell = row.getCell(columnIndices.get("Visit Date"));
        if (visitDateCell != null) {
            try {
                String dateStr = formatter.formatCellValue(visitDateCell);
                if (dateStr != null && !dateStr.isEmpty()) {
                    SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
                    record.setVisitDate(dateFormat.parse(dateStr));
                }
            } catch (ParseException e) {
                logger.warning("Error parsing date: " + e.getMessage());
            }
        }

        Cell mrnCell = row.getCell(columnIndices.get("MRN"));
        if (mrnCell != null) {
            record.setMrn(formatter.formatCellValue(mrnCell));
        }

        // Extract Name
        Cell nameCell = row.getCell(columnIndices.get("Name"));
        if (nameCell != null) {
            record.setName(formatter.formatCellValue(nameCell));
        }

        // Extract Passport No
        Cell passportCell = row.getCell(columnIndices.get("Passport No"));
        if (passportCell != null) {
            record.setPassportNo(formatter.formatCellValue(passportCell));
        }

        // Extract UID
        Cell uidCell = row.getCell(columnIndices.get("UID"));
        if (uidCell != null) {
            record.setUid(formatter.formatCellValue(uidCell));
        }

        // Extract Type of Service
        Cell serviceTypeCell = row.getCell(columnIndices.get("Type of Service"));
        if (serviceTypeCell != null) {
            record.setTypeOfService(formatter.formatCellValue(serviceTypeCell));
        }

        Cell amountCell = row.getCell(columnIndices.get("Amount"));
        if (amountCell != null) {
            try {
                if (amountCell.getCellType() == Cell.CELL_TYPE_NUMERIC) {
                    record.setAmount(amountCell.getNumericCellValue());
                } else {
                    String amountStr = formatter.formatCellValue(amountCell);
                    if (amountStr != null && !amountStr.isEmpty()) {
                        record.setAmount(Double.parseDouble(amountStr));
                    }
                }
            } catch (NumberFormatException e) {
                logger.warning("Error parsing amount: " + e.getMessage());
            }
        }

        return record;
    }

    private void extractTaxInvoiceInfoFromText(String ocrText, InvoiceStatement statement) {
        logger.info("Extracting tax invoice info from OCR text");

        try {
            // Extract Tax Invoice Number
            String invoiceNumberPattern = "(?i)tax\\s+invoice\\s+no\\.?\\s*:?\\s*([A-Z0-9\\-]+)";
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(invoiceNumberPattern);
            java.util.regex.Matcher matcher = pattern.matcher(ocrText);
            if (matcher.find()) {
                String invoiceNumber = matcher.group(1).trim();
                statement.setTaxInvoiceNumber(invoiceNumber);
                logger.info("Found Tax Invoice No.: " + invoiceNumber);
            }

            // Extract Tax Invoice Date
            String datePattern = "(?i)tax\\s+invoice\\s+date\\s*:?\\s*(\\d{1,2}[-/]\\w{3}[-/]\\d{4})";
            pattern = java.util.regex.Pattern.compile(datePattern);
            matcher = pattern.matcher(ocrText);
            if (matcher.find()) {
                String dateStr = matcher.group(1).trim();
                try {
                    SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MMM-yyyy");
                    java.util.Date invoiceDate = dateFormat.parse(dateStr);
                    statement.setTaxInvoiceDate(new java.sql.Date(invoiceDate.getTime()));
                    logger.info("Found Tax Invoice Date: " + dateStr);
                } catch (ParseException e) {
                    logger.warning("Error parsing Tax Invoice Date: " + e.getMessage());
                }
            }

            // Extract Due Date
            String dueDatePattern = "(?i)due\\s+date\\s*:?\\s*(\\d{1,2}[-/]\\w{3}[-/]\\d{4})";
            pattern = java.util.regex.Pattern.compile(dueDatePattern);
            matcher = pattern.matcher(ocrText);
            if (matcher.find()) {
                String dateStr = matcher.group(1).trim();
                try {
                    SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MMM-yyyy");
                    java.util.Date dueDate = dateFormat.parse(dateStr);
                    statement.setDueDate(new java.sql.Date(dueDate.getTime()));
                    logger.info("Found Due Date: " + dateStr);
                } catch (ParseException e) {
                    logger.warning("Error parsing Due Date: " + e.getMessage());
                }
            }

        } catch (Exception e) {
            logger.warning("Error extracting tax invoice info from OCR text: " + e.getMessage());
        }

        invoiceStatementRepository.save(statement);
    }

    private List<InvoiceStatementRecord> parseRecordsFromOcrText(String ocrText, InvoiceStatement statement) throws Exception {
        List<InvoiceStatementRecord> records = new ArrayList<>();

        logger.info("Sending OCR text to ChatGPT for parsing");

        String result = chatAIService.sendToChatGPT(new ChatAIRequestBuilder()
                .template(templateRepository.findByNameIgnoreCase("gpt_parse_annexure_ocr_text"))
                .templateParam(new HashMap<String, String>() {{
                    put("ocr_text", ocrText);
                }}));
        logger.info("ChatGPT CSV result: " + result);

        try {
            logger.info("Parsing ChatGPT CSV result");
            String[] lines = result.split("\\r?\\n");
            boolean isFirstLine = true;

            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty()) continue;
                logger.info("Processing line: " + line);
                if (isFirstLine) {
                    isFirstLine = false;
                    logger.info("Skipping CSV header: " + line);
                    continue;
                }

                InvoiceStatementRecord record = parseRecordFromCsvLine(line, statement);

                if (record != null) {
                    records.add(record);
                }
            }
            logger.info("Parsed " + records.size() + " records from ChatGPT CSV result");
        } catch (Exception e) {
            logger.severe("Error parsing records from OCR text: " + e.getMessage());
        }

        return records;
    }

    private InvoiceStatementRecord parseRecordFromCsvLine(String line, InvoiceStatement statement) {
        logger.info("Parsing record from line: " + line);
        try {
            String[] parts = line.split(",");
            for (int i = 0; i < parts.length; i++) {
                parts[i] = parts[i].trim();
            }
            InvoiceStatementRecord record = new InvoiceStatementRecord();
            record.setStatement(statement);

            String dateStr = parts[1];
            SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
            record.setVisitDate(dateFormat.parse(dateStr));

            record.setMrn(parts[2]);           // Index 2 is MRN
            record.setName(parts[3]);          // Index 3 is Name
            record.setPassportNo(parts[4]);    // Index 4 is Passport No
            record.setUid(parts[5]);           // Index 5 is UID
            record.setTypeOfService(parts[6]); // Index 6 is Type of Service

            record.setAmount(Double.parseDouble( parts[7]));

            List<Housemaid> housemaids = housemaidRepository.findByPassportNumber(record.getPassportNo());
            if (housemaids != null && !housemaids.isEmpty()) {
                record.setHousemaid(housemaids.get(0));
            }
            if (record.getPassportNo() != null && !record.getPassportNo().trim().isEmpty() &&
                record.getName() != null && !record.getName().trim().isEmpty()) {
                logger.info("Parsed record: " + record);
                return record;
            }

        } catch (Exception e) {
            logger.warning("Error parsing record from line: " + line + " - " + e.getMessage());
        }

        return null;
    }

}
