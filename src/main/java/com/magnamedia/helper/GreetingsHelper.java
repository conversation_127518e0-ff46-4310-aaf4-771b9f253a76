package com.magnamedia.helper;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.entity.Contract;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created at Jun 20, 2019
 * <AUTHOR> kanaan <<EMAIL>>
 * Jirra ACC-1092
 */
public class GreetingsHelper {

    public static String getGreetingsParamName(String lang) {
        if (lang.equals("en")) {
            return "@greetings@";
        }
        return "@"
                + "greetings"
                + "_" + lang
                + "@";
    }
    public static String getGreetingsParamName(PicklistItem nat) {
        String lang=getLanguageCode(nat);
        return getGreetingsParamName(lang);
    }

    public static String getGreetingsValue(Contract c, PicklistItem nat) {
        String lang=getLanguageCode(nat);
        return getGreetingsValue(c,lang);
    }
    public static String getGreetingsValue(Contract c, String lang) {
        String code = "";
        if ("maids.cc_prospect".equals(c.getContractProspectType().getCode())) {
            code += "maidcc_greetings";
        } else if ("maidvisa.ae_prospect".equals(c.getContractProspectType().getCode())) {
            code += "maidvisa_greetings";
        }
        if (!lang.equals("en")) {
            code += "_" + lang;
        }else
            code += "_en"; 
        
        return Setup.getParameter(Setup.getCurrentModule(), code);
    }  
    public static String getLanguageName(PicklistItem nationality) {
        if (nationality == null) {
            return null;
        }
        if (nationality.getCode() == null) {
            return null;
        }
        switch (nationality.getCode()) {
            case "kenyan":
                return "Swahili";
            case "philippines": 
                return "Tagalog";
            case "ethiopian":
                return "Amharic";
            case "Congolese":
                return "French";
            case "Cameroonian":
                return "French";
            case "nigerian":
                return "French";
            case "ivoirienne":
                return "French";
            default:
                return "English";
                 
        }
    }  
    public static String getLanguageCode(PicklistItem nationality) {
        if (nationality == null) {
            return null;
        }
        if (nationality.getCode() == null) {
            return null;
        }
        switch (nationality.getCode()) {
            case "kenyan":
                return "sw";
            case "philippines":
                return "tg";
            case "ethiopian":
                return "am";
            case "Congolese":
                return "fr";
            case "Cameroonian":
                return "fr";
            case "nigerian":
                return "fr";
            case "ivoirienne":
                return "fr";
            default:
                return "en";   
        }
    }

}
