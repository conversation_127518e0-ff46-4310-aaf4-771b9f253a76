package com.magnamedia.report;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Sep 06, 2020
 *         Jirra ACC-2523
 */

public class OCR_Stats_Report extends BaseReport {

    String[] headers = new String[]{"Function", "Last 24 hours", "Last 7 days", "Last 30 days"};
    Table table;

    public OCR_Stats_Report() {
        TableTitle tt = new TableTitle("Table 7 - External Functions Performance").withAlign(Align.Center).withBackColor(Color.Grey);

        List<Column> columnList = new ArrayList<>();

        for (String header : headers) {
            columnList.add(new Column(new ColumnTitle(header).withAlign(Align.Center).withBold(true)));
        }
        Column[] columns = new Column[headers.length];
        columnList.toArray(columns);

        table = new Table(tt, columns);
    }

    @Override
    public void build() {
        addSection(table);
    }

    public void addRow(String tag, Map<String, Object> data) {
        Cell[] res = new Cell[headers.length];
        res[0] = new Cell("OCR - " + tag);
        res[1] = new Cell(data.get("today") + "%").withFontColor((Integer) data.get("today") < 80 ? Color.Red : Color.Green);
        res[2] = new Cell(data.get("lastWeek") + "%").withFontColor((Integer) data.get("lastWeek") < 80 ? Color.Red : Color.Green);
        res[3] = new Cell(data.get("lastMonth") + "%").withFontColor((Integer) data.get("lastMonth") < 80 ? Color.Red : Color.Green);
        table.addRow(res);
    }
}
