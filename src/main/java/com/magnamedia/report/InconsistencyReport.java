/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.report;

import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.helper.DateUtil;

import java.util.Date;

/**
 *
 * <AUTHOR>
 * @param <S>
 */
public interface InconsistencyReport<S extends BaseEntity> extends TemplatedReport<S> {

    static String format(Date d) {
        return DateUtil.formatFullDate(d);
    }

    static String format(Double salary) {
        return String.format("%.2f", salary == null ? 0.0 : salary);
    }

    default void fix() {
        throw new UnsupportedOperationException(String.format("Cannot fix %s", this.getHeaders()));
    }

    default boolean defaultFix() {
        return false;
    }
}
