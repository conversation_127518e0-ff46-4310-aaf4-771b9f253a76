package com.magnamedia.report;

import com.magnamedia.core.entity.PicklistItem;

import java.util.ArrayList;
import java.util.List;

public class CheckOutErrorCodeIssueReport extends BaseReport {
    String[] headers = new String[]{ "Code", "Name"};
    Table table;
    private List<PicklistItem> errorCodes;

    public CheckOutErrorCodeIssueReport() {

    }

    public CheckOutErrorCodeIssueReport(List<PicklistItem> errorCodes) {
        TableTitle tt = new TableTitle("");

        List<Column> columnList = new ArrayList<>();

        String style = "border: 1px solid black  !important; background-color: lightgray !important; text-align: center !important;";
        for (String header : headers) {
            columnList.add(new Column(new ColumnTitle(header).withBold(true).withStyle(style)));
        }

        Column[] columns = new Column[headers.length];
        columnList.toArray(columns);

        this.table = new Table(tt, columns).withStyle("width: 100% !important");

        this.errorCodes = errorCodes;
    }

    @Override
    public void build() {
        addSection(table);

        for (PicklistItem record : errorCodes) {
            addRow(record);
        }
    }

    public void addRow(PicklistItem p) {

        String style = "border: 1px solid black  !important; text-align: center !important;";
        Cell[] res = new Cell[headers.length];
        res[0] = new Cell(p.getCode()).withStyle(style);
        res[1] = new Cell(p.getName()).withStyle(style);

        table.addRow(res);
    }

}
