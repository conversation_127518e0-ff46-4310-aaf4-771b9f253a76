package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.Payment;
import com.magnamedia.entity.Contract;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractPaymentTermReason;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.ContractPaymentTermRepository;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.service.ClientMessagingAndRefundService;
import com.magnamedia.service.SwitchingNationalityService;
import org.joda.time.DateTime;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import org.joda.time.LocalDate;

/**
 * <AUTHOR> Masod <<EMAIL>>
 *         Created on Jan 06, 2021
 *          ACC-2933
 */

// handle by switch nationality flow -> add refund
@BusinessRule(entity = Payment.class, events = {BusinessEvent.AfterCreate, BusinessEvent.AfterUpdate},
        fields = {"id", "directDebit.id", "status", "replacementFor.id", "dateOfPayment", "typeOfPayment.id"})
public class PaymentReceivalAfterSwitchingNationality implements BusinessAction<Payment> {
    private static final Logger logger = Logger.getLogger(PaymentReceivalAfterSwitchingNationality.class.getName());
    private static final String prefix = "MMM ";

    @Override
    public boolean validate(Payment entity, BusinessEvent event) {
        logger.log(Level.SEVERE, prefix + "PaymentReceivalAfterSwitchingNationality Validation.");

        logger.log(Level.SEVERE, prefix + prefix + "payment id: " + (entity.isNewInstance() ? "new instance" : entity.getId()));

        HistorySelectQuery<Payment> historyQuery = new HistorySelectQuery(Payment.class);
        historyQuery.filterBy("id", "=", entity.getId());
        historyQuery.filterByChanged("status");
        historyQuery.sortBy("lastModificationDate", false, true);

        List<Payment> oldPayments = historyQuery.execute();
        Payment old = null;

        if (oldPayments != null && !oldPayments.isEmpty()) {
            old = oldPayments.get(0);
            logger.log(Level.SEVERE, prefix + "old payment founded");
        }

        if (entity.getStatus() == null) {
            logger.log(Level.SEVERE, prefix + "STATUS IS NULL");
            return false;
        }

        List<DirectDebit> directDebits = getReplacedDDs(entity);

        logger.log(Level.SEVERE, prefix + prefix + "Replaced DDs Size" + (directDebits.size()));

        return entity.getStatus().equals(PaymentStatus.RECEIVED) &&
                (old == null || old.getStatus() == null || !old.getStatus().equals(PaymentStatus.RECEIVED)) &&
                !directDebits.isEmpty();
    }

    @Override
    public Map execute(Payment entity, BusinessEvent even) {
        logger.log(Level.SEVERE, prefix + "PaymentReceivalAfterSwitchingNationality Execution.");

        SwitchingNationalityService switchingNationalityService = Setup.getApplicationContext().getBean(SwitchingNationalityService.class);
        ClientMessagingAndRefundService clientMessagingAndRefundService = Setup.getApplicationContext().getBean(ClientMessagingAndRefundService.class);
        ContractPaymentTermRepository cptRepository = Setup.getRepository(ContractPaymentTermRepository.class);
        DirectDebitRepository ddRepository = Setup.getRepository(DirectDebitRepository.class);

        List<DirectDebit> directDebits = getReplacedDDs(entity);

        for (DirectDebit directDebit : directDebits) {
            ContractPaymentTerm currentCPT = directDebit.getContractPaymentTerm();
            logger.log(Level.SEVERE, prefix + "PaymentReceivalAfterSwitchingNationality directDebit id "
                    + directDebit.getId());

            // ACC-2933
            if (directDebit.isRequiredAfterSwitching()) {
                directDebit.setRequiredAfterSwitching(false);
                ddRepository.save(directDebit);
                Contract contract = currentCPT.getContract();
                ContractPaymentTerm prevCPT = cptRepository.findFirstByContractAndIdLessThanOrderByIdDesc(contract, currentCPT.getId());

                // ACC-2933
                List<DirectDebit> requiredDDsAfterSwitching = ddRepository.findByContractPaymentTermAndRequiredAfterSwitchingAndIdNotIn(currentCPT,
                        true, Arrays.asList(directDebit.getId()));

                logger.log(Level.SEVERE, prefix + "PaymentReceivalAfterSwitchingNationality requiredDDsAfterSwitching" +
                        " size id " + requiredDDsAfterSwitching.size());
                
                if (requiredDDsAfterSwitching == null || requiredDDsAfterSwitching.isEmpty()) {
                    Double switchingMonthOldAmount = switchingNationalityService.getCPTAmountAtTime(currentCPT.getSwitchOrReplaceNationalityDate(), prevCPT, false);
                    Double switchingMonthNewAmount = switchingNationalityService.getCPTAmountAtTime(currentCPT.getSwitchOrReplaceNationalityDate(), currentCPT, false);
                    Double amountToRefund = switchingMonthOldAmount - switchingMonthNewAmount;
                    
                    logger.log(Level.SEVERE, prefix + "PaymentReceivalAfterSwitchingNationality amountToRefund " + amountToRefund);
                    Boolean addRefund = true;

                    if (contract.getStatus().equals(ContractStatus.CANCELLED)) {
                        ContractPaymentTerm lastCreatedCPT = cptRepository
                                .findFirstByContractAndReasonOrderByCreationDateDesc(
                                        contract, ContractPaymentTermReason.SWITCHING);
                        DateTime replacementDate = lastCreatedCPT != null && lastCreatedCPT.getReplacement() !=null ?
                                new DateTime(lastCreatedCPT.getReplacement().getCreationDate()) : new DateTime();

                        boolean bypassPaymentReceived = new LocalDate(entity.getDateOfPayment().getTime()).toString("yyyy-MM")
                                .equals(new LocalDate(contract.getDateOfTermination().getTime()).toString("yyyy-MM")) &&
                                entity.getTypeOfPayment().equals(
                                        Setup.getItem("TypeOfPayment", "monthly_payment"));
                        
                        addRefund = switchingNationalityService
                                .downgradeFlowCheckContractCancelledBeforeAddRefund(contract, replacementDate, bypassPaymentReceived);
                    }
                    logger.log(Level.SEVERE, prefix + "PaymentReceivalAfterSwitchingNationality addRefund " + addRefund);

                    if (amountToRefund > 0 && addRefund) {
                        clientMessagingAndRefundService.refundAfterReplacement(contract, amountToRefund,
                                AccountingModule.PAYMENT_REQUEST_PURPOSE_SWITCHING_TO_A_CHEAPER_NATIONALITY_REFUND,
                            "Downgrading switching nationality flow");
                    }
                }
            }
        }

        return null;
    }

    private List<DirectDebit> getReplacedDDs(Payment entity) {
        List<DirectDebit> directDebits = new ArrayList();
        DirectDebitRepository directDebitRepository = Setup.getRepository(DirectDebitRepository.class);

        if (entity.getDirectDebit() != null && entity.getDirectDebit().getId() != null) {
            directDebits.add(directDebitRepository.findOne(entity.getDirectDebit().getId()));
        }

        if (entity.getReplacementFor() != null && entity.getReplacementFor().getId() != null) {
            List<Payment> replacedPayments = getReplacedPayments(entity);

            directDebits.addAll(replacedPayments.stream()
                    .filter(payment -> payment.getDirectDebit() != null)
                    .map(payment -> payment.getDirectDebit())
                    .collect(Collectors.toList()));
        }

        return directDebits;
    }

    private List<Payment> getReplacedPayments(Payment payment) {
        List<Payment> payments = new ArrayList();
        if (payment.getReplacementFor() == null || payment.getReplacementFor().getId() == null) return payments;

        PaymentRepository paymentRepository = Setup.getRepository(PaymentRepository.class);
        Payment replacedPayment = paymentRepository.findOne(payment.getReplacementFor().getId());
        payments.add(replacedPayment);
        while (replacedPayment.getReplacementFor() != null) {
            replacedPayment = replacedPayment.getReplacementFor();
            payments.add(replacedPayment);
        }

        return payments;
    }
}
