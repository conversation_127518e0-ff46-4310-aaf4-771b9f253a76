package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.Payment;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.type.HousemaidType;
import com.magnamedia.repository.ContractRepository;

import java.util.Map;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 *
 * <AUTHOR>
 */
//FO-391
// ACC-8228
@BusinessRule(moduleCode = "", entity = Payment.class,
        events = {BusinessEvent.AfterCreate},
        fields = {"id", "contract.id", "typeOfPayment.code"})
public class MaidVisaRequestRule implements BusinessAction<Payment> {

    private static final Logger logger =
            Logger.getLogger(MaidVisaRequestRule.class.getName());

    @Override
    public boolean validate(Payment entity, BusinessEvent event) {
        logger.log(Level.INFO, "Start Maid Visa Request Rule");

        PicklistItem same_day_recruitment_fee = PicklistHelper.getItem("TypeOfPayment", "same_day_recruitment_fee");
        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
        if (same_day_recruitment_fee.getCode().equals(entity.getTypeOfPayment().getCode()) && entity.getContract() != null) {
            Contract contract = contractRepository.findOne(entity.getContract().getId());
            if (contract.getHousemaid() != null && contract.getHousemaid().getHousemaidType().equals(HousemaidType.MAID_VISA)
                    && contract.getHousemaid().getVisaNewRequest() != null
                    && contract.getWorkerCurrentSituation() != null
                    && contract.getWorkerCurrentSituation().getCode().equalsIgnoreCase("outside_country")) {
                logger.log(Level.INFO, "Maid Visa Request Rule validate returns true");
                return true;
            }
        }
        return false;
    }

    @Override
    public Map<String, Object> execute(Payment entity, BusinessEvent even) {

        Setup.getApplicationContext()
                .getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "maidVisaRequestRule_" + entity.getContract().getId(),
                        "visa",
                        "maidVisaRequestRule",
                        "execute")
                        .withRelatedEntity("Contract", entity.getContract().getId())
                        .withParameters(new Class[] { Long.class },
                                new Object[]{ entity.getContract().getId() })
                        .withQueue(BackgroundTaskQueues.SequentialQueue)
                        .build());

        return null;
    }
}
