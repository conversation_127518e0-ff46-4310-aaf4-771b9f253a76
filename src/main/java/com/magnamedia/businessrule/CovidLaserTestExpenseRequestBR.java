package com.magnamedia.businessrule;

import com.magnamedia.controller.ExpenseRequestTodoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Expense;
import com.magnamedia.entity.ExpenseRelatedTo;
import com.magnamedia.entity.LogisticsWorkOrder;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ExpenseBeneficiaryType;
import com.magnamedia.module.type.ExpenseRequestType;
import com.magnamedia.repository.ExpenseRepository;
import com.magnamedia.repository.ExpenseRequestTodoRepository;
import com.magnamedia.repository.LogisticsWorkOrderRepository;

import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@BusinessRule(moduleCode = "", entity = LogisticsWorkOrder.class, events = {
        BusinessEvent.AfterUpdate}, fields = {"id", "housemaid.id", "rideUrl", "requestCovidLaserTest"})
public class CovidLaserTestExpenseRequestBR implements BusinessAction<LogisticsWorkOrder> {

    @Override
    public boolean validate(LogisticsWorkOrder entity, BusinessEvent event) {
        Logger logger = Logger.getLogger(CovidLaserTestExpenseRequestBR.class.getName());
        logger.log(Level.INFO, "CovidLaserTestExpenseRequestBR validation");
        logger.log(Level.INFO, "CovidLaserTestExpenseRequestBR validation " + entity.getHousemaid());
        logger.log(Level.INFO, "CovidLaserTestExpenseRequestBR validation " + entity.getRideUrl());
        logger.log(Level.INFO, "CovidLaserTestExpenseRequestBR validation " + entity.getRequestCovidLaserTest());
        if (entity.getHousemaid() == null || entity.getRideUrl() == null || !entity.getRequestCovidLaserTest())
            return false;
        ExpenseRequestTodoRepository expenseRequestTodoRepository = Setup.getRepository(ExpenseRequestTodoRepository.class);

        List<ExpenseRequestTodo> todos = expenseRequestTodoRepository.findByCovidLaserTestTaxiOrderAndExpenseRequestType(entity, ExpenseRequestType.COVID_LASER_TEST);
        logger.log(Level.INFO, "CovidLaserTestExpenseRequestBR validation " + todos.size());
        if (todos != null && !todos.isEmpty())
            return false;
        return true;
    }

    @Override
    public Map<String, Object> execute(LogisticsWorkOrder entity, BusinessEvent even) {
        Logger.getLogger(CovidLaserTestExpenseRequestBR.class.getName()).log(Level.INFO, "CovidLaserTestExpenseRequestBR execution");
        ExpenseRepository expenseRepository = Setup.getRepository(ExpenseRepository.class);
        LogisticsWorkOrderRepository logisticsWorkOrderRepository = Setup.getRepository(LogisticsWorkOrderRepository.class);
        LogisticsWorkOrder dbTaxiOrder = logisticsWorkOrderRepository.findOne(entity.getId());

        String covidExpenseCode = Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_COVID_LASER_TEST_EXPENSE_CODE);
        Expense expense = expenseRepository.findOneByCode(covidExpenseCode);
        if (expense == null)
            throw new RuntimeException("Covid19 Laser Test expense setup doesn't exist, please add it with code " + covidExpenseCode + ".");
        if (expense.getBeneficiaryType() == null || !expense.getBeneficiaryType().equals(ExpenseBeneficiaryType.SUPPLIER))
            throw new RuntimeException(covidExpenseCode + " expense beneficiary type should be Supplier");
        if (expense.getSuppliers() == null || expense.getSuppliers().isEmpty() || expense.getSuppliers().size() > 1)
            throw new RuntimeException(covidExpenseCode + " expense must have one supplier");
        if (expense.getPaymentMethods() == null || expense.getPaymentMethods().isEmpty() || expense.getPaymentMethods().size() > 1)
            throw new RuntimeException(covidExpenseCode + " expense must have one payment method");
        if (expense.getDefaultAmount() == null)
            throw new RuntimeException(covidExpenseCode + " expense must have default amount");
        ExpenseRequestTodo expenseRequestTodo = new ExpenseRequestTodo();
        expenseRequestTodo.setExpense(expense);
        expenseRequestTodo.setBeneficiaryId(expense.getSuppliers().get(0).getId());
        expenseRequestTodo.setBeneficiaryType(ExpenseBeneficiaryType.SUPPLIER);
        expenseRequestTodo.setPaymentMethod(expense.getPaymentMethods().iterator().next());
        expenseRequestTodo.setRelatedToId(entity.getHousemaid().getId());
        expenseRequestTodo.setRelatedToType(ExpenseRelatedTo.ExpenseRelatedToType.MAID);
        expenseRequestTodo.setExpenseRequestType(ExpenseRequestType.COVID_LASER_TEST);
        expenseRequestTodo.setAmount(expense.getDefaultAmount());
        expenseRequestTodo.setCovidLaserTestTaxiOrder(dbTaxiOrder);
        ExpenseRequestTodoController expenseRequestTodoController = Setup.getApplicationContext().getBean(ExpenseRequestTodoController.class);
        expenseRequestTodoController.createEntity(expenseRequestTodo);

        return null;
    }

}
