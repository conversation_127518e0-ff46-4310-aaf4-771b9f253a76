/*
package com.magnamedia.businessrule;

import com.magnamedia.controller.ClientRefundTodoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.Payment;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.entity.workflow.PaymentRequestPurpose;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.PaymentRequestPurposeRepository;
import com.magnamedia.workflow.type.ClientRefundPaymentMethod;
import com.magnamedia.workflow.type.ClientRefundRequestType;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

*/
/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Aug 29, 2020
 *         Jirra ACC-2475
 *//*

// Last payment created on 2021-03-22 18:21:19
// create ClientRefundToDo if payment.getRefundPurpose() != null
@BusinessRule(moduleCode = "clientmgmt", entity = Payment.class, events = {BusinessEvent.AfterUpdate},
        fields = {"id", "status", "refundAmount", "clientRefundToDo.id", "refundPurpose.id", "contract.id"})
public class UndoTerminationFlowPaymentRefundBR implements BusinessAction<Payment> {
    private static final Logger logger =
            Logger.getLogger(UndoTerminationFlowPaymentRefundBR.class.getName());
    private static final String prefix = "MMM ";


    @Override
    public boolean validate(Payment entity, BusinessEvent businessEvent) {
        logger.log(Level.INFO, "start validation for UndoTerminationFlowPaymentRefundBR for payment: " + entity.getId());

        return entity.getStatus() != null && entity.getStatus().equals(PaymentStatus.RECEIVED) &&
                entity.getRefundAmount() != null && entity.getRefundAmount() > 0 &&
                (entity.getClientRefundToDo() == null || entity.getClientRefundToDo().getId() == null) &&
                entity.getRefundPurpose() != null && entity.getRefundPurpose().getId() != null &&
                entity.getContract() != null && entity.getContract().getId() != null;
    }

    @Override
    public Map<String, Object> execute(Payment entity, BusinessEvent businessEvent) {
        logger.log(Level.INFO, "start execution of UndoTerminationFlowPaymentRefundBR for payment: " + entity.getId());

        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
        PaymentRequestPurposeRepository paymentRequestPurposeRepository = Setup.getRepository(PaymentRequestPurposeRepository.class);

        Contract contract = contractRepository.findOne(entity.getContract().getId());
        if (contract == null) {
            logger.log(Level.INFO, "Contract#" + entity.getContract().getId() + " Not Found");
            return null;
        }

        Client client = contract.getClient();

        PaymentRequestPurpose paymentRequestPurpose = paymentRequestPurposeRepository.findOne(entity.getRefundPurpose().getId());
        if (paymentRequestPurpose == null) {
            logger.log(Level.INFO, "PaymentRequestPurpose#" + entity.getRefundPurpose().getId() + " Not Found");
            return null;
        }

        ClientRefundTodoController clientRefundController = Setup.getApplicationContext().getBean(ClientRefundTodoController.class);
        // Last record created with the same type on 2021-04-05
        ClientRefundToDo clientRefundToDo = new ClientRefundToDo();
        clientRefundToDo.setAutomaticRefund(true);
        clientRefundToDo.setFlowTriggered("Undo termination flow");
        clientRefundToDo.setPurpose(paymentRequestPurpose);
        clientRefundToDo.setRequestType(ClientRefundRequestType.DISCOUNT_REFUND);
        clientRefundToDo.setContract(contract);
        client.setContractPaymentTermInfo(contract);

        clientRefundToDo.setClient(client);
        clientRefundToDo.setMethodOfPayment(ClientRefundPaymentMethod.BANK_TRANSFER);
        clientRefundToDo.setAmount(entity.getRefundAmount());
        clientRefundToDo.setIban(client.getClientIBAN());
        clientRefundToDo.setAccountName(client.getAccountName());

        clientRefundToDo = (ClientRefundToDo) clientRefundController.createEntity(clientRefundToDo).getBody();

        Map<String, Object> clientRefundToDoMap = new HashMap<>();
        clientRefundToDoMap.put("id", clientRefundToDo.getId());

        //ACC-6103
        Map<String, Object>  paymentMap = new HashMap<>();
        paymentMap.put("clientRefundToDo", clientRefundToDoMap);

        logger.info("update Payment Async, #" + entity.getId());

        return paymentMap;
    }
}*/
