package com.magnamedia.businessrule;

import com.magnamedia.controller.DirectDebitFileController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.Position;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.repository.PositionRepository;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.core.type.BusinessEvent;

import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Nov 28, 2020
 *         Jirra ACC-2829
 */
@BusinessRule(entity = User.class, events = {BusinessEvent.AfterUpdate}, fields = {"id", "positions.code"})
public class DDFormDownloadPositionBR implements BusinessAction<User> {
    private static final Logger logger = Logger.getLogger(DDFormDownloadPositionBR.class.getName());

    @Override
    public boolean validate(User entity, BusinessEvent event) {
        logger.log(Level.SEVERE, "User ID#" + entity.getId());
        logger.log(Level.SEVERE, "User Positions " + (entity.getPositions() == null ? "NULL" : entity.getPositions().size()));

        if (entity.getPositions() != null) {
            entity.getPositions().forEach(position -> logger.log(Level.SEVERE, "Position Code: " + position.getCode()));
        }

        return entity.getPositions() != null && !entity.getPositions().isEmpty()
                && entity.getPositions().stream().anyMatch(position -> position.getCode().equalsIgnoreCase(DirectDebitFileController.DD_FORM_DOWNLOAD_POSITION));
    }

    @Override
    public Map execute(User entity, BusinessEvent even) {
        PositionRepository positionRepository = Setup.getRepository(PositionRepository.class);
        UserRepository userRepository = Setup.getRepository(UserRepository.class);

        Position ddForDownloadPosition = positionRepository.findByCode(DirectDebitFileController.DD_FORM_DOWNLOAD_POSITION);

        List<User> positionUsers = userRepository.findByPositionOrderByFullNameAsc(ddForDownloadPosition);
        boolean isThereOtherUserHasThePosition = positionUsers != null && positionUsers.stream().anyMatch(user -> !user.getId().equals(entity.getId()));

        if (isThereOtherUserHasThePosition) {
            throw new RuntimeException(ddForDownloadPosition.getName() + " can't be granted for more than one User");
        }

        return null;
    }
}
