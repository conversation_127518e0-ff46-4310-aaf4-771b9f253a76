package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.PushNotification;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.HousemaidAttendanceLog;
import com.magnamedia.helper.PushNotificationHelper;
import com.magnamedia.repository.ContractRepository;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jan 25, 2021
 *         Jirra ACC-2956
 */

@BusinessRule(entity = HousemaidAttendanceLog.class, events = {BusinessEvent.AfterCreate, BusinessEvent.AfterUpdate},
        fields = {"id", "housemaid.id", "logType"})
public class HousemaidAttendanceBR implements BusinessAction<HousemaidAttendanceLog> {
    private static final Logger logger =
            Logger.getLogger(HousemaidAttendanceBR.class.getName());

    @Override
    public boolean validate(HousemaidAttendanceLog entity, BusinessEvent event) {
        logger.info("ID: " + entity.getId());
        logger.info("Log Type: " + entity.getLogType());
        logger.info("Attendance Status: " + entity.getAttendanceStatus());

        if (event.equals(BusinessEvent.AfterCreate)) {
            if (entity.getLogType() != null && entity.getLogType().equals("UNEXPECTED_MAIDS")) {
                logger.info("the maid shows up in unexpected attendance");
                return true;
            }
        } else if (event.equals(BusinessEvent.AfterUpdate)) {
            HistorySelectQuery<HousemaidAttendanceLog> query = new HistorySelectQuery(HousemaidAttendanceLog.class);
            query.filterBy("id", "=", entity.getId());
            query.filterByChanged("attendanceStatus");
            query.sortBy("lastModificationDate", false, true);
            query.setLimit(1);

            List<HousemaidAttendanceLog> revisions = query.execute();


            if (revisions == null || revisions.isEmpty()) {
                logger.severe("No Revisions -> do nothing");
                return false;
            }

            HousemaidAttendanceLog revision = revisions.get(0);

            boolean attendanceStatusChangedToPresent = entity.getAttendanceStatus() != null && entity.getAttendanceStatus().equals(HousemaidAttendanceLog.AttendanceStatus.PRESENT) &&
                    (revision.getAttendanceStatus() == null || !revision.getAttendanceStatus().equals(HousemaidAttendanceLog.AttendanceStatus.PRESENT));

            if (attendanceStatusChangedToPresent &&
                    entity.getLogType() != null && Arrays.asList("MORNING_ROLL_CALL", "EVENING_ROLL_CALL").contains(entity.getLogType())) {
                logger.info("the maid appears in attendance");
                return true;
            }
        }

        return false;
    }

    @Override
    public Map<String, Object> execute(HousemaidAttendanceLog entity, BusinessEvent even) {

        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
        List<Contract> contracts = contractRepository.findByHousemaid(entity.getHousemaid());
        PushNotificationHelper pushNotificationHelper = Setup.getApplicationContext().getBean(PushNotificationHelper.class);

        for (Contract contract : contracts) {
            logger.info("Checking Contract#" + contract.getId());
            List<PushNotification> taxiWorkOrderNotifications = pushNotificationHelper.getByOwnerTypeAndId("OnCloseTaxiWorkOrder", contract.getId());

            logger.log(Level.SEVERE, "founded taxiWorkOrderNotifications: " + taxiWorkOrderNotifications.size());

            pushNotificationHelper.stopDisplaying(taxiWorkOrderNotifications);
        }

        return null;
    }
}
