package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.ContractPaymentTerm;

import java.util.List;
import java.util.Map;


@BusinessRule(entity = ContractPaymentTerm.class, events = {BusinessEvent.AfterCreate, BusinessEvent.AfterUpdate},
        fields = {"id", "contract.id", "creditNote", "creditNoteMonths"})
public class AccountingContractPaymentTermBR implements BusinessAction<ContractPaymentTerm>{

    @Override
    public boolean validate(ContractPaymentTerm entity, BusinessEvent event) {

        if (event.isCreateEvent()) {
            return entity.getCreditNoteMonths() != null && entity.getCreditNoteMonths() > 0 &&
                    entity.getCreditNote() != null && entity.getCreditNote() > 0;
        }

        HistorySelectQuery<ContractPaymentTerm> historyQuery = new HistorySelectQuery<>(ContractPaymentTerm.class);
        historyQuery.filterBy("id", "=", entity.getId());
        historyQuery.sortBy("lastModificationDate", false, true);
        historyQuery.setLimit(1);

        List<ContractPaymentTerm> l = historyQuery.execute();
        ContractPaymentTerm old = l.isEmpty() ? null : l.get(0);
        return (old != null &&
                (((old.getCreditNoteMonths() == null || old.getCreditNoteMonths() <= 0)
                        && entity.getCreditNoteMonths() != null && entity.getCreditNoteMonths() > 0) ||
                (old.getCreditNoteMonths() != null && old.getCreditNoteMonths() > 0
                        && (entity.getCreditNoteMonths() == null || entity.getCreditNoteMonths() <= 0))));
    }
    
    @Override
    public Map execute(ContractPaymentTerm entity, BusinessEvent event) {

        Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "generateCreditNoteForNewCpt_" + entity.getId(),
                        "accounting",
                        "contractPaymentTermDetailsService",
                        "generateCreditNoteForNewCpt")
                        .withRelatedEntity("ContractPaymentTerm", entity.getId())
                        .withParameters(
                                new Class[] {Long.class},
                                new Object[] {entity.getId()})
                        .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                        .build());

        return null;
    }
}