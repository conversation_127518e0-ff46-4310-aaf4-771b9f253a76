package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Payment;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.repository.TransactionRepository;
import com.magnamedia.service.PaymentService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> masod <<EMAIL>>
 *         Created on Dec 21, 2019
 *         ACC-1230
 */
@BusinessRule(moduleCode = "", entity = Payment.class,
        events = {BusinessEvent.AfterUpdate, BusinessEvent.AfterCreate},
        fields = {"id", "contract.id", "methodOfPayment", "isInitial", "dateOfPayment", "typeOfPayment.id", "vat",
                "online", "status", "amountOfPayment", "note", "directDebit.id", "attachments.id",
                "attachments.name", "attachments.path", "attachments.size", "attachments.tag",
                "attachments.extension", "attachments.amazon", "attachments.ownerId",
                "attachments.ownerType", "tPEBrChecked", "updatedFromBankStatement", "bankStatmentTransactionId", "requiredForUnfitToWork", "ignorePostingEngineBR",
                "clientRefundToDo.id"})
public class TransactionPostingEngineBusinessRule implements BusinessAction<Payment> {

    private static final Logger logger =
            Logger.getLogger(TransactionPostingEngineBusinessRule.class.getName());

    @Override
    public boolean validate(Payment entity, BusinessEvent businessEvent) {

        logger.info("Validation payment id: " + entity.getId());

        if (entity.istPEBrChecked()) return false;

        if (entity.isIgnorePostingEngineBR()) {
            logger.info("ignorePostingEngine = True");
            return false;
        }


        // if it has been processed before (transaction posted for the payment before)
        if (entity.getId() != null && Setup.getRepository(TransactionRepository.class).existsByPaymentId(entity.getId())) {
            logger.info("payment already has related transaction");
            return false;
        }

        // ACC-5225
        if (entity.isOnline() && entity.getMethodOfPayment().equals(PaymentMethod.CARD)) {
            logger.info("online CARD payment");
            return false;
        }

        logger.log(Level.SEVERE, "TypeOfPayment " + entity.getTypeOfPayment());
        logger.log(Level.SEVERE, "MethodOfPayment " + entity.getMethodOfPayment());
        logger.log(Level.SEVERE, "PaymentStatus " + entity.getStatus());

        HistorySelectQuery<Payment> historyQuery = new HistorySelectQuery(Payment.class);
        historyQuery.filterBy("id", "=", entity.getId());
        historyQuery.sortBy("lastModificationDate", false, true);
        historyQuery.setLimit(1);
        
        List<Payment> oldPayments = historyQuery.execute();

        boolean statusChanged = oldPayments == null || oldPayments.isEmpty() ||
                !oldPayments.get(0).getStatus().equals(entity.getStatus());

        // if payment matches a posting rule
        return statusChanged;
    }

    @Override
    public Map<String, Object> execute(Payment entity, BusinessEvent businessEvent) {

        logger.log(Level.SEVERE, "TransactionPostingEngineBusinessRule execute.");

        Setup.getApplicationContext().getBean(PaymentService.class)
                .createTransactionForPayment(entity);

        return new HashMap<String, Object>(){{
            put("tPEBrChecked", true);
            put("updatedFromBankStatement", false);
        }};
    }
}