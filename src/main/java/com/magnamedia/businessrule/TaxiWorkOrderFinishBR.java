package com.magnamedia.businessrule;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.configuration.ObjectMapperConfiguration;
import com.magnamedia.core.entity.PushNotification;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.repository.PushNotificationRepository;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.core.type.FunctionType;
import com.magnamedia.core.type.NavigationType;
import com.magnamedia.entity.LogisticsWorkOrder;
import com.magnamedia.extra.StringUtils;
import com.magnamedia.helper.PushNotificationHelper;
import com.magnamedia.module.type.LogisticsWorkOrderPurpose;
import com.magnamedia.module.type.TaxiWorkOrderStatus;
import org.apache.commons.lang3.BooleanUtils;
import org.joda.time.DateTime;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Oct 11, 2020
 *         Jirra ACC-2677
 * 
 */

@BusinessRule(moduleCode = "", entity = LogisticsWorkOrder.class,
        events = {BusinessEvent.BeforeUpdate, BusinessEvent.AfterCreate},
        fields = {"id", "taxiWorkOrderStatus", "contract.id", "Purpose", "leaveOn", "initNote", "addedFromCCApp"})
public class TaxiWorkOrderFinishBR implements BusinessAction<LogisticsWorkOrder> {

    private static final Logger logger = Logger.getLogger(TaxiWorkOrderFinishBR.class.getName());

    @Override
    public boolean validate(LogisticsWorkOrder entity, BusinessEvent event) {

        logger.log(Level.SEVERE, "TaxiWorkOrderFinishBR Validation.");

        logger.log(Level.SEVERE, "TaxiWorkOrderFinishBR entity id: " + entity.getId());
        logger.log(Level.SEVERE, "TaxiWorkOrderFinishBR contract id: " + (entity.getContract() != null ? entity.getContract().getId() : "NULL"));
        logger.log(Level.SEVERE, "TaxiWorkOrderFinishBR entity status: " + entity.getTaxiWorkOrderStatus());

        if (entity.getContract() == null || entity.getContract().getId() == null) {
            return false;
        }

        if (event.equals(BusinessEvent.BeforeUpdate)) {
            HistorySelectQuery<LogisticsWorkOrder> historyQuery = new HistorySelectQuery<>(LogisticsWorkOrder.class);
            historyQuery.filterBy("id", "=", entity.getId());
            historyQuery.sortBy("lastModificationDate", false, true);
            historyQuery.setLimit(1);

            List<LogisticsWorkOrder> oldLogisticsWorkOrders = historyQuery.execute();
            LogisticsWorkOrder oldLogisticsWorkOrder = null;

            if (oldLogisticsWorkOrders != null && !oldLogisticsWorkOrders.isEmpty()) {
                oldLogisticsWorkOrder = oldLogisticsWorkOrders.get(0);
            }

            logger.log(Level.SEVERE, "TaxiWorkOrderFinishBR execute ddf old status:" +
                    (oldLogisticsWorkOrder != null ? oldLogisticsWorkOrder.getTaxiWorkOrderStatus() : "old oldLogisticsWorkOrder is null"));

            return (oldLogisticsWorkOrder == null && entity.getTaxiWorkOrderStatus() == TaxiWorkOrderStatus.CLOSED)
                    || (oldLogisticsWorkOrder != null && oldLogisticsWorkOrder.getTaxiWorkOrderStatus() != TaxiWorkOrderStatus.CLOSED
                    && entity.getTaxiWorkOrderStatus() == TaxiWorkOrderStatus.CLOSED)
                    || (oldLogisticsWorkOrder != null && !oldLogisticsWorkOrder.getLeaveOn().equals(entity.getLeaveOn()));
        } else if (event.equals(BusinessEvent.AfterCreate)) {
            boolean isEndOfContract = entity.getPurpose() != null && entity.getPurpose().equals(LogisticsWorkOrderPurpose.END_OF_CONTRACT);
            boolean isFromCcApp = BooleanUtils.toBoolean(entity.getAddedFromCCApp());

            return isEndOfContract || isFromCcApp;
        }

        return false;
    }

    @Override
    public Map<String, Object> execute(LogisticsWorkOrder entity, BusinessEvent event) {

        logger.log(Level.SEVERE, "TaxiWorkOrderFinishBR execute.");


        if (event.equals(BusinessEvent.BeforeUpdate)) {
            logger.log(Level.SEVERE, "before update.");
            stopDisplayingNotification(entity);
        } else {

            boolean isEndOfContract = entity.getPurpose() != null && entity.getPurpose().equals(LogisticsWorkOrderPurpose.END_OF_CONTRACT);
            boolean isFromCcApp = BooleanUtils.toBoolean(entity.getAddedFromCCApp());

            if (isEndOfContract) {
                doReschedule(entity);
            }

            if (isFromCcApp) {
                stopDisplayingNotification(entity);
            }

            logger.log(Level.SEVERE, "before create.");
        }

        logger.log(Level.SEVERE, "TaxiWorkOrderFinishBR execute ends");

        return null;
    }

    private void stopDisplayingNotification(LogisticsWorkOrder entity) {
        PushNotificationHelper pushNotificationHelper = Setup.getApplicationContext().getBean(PushNotificationHelper.class);
        List<PushNotification> taxiWorkOrderNotifications = pushNotificationHelper.getByOwnerTypeAndId("OnCloseTaxiWorkOrder", entity.getContract().getId());
        logger.log(Level.SEVERE, "TaxiWorkOrderFinishBR founded taxiWorkOrderNotifications: " + taxiWorkOrderNotifications.size());

        pushNotificationHelper.stopDisplaying(taxiWorkOrderNotifications);
    }
    // To be removed after 2024-01-01
    //we kept it for backward compatibility, because send uber CTA was removed from ACC notification ACC-5235
    private void doReschedule(LogisticsWorkOrder entity) {
        Date date = DateTime.now().minusHours(1).toDate();

        PushNotificationHelper pushNotificationHelper = Setup.getApplicationContext().getBean(PushNotificationHelper.class);
        List<PushNotification> taxiWorkOrderNotifications = pushNotificationHelper.getByOwnerTypeAndIdAndCreationDateAfter("OnCloseTaxiWorkOrder", entity.getContract().getId(), date);
        logger.log(Level.SEVERE, "TaxiWorkOrderFinishBR founded taxiWorkOrderNotifications: " + taxiWorkOrderNotifications.size());

        if (taxiWorkOrderNotifications.isEmpty()) return;

        for (PushNotification pushNotification : taxiWorkOrderNotifications) {
            if (!StringUtils.isEmpty(pushNotification.getContext())) {
                try {
                    ObjectMapper objectMapper = Setup
                            .getApplicationContext()
                            .getBean(ObjectMapperConfiguration.class)
                            .objectMapper();
                    
                    objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);

                    Map<String, Object> context = objectMapper.readValue(pushNotification.getContext(), HashMap.class);
                    if (context.containsKey("@Action1@") && context.get("@Action1@") != null) {
                        Map<String, Object> actions = (Map) context.get("@Action1@");

                        actions.put("functionType", FunctionType.NAVIGATE);
                        actions.put("navigationType", NavigationType.INAPP);
                        actions.put("appRouteName", "/replace/uber_becka/send_uber");

                        if (actions.containsKey("appRouteArguments") && actions.get("appRouteArguments") != null) {
                            Map<String, String> appRouteArguments = (Map) actions.get("appRouteArguments");

                            if (appRouteArguments.containsKey("notificationArguments") && appRouteArguments.get("notificationArguments") != null) {
                                Map<String, String> notificationArguments = objectMapper.readValue(appRouteArguments.get("notificationArguments"), Map.class);

//                                notificationArguments.put("newHousemaidId", null);
                                notificationArguments.put("oldHousemaidId", notificationArguments.get("housemaidId"));
//                                notificationArguments.put("newHousemaidName", null);
                                notificationArguments.put("oldHousemaidName", notificationArguments.get("housemaidName"));
                                notificationArguments.put("contractId", notificationArguments.get("contractId"));
                                notificationArguments.put("reschedule", Boolean.TRUE.toString());

                                SimpleDateFormat dateFormat = new SimpleDateFormat(
                                        "EEEE, MMM dd 'at' hh:mm aaa");

                                notificationArguments.put("currentTaxiDateTime", dateFormat.format(entity.getLeaveOn()));
                                notificationArguments.put("luggageInClintHome", Boolean.FALSE.toString());
                                notificationArguments.put("newWorkOrder", Boolean.FALSE.toString());
                                notificationArguments.put("oldWorkOrder", Boolean.TRUE.toString());
                                notificationArguments.put("complaintCode", null);
                                notificationArguments.put("taxiWorkOrderId", entity.getId().toString());
                                notificationArguments.put("alreadyLeftHome", Boolean.FALSE.toString());
                                notificationArguments.put("luggageTaxiWorkOrderUuid", null);

                                // remove old fields
                                notificationArguments.remove("housemaidId");
                                notificationArguments.remove("housemaidName");

                                appRouteArguments.put("notificationArguments", objectMapper.writeValueAsString(notificationArguments));
                            }

                            actions.put("appRouteArguments", appRouteArguments);
                        }

                        context.put("@Action1@", actions);

                        pushNotification.setContext(objectMapper.writeValueAsString(context));

                        PushNotificationRepository pushNotificationRepo = Setup.getRepository(PushNotificationRepository.class);
                        pushNotificationRepo.save(pushNotification);
                    }
                } catch (Exception e) {
                    logger.severe("Exception while parsing notification context");
                    throw new RuntimeException(e);
                }
            }
        }
    }
}
