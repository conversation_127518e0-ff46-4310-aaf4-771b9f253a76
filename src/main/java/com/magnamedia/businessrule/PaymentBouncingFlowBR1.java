package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.entity.Payment;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.DirectDebitFileRepository;
import com.magnamedia.service.BouncingFlowService;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Apr 15, 2020
 *         Jirra ACC-1721
 *         this BR is for inserting Bouncing Log
 */

// add log in DB and mark the payment as required if the contract MV
// add bounced log + set payment as required + set trials 0 + set sentToBank = false
@BusinessRule(entity = Payment.class, events = {BusinessEvent.AfterCreate, BusinessEvent.AfterUpdate},
        fields = {"id", "dateOfPayment", "typeOfPayment.id", "amountOfPayment", "contract.id", "status",
                "directDebit.id", "directDebitFile.id", "bouncedPaymentLogs.id", "methodOfPayment",
                "pBF1BrChecked", "updatedFromBankStatement"})
public class PaymentBouncingFlowBR1 implements BusinessAction<Payment> {

    private static final Logger logger =
            Logger.getLogger(PaymentBouncingFlowBR1.class.getName());
    private static final String prefix = "MMM ";

    @Override
    public boolean validate(Payment entity, BusinessEvent event) {
        logger.log(Level.SEVERE, "PaymentBouncingFlowBR1 Validation.");

        if (entity.ispBF1BrChecked()) return false;

        logger.log(Level.SEVERE, prefix + "payment id: " + (entity.isNewInstance() ? "new instance" : entity.getId()));

        HistorySelectQuery<Payment> historyQuery = new HistorySelectQuery(Payment.class);
        historyQuery.filterBy("id", "=", entity.getId());
        historyQuery.filterByChanged("status");
        historyQuery.sortBy("lastModificationDate", false, true);

        List<Payment> oldPayments = historyQuery.execute();
        Payment old = null;

        if (!oldPayments.isEmpty()) {
            old = oldPayments.get(0);
        }

        return entity.getStatus() != null && entity.getStatus().equals(PaymentStatus.BOUNCED) &&
                (old == null || old.getStatus() == null || !old.getStatus().equals(PaymentStatus.BOUNCED));

    }

    @Override
    public Map execute(Payment entity, BusinessEvent even) {
        logger.log(Level.SEVERE, "PaymentBouncingFlowBR1 Execution.");
        BouncingFlowService bouncingFlowService = Setup.getApplicationContext().getBean(BouncingFlowService.class);
        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);

        Map<String, Object> map = new HashMap<>();

        map.put("trials", 0);
        map.put("sentToBankByMDD", Boolean.FALSE);
        map.put("pBF1BrChecked", true);

        DirectDebitFile ddf = null;

        if (entity.getDirectDebitFile() != null && entity.getDirectDebitFile().getId() != null) {
            DirectDebitFileRepository ddfRepository = Setup.getRepository(DirectDebitFileRepository.class);
            ddf = ddfRepository.findOne(entity.getDirectDebitFile().getId());
        }

        if ((entity.getUpdatedFromBankStatement() == null || !entity.getUpdatedFromBankStatement())
                && (entity.getBouncedPaymentLogs() == null || entity.getBouncedPaymentLogs().isEmpty())) {
            bouncingFlowService.addBouncedPaymentLog(entity, ddf, null, new Date());
        }

        //Jirra ACC-2503
        if (entity.getContract() != null && entity.getContract().getId() != null) {
            Contract contract = contractRepository.findOne(entity.getContract().getId());
            if (!Arrays.asList(ContractStatus.EXPIRED, ContractStatus.CANCELLED).contains(contract.getStatus()) &&
                    contract.getContractProspectType().getCode().equals(PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE))) {
                map.put("requiredForBouncing", Boolean.TRUE);
            }
        }
        return map;
    }
}
