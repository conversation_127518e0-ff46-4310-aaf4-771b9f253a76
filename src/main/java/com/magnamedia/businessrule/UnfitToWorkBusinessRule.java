package com.magnamedia.businessrule;

import com.magnamedia.controller.DirectDebitCancelationToDoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.Payment;
import com.magnamedia.module.type.DirectDebitCancellationToDoReason;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.service.PaymentService;
import org.joda.time.DateTime;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;


/**
 * <AUTHOR> <<EMAIL>>
 *         Created on May 27, 2020
 *         ACC-1633
 */

/* ACC-1633
 ** Once an MV maid is unfit medical and related to MV client,
 **  we need to take the next month payment we will not cancel direct debit until receiving this payment (flag the next month payment as required).
 ** If required payment is bounced we will keep trying each month until getting a payment then we can cancel Client's DD
 */
// if current payment mark as requiredForUnfitToWork -> flag the next month payment as requiredForUnfitToWork
@BusinessRule(moduleCode = "", entity = Payment.class,
        events = {BusinessEvent.BeforeUpdate},
        fields = {"id", "contract.id", "dateOfPayment", "status", "requiredForUnfitToWork"})
public class UnfitToWorkBusinessRule implements BusinessAction<Payment> {

    private static final Logger logger =
            Logger.getLogger(UnfitToWorkBusinessRule.class.getName());

    @Override
    public boolean validate(Payment entity, BusinessEvent businessEvent) {
        logger.log(Level.INFO, "start validation for UnfitToWorkBusinessRule for payment: " + entity.getId());
        HistorySelectQuery<Payment> query = new HistorySelectQuery<>(Payment.class);
        query.filterBy("id", "=", entity.getId());
        query.sortBy("lastModificationDate", false, true);
        List<Payment> paymentHistory = query.execute();

        logger.log(Level.INFO, "get payment History for payment: " + entity.getId());
        if (paymentHistory == null || paymentHistory.isEmpty()) {
            logger.log(Level.INFO, "no Historic entities for payment: " + entity.getId());
            return false;
        }

        Payment old = paymentHistory.get(0);
        logger.log(Level.INFO, "RequiredForUnfitToWork value for payment: " + entity.getId() + " is: " + entity.getRequiredForUnfitToWork());
        logger.log(Level.INFO, "status of payment: " + entity.getId() + " is: " + entity.getStatus());
        logger.log(Level.INFO, "status of old for payment: " + old.getId() + " is: " + old.getStatus());

        if (entity.getStatus() == null) return false;

        // if (RequiredForUnfitToWork) and (changed to received or bounced)
        return entity.getRequiredForUnfitToWork() &&
                ((entity.getStatus().equals(PaymentStatus.BOUNCED) && (old.getStatus() == null || !old.getStatus().equals(PaymentStatus.BOUNCED)))
                        || (entity.getStatus().equals(PaymentStatus.RECEIVED) && (old.getStatus() == null || !old.getStatus().equals(PaymentStatus.RECEIVED))));
    }

    @Override
    public Map<String, Object> execute(Payment entity, BusinessEvent businessEvent) {
        Map map = new HashMap();
        logger.log(Level.INFO, "start execution of UnfitToWorkBusinessRule for payment: " + entity.getId());
        logger.log(Level.INFO, "get contract: " + entity.getContract().getId() + " of payment: " + entity.getId());
        Contract contract = Setup.getRepository(ContractRepository.class).findOne(entity.getContract().getId());
        PaymentRepository paymentRepository = Setup.getRepository(PaymentRepository.class);
        DirectDebitCancelationToDoController directDebitCancelationToDoController = Setup.getApplicationContext().getBean(DirectDebitCancelationToDoController.class);
        PaymentService paymentService = Setup.getApplicationContext().getBean(PaymentService.class);

        // Received
        if (entity.getStatus().equals(PaymentStatus.RECEIVED)) {
            logger.log(Level.INFO, "status for payment: " + entity.getId() + "is changed to [Received]");
            logger.log(Level.INFO, "start calling createToDoIfValid to cancel the contract: " + contract.getId() + " for payment: " + entity.getId());
            
            directDebitCancelationToDoController.createToDoIfValid(contract, false, null,
                    DirectDebitCancellationToDoReason.PAYMENT_RECEIVAL_NO_NEED_MONEY);
            
            logger.log(Level.INFO, "finish calling createToDoIfValid to cancel the contract: " + contract.getId() + " for payment: " + entity.getId());
            logger.log(Level.INFO, "start changing RequiredForUnfitToWork values for payments of contract: " + contract.getId());

            List<Payment> otherContractPayments = paymentRepository.findByContractAndRequiredForUnfitToWorkAndIdNotIn(contract,
                    true, Arrays.asList(entity.getId()));

            for (Payment payment : otherContractPayments) {
                logger.log(Level.INFO, "RequiredForUnfitToWork value is [True] for payment: " + payment.getId() + " and we change to [False]");
                payment.setRequiredForUnfitToWork(false);
                paymentService.forceUpdatePayment(payment);

                logger.log(Level.INFO, "we will set the payment " + payment.getId() + " RequiredForUnfitToWork as [False]");
            }
            logger.log(Level.INFO, "finish changing RequiredForUnfitToWork values for payments of contract: " + contract.getId());

            map.put("requiredForUnfitToWork", Boolean.FALSE);
            return map;
        }

        // Bounced
        if (entity.getStatus().equals(PaymentStatus.BOUNCED)) {
            logger.log(Level.INFO, "status for payment: " + entity.getId() + "is changed to [Bounced]");

            Date startDate = new DateTime(entity.getDateOfPayment()).plusMonths(1).dayOfMonth().withMinimumValue().toDate();
            Date endDate = new DateTime(entity.getDateOfPayment()).plusMonths(1).dayOfMonth().withMaximumValue().toDate();
            PicklistItem monthlyPayment = Setup.getItem("TypeOfPayment", "monthly_payment");

            Payment nextPayment = paymentRepository.findFirstByContractAndStatusAndTypeOfPaymentAndDateOfPaymentGreaterThanEqualAndDateOfPaymentLessThanEqual(contract,
                    PaymentStatus.PDC, monthlyPayment, startDate, endDate);

            if (nextPayment == null) return null;

            logger.log(Level.INFO, "we found the next payment : " + nextPayment.getId() + " of payment: " + entity.getId());
            logger.log(Level.INFO, "we will set the payment : " + nextPayment.getId() + " RequiredForUnfitToWork as [True]");

            nextPayment.setRequiredForUnfitToWork(true);
            paymentService.forceUpdatePayment(nextPayment);

            logger.log(Level.INFO, "set the payment : " + nextPayment.getId() + " RequiredForUnfitToWork as [True]");
            logger.log(Level.INFO, "we didn't find the next payment of one: " + entity.getId());
        }

        return null;
    }
}
