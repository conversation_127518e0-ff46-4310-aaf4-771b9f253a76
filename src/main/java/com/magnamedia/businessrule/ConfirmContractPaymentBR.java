package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.ContractPayment;
import com.magnamedia.entity.Payment;
import com.magnamedia.repository.ContractPaymentRepository;
import com.magnamedia.repository.ContractPaymentTermRepository;
import org.joda.time.DateTime;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Mar 1, 2020
 *         Jirra ACC-1435
 */

@BusinessRule(entity = Payment.class,
        events = {BusinessEvent.AfterCreate},
        fields = {"contract.id", "dateOfPayment", "methodOfPayment", "typeOfPayment.id", "amountOfPayment"})
public class ConfirmContractPaymentBR implements BusinessAction<Payment> {

    @Override
    public boolean validate(Payment entity, BusinessEvent event) {
        return Setup.getRepository(ContractPaymentTermRepository.class)
                .existsByContract_IdAndIsActiveTrue(entity.getContract().getId());
    }

    @Override
    public Map execute(Payment entity, BusinessEvent even) {
        ContractPaymentRepository contractPaymentRep = Setup.getRepository(ContractPaymentRepository.class);

        DateTime startDate = new DateTime(entity.getDateOfPayment()).withHourOfDay(0).withMinuteOfHour(0)
                .withSecondOfMinute(0);
        DateTime endDate = new DateTime(entity.getDateOfPayment()).withHourOfDay(23).withMinuteOfHour(59)
                .withSecondOfMinute(59);

        List<ContractPayment> contractPayments = contractPaymentRep.findMatchedContractPayment(
                entity.getContract().getId(), entity.getMethodOfPayment(), entity.getTypeOfPayment().getId(),
                startDate.toDate(), endDate.toDate(), entity.getAmountOfPayment());

        if (contractPayments != null && !contractPayments.isEmpty()) {
            ContractPayment contractPayment = contractPayments.get(0);
            contractPayment.setConfirmed(Boolean.TRUE);
            contractPaymentRep.save(contractPayment);
        }
        return null;
    }
}
