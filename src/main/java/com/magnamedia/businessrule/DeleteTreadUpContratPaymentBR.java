/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.ContractPayment;
import com.magnamedia.entity.Payment;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.ContractPaymentRepository;
import com.magnamedia.repository.ContractPaymentTermRepository;
import com.magnamedia.repository.ContractRepository;
import org.joda.time.DateTime;

import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Mar 1, 2020
 *         Jirra ACC-1435
 */

@BusinessRule(entity = Payment.class, events = {BusinessEvent.AfterCreate, BusinessEvent.AfterUpdate}, fields = {
        "contract.id", "dateOfPayment", "methodOfPayment", "typeOfPayment.id", "amountOfPayment",
        "status"})
public class DeleteTreadUpContratPaymentBR implements BusinessAction<Payment> {

    @Override
    public boolean validate(Payment entity, BusinessEvent event) {
        ContractRepository contractRep = Setup.getRepository(ContractRepository.class);
        ContractPaymentTermRepository contractPaymentTermRepository = Setup
                .getRepository(ContractPaymentTermRepository.class);

        if (entity.getStatus().equals(PaymentStatus.TEARED_UP) && !contractPaymentTermRepository
                .findActiveTermsByContract(contractRep.findOne(entity.getContract().getId())).isEmpty()) {
            PicklistItem monthlyPaymentItem = Setup.getRepository(PicklistItemRepository.class)
                    .findByListAndCodeIgnoreCase(
                            Setup.getRepository(PicklistRepository.class).findByCode("TypeOfPayment"),
                            PicklistItem.getCode("monthly_payment"));
            return entity.getTypeOfPayment().getId().longValue() == monthlyPaymentItem.getId().longValue();
        }
        return false;
    }

    @Override
    public Map execute(Payment entity, BusinessEvent even) {
        ContractPaymentRepository contractPaymentRep = Setup.getRepository(ContractPaymentRepository.class);
        DateTime startDate = new DateTime(entity.getDateOfPayment()).withHourOfDay(0).withMinuteOfHour(0)
                .withSecondOfMinute(0);
        DateTime endDate = new DateTime(entity.getDateOfPayment()).withHourOfDay(23).withMinuteOfHour(59)
                .withSecondOfMinute(59);

        List<ContractPayment> contractPayments = contractPaymentRep.findMatchedContractPayment(
                entity.getContract().getId(), entity.getMethodOfPayment(), entity.getTypeOfPayment().getId(),
                startDate.toDate(), endDate.toDate(), entity.getAmountOfPayment());

        if (contractPayments != null && !contractPayments.isEmpty()) {
            contractPaymentRep.delete(contractPayments.get(0));
            contractPaymentRep.flush();
        }
        return null;

    }
}
