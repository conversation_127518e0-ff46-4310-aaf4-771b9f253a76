package com.magnamedia.businessrule;


import com.magnamedia.controller.ContractPaymentTermController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.*;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.DirectDebitSignatureRepository;
import com.magnamedia.service.DirectDebitSignatureService;
import com.magnamedia.workflow.newversion.services.graphicdesigner.GraphicToDoType;

import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jan 11, 2021
 *         Jirra ACC-2860
 */

@BusinessRule(entity = GraphicDesignerToDo.class, events = {BusinessEvent.BeforeUpdate},
        fields = {"id", "clientId", "contractId", "toDoType", "completed",
                "attachments.id", "attachments.name", "attachments.path", "attachments.size", "attachments.tag",
                "attachments.extension", "attachments.amazon", "attachments.ownerId", "attachments.ownerType"})
public class SigningPaperModeBR implements BusinessAction<GraphicDesignerToDo> {

    private static final Logger logger = Logger.getLogger(SigningPaperModeBR.class.getName());

    @Override
    public boolean validate(GraphicDesignerToDo entity, BusinessEvent event) {
        logger.info("SigningPaperModeBR Validation.");

        logger.info("Todo ID " + entity.getId());
        logger.info("Todo Is Completed " + entity.getCompleted());
        logger.info("Todo Type " + entity.getToDoType());
        logger.info("Todo ClientId " + entity.getClientId());
        logger.info("Todo ContractId " + entity.getContractId());
        logger.info("Todo Attachments Size " + (entity.getAttachments() != null ? entity.getAttachments().size() : " NULL"));

        if (!entity.getToDoType().equals(GraphicToDoType.ACCOUNTING_DIGITALIZE_CLIENT_SIGNATURE)) return false;

        SelectQuery<GraphicDesignerToDo> query = new SelectQuery(GraphicDesignerToDo.class);
        query.filterBy("id", "=", entity.getId());

        List<GraphicDesignerToDo> oldToDos = query.execute();
        if (oldToDos == null || oldToDos.isEmpty()) return false;

        GraphicDesignerToDo oldToDo = oldToDos.get(0);

        return entity.getCompleted() != null && entity.getCompleted() &&
                (oldToDo.getCompleted() == null || !oldToDo.getCompleted()) &&
                entity.getAttachments() != null;
    }

    @Override
    public Map execute(GraphicDesignerToDo entity, BusinessEvent even) {
        Contract contract = Setup.getRepository(ContractRepository.class).findOne(entity.getContractId());
        ContractPaymentTerm activeCPT = contract.getActiveContractPaymentTerm();

        Attachment eidPhoto = activeCPT.getAttachment(ContractPaymentTermController.FILE_TAG_PAPER_MODE_BANK_INFO_EID);
        Attachment ibanPhoto = activeCPT.getAttachment(ContractPaymentTermController.FILE_TAG_PAPER_MODE_BANK_INFO_IBAN);
        Attachment accountNamePhoto = activeCPT.getAttachment(ContractPaymentTermController.FILE_TAG_PAPER_MODE_BANK_INFO_ACCOUNT_NAME);

        //ACC-4657
        boolean pendingOcr = false;
        if (eidPhoto == null && ibanPhoto == null && accountNamePhoto == null) {
            eidPhoto = activeCPT.getAttachment(ContractPaymentTermController.FILE_TAG_PAPER_MODE_BANK_INFO_PENDING_OCR);
            ibanPhoto = activeCPT.getAttachment(ContractPaymentTermController.FILE_TAG_PAPER_MODE_BANK_INFO_PENDING_OCR);
            accountNamePhoto = activeCPT.getAttachment(ContractPaymentTermController.FILE_TAG_PAPER_MODE_BANK_INFO_PENDING_OCR);
            pendingOcr = true;
        }

        Set<String> uniqueSignatures = new HashSet<>();
        DirectDebitSignatureService directDebitSignatureService = Setup.getApplicationContext()
                .getBean(DirectDebitSignatureService.class);

        // VPM-3805
        List<Attachment> signatures = new ArrayList<>();
        entity.getAttachments()
                .stream()
                .filter(attachment -> attachment.getTag().startsWith(DirectDebitFile.FILE_TAG_DD_SIGNATURE+ "_1"))
                .findFirst()
                .ifPresent(signatures::add);

        entity.getAttachments()
                .stream()
                .filter(attachment -> attachment.getTag().startsWith(DirectDebitFile.FILE_TAG_DD_SIGNATURE+ "_2"))
                .findFirst()
                .ifPresent(signatures::add);

        entity.getAttachments()
                .stream()
                .filter(attachment -> attachment.getTag().startsWith(DirectDebitFile.FILE_TAG_DD_SIGNATURE+ "_3"))
                .findFirst()
                .ifPresent(signatures::add);

        signatures = signatures.stream()
                .map(attachment -> {
                    String encoded = Base64.getEncoder()
                            .encodeToString(directDebitSignatureService.getImageByteArray(
                                    Storage.getStream(attachment)));
                    uniqueSignatures.add(encoded);
                    return Storage.storeTemporary(attachment.getName(), Storage.getStream(attachment), "Paper_Mode_Temp_Signature", Boolean.FALSE, true);
                }).collect(Collectors.toList());

        List<DirectDebitSignature> allClientSignatures = Setup.getRepository(DirectDebitSignatureRepository.class)
                .findOldSignaturesByClient(activeCPT.getContract().getClient());

        // ACC-6604
        if (!allClientSignatures.isEmpty()) {
            for (DirectDebitSignature sign : allClientSignatures) {
                if (sign.getSignatureAttachment() == null) continue;

                String encoded = Base64.getEncoder()
                        .encodeToString(directDebitSignatureService.getImageByteArray(
                                Storage.getStream(sign.getSignatureAttachment())));

                if (uniqueSignatures.contains(encoded)) {
                    throw new BusinessException("Error: The uploaded signature already exist, please upload a new one");
                }
            }
        }

        Setup.getApplicationContext().getBean(ContractPaymentTermController.class)
                .createBackgroundTaskToSign(contract.getUuid(), contract,
                        eidPhoto, ibanPhoto, accountNamePhoto, true, true, true,
                        activeCPT.getEid(), activeCPT.getIbanNumber(), activeCPT.getAccountName(),
                        signatures, false,  true, null,
                        null, null, null, pendingOcr);

        return null;
    }
}