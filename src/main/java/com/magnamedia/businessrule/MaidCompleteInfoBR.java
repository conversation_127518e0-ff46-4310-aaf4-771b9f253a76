package com.magnamedia.businessrule;

import com.magnamedia.controller.ContractController;
import com.magnamedia.controller.ContractPaymentTermController;
import com.magnamedia.controller.DirectDebitController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.DirectDebitSignature;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.helper.ContractPaymentTermHelper;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.repository.ContractPaymentTermRepository;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.service.ContractPaymentTermServiceNew;
import com.magnamedia.service.DirectDebitSignatureService;

import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> Masod <<EMAIL>>
 *         Created on May 5, 2020
 *         Jirra SAL-2034
 */

@BusinessRule(entity = Housemaid.class,
        events = {BusinessEvent.AfterUpdate},
        fields = {"id", "name", "nationality.id"})
public class MaidCompleteInfoBR implements BusinessAction<Housemaid> {

    private static final Logger logger =
            Logger.getLogger(MaidCompleteInfoBR.class.getName());
    private static final String prefix = "MMM ";

    @Override
    public boolean validate(Housemaid entity, BusinessEvent event) {
        logger.log(Level.SEVERE, prefix + "MaidCompleteInfoBR Validation.");
        ContractPaymentTermHelper contractPaymentTermHelper = Setup.getApplicationContext().getBean(ContractPaymentTermHelper.class);
        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
        PicklistItemRepository picklistItemRepository = Setup.getRepository(PicklistItemRepository.class);

        if (entity.getNationality() == null || entity.getNationality().getId() == null) {
            return false;
        }

        HistorySelectQuery<Housemaid> historyQuery = new HistorySelectQuery(Housemaid.class);
        historyQuery.filterBy("id", "=", entity.getId());
        historyQuery.filterByChanged("name");
        historyQuery.sortBy("lastModificationDate", false, true);

        List<Housemaid> oldMaids = historyQuery.execute();
        boolean nameChanged = false;
        if (oldMaids == null || oldMaids.isEmpty() || oldMaids.get(0).getName() == null || oldMaids.get(0).getName().isEmpty() || oldMaids.get(0).getName().toLowerCase().contains("unknown"))
            nameChanged = true;

        HistorySelectQuery<Housemaid> historyQuery2 = new HistorySelectQuery(Housemaid.class);
        historyQuery2.filterBy("id", "=", entity.getId());
        historyQuery2.filterByChanged("nationality");
        historyQuery2.sortBy("lastModificationDate", false, true);

        List<Housemaid> oldMaids2 = historyQuery.execute();
        boolean nationalityChanged = false;
        if (oldMaids2 == null || oldMaids2.isEmpty() || oldMaids2.get(0).getNationality() == null || oldMaids2.get(0).getNationality().getId() == null) {
            nationalityChanged = true;
        } else {
            PicklistItem oldNationality = picklistItemRepository.findOne(oldMaids2.get(0).getNationality().getId());
            if (oldNationality.getCode().toLowerCase().contains("unknown"))
                nationalityChanged = true;
        }


        List<Contract> contracts = contractRepository.findByHousemaidAndStatus(entity, ContractStatus.ACTIVE);
        if (contracts == null || contracts.isEmpty()) return false;

        Contract contract = contracts.get(0);

        return contract.getIsMaidVisaServiceApplication() && (nameChanged || nationalityChanged) &&
                contractPaymentTermHelper.isAttachmentInfoCompleted(entity) && contractPaymentTermHelper.isAttachmentInfoCompleted(contract.getClient());
    }

    @Override
    public Map<String, Object> execute(Housemaid entity, BusinessEvent even) {
        logger.log(Level.SEVERE, prefix + "ClientCompleteInfoBR execute.");
        Map<String, Object> map = new HashMap();
        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
        ContractPaymentTermRepository cptRepository = Setup.getRepository(ContractPaymentTermRepository.class);
        ContractPaymentTermServiceNew contractPaymentTermServiceNew = Setup.getApplicationContext().getBean(ContractPaymentTermServiceNew.class);
        DirectDebitController ddController = Setup.getApplicationContext().getBean(DirectDebitController.class);
        DirectDebitSignatureService directDebitSignatureService = Setup.getApplicationContext().getBean(DirectDebitSignatureService.class);
        Contract contract = contractRepository.findByHousemaidAndStatus(entity, ContractStatus.ACTIVE).get(0);
        List<ContractPaymentTerm> cpts = cptRepository.findByContractAndIsActiveOrderByCreationDateDesc(contract, true);
        if (cpts == null || cpts.isEmpty()) return null;
        ContractPaymentTerm cpt = cpts.get(0);

        // generate payments receipt
        Attachment contractSignature = contract.getAttachments().stream()
                .filter(att -> att.getTag().equals(ContractController.SIGNATURE_FILE_TAG_NAME)).findFirst()
                .orElse(null);

        InputStream signatureForReceipt = null;
        // new block for payment details email signature
        if (contractSignature == null) {
            Map<String, Object> result = directDebitSignatureService.getLastSignatureType(cpt, true, false);
            logger.log(Level.SEVERE, "Break Code Redundancy Alert");
            if (result.get("currentSignatures") != null) {
                contractSignature = ((List<DirectDebitSignature>) result.get("currentSignatures")).get(0)
                        .getSignatureAttachment();
                signatureForReceipt = Storage.getStream(contractSignature);
            }
        } else {
            signatureForReceipt = Storage.getStream(contractSignature);
        }

        try {
            contractPaymentTermServiceNew.generateAndSavePaymentsReceipt(cpt, null, entity, signatureForReceipt, true);
            contractPaymentTermServiceNew.sendDDFilesToClient(contract, null);
        } catch (Exception ex) {
            Logger.getLogger(MaidCompleteInfoBR.class.getName()).log(Level.SEVERE, null, ex);
            throw new RuntimeException(ex.getMessage());
        } finally {
            StreamsUtil.closeStream(signatureForReceipt);
        }

        return map;
    }
}
