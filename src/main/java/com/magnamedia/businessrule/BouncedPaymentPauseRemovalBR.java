package com.magnamedia.businessrule;

import com.magnamedia.controller.DirectDebitCancelationToDoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.entity.Payment;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.module.type.DirectDebitCancellationToDoReason;
import com.magnamedia.module.type.DirectDebitMethod;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.DirectDebitRejectionToDoRepository;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.service.DirectDebitCancellationService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on Aug 24, 2020
 * Jirra ACC-CC_APP
 */

// stop DirectDebitBouncingRejectionToDo and cancel switch bank account dd
@BusinessRule(entity = Payment.class, events = {BusinessEvent.BeforeUpdate},
        fields = {"id", "replaced", "status", "bouncedFlowPausedForReplacement"})

public class BouncedPaymentPauseRemovalBR implements BusinessAction<Payment> {

    private static final Logger logger =
            Logger.getLogger(BouncedPaymentPauseRemovalBR.class.getName());

    @Override
    public boolean validate(Payment entity, BusinessEvent event) {
        logger.log(Level.SEVERE, "BouncedPaymentPauseRemovalBR validation");
        logger.log(Level.SEVERE, "BouncedPaymentPauseRemovalBR payment id " + entity.getId());
        
        if (!entity.getStatus().equals(PaymentStatus.BOUNCED)) {
            logger.info("Payment status not bounced");
            return false;
        }
        
        HistorySelectQuery<Payment> historyQuery = new HistorySelectQuery<>(Payment.class);
        historyQuery.filterBy("id", "=", entity.getId());
        historyQuery.sortBy("lastModificationDate", false, true);
        historyQuery.setLimit(1);
        
        List<Payment> oldPayments = historyQuery.execute();
        Payment old = oldPayments.isEmpty() ? null : oldPayments.get(0);
        
        logger.log(Level.SEVERE, "BouncedPaymentPauseRemovalBR payment new status " + entity.getStatus());
        logger.log(Level.SEVERE, "BouncedPaymentPauseRemovalBR payment old status " 
                + (old != null ? old.getStatus() : ""));
        logger.log(Level.SEVERE, "BouncedPaymentPauseRemovalBR payment old replaced : "
                + (old != null ? old.getReplaced() : null));
        logger.log(Level.SEVERE, "BouncedPaymentPauseRemovalBR payment new replaced : " + entity.isReplaced());

        return ((old == null || !old.isReplaced()) && entity.isReplaced())
                && entity.getBouncedFlowPausedForReplacement();
    }

    @Override
    public Map execute(Payment entity, BusinessEvent event) {
        logger.log(Level.SEVERE, "BouncedPaymentPauseRemovalBR execute");

        Payment payment = Setup.getRepository(PaymentRepository.class).findOne(entity.getId());
        if(payment == null) return null;
        
        Map<String, Object> map = new HashMap<>();
        map.put("bouncedFlowPausedForReplacement", false);
        
        if (payment.getDirectDebit() == null || 
                payment.getDirectDebit().getDirectDebitBouncingRejectionToDo() == null) 
            return map;
            
        DirectDebitRejectionToDo todo = payment.getDirectDebit().getDirectDebitBouncingRejectionToDo();
        todo.setStopped(true);
        todo.setStopReason("bounced payment was replaced");
        Setup.getRepository(DirectDebitRejectionToDoRepository.class)
                .save(todo);

        if(payment.getDirectDebit().getImageForDD() == null ||
                !payment.getDirectDebit().getImageForDD()
                        .getContractPaymentTerm().getSwitchedBankAccount()) {
            
            return map;
        }

        List<DirectDebitFile> ddfs = payment.getDirectDebit()
                .getDirectDebitFiles().stream()
                .filter(ddf -> ddf.getDdMethod() == DirectDebitMethod.MANUAL
                        && ddf.getForBouncingPayment() != null && ddf.getForBouncingPayment())
                .collect(Collectors.toList());

        if (!ddfs.isEmpty()) {
            DirectDebitCancellationService directDebitCancellationService =
                Setup.getApplicationContext().getBean(DirectDebitCancellationService.class);
    
            for (DirectDebitFile ddf : ddfs) {
                directDebitCancellationService.cancelDDf(
                    ddf, DirectDebitCancellationToDoReason.SWITCHING_BANK_ACCOUNT);
            }
        }
        
        return map;
    }
}
