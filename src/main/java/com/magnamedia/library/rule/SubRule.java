/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.library.rule;

import com.fasterxml.jackson.annotation.JsonGetter;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 *
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD) //on method level
public @interface SubRule {

    /**
     *
     * @return
     */
    @JsonGetter(value = "name")
    public String name();

    /**
     *
     * @return
     */
    @JsonGetter(value = "code")
    public String code();

    /**
     *
     * @return
     */
    @JsonGetter(value = "creator")
    public String creator();

    /**
     *
     * @return
     */
    @JsonGetter(value = "priority")
    public int priority();
}
