/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.library.rule;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 *
 * <AUTHOR>
 * @param <T>
 */
@PreAuthorize("hasPermission('admin','admin')")
public interface BaseRuleController<T> {

    /**
     *
     * @return
     */
    public BaseRuleServiceDefinition<T> getBaseRuleServiceDefinition();

    /**
     *
     * @return
     */
    @RequestMapping("/getall")
    public default List<BaseRule<T>> getAllRules() {
        return getBaseRuleServiceDefinition().getAllGeneralRules();
    }

    /**
     *
     * @param code
     * @return
     */
    @RequestMapping("/get/{code}")
    public default List<BaseRule<T>> getRule(@PathVariable String code) {
        return getBaseRuleServiceDefinition().getAllGeneralRules(code);
    }

    /**
     *
     * @return
     */
    @RequestMapping("/applyall")
    public default List<GeneralRuleResult<T>> applyAllRules() {
        return getBaseRuleServiceDefinition().applyAllGeneralRules();
    }

    /**
     *
     * @param code
     * @return
     */
    @RequestMapping("/apply/{code}")
    public default List<GeneralRuleResult<T>> applyRules(@PathVariable String code) {

        return getBaseRuleServiceDefinition().applyAllGeneralRules(code);
    }

    /**
     *
     * @param update
     * @return
     */
    @RequestMapping("/applyallwithsubrules")
    public default List<GeneralRuleResult<T>> applyAllWithSubRules(@RequestParam(required = false) Boolean update) {
        if (update == null) {
            update = false;
        }
        return getBaseRuleServiceDefinition().applyAllGeneralRulesAndSubRules(update);
    }

    /**
     *
     * @param code
     * @param update
     * @param subRuleCode
     * @return
     */
    @RequestMapping("/applywithsubrules/{code}")
    public default List<GeneralRuleResult<T>> applyWithSubRules(@PathVariable String code,
            @RequestParam(required = false, name = "update") Boolean update,
            @RequestParam(required = false, name = "subrulecode") String subRuleCode) {
        if (update == null) {
            update = false;
        }
        return getBaseRuleServiceDefinition().applyAllGeneralRulesAndSubRules(code, update, subRuleCode);
    }
}
