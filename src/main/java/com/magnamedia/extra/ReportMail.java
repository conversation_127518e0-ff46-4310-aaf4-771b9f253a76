package com.magnamedia.extra;

import java.util.List;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Mar 7, 2018
 */
@Component
public class ReportMail {

    public String toHtmlTable(List<String[]> resultList, String[] headers, String title, Integer maxRecords){
        
        StringBuilder sb  = new StringBuilder();
        sb.append("<div>");
        //Mail Header
        if (title !=null)
            sb.append("<h2>").append(title).append("</h2>");
        sb.append("<h2>The result count is: ")
                .append(resultList.size()).append("</h2>");
        //Tablecontent
        sb.append("<table border=\"1\" style=\"border-collapse:collapse;text-align:center\">");
        sb.append("<tr>");
        for(String header: headers){
            sb.append("<th>");
            sb.append(header);
            sb.append("</th>");
        }
        for(int i=0; ((i< resultList.size()) && (i<maxRecords)); i++){
            sb.append("<tr>");
            for (String get : resultList.get(i)) {
                sb.append("<td>");
                sb.append(get);
                sb.append("</td>");
            }
            sb.append("</tr>");
        }
        sb.append("</tr>");
        sb.append("</table>");
        sb.append("</div>");
        
        return sb.toString();
    }
    
    public String toHtmlTableNoCount(List<String[]> resultList, String[] headers, String title, Integer maxRecords){
        
        StringBuilder sb  = new StringBuilder();
        sb.append("<div>");
        if (title !=null)
            sb.append(title);
        sb.append("<table border=\"1\" style=\"border-collapse:collapse;text-align:center\">");
        sb.append("<tr>");
        for(String header: headers){
            sb.append("<th>");
            sb.append(header);
            sb.append("</th>");
        }
        for(int i=0; ((i< resultList.size()) && (i<maxRecords)); i++){
            sb.append("<tr>");
            for (String get : resultList.get(i)) {
                sb.append("<td>");
                sb.append(get);
                sb.append("</td>");
            }
            sb.append("</tr>");
        }
        sb.append("</tr>");
        sb.append("</table>");
        sb.append("</div>");
        
        return sb.toString();
    }
}
