package com.magnamedia.extra;

import com.magnamedia.core.Setup;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.entity.Payment;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;

import java.util.Date;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Apr 13, 2020
 *         Jirra ACC-1598
 */

public class ManualDDFBean {

    private Long id;
    private String rowIndex;
    private String applicationId;
    private String amount;
    private String iban;

    private Payment relatedPayment;

    public ManualDDFBean() {
    }

    public ManualDDFBean(DirectDebitFile directDebitFile, Payment payment, Long index) {
        this.id = directDebitFile.getId();
        Date now = new Date();

        this.rowIndex = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_MANUAL_DD_BATCH_RECORD_INDEX) + DateUtil.formatDayNumber(now)
                + DateUtil.formatMonthNumber(now) + DateUtil.formatYearNumber(now) + String.format("%04d", index);

        this.applicationId = directDebitFile.getDdaRefNo();
        this.amount = String.format("%.2f", directDebitFile.getAmount());
        this.iban = directDebitFile.getIbanNumber();

        this.relatedPayment = payment;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRowIndex() {
        return rowIndex;
    }

    public void setRowIndex(String rowIndex) {
        this.rowIndex = rowIndex;
    }

    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getIban() {
        return iban;
    }

    public void setIban(String iban) {
        this.iban = iban;
    }

    public Payment getRelatedPayment() {
        return relatedPayment;
    }

    public void setRelatedPayment(Payment relatedPayment) {
        this.relatedPayment = relatedPayment;
    }

//    @Override
//    public boolean equals(Object obj) {
//        if (obj == null || !(obj instanceof ManualDDFBean)) return false;
//
//        ManualDDFBean manualDDFBean = (ManualDDFBean) obj;
//        if (this.id == null || manualDDFBean.id == null) return false;
//
//        return manualDDFBean.id.equals(this.id);
//    }
}
