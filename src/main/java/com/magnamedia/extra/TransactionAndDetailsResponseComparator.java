package com.magnamedia.extra;

import com.magnamedia.entity.Bucket;
import com.magnamedia.entity.Expense;
import com.magnamedia.entity.Revenue;

import java.lang.reflect.Field;
import java.util.Comparator;
import java.util.Date;

public class TransactionAndDetailsResponseComparator implements Comparator<TransactionAndDetailsResponse> {

    final String sortBy;
    final String sortOrder;

    public TransactionAndDetailsResponseComparator(String sortBy, String sortOrder) {
        this.sortBy = sortBy;
        this.sortOrder = sortOrder;
    }

    @Override
    public int compare(TransactionAndDetailsResponse o1, TransactionAndDetailsResponse o2) {

        try {
            Field field1 = o1.getClass().getDeclaredField(sortBy);
            Field field2 = o2.getClass().getDeclaredField(sortBy);

            field1.setAccessible(true);
            field2.setAccessible(true);

            if (field1.get(o1) == null || field2.get(o2) == null)
                return -1;

            if (field1.getType() == Long.class) {
                Long d1 = (Long) field1.get(o1);
                Long d2 = (Long) field2.get(o2);
                return (sortOrder.toLowerCase().equals("asc")) ? d1.compareTo(d2) : d2.compareTo(d1);

            }
            if (field1.getType() == Double.class) {
                Double d1 = (Double) field1.get(o1);
                Double d2 = (Double) field2.get(o2);
                return (sortOrder.toLowerCase().equals("asc")) ? d1.compareTo(d2) : d2.compareTo(d1);

            } else if (field1.getType() == Date.class) {
                Date d1 = (Date) field1.get(o1);
                Date d2 = (Date) field2.get(o2);
                return (sortOrder.toLowerCase().equals("asc")) ? d1.compareTo(d2) : d2.compareTo(d1);

            } else if (field1.getType() == Bucket.class) {
                Bucket d1 = (Bucket) field1.get(o1);
                Bucket d2 = (Bucket) field2.get(o2);
                return (sortOrder.toLowerCase().equals("asc")) ? d1.getName().compareTo(d2.getName()) : d2.getName().compareTo(d1.getName());

            } else if (field1.getType() == Expense.class) {
                Expense d1 = (Expense) field1.get(o1);
                Expense d2 = (Expense) field2.get(o2);
                return (sortOrder.toLowerCase().equals("asc")) ? d1.getName().compareTo(d2.getName()) : d2.getName().compareTo(d1.getName());

            } else if (field1.getType() == Revenue.class) {
                Revenue d1 = (Revenue) field1.get(o1);
                Revenue d2 = (Revenue) field2.get(o2);
                return (sortOrder.toLowerCase().equals("asc")) ? d1.getName().compareTo(d2.getName()) : d2.getName().compareTo(d1.getName());

            } else if (field1.getType() == java.sql.Date.class) {
                java.sql.Date d1 = (java.sql.Date) field1.get(o1);
                java.sql.Date d2 = (java.sql.Date) field2.get(o2);
                return (sortOrder.toLowerCase().equals("asc")) ? d1.compareTo(d2) : d2.compareTo(d1);

            } else if (field1.getType() == String.class){
                String d1 = (String) field1.get(o1);
                String d2 = (String) field2.get(o2);
                return (sortOrder.toLowerCase().equals("asc")) ? d1.compareTo(d2) : d2.compareTo(d1);

            }else return 0;
        } catch (SecurityException e) {
            throw new RuntimeException(e);
        } catch (NoSuchFieldException e) {
            throw new RuntimeException("Missing variable sortBy");
        } catch (ClassCastException e) {
            throw new RuntimeException("sortBy is not found in class list");
        } catch (IllegalArgumentException e) {
            //shoud not happen
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }


}