/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.extra;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.core.mail.Recipient;
import com.magnamedia.core.mail.TextEmail;
import com.magnamedia.core.type.EmailReceiverType;
import com.magnamedia.entity.Payment;
import com.magnamedia.entity.Transaction;
import com.magnamedia.helper.CsvHelper;
import com.magnamedia.service.MessagingService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;

/**
 *
 * <AUTHOR>
 */

@Service
public class EmailHelper {
    
    public static List<EmailRecipient> getMailRecipients(String emails){
		List<EmailRecipient> recipients = new ArrayList<>();
		if (emails.length() > 0) {
			String[] emails_array = emails.split(";");
			for (int i = 0; i < emails_array.length; i++) {
				String email = emails_array[i].trim();
				if (email.length() > 0)
					recipients.add(new Recipient(email, email));
			}
		}
		return recipients;
	}

	public static List<String> getEmails(String emails){
		List<String> recipients = new ArrayList<>();
		if (emails.length() > 0) {
			String[] emails_array = emails.split(emails.contains(";") ? ";" : ",");
			for (String s : emails_array) {
				String email = s.trim();
				if (email.length() > 0) recipients.add(email);
			}
		}
		return recipients;
	}

	public static List<EmailRecipient> getRecipients(String emails){
		List<EmailRecipient> recipients = new ArrayList<>();
		if (emails.length() > 0) {
			String[] emails_array = emails.split(emails.contains(";") ? ";" : ",");
			for (String s : emails_array) {
				String email = s.trim();
				if (email.length() > 0) recipients.add(new Recipient(email, email));
			}
		}
		return recipients;
	}

	public static void sendEmailByText(
			String emails,
			String subject,
			String body) {

		if (emails == null || emails.isEmpty()) return;

		Setup.getMailService().sendEmail(
				EmailHelper.getRecipients(emails),
				new TextEmail(subject, body),
				EmailReceiverType.Office_Staff);
	}

	@Async
	public void generateCSVFileAndSendViaMail(
			String emails,
			String subject,
			String[] headers,
			String[] columnNames,
			Class projection,
			String fileName,
			SelectQuery query,
			User user) throws IOException {

		if (user != null) {
			SecurityContext sc = SecurityContextHolder.getContext();
			try {
				UsernamePasswordAuthenticationToken authReq = new UsernamePasswordAuthenticationToken(user, null, null);
				sc.setAuthentication(authReq);

				List<Transaction> transactions = query.execute();
				File file = CsvHelper.generateCsv(transactions, projection, headers, columnNames, !Utils.isEmpty(fileName) ?
						fileName : "Transactions Report", ".csv");

				if (emails != null && !emails.isEmpty()) {
					List<EmailRecipient> recipients = EmailHelper.getRecipients(emails);
					TextEmail mail = new TextEmail(!Utils.isEmpty(subject) ? subject : "Transactions Report", "");
					mail.addAttachement(file);
					Setup.getMailService().sendEmail(recipients, mail, null);
				}
			} finally {
				sc.setAuthentication(null);
			}
		}
	}


	/**
	 * Builds a CSV and e-mails it. Allows distinct pretty-headers and
	 * property-mapping arrays.
	 */
	@Async
	public void sendCsvMail(List<?>        data,
							Class<?>       projection,
							String[]       headers,        // what the user sees
							String[]       columnNames,    // getter/field order
							String         fileNameBase,
							String         subject,
							String         recipientEmails) throws IOException {

		if (Utils.isEmpty(recipientEmails) || data == null || data.isEmpty()) {
			return; // nothing to do
		}

		/* 1 ─ CSV file ---------------------------------------------------- */
		File csvFile = CsvHelper.generateCsv(
				data,
				projection,
				headers,
				columnNames != null ? columnNames : headers,
				Utils.isEmpty(fileNameBase) ? "report" : fileNameBase,
				".csv");

		/* 2 ─ Mail -------------------------------------------------------- */
		List<EmailRecipient> recipients = EmailHelper.getRecipients(recipientEmails);

		TextEmail mail = new TextEmail(
				Utils.isEmpty(subject) ? fileNameBase : subject,
				"Hi,\n\nYour CSV report is attached.\n\nRegards,\nAccounting System");
		mail.addAttachement(csvFile);

		Setup.getMailService().sendEmail(recipients, mail, null);
	}

	@Async
	public void sendCsvMail(List<?> data,
							Class<?> projection,
							String[] headers,
							String fileName,
							String subject,
							String recipientEmails) throws IOException {

		if (Utils.isEmpty(recipientEmails) || data == null || data.isEmpty()) {
			return;   // nothing to do
		}

		// 1) Build the CSV file
		File csvFile = CsvHelper.generateCsv(
				data,
				projection,
				headers,
				headers,
				Utils.isEmpty(fileName) ? "report" : fileName,
				".csv");

		// 2) Prepare the mail
		List<EmailRecipient> recipients = EmailHelper.getRecipients(recipientEmails);
		TextEmail mail = new TextEmail(
				Utils.isEmpty(subject) ? fileName : subject,
				"Hi,\n\nYour CSV report is attached.\n\nRegards,\nAccounting System");
		mail.addAttachement(csvFile);

		// 3) Send
		Setup.getMailService().sendEmail(recipients, mail, null);
	}
	@Async
	public void generateCSVFileAndSendViaMail(
			String emails,
			String subject,
			String templateName,
			String[] headers,
			String[] columnNames,
			Class projection,
			String fileName,
			SelectQuery query) throws IOException {

		List<Payment> payments = query.execute();
		File file = CsvHelper.generateCsv(payments, projection, headers, columnNames, !Utils.isEmpty(fileName) ?
				fileName : "Payments Report", ".csv");

		Setup.getApplicationContext()
				.getBean(MessagingService.class)
				.sendEmailToOfficeStaffWithAttachments(
						templateName,
						new HashMap<>(),
						emails,
						Collections.singletonList(Storage.storeTemporary(file.getName(),
								new FileInputStream(file), null,false)),
						!Utils.isEmpty(subject) ? subject : "Payments Report" );
	}
}