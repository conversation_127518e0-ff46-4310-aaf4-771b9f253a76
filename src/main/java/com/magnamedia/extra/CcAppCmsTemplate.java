package com.magnamedia.extra;

public enum CcAppCmsTemplate {

    CC_CLIENT_PAYING_VIA_CREDIT_CARD_REPLACEMENT_SCREEN,
    DD_AMENDMENT_ONE_MONTH_AGREEMENT_DOWNGRADE,
    DD_AMENDMENT_ONE_MONTH_AGREEMENT_UPGRADE,
    CC_PAYMENT_IN_ADVANCE_AFTER_REPLACEMENT,

    CC_PAYMENT_APPROVED_MONTHLY_PAYMENT_PENDING_REFUND_NON_CONDITIONAL,
    CC_PAYMENT_APPROVED_MONTHLY_PAYMENT_PENDING_REFUND_CONDITIONAL,
    CC_PAYMENT_APPROVED_MONTHLY_PAYMENT_PENDING_REFUND_PROOF_UPLOADED,

    PAYMENT_SECTION_BOUNCED_PAYMENT_DEFAULT_MESSAGE,

    PAYMENT_SECTION_DD_REJECTION_DEFAULT_MESSAGE,

    PAYMENT_SECTION_INCOMPLETE_DDS_ONE_DOC_REJECTED_DEFAULT_MESSAGE,
    PAYMENT_SECTION_INCOMPLETE_DDS_TWO_DOC_REJECTED_DEFAULT_MESSAGE,
    PAYMENT_SECTION_INCOMPLETE_DDS_THREE_DOC_REJECTED_DEFAULT_MESSAGE,
    PAYMENT_SECTION_INCOMPLETE_DDS_MISSING_BANK_INFO_DEFAULT_MESSAGE,

    PAYMENT_SECTION_PAYING_VIA_CREDIT_CARD_DEFAULT_MESSAGE,

    PAYMENT_SECTION_PAYING_VIA_CREDIT_CARD_WITH_TOKEN_DEFAULT_MESSAGE,

    PAYMENT_SECTION_INITIAL_PAYMENT_BY_ALTERNATIVE_METHOD_DEFAULT_MESSAGE,
    PAYMENT_SECTION_ONE_MONTH_AGREEMENT_DEFAULT_MESSAGE,

    PAYMENT_SECTION_ONLINE_PAYMENTS_REMINDER_CLIENT_HAS_CONFIRMED_DDS_DEFAULT_MESSAGE,
    PAYMENT_SECTION_ONLINE_PAYMENTS_REMINDER_CLIENT_WITH_NO_CONFIRMED_DDS_DEFAULT_MESSAGE,

    PAYMENT_SECTION_DD_CONFIRMED_DEFAULT_MESSAGE,
    PAYMENT_SECTION_DD_CONFIRMED_DEFAULT_MESSAGE_INTRO,

    PAYMENT_SECTION_DD_PENDING_DEFAULT_MESSAGE,

    PAYMENT_SECTION_CONTRACT_SCHEDULED_FOR_TERMINATION,

    PAYMENT_SECTION_NO_RULES_APPLY_DEFAULT_MESSAGE,

    // ACC-6587
    PAYMENT_SECTION_RECEIVE_PAYMENT_NOTIFICATIONS_SUB_TITLE,

    // FAQS
    CC_FAQ_BOUNCED_PAY_BY_CARD,

    DD_AMENDMENT_TO_FILIPINA_PAYMENT_TERMS,
    // ِACC-8295
    DD_AMENDMENT_NOTE_LIVE_OUT,

    //ACC-7105
    MAID_GO_ON_VACATION_UPDATE_MONTHLY_PAYMENT_AND_MAID_CONTRACT_TITLE,
    MAID_GO_ON_VACATION_UPDATE_MONTHLY_PAYMENT_AND_MAID_CONTRACT_BODY,
    MAID_GO_ON_VACATION_UPDATE_MONTHLY_PAYMENT_AND_MAID_CONTRACT_VIA_CC_TITLE,
    MAID_GO_ON_VACATION_UPDATE_MONTHLY_PAYMENT_AND_MAID_CONTRACT_VIA_CC_BODY,
    MAID_GO_ON_VACATION_UPDATE_MONTHLY_PAYMENT_AND_MAID_CONTRACT_DD_TITLE,
    MAID_GO_ON_VACATION_UPDATE_MONTHLY_PAYMENT_AND_MAID_CONTRACT_DD_BODY,

    // ACC-8455
    FLEXIBLE_PACKAGE_NOTE,
    LIVE_OUT_ALLOWENCE_NOTE,
    SWITCH_SAME_MAID_LIVE_IN_OUT,

    // DEPRECATED
    CC_PAYMENT_APPROVED_MONTHLY_PAYMENT_PENDING_REFUND,
    CC_PAYMENT_PENDING_DD_NO_APPROVED_DD,
    CC_PAYMENT_PAID_BY_CARD_AND_NOT_SUBMIT_DD,
    CC_PAYMENT_INCOMPLETE_DDS,
    CC_PAYMENT_BOUNCED_DDS,
    CC_PAYMENT_BOUNCED_DDS_PAID,
    CC_PAYMENT_REJECTED_DDS,
    CC_PAYMENT_APPROVED_DDs,
    CC_PAYMENT_APPROVED_DDS_MONTHLY_PAYMENT_INTRO,
    CC_PAYMENT_PAY_WITH_CREDIT_CARD_EARLY_CC_PAYMENT,
    CC_PAYMENT_PAY_WITH_CREDIT_CARD_EARLY_CC_NOTE,
    CC_PAYMENT_NO_RULES_APPLY,
    CC_CLIENT_PAYING_VIA_CREDIT_CARD,
    FLOW_PROCESSOR_ENTITY_TERMINATION_MESSAGE_DETAILS
}
