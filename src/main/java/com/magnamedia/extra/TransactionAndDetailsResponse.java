package com.magnamedia.extra;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.entity.*;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.module.type.TransactionEntityType;
import com.magnamedia.module.type.VatType;

import java.sql.Date;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on 19-7-2020
 *         Jirra ACC-1574
 */
public class TransactionAndDetailsResponse {

    private Long id;

    private java.util.Date pnlValueDate;

    private Revenue revenue;

    private Expense expense;

    private Bucket fromBucket;

    private Bucket toBucket;

    private Date date;

    private String description;

    private PaymentMethod paymentType;

    private TransactionEntityType transactionType;

    private java.util.Date creationDate;

    private Double vatAmount = 0D;

    private VatType vatType;

    private PicklistItem license;

    private Double amount = 0.0;

    private Double averageAmount;

    private Double profitAdjustment;

    public TransactionAndDetailsResponse(Transaction transaction, TransactionDetails transactionDetails) {
        Transaction trans = transaction;
        this.amount = trans.getAmount();
        if (transactionDetails != null) {
            trans = transactionDetails.getTransaction();
            this.averageAmount = transactionDetails.getAverageAmount();
            this.profitAdjustment = transactionDetails.getProfitAdjustment();
            this.amount = transactionDetails.getTransactionAmount();
        }
        this.id = trans.getId();

        this.pnlValueDate = trans.getPnlValueDate();
        this.revenue = trans.getRevenue();
        this.expense = trans.getExpense();
        this.fromBucket = trans.getFromBucket();
        this.toBucket = trans.getToBucket();
        this.date = trans.getDate();
        this.description = trans.getDescription();
        this.paymentType = trans.getPaymentType();
        this.transactionType = trans.getTransactionType();
        this.creationDate = trans.getCreationDate();

        this.vatAmount = trans.getVatAmount();
        this.vatType = trans.getVatType();
        //Jirra ACC-2500
        if (this.amount != null && this.vatAmount != null) {
            this.amount = this.amount - this.vatAmount;
        }

        this.license = trans.getLicense();

    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public java.util.Date getPnlValueDate() {
        return pnlValueDate;
    }

    public void setPnlValueDate(java.util.Date pnlValueDate) {
        this.pnlValueDate = pnlValueDate;
    }

    public Revenue getRevenue() {
        return revenue;
    }

    public void setRevenue(Revenue revenue) {
        this.revenue = revenue;
    }

    public Expense getExpense() {
        return expense;
    }

    public void setExpense(Expense expense) {
        this.expense = expense;
    }

    public Bucket getFromBucket() {
        return fromBucket;
    }

    public void setFromBucket(Bucket fromBucket) {
        this.fromBucket = fromBucket;
    }

    public Bucket getToBucket() {
        return toBucket;
    }

    public void setToBucket(Bucket toBucket) {
        this.toBucket = toBucket;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public PaymentMethod getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(PaymentMethod paymentType) {
        this.paymentType = paymentType;
    }

    public TransactionEntityType getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(TransactionEntityType transactionType) {
        this.transactionType = transactionType;
    }

    public java.util.Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(java.util.Date creationDate) {
        this.creationDate = creationDate;
    }

    public Double getVatAmount() {
        return vatAmount;
    }

    public void setVatAmount(Double vatAmount) {
        this.vatAmount = vatAmount;
    }

    public VatType getVatType() {
        return vatType;
    }

    public void setVatType(VatType vatType) {
        this.vatType = vatType;
    }

    public PicklistItem getLicense() {
        return license;
    }

    public void setLicense(PicklistItem license) {
        this.license = license;
    }

    public Double getAmount() {
        return amount;
    }

    public Long getRoundedToThousandsAmount() {
        return Math.round(amount / 1000) * 1000;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Double getAverageAmount() {
        return averageAmount;
    }

    public void setAverageAmount(Double averageAmount) {
        this.averageAmount = averageAmount;
    }

    public Double getProfitAdjustment() {
        return profitAdjustment;
    }

    public void setProfitAdjustment(Double profitAdjustment) {
        this.profitAdjustment = profitAdjustment;
    }
}
