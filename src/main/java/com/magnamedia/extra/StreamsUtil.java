package com.magnamedia.extra;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class StreamsUtil {
    
    public static void closeStream(InputStream s) {
        if(s != null) {
            try {
                s.close();
            } catch (IOException ex) {
                Logger.getLogger(StreamsUtil.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }
    
    public static void closeStream(ByteArrayOutputStream s) {
        if(s != null) {
            try {
                s.close();
            } catch (IOException ex) {
                Logger.getLogger(StreamsUtil.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }
}
