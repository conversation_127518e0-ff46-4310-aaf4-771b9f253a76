package com.magnamedia.extra.DTOs;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.JsonNode;
import com.magnamedia.core.entity.Attachment;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON>.Masod on 6/30/2021.
 */

@JsonIgnoreProperties(ignoreUnknown = true)
public class AddMultipleDirectDebitsDTO {

    private List<DirectDebitDTO> directDebitList;
    private boolean clientPaidVat = false;
    private boolean useApprovedSignature = false;
    private boolean informClient = false;
    private boolean ignoreRejectionDDs = false;
    private List<Attachment> signatures;
    private JsonNode contractPaymentTerm;

    public List<DirectDebitDTO> getDirectDebitList() {
        return directDebitList;
    }

    public void setDirectDebitList(List<DirectDebitDTO> directDebitList) {
        this.directDebitList = directDebitList;
    }

    public boolean isClientPaidVat() {
        return clientPaidVat;
    }

    public void setClientPaidVat(boolean clientPaidVat) {
        this.clientPaidVat = clientPaidVat;
    }

    public boolean isUseApprovedSignature() {
        return useApprovedSignature;
    }

    public void setUseApprovedSignature(boolean useApprovedSignature) {
        this.useApprovedSignature = useApprovedSignature;
    }

    public boolean isInformClient() {
        return informClient;
    }

    public void setInformClient(boolean informClient) {
        this.informClient = informClient;
    }

    public boolean isIgnoreRejectionDDs() {
        return ignoreRejectionDDs;
    }

    public void setIgnoreRejectionDDs(boolean ignoreRejectionDDs) {
        this.ignoreRejectionDDs = ignoreRejectionDDs;
    }

    public List<Attachment> getSignatures() {
        if (signatures == null) return new ArrayList();
        return signatures;
    }

    public void setSignatures(List<Attachment> signatures) {
        this.signatures = signatures;
    }

    public JsonNode getContractPaymentTerm() {
        return contractPaymentTerm;
    }

    public void setContractPaymentTerm(JsonNode contractPaymentTerm) {
        this.contractPaymentTerm = contractPaymentTerm;
    }
}
