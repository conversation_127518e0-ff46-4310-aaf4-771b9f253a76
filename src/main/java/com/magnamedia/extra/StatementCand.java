/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.extra;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.magnamedia.module.type.OperationType;
import java.util.Date;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
public class StatementCand {
 
    
    @JsonIgnore
    private String column1;
    @JsonIgnore
    private String column2;
    @JsonIgnore
    private String column3;
    @JsonIgnore
    private String column4;
    @JsonIgnore
    private String column5;
    
    private Double balance;
    
    private String bookingConfirmationCode;
    
    private String company;
    
    private Double credit;
    
    private Double debit;
    
    private String description;
    
    private Date transactionDate;
    
    private OperationType opertaionType;
    
    private Date operationDate;
    
    private Boolean isValid=Boolean.TRUE;
    
    private Boolean isDuplicated;

    public String getColumn1() {
        return column1;
    }

    public void setColumn1(String column1) {
        this.column1 = column1;
    }

    public String getColumn2() {
        return column2;
    }

    public void setColumn2(String column2) {
        this.column2 = column2;
    }

    public String getColumn3() {
        return column3;
    }

    public void setColumn3(String column3) {
        this.column3 = column3;
    }

    public String getColumn4() {
        return column4;
    }

    public void setColumn4(String column4) {
        this.column4 = column4;
    }

    public StatementCand(String column1, String column2, String column3, String column4, String column5) {
        this.column1 = column1;
        this.column2 = column2;
        this.column3 = column3;
        this.column4 = column4;
        this.column5 = column5;
    }

    

    public StatementCand() {
    }

    public Double getBalance() {
        return balance;
    }

    public void setBalance(Double balance) {
        this.balance = balance;
    }

    public String getBookingConfirmationCode() {
        return bookingConfirmationCode;
    }

    public String getCompany() {
        return company;
    }



    public Double getCredit() {
        return credit;
    }

    public void setCredit(Double credit) {
        this.credit = credit;
    }

    public Double getDebit() {
        return debit;
    }

    public void setDebit(Double debit) {
        this.debit = debit;
    }

    public String getDescription() {
        return description;
    }

    public Date getTransactionDate() {
        return transactionDate;
    }

    public void setBookingConfirmationCode(String bookingConfirmationCode) {
        this.bookingConfirmationCode = bookingConfirmationCode;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setTransactionDate(Date transactionDate) {
        this.transactionDate = transactionDate;
    }

    public OperationType getOpertaionType() {
        return opertaionType;
    }

    public void setOpertaionType(OperationType opertaionType) {
        this.opertaionType = opertaionType;
    }

    public Date getOperationDate() {
        return operationDate;
    }

    public void setOperationDate(Date operationDate) {
        this.operationDate = operationDate;
    }

    public Boolean getIsValid() {
        return isValid;
    }

    public void setIsValid(Boolean isValid) {
        this.isValid = isValid;
    }

    public Boolean getIsDuplicated() {
        return isDuplicated;
    }

    public void setIsDuplicated(Boolean isDuplicated) {
        this.isDuplicated = isDuplicated;
    }

    public String getColumn5() {
        return column5;
    }

    public void setColumn5(String column5) {
        this.column5 = column5;
    }



    
    
    

 
    
    
}
