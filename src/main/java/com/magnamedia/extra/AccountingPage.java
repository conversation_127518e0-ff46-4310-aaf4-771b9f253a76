package com.magnamedia.extra;

import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 */
public class AccountingPage extends PageImpl<Object> {

    private Double totalSum;
    private String totalSumFormatted;
    private Double totalVat;
    private Long total;
    private Long secondaryTotal;

    public AccountingPage(
            List content,
            Pageable pageable,
            long total, Double sum) {
        super(content, pageable, total);
        totalSum = sum != null ? sum : 0D;
    }

    @SuppressWarnings("unchecked")
    public AccountingPage(
            List content,
            Pageable pageable,
            long total, Long secondaryTotal) {
        super(content, pageable, total);
        this.secondaryTotal = secondaryTotal;
    }

    public AccountingPage(
            List content,
            Pageable pageable,
            Double sum,
            long total) {
        super(content, pageable, total);
        this.total = total;
        totalSum = sum != null ? sum : 0D;
    }

    public AccountingPage(
            List content,
            Pageable pageable,
            long total, double sum, double totalVat) {
        super(content, pageable, total);
        this.totalSum = sum;
        this.totalVat = totalVat;
    }

    public AccountingPage(
            List content,
            Pageable pageable,
            long total, double sum, String totalSumFormatted) {
        super(content, pageable, total);
        this.totalSumFormatted = totalSumFormatted;
        this.totalSum = sum;
    }

    public Double getTotalSum() {
        return totalSum;
    }

    public void setTotalSum(Double totalSum) {
        this.totalSum = totalSum;
    }

    public Double getTotalVat() {
        return totalVat;
    }

    public void setTotalVat(Double totalVat) {
        this.totalVat = totalVat;
    }

    public String getTotalSumFormatted() { return totalSumFormatted; }

    public void setTotalSumFormatted(String totalSumFormatted) { this.totalSumFormatted = totalSumFormatted; }

    public Long getTotal() { return this.total; }

    public void setTotal(Long total) { this.total = total; }

    public Long getSecondaryTotal() { return secondaryTotal; }

    public void setSecondaryTotal(Long secondaryTotal) { this.secondaryTotal = secondaryTotal; }
}
