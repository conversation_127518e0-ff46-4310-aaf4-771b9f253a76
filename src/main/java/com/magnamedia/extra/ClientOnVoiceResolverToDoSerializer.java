package com.magnamedia.extra;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.entity.Client;

import java.io.IOException;

/**
 * <AUTHOR>
 */
public class ClientOnVoiceResolverToDoSerializer extends JsonSerializer<Client> {

    @Override
    public void serialize(Client value, JsonGenerator gen, SerializerProvider sp) throws IOException, JsonProcessingException {
        if (value == null) {
            gen.writeNull();
            return;
        }
        gen.writeStartObject();
        gen.writeNumberField("id", value.getId());
        gen.writeStringField("label", value.getLabel());
        gen.writeStringField("name", value.getName());
        gen.writeStringField("spouseName", value.getSpouseName());
//        gen.writeStringField("mobileNumber", value.getMobileNumber());
//        gen.writeStringField("spouseMobileNumber", value.getSpouseMobileNumber());
//        gen.writeStringField("spouseMobileNumber", value.getSpouseMobileNumber());
        gen.writeStringField("gcc", value.getGcc() != null ? value.getGcc().toString() : "false");
        gen.writeStringField("vip", value.isVip() != null ? value.isVip().toString() : "false");


        gen.writeEndObject();
    }
}