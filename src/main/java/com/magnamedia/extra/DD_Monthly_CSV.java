package com.magnamedia.extra;

import java.util.Date;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Dec 17, 2020
 *         Jirra ACC-2906
 */
public class DD_Monthly_CSV {
    private Long contractId;
    private Date ddStartDate;
    private Date ddEndDate;
    private Double ddAmount;
    private Double discount;
    private Integer discountEffectiveAfter;

    public Long getContractId() {
        return contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }

    public Date getDdStartDate() {
        return ddStartDate;
    }

    public void setDdStartDate(Date ddStartDate) {
        this.ddStartDate = ddStartDate;
    }

    public Date getDdEndDate() {
        return ddEndDate;
    }

    public void setDdEndDate(Date ddEndDate) {
        this.ddEndDate = ddEndDate;
    }

    public Double getDdAmount() {
        return ddAmount;
    }

    public void setDdAmount(Double ddAmount) {
        this.ddAmount = ddAmount;
    }

    public Double getDiscount() {
        return discount;
    }

    public void setDiscount(Double discount) {
        this.discount = discount;
    }

    public Integer getDiscountEffectiveAfter() {
        return discountEffectiveAfter;
    }

    public void setDiscountEffectiveAfter(Integer discountEffectiveAfter) {
        this.discountEffectiveAfter = discountEffectiveAfter;
    }
}
