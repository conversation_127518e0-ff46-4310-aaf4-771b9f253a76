/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.extra;

import java.util.List;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
public class StringLibrary {
    
    public static Double CustomSimilarityAlgorithm(String s1, String s2, List<String> commonWords){
        if(s1==null)
            return 0.0;
        if(s2==null)
            return 0.0;
        
        //First, Remove spaces from both strings
        s1=s1.toUpperCase().replace(" ", "");
        s2=s2.toUpperCase().replace(" ", "");
        
        if(commonWords!=null){
            s1=remove(s1,commonWords);
            s2=remove(s2,commonWords);
        }
        
        Integer I=lcs(s1.toCharArray(), s2.toCharArray(), s1.length(), s2.length());
        Integer minimum=Math.min(s1.length(), s2.length());
        if(minimum==0)
            return 0.0;
        if(I>minimum)
            return 1.0;
        return Double.valueOf(I/minimum);
     }
    
    public static String remove(String s,List<String> commonWords){
        for(String temp:commonWords)
            s=s.replace(temp,"");
        return s;
    }
    /* Returns length of LCS for X[0..m-1], Y[0..n-1] */
    public static int lcs(char[] X, char[] Y, int m, int n) {
        int L[][] = new int[m + 1][n + 1];

        for (int i = 0; i <= m; i++) {
            for (int j = 0; j <= n; j++) {
                if (i == 0 || j == 0) {
                    L[i][j] = 0;
                } else if (X[i - 1] == Y[j - 1]) {
                    L[i][j] = L[i - 1][j - 1] + 1;
                } else {
                    L[i][j] = max(L[i - 1][j], L[i][j - 1]);
                }
            }
        }
        return L[m][n];
    }

    public static int max(int a, int b) {
        return (a > b) ? a : b;
    }

    public static boolean checkIfUpperCase(String s) {
        boolean flag = true;
        for (int i = 0; i < s.length(); i++) {
            char c = s.charAt(i);
            if (Character.isDigit(c)) {
                continue;
            }
            if (Character.isLowerCase(c)) {
                flag = false;
                break;
            } else {
                flag = true;
            }
        }
        return flag;
    }
}
