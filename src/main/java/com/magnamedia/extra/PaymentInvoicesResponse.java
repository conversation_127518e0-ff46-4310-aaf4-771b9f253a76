package com.magnamedia.extra;

import com.magnamedia.entity.Contract;

import java.util.List;

public class PaymentInvoicesResponse {
    private List<PaymentResponse> payments;
    private double totalPayments;
    private int numOfUngivenVacations;
    private Contract contract;

    public PaymentInvoicesResponse(List<PaymentResponse> payments, double totalPayments, int numOfUngivenVacations, Contract contract) {
        this.payments = payments;
        this.totalPayments = totalPayments;
        this.numOfUngivenVacations = numOfUngivenVacations;
        this.contract = contract;
    }

    public List<PaymentResponse> getPayments() {
        return payments;
    }

    public void setPayments(List<PaymentResponse> payments) {
        this.payments = payments;
    }

    public double getTotalPayments() {
        return totalPayments;
    }

    public void setTotalPayments(double totalPayments) {
        this.totalPayments = totalPayments;
    }

    public int getNumOfUngivenVacations() {
        return numOfUngivenVacations;
    }

    public void setNumOfUngivenVacations(int numOfUngivenVacations) {
        this.numOfUngivenVacations = numOfUngivenVacations;
    }
}