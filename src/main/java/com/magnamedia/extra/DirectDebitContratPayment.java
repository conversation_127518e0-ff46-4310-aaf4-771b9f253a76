package com.magnamedia.extra;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.module.type.ContractType;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.repository.ContractPaymentRepository;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Jan 21, 2019
 * Jirra ACC-391
 */
public class DirectDebitContratPayment {

    private Long clientId;
    private String clientName;
    private Long housemaidId;
    private String housemaidName;
    private Long contractId;
    private ContractType contractType;
    private Long nationalityId;
    private String nationalityName;
    private Date date;
    private PaymentMethod paymentMethod;
    private Long paymentTypeId;
    private String paymentTypeName;
    private Long directDebitId;
    private Double directDebitAmount;
    private Date directDebitCreationDate;
    //Jirra ACC-760
    private String referenceNumber;
    private List<Attachment> attachments;
    private long count;

    public DirectDebitContratPayment(
            Client client, Housemaid housemaid, Contract contract, PicklistItem nationality, PicklistItem paymentType, DirectDebit directDebit, long count) {
        ContractPaymentRepository contractPaymentRepository =
                Setup.getRepository(ContractPaymentRepository.class);
        if (client!= null){
            this.clientId = client.getId();
            this.clientName = client.getName();
        }
        if (housemaid!= null){
            this.housemaidId = housemaid.getId();
            this.housemaidName = housemaid.getName();
        }
        if (contract!= null){
            this.contractId = contract.getId();
            this.contractType = contract.getContractType();
        }
        if (nationality!= null){
            this.nationalityId = nationality.getId();
            this.nationalityName = nationality.getName();
        }
        this.date = contractPaymentRepository.getFirstDate(
                client, contract, nationality, paymentType, directDebit);
        if (paymentType!= null){
            this.paymentTypeId = paymentType.getId();
            this.paymentTypeName = paymentType.getName();
        }
        if (directDebit!= null){
            this.directDebitId =directDebit.getId();
            this.directDebitAmount = directDebit.getAmount();
            this.directDebitCreationDate = directDebit.getCreationDate();
            this.attachments = Storage.getAttachments(directDebit);
            //ask osamah
            //Jirra ACC-1588
//            this.referenceNumber = directDebit.getApplicationId();
        }
        this.count = count;
        this.paymentMethod = PaymentMethod.DIRECT_DEBIT;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public Long getHousemaidId() {
        return housemaidId;
    }

    public void setHousemaidId(Long housemaidId) {
        this.housemaidId = housemaidId;
    }

    public String getHousemaidName() {
        return housemaidName;
    }

    public void setHousemaidName(String housemaidName) {
        this.housemaidName = housemaidName;
    }

    public Long getContractId() {
        return contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }

    public ContractType getContractType() {
        return contractType;
    }

    public void setContractType(ContractType contractType) {
        this.contractType = contractType;
    }

    public Long getNationalityId() {
        return nationalityId;
    }

    public void setNationalityId(Long nationalityId) {
        this.nationalityId = nationalityId;
    }

    public String getNationalityName() {
        return nationalityName;
    }

    public void setNationalityName(String nationalityName) {
        this.nationalityName = nationalityName;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public PaymentMethod getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(PaymentMethod paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public Long getPaymentTypeId() {
        return paymentTypeId;
    }

    public void setPaymentTypeId(Long paymentTypeId) {
        this.paymentTypeId = paymentTypeId;
    }

    public String getPaymentTypeName() {
        return paymentTypeName;
    }

    public void setPaymentTypeName(String paymentTypeName) {
        this.paymentTypeName = paymentTypeName;
    }

    public Long getDirectDebitId() {
        return directDebitId;
    }

    public void setDirectDebitId(Long directDebitId) {
        this.directDebitId = directDebitId;
    }

    public Double getDirectDebitAmount() {
        return directDebitAmount;
    }

    public void setDirectDebitAmount(Double directDebitAmount) {
        this.directDebitAmount = directDebitAmount;
    }

    public Date getDirectDebitCreationDate() {
        return directDebitCreationDate;
    }

    public void setDirectDebitCreationDate(Date directDebitCreationDate) {
        this.directDebitCreationDate = directDebitCreationDate;
    }

    public List<Attachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<Attachment> attachments) {
        this.attachments = attachments;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public long getCount() {
        return count;
    }

    public void setCount(long count) {
        this.count = count;
    }
    
}

