package com.magnamedia.extra.ExpenseFlow;

import com.magnamedia.module.type.ExpensePaymentMethod;
import java.util.List;

public class CovidTestExpenseRequestDto {
    private Double amountForOne;
    private ExpensePaymentMethod expensePaymentMethod; 
    private List<Long> housemaidsIds;

    public CovidTestExpenseRequestDto() {
    }

    public Double getAmountForOne() {
        return amountForOne;
    }

    public void setAmountForOne(Double amountForOne) {
        this.amountForOne = amountForOne;
    }
    
    public ExpensePaymentMethod getExpensePaymentMethod() {
        return expensePaymentMethod;
    }

    public void setExpensePaymentMethod(ExpensePaymentMethod expensePaymentMethod) {
        this.expensePaymentMethod = expensePaymentMethod;
    }
    
    public List<Long> getHousemaidsIds() {
        return housemaidsIds;
    }

    public void setHousemaidsIds(List<Long> housemaidsIds) {
        this.housemaidsIds = housemaidsIds;
    }
}
