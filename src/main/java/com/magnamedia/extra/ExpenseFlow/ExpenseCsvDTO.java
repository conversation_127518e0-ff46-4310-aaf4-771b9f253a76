package com.magnamedia.extra.ExpenseFlow;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.magnamedia.controller.ExpensesController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.entity.Bucket;
import com.magnamedia.entity.Expense;
import com.magnamedia.entity.ExpenseRelatedTo;
import com.magnamedia.entity.ExpenseRelatedToTeam;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by Mamon.Masod on 4/19/2021.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ExpenseCsvDTO {
    protected static final Logger logger = Logger.getLogger(ExpenseCsvDTO.class.getName());

    private String oldExpenseName;
    private String oldExpenseCode;
    private String newExpenseName;
    private String newExpenseCode;
    private String expenseCaption;
    private Boolean subExpense;
    private String parentExpense;
    private Long expenseManager;
    private String requestedFrom;
    private Boolean needsAttachment;
    private Boolean requiresInvoice;
    private String requesters;
    private String approvalMethod;
    private String approveHolderType;
    private Long approvedByUser;
    private String approvedByEmail;
    private String approvedByFinalManager;
    private Double limitForApproval;
    private Boolean enableCOOApprovalLimit;
    private Double LimitForCOOApproval;
    private String expensePaymentMethod;
    private Boolean paidOnSpotCash;
    private Boolean paidOnSpotCard;
    private String cashBucket;
    private String cardBucket;
    private String additionReason;
    private Boolean allowToAddLoan;
    private String loanType;
    private Double defaultAmountForExpense;
    private String beneficiaryType;
    private String suppliers;
    private Boolean relatedToMaid;
    private Boolean relatedToApplicant;
    private Boolean relatedToOfficeStaff;
    private Boolean relatedToTeam;
    private String expenseNameOfMaidCc;
    private String expenseNameOfMaidVisa;
    private String expenseNameOfApplicant;
    private String expenseNameOfOfficeStaff;
    private String expenseNameOfTeam;
    private Boolean autoDeducted;
    private Boolean allowToCreateSubExpenseFromRequestExpensesPage;
    private Boolean allowToAddSupplierInExpenseRequest;

    private String expenseGeneratedWithCode;

    public String getExpenseGeneratedWithCode() {
        return expenseGeneratedWithCode;
    }

    public void setExpenseGeneratedWithCode(String expenseGeneratedWithCode) {
        this.expenseGeneratedWithCode = expenseGeneratedWithCode;
    }

    public String getOldExpenseName() {
        return oldExpenseName;
    }

    public void setOldExpenseName(String oldExpenseName) {
        this.oldExpenseName = oldExpenseName;
    }

    public String getOldExpenseCode() {
        return oldExpenseCode;
    }

    public void setOldExpenseCode(String oldExpenseCode) {
        this.oldExpenseCode = oldExpenseCode;
    }

    public String getNewExpenseName() {
        return newExpenseName;
    }

    public void setNewExpenseName(String newExpenseName) {
        this.newExpenseName = newExpenseName;
    }

    public String getNewExpenseCode() {
        Expense parentExpense = this.getParentExpenseAsExpense();
        if (parentExpense != null) {
            return Setup.getApplicationContext().getBean(ExpensesController.class).getSubExpenseCode(parentExpense);
        }

        if (!StringUtils.isEmpty(this.newExpenseCode) || StringUtils.isEmpty(this.newExpenseName))
            return this.newExpenseCode;

        String code = PicklistItem.getCode(this.newExpenseName);
        logger.info("Replacing Expense Code with Expense Name: " + code);
        return code;
    }

    public void setNewExpenseCode(String newExpenseCode) {
        this.newExpenseCode = newExpenseCode;
    }

    public String getExpenseCaption() {
        return expenseCaption;
    }

    public void setExpenseCaption(String expenseCaption) {
        this.expenseCaption = expenseCaption;
    }

    public Boolean getSubExpense() {
        return subExpense;
    }

    public void setSubExpense(Boolean subExpense) {
        this.subExpense = subExpense;
    }

    public String getParentExpense() {
        return parentExpense;
    }

    public void setParentExpense(String parentExpense) {
        this.parentExpense = parentExpense;
    }

    @JsonIgnore
    public Expense getParentExpenseAsExpense() {
        if (parentExpense == null || parentExpense.isEmpty() || !BooleanUtils.toBoolean(subExpense))
            return null;

        return Setup.getRepository(ExpenseRepository.class).findOneByCode(parentExpense);
    }

    public Long getExpenseManager() {
        return expenseManager;
    }

    @JsonIgnore
    public User getExpenseManagerAsUser() {
        return expenseManager != null ? Setup.getRepository(UserRepository.class).findOne(expenseManager) : null;
    }

    public void setExpenseManager(Long expenseManager) {
        this.expenseManager = expenseManager;
    }

    public String getRequestedFrom() {
        return requestedFrom;
    }

    @JsonIgnore
    public PicklistItem getRequestedFromAsPickListItem() {
        if (requestedFrom == null || requestedFrom.isEmpty()) return null;

        List<PicklistItem> picklistItems = Setup.getRepository(PicklistRepository.class).findByCode(AccountingModule.PICKLIST_EXPENSE_REQUESTED_FROM).getItems();

        for (PicklistItem item : picklistItems) {
            if (item.getName().toLowerCase().contains(requestedFrom.toLowerCase())) {
                return item;
            }
        }

        return null;
    }

    public void setRequestedFrom(String requestedFrom) {
        this.requestedFrom = requestedFrom;
    }

    public Boolean getNeedsAttachment() {
        return needsAttachment;
    }

    public void setNeedsAttachment(Boolean needsAttachment) {
        this.needsAttachment = needsAttachment;
    }

    public Boolean getRequiresInvoice() {
        return requiresInvoice;
    }

    public void setRequiresInvoice(Boolean requiresInvoice) {
        this.requiresInvoice = requiresInvoice;
    }

    public String getRequesters() {
        return requesters;
    }

    @JsonIgnore
    public List<User> getRequestersAsListOfUsers() {
        if (requesters == null || requesters.isEmpty()) return new ArrayList();

        UserRepository userRepo = Setup.getRepository(UserRepository.class);
        return Arrays.stream(requesters.split("[\\r\\n]+"))
                .map(id -> userRepo.findOne(Long.parseLong(id.replaceAll("\\s", ""))))
                .collect(Collectors.toList());
    }

    public void setRequesters(String requesters) {
        this.requesters = requesters;
    }

    public String getApprovalMethod() {
        return approvalMethod;
    }

    @JsonIgnore
    public ExpenseApprovalMethod getApprovalMethodAsEnum() {
        if (approvalMethod == null || approvalMethod.isEmpty()) return null;

        return ExpenseApprovalMethod.valueOf(approvalMethod);
    }

    public void setApprovalMethod(String approvalMethod) {
        this.approvalMethod = approvalMethod;
    }

    public String getApproveHolderType() {
        return approveHolderType;
    }

    @JsonIgnore
    public ExpenseApproveHolderType getApproveHolderTypeAsEnum() {
        if (approveHolderType == null || approveHolderType.isEmpty()) return null;

        return ExpenseApproveHolderType.valueOf(approveHolderType);
    }

    public void setApproveHolderType(String approveHolderType) {
        this.approveHolderType = approveHolderType;
    }

    public Long getApprovedByUser() {
        return approvedByUser;
    }

    @JsonIgnore
    public User getApproverUserAsUser() {
        return approvedByUser != null ? Setup.getRepository(UserRepository.class).findOne(approvedByUser) : null;
    }

    public void setApprovedByUser(Long approvedByUser) {
        this.approvedByUser = approvedByUser;
    }

    public String getApprovedByEmail() {
        return approvedByEmail;
    }

    public void setApprovedByEmail(String approvedByEmail) {
        this.approvedByEmail = approvedByEmail;
    }

    public String getApprovedByFinalManager() {
        return approvedByFinalManager;
    }

    public void setApprovedByFinalManager(String approvedByFinalManager) {
        this.approvedByFinalManager = approvedByFinalManager;
    }

    public Double getLimitForApproval() {
        return limitForApproval;
    }

    public void setLimitForApproval(Double limitForApproval) {
        this.limitForApproval = limitForApproval;
    }

    public Boolean getEnableCOOApprovalLimit() {
        return enableCOOApprovalLimit;
    }

    public void setEnableCOOApprovalLimit(Boolean enableCOOApprovalLimit) {
        this.enableCOOApprovalLimit = enableCOOApprovalLimit;
    }

    public Double getLimitForCOOApproval() {
        return LimitForCOOApproval;
    }

    public void setLimitForCOOApproval(Double limitForCOOApproval) {
        LimitForCOOApproval = limitForCOOApproval;
    }

    public String getExpensePaymentMethod() {
        return expensePaymentMethod;
    }

    @JsonIgnore
    public Set<ExpensePaymentMethod> getExpensePaymentMethodAsSet() {
        if (expensePaymentMethod == null || expensePaymentMethod.isEmpty()) return new HashSet();

        Set<ExpensePaymentMethod> expensePaymentMethods = new HashSet();

        Arrays.stream(expensePaymentMethod.split("[\\r\\n]+")).map(paymentMethod -> ExpensePaymentMethod.valueOf(paymentMethod.replaceAll("\\s", "")))
                .forEach(paymentMethod -> expensePaymentMethods.add(paymentMethod));

        return expensePaymentMethods;
    }

    public void setExpensePaymentMethod(String expensePaymentMethod) {
        this.expensePaymentMethod = expensePaymentMethod;
    }

    public Boolean getPaidOnSpotCash() {
        return paidOnSpotCash;
    }

    public void setPaidOnSpotCash(Boolean paidOnSpotCash) {
        this.paidOnSpotCash = paidOnSpotCash;
    }

    public Boolean getPaidOnSpotCard() {
        return paidOnSpotCard;
    }

    public void setPaidOnSpotCard(Boolean paidOnSpotCard) {
        this.paidOnSpotCard = paidOnSpotCard;
    }

    public String getCashBucket() {
        return cashBucket;
    }

    @JsonIgnore
    public Bucket getCashBucketAsBucket() {
        if (cashBucket == null || cashBucket.isEmpty()) return null;

        return Setup.getRepository(BucketRepository.class).findByCode(cashBucket);
    }

    public void setCashBucket(String cashBucket) {
        this.cashBucket = cashBucket;
    }

    public String getCardBucket() {
        return cardBucket;
    }

    @JsonIgnore
    public Bucket getCardBucketAsBucket() {
        if (cardBucket == null || cardBucket.isEmpty()) return null;

        return Setup.getRepository(BucketRepository.class).findByCode(cardBucket);
    }

    public void setCardBucket(String cardBucket) {
        this.cardBucket = cardBucket;
    }

    public String getAdditionReason() {
        return additionReason;
    }

    @JsonIgnore
    public PicklistItem getAdditionReasonAsPickListItem() {
        if (additionReason == null || additionReason.isEmpty()) return null;

        return PicklistHelper.getItem(AccountingModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE, additionReason);
    }

    public void setAdditionReason(String additionReason) {
        this.additionReason = additionReason;
    }

    public Boolean getAllowToAddLoan() {
        return allowToAddLoan;
    }

    public void setAllowToAddLoan(Boolean allowToAddLoan) {
        this.allowToAddLoan = allowToAddLoan;
    }

    public String getLoanType() {
        return loanType;
    }

    @JsonIgnore
    public LoanType getLoanTypeAsEnum() {
        if (loanType == null || loanType.isEmpty()) return null;

        return LoanType.valueOf(loanType);
    }

    public void setLoanType(String loanType) {
        this.loanType = loanType;
    }

    public Double getDefaultAmountForExpense() {
        return defaultAmountForExpense;
    }


    public void setDefaultAmountForExpense(Double defaultAmountForExpense) {
        this.defaultAmountForExpense = defaultAmountForExpense;
    }

    public String getBeneficiaryType() {
        return beneficiaryType;
    }

    @JsonIgnore
    public ExpenseBeneficiaryType getBeneficiaryTypeAsEnum() {
        if (beneficiaryType == null || beneficiaryType.isEmpty()) return null;

        return ExpenseBeneficiaryType.valueOf(beneficiaryType);
    }

    public void setBeneficiaryType(String beneficiaryType) {
        this.beneficiaryType = beneficiaryType;
    }

    public String getSuppliers() {
        return suppliers;
    }

    @JsonIgnore
    public List getSuppliersAsList() {
        if (suppliers == null || suppliers.isEmpty()) return new ArrayList();

        SupplierRepository supplierRepo = Setup.getRepository(SupplierRepository.class);
        return Arrays.stream(suppliers.split("[\\r\\n]+"))
                .map(name -> supplierRepo.findFirstByName(name))
                .collect(Collectors.toList());
    }

    public void setSuppliers(String suppliers) {
        this.suppliers = suppliers;
    }

    @JsonIgnore
    public List<ExpenseRelatedTo> getExpenseRelatedToList(Expense expense) {
//        initRelatedToList(expense);

        ExpenseRepository expenseRepo = Setup.getRepository(ExpenseRepository.class);
        ExpenseRelatedToRepository expenseRelatedToRepo = Setup.getRepository(ExpenseRelatedToRepository.class);
        List<ExpenseRelatedTo> expenseRelatedTos = new ArrayList();

        if (BooleanUtils.toBoolean(relatedToMaid)) {
            Expense maidCCExpense = !StringUtils.isEmpty(expenseNameOfMaidCc) ? expenseRepo.findOneByCode(expenseNameOfMaidCc) : null;
            Expense maidVisaExpense = !StringUtils.isEmpty(expenseNameOfMaidVisa) ? expenseRepo.findOneByCode(expenseNameOfMaidVisa) : null;

            ExpenseRelatedTo expenseRelatedTo = new ExpenseRelatedTo();
            expenseRelatedTo.setExpense(expense);
            expenseRelatedTo.setRelatedToType(ExpenseRelatedTo.ExpenseRelatedToType.MAID);
            expenseRelatedTo.setExpenseCC(maidCCExpense);
            expenseRelatedTo.setExpenseVisa(maidVisaExpense);

            expenseRelatedTos.add(expenseRelatedToRepo.save(expenseRelatedTo));
        }

        if (BooleanUtils.toBoolean(relatedToApplicant)) {
            Expense applicantExpense = !StringUtils.isEmpty(expenseNameOfApplicant) ? expenseRepo.findOneByCode(expenseNameOfApplicant) : null;

            ExpenseRelatedTo expenseRelatedTo = new ExpenseRelatedTo();
            expenseRelatedTo.setExpense(expense);
            expenseRelatedTo.setRelatedExpense(applicantExpense);
            expenseRelatedTo.setRelatedToType(ExpenseRelatedTo.ExpenseRelatedToType.APPLICANT);

            expenseRelatedTos.add(expenseRelatedToRepo.save(expenseRelatedTo));
        }

        if (BooleanUtils.toBoolean(relatedToOfficeStaff)) {
            Expense officeStaffExpense = !StringUtils.isEmpty(expenseNameOfOfficeStaff) ? expenseRepo.findOneByCode(expenseNameOfOfficeStaff) : null;

            ExpenseRelatedTo expenseRelatedTo = new ExpenseRelatedTo();
            expenseRelatedTo.setExpense(expense);
            expenseRelatedTo.setRelatedExpense(officeStaffExpense);
            expenseRelatedTo.setRelatedToType(ExpenseRelatedTo.ExpenseRelatedToType.OFFICE_STAFF);

            expenseRelatedTos.add(expenseRelatedToRepo.save(expenseRelatedTo));
        }

        if (BooleanUtils.toBoolean(relatedToTeam)) {
            String[] expenseDetails = !StringUtils.isEmpty(expenseNameOfTeam) ? expenseNameOfTeam.split("/") : null;
            String teamName = expenseDetails != null && expenseDetails.length > 0 ? expenseDetails[0].trim() : null;
            String expenseCode = expenseDetails != null && expenseDetails.length > 1 ? expenseDetails[1].trim() : null;

            ExpenseRelatedTo expenseRelatedTo = new ExpenseRelatedTo();
            expenseRelatedTo.setExpense(expense);
            expenseRelatedTo.setRelatedToType(ExpenseRelatedTo.ExpenseRelatedToType.TEAM);
            expenseRelatedTo = expenseRelatedToRepo.save(expenseRelatedTo);

            List<ExpenseRelatedToTeam> expenseRelatedToTeams = new ArrayList();

            if (!StringUtils.isEmpty(teamName)) {
                if (teamName.equalsIgnoreCase("all teams")) {
                    List<PicklistItem> allTeams = Setup.getRepository(PicklistRepository.class).findByCode(AccountingModule.PICKLIST_OFFICESTAFF_TEAM_CODE).getItems();
                    for (PicklistItem team : allTeams) {
                        expenseRelatedToTeams.add(createExpenseRelatedToTeam(expenseRelatedTo, expenseCode, team.getCode()));
                    }
                } else {
                    expenseRelatedToTeams.add(createExpenseRelatedToTeam(expenseRelatedTo, expenseCode, teamName));
                }
            }

            expenseRelatedTo.setTeams(expenseRelatedToTeams);

            expenseRelatedTos.add(expenseRelatedToRepo.save(expenseRelatedTo));
        }

        return expenseRelatedTos;
    }

   /* private void initRelatedToList(Expense expense) {
        if (expense == null || expense.getRelatedTos() == null) return;

        ExpenseRelatedToRepository expenseRelatedToRepo = Setup.getRepository(ExpenseRelatedToRepository.class);
        ExpenseRelatedToTeamRepository expenseRelatedToTeamRepo = Setup.getRepository(ExpenseRelatedToTeamRepository.class);
        for (ExpenseRelatedTo expenseRelatedTo : expense.getRelatedTos()) {
            if (expenseRelatedTo.getRelatedToType() != null && expenseRelatedTo.getRelatedToType().equals(ExpenseRelatedTo.ExpenseRelatedToType.TEAM)) {
                if (expenseRelatedTo.getTeams() == null) continue;

                for (ExpenseRelatedToTeam expenseRelatedToTeam : expenseRelatedTo.getTeams()) {
                    logger.warning("Deleting expenseRelatedToTeam#" + expenseRelatedToTeam.getId() + ", Name: " + (expenseRelatedToTeam.getTeam() != null ? expenseRelatedToTeam.getTeam().getName() : "NULL"));
                    expenseRelatedToTeamRepo.delete(expenseRelatedToTeam);
                }
            }

            logger.warning("Deleting expenseRelatedTo#" + expenseRelatedTo.getId() + ", Type: " + expenseRelatedTo.getRelatedToType());
            expenseRelatedToRepo.delete(expenseRelatedTo);
        }
    }*/

    @JsonIgnore
    private ExpenseRelatedToTeam createExpenseRelatedToTeam(ExpenseRelatedTo expenseRelatedTo, String expenseCode, String teamNameOrCode) {
        ExpenseRepository expenseRepo = Setup.getRepository(ExpenseRepository.class);

        ExpenseRelatedToTeam expenseRelatedToTeam = new ExpenseRelatedToTeam();
        expenseRelatedToTeam.setRelatedTo(expenseRelatedTo);
        expenseRelatedToTeam.setRelatedExpense(!StringUtils.isEmpty(expenseCode) ? expenseRepo.findOneByCode(expenseCode) : null);
        expenseRelatedToTeam.setTeam(PicklistHelper.getItem(AccountingModule.PICKLIST_OFFICESTAFF_TEAM_CODE, teamNameOrCode));
        expenseRelatedToTeam = Setup.getRepository(ExpenseRelatedToTeamRepository.class).save(expenseRelatedToTeam);

        return expenseRelatedToTeam;
    }

    public Boolean getRelatedToMaid() {
        return relatedToMaid;
    }

    public void setRelatedToMaid(Boolean relatedToMaid) {
        this.relatedToMaid = relatedToMaid;
    }

    public Boolean getRelatedToApplicant() {
        return relatedToApplicant;
    }

    public void setRelatedToApplicant(Boolean relatedToApplicant) {
        this.relatedToApplicant = relatedToApplicant;
    }

    public Boolean getRelatedToOfficeStaff() {
        return relatedToOfficeStaff;
    }

    public void setRelatedToOfficeStaff(Boolean relatedToOfficeStaff) {
        this.relatedToOfficeStaff = relatedToOfficeStaff;
    }

    public Boolean getRelatedToTeam() {
        return relatedToTeam;
    }

    public void setRelatedToTeam(Boolean relatedToTeam) {
        this.relatedToTeam = relatedToTeam;
    }

    public String getExpenseNameOfMaidCc() {
        return expenseNameOfMaidCc;
    }

    public void setExpenseNameOfMaidCc(String expenseNameOfMaidCc) {
        this.expenseNameOfMaidCc = expenseNameOfMaidCc;
    }

    public String getExpenseNameOfMaidVisa() {
        return expenseNameOfMaidVisa;
    }

    public void setExpenseNameOfMaidVisa(String expenseNameOfMaidVisa) {
        this.expenseNameOfMaidVisa = expenseNameOfMaidVisa;
    }

    public String getExpenseNameOfApplicant() {
        return expenseNameOfApplicant;
    }

    public void setExpenseNameOfApplicant(String expenseNameOfApplicant) {
        this.expenseNameOfApplicant = expenseNameOfApplicant;
    }

    public String getExpenseNameOfOfficeStaff() {
        return expenseNameOfOfficeStaff;
    }

    public void setExpenseNameOfOfficeStaff(String expenseNameOfOfficeStaff) {
        this.expenseNameOfOfficeStaff = expenseNameOfOfficeStaff;
    }

    public String getExpenseNameOfTeam() {
        return expenseNameOfTeam;
    }

    public void setExpenseNameOfTeam(String expenseNameOfTeam) {
        this.expenseNameOfTeam = expenseNameOfTeam;
    }

    public Boolean getAutoDeducted() {
        return autoDeducted;
    }

    public void setAutoDeducted(Boolean autoDeducted) {
        this.autoDeducted = autoDeducted;
    }

    public Boolean getAllowToCreateSubExpenseFromRequestExpensesPage() {
        return allowToCreateSubExpenseFromRequestExpensesPage;
    }

    public void setAllowToCreateSubExpenseFromRequestExpensesPage(Boolean allowToCreateSubExpenseFromRequestExpensesPage) {
        this.allowToCreateSubExpenseFromRequestExpensesPage = allowToCreateSubExpenseFromRequestExpensesPage;
    }

    public Boolean getAllowToAddSupplierInExpenseRequest() {
        return allowToAddSupplierInExpenseRequest;
    }

    public void setAllowToAddSupplierInExpenseRequest(Boolean allowToAddSupplierInExpenseRequest) {
        this.allowToAddSupplierInExpenseRequest = allowToAddSupplierInExpenseRequest;
    }

    @JsonIgnore
    public Expense fillExpenseProps(Expense expense) {
        expense.setManager(this.getExpenseManagerAsUser());
        expense.setRequestedFrom(this.getRequestedFromAsPickListItem());
        expense.setRequireAttachment(this.getNeedsAttachment());
        expense.setRequireInvoice(this.getRequiresInvoice());
        expense.setRequestors(this.getRequestersAsListOfUsers());
        expense.setApprovalMethod(this.getApprovalMethodAsEnum());
        expense.setApproveHolderType(this.getApproveHolderTypeAsEnum());
        expense.setApproveHolder(this.getApproverUserAsUser());
        expense.setApproveHolderEmail(this.getApprovedByEmail());
        expense.setLimitForApproval(this.getLimitForApproval());
        expense.setLimitedCOO(this.getEnableCOOApprovalLimit());
        expense.setLimitCOO(this.getLimitForCOOApproval());
        expense.setPaymentMethods(this.getExpensePaymentMethodAsSet());
        expense.setPaidOnTheSpotCash(this.getPaidOnSpotCash());
        expense.setPaidOnTheSpotCreditCard(this.getPaidOnSpotCard());
        expense.setFromCashBuckets(Collections.singletonList(this.getCashBucketAsBucket()));
        expense.setFromCreditCardBuckets(Collections.singletonList(this.getCardBucketAsBucket()));
        expense.setSalaryAdditionType(this.getAdditionReasonAsPickListItem());
        expense.setAllowToAddLoan(this.getAllowToAddLoan());
        expense.setLoanType(this.getLoanTypeAsEnum());
        expense.setDefaultAmount(this.getDefaultAmountForExpense());
        expense.setBeneficiaryType(this.getBeneficiaryTypeAsEnum());
        expense.setSuppliers(this.getSuppliersAsList());
        expense.setAutoDeducted(this.getAutoDeducted());
        expense.setAllowToAddSupplierToExpenseRequest(BooleanUtils.toBoolean(this.getAllowToAddSupplierInExpenseRequest()));
        expense.setAllowSubExpense(this.getAllowToCreateSubExpenseFromRequestExpensesPage());

        expense = (Expense) Setup.getApplicationContext().getBean(ExpensesController.class).createEntity(expense).getBody();

        expense.setRelatedTos(this.getExpenseRelatedToList(expense));

        return expense;
    }

}
