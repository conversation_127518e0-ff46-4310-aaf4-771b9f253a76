package com.magnamedia.extra.ExpenseFlow;

public class TransportationExpenseAttachmentRecord {
    private String bookingId;
    private String passenger;
    private String date;
    private String pickupLocation;
    private String dropOffLocation;
    private Double billAmount;

    public TransportationExpenseAttachmentRecord(String bookingId, String passenger, String date, String pickupLocation, String dropOffLocation, Double billAmount) {
        this.bookingId = bookingId;
        this.passenger = passenger;
        this.date = date;
        this.pickupLocation = pickupLocation;
        this.dropOffLocation = dropOffLocation;
        this.billAmount = billAmount;
    }

    public TransportationExpenseAttachmentRecord() {
    }

    public String getPassenger() {
        return passenger;
    }

    public void setPassenger(String passenger) {
        this.passenger = passenger;
    }

    public String getPickupLocation() {
        return pickupLocation;
    }

    public void setPickupLocation(String pickupLocation) {
        this.pickupLocation = pickupLocation;
    }

    public String getDropOffLocation() {
        return dropOffLocation;
    }

    public void setDropOffLocation(String dropOffLocation) {
        this.dropOffLocation = dropOffLocation;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getBookingId() {
        return bookingId;
    }

    public void setBookingId(String bookingId) {
        this.bookingId = bookingId;
    }

    public Double getBillAmount() {
        return billAmount;
    }

    public void setBillAmount(Double billAmount) {
        this.billAmount = billAmount;
    }
}
