package com.magnamedia.extra.ExpenseFlow;

import java.util.List;

public class ParsedTransportationExpenseAttachmentDto {
    private List<TaxiOrderDto> erpTaxiOrders;
    private List<TransportationExpenseAttachmentRecord> attachmentTaxiOrders;


    public ParsedTransportationExpenseAttachmentDto(List<TaxiOrderDto> erpTaxiOrders,
                                                    List<TransportationExpenseAttachmentRecord> attachmentTaxiOrders) {
        this.erpTaxiOrders = erpTaxiOrders;
        this.attachmentTaxiOrders = attachmentTaxiOrders;
    }

    public ParsedTransportationExpenseAttachmentDto() {
    }

    public List<TaxiOrderDto> getErpTaxiOrders() {
        return erpTaxiOrders;
    }

    public void setErpTaxiOrders(List<TaxiOrderDto> erpTaxiOrders) {
        this.erpTaxiOrders = erpTaxiOrders;
    }

    public List<TransportationExpenseAttachmentRecord> getAttachmentTaxiOrders() {
        return attachmentTaxiOrders;
    }

    public void setAttachmentTaxiOrders(List<TransportationExpenseAttachmentRecord> attachmentTaxiOrders) {
        this.attachmentTaxiOrders = attachmentTaxiOrders;
    }

}
