/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.extra;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
public class PayslipImage{
        public String cleanerName;
        public String cleanerId;
        public String cleanerType;
        public String payrollMonth;
        public String payslipImageBody;
        
        public PayslipImage(String cleanerName, String cleanerId, String payrollMonth, String payslipImageBody, String cleanerType){
            this.cleanerName = cleanerName;
            this.cleanerId = cleanerId;
            this.payrollMonth = payrollMonth;
            this.payslipImageBody = payslipImageBody;
            this.cleanerType = cleanerType;
        }

        public PayslipImage() {
        }

        public String getCleanerName() {
            return cleanerName;
        }

        public void setCleanerName(String cleanerName) {
            this.cleanerName = cleanerName;
        }

        public String getCleanerId() {
            return cleanerId;
        }

        public void setCleanerId(String cleanerId) {
            this.cleanerId = cleanerId;
        }

        public String getCleanerType() {
            return cleanerType;
        }

        public void setCleanerType(String cleanerType) {
            this.cleanerType = cleanerType;
        }

        public String getPayrollMonth() {
            return payrollMonth;
        }

        public void setPayrollMonth(String payrollMonth) {
            this.payrollMonth = payrollMonth;
        }

        public String getPayslipImageBody() {
            return payslipImageBody;
        }

        public void setPayslipImageBody(String payslipImageBody) {
            this.payslipImageBody = payslipImageBody;
        }
        
        
    }
   
