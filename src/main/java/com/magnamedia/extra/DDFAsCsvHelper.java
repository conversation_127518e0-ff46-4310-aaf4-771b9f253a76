package com.magnamedia.extra;

import com.magnamedia.core.Setup;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.DDFExportingConfig;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.entity.projection.DDFAsCsvProjection;
import com.magnamedia.helper.CsvHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.DDFExportingConfigRepository;
import com.magnamedia.repository.DirectDebitFileRepository;

import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Apr 10, 2020
 *         Jirra ACC-1604
 */

public class DDFAsCsvHelper {

    String[] headers = new String[]{"Record number", "Originator Identification Code", "Payer identification number with originator",
            "Customer type", "Customer ID Type", "Customer ID number", "DDA Issued for", "Payer name", "Payer mobile number",
            "Payer email address", "Funding account type", "IBAN", "DDA Commencement date", "DDA Expiry date", "Debit request instance allowed",
            "DDA Amount type", "Payment Frequency", "DDR Defined days", "DDR Maximum Amount / Fixed Amount 2", "DDR Minimum Amount / Fixed Amount 1", "Capture mode", "Scanned DDA file name"};

    String[] columns = new String[]{"recordNumber", "oic", "applicationId", "customerType", "customerIdType", "eid", "dDAIssuedFor"
            , "payerName", "payerMobileNumber", "payerEmailAddress", "fundingAccountType", "iban", "dDACommencementDate", "dDAExpiryDate", "debitRequestInstanceAllowed"
            , "dDAAmountType", "paymentFrequency", "dDRDefinedDays", "dDRMaximumAmount", "dDRMinimumAmount", "captureMode", "scannedDDAFileName"};

    private Map<String, DDFList> map;
    private Integer maxSignatures;
    private Integer maxBatchSize;
    private DDFExportingConfig ddfExportingConfig;

    public DDFAsCsvHelper() {

    }

    public DDFAsCsvHelper(DDFExportingConfig ddfExportingConfig) {
        this.ddfExportingConfig = ddfExportingConfig;
        maxSignatures = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_SIGNATURE_BATCH_SIZE));
        maxBatchSize = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_ACTIVATION_BATCH_MAX));
        map = new HashMap();
    }

    public void put(Long id) {
        String positionKey = getPositionKey(id);
        map.get(positionKey).add(id);
    }

    private String createNewIndex() {
        DDFList ddfList = new DDFList();
        ddfList.fileName = getNewFileName();
        String key = ddfList.fileName;
        map.put(key, ddfList);
        return key;
    }

    private String getPositionKey(Long id) {
        for (String key : map.keySet()) {
            if (map.get(key).size() < maxBatchSize && this.getSignaturesByClient(key, id) < maxSignatures) {
                return key;
            }
        }

        return createNewIndex();
    }

    private Integer getSignaturesByClient(String key, Long id) {
        DDFList ddfList = map.get(key);
        Integer clientSignatures = 0;
        DirectDebitFileRepository ddfRepository = Setup.getRepository(DirectDebitFileRepository.class);
        DirectDebitFile ddf = ddfRepository.findOne(id);
        Client ddfClient = ddf.getDirectDebit().getContractPaymentTerm().getContract().getClient();

        for (Long ddfId : ddfList.getIds()) {
            DirectDebitFile ddf1 = ddfRepository.findOne(ddfId);
            if (ddfClient.getId().equals(ddf1.getDirectDebit().getContractPaymentTerm().getContract().getClient().getId())) {
                clientSignatures++;
            }
        }

        return clientSignatures;
    }

    public List<File> generateCsvFiles() throws IOException {
        List<File> csvFiles = new ArrayList();

        for (DDFList ddfList : map.values()) {
            csvFiles.add(generateCsvFile(ddfList));
        }

        return csvFiles;
    }

    public File generateCsvFile(DDFList ddfList) throws IOException {
        return generateCsvFile(ddfList.ids, ddfList.fileName);
    }

    public File generateCsvFile(List<Long> ids, String fileName) throws IOException {
        List<DDFAsCsvBean> ddfAsCsvBeans = new ArrayList();
        DirectDebitFileRepository ddfRepo = Setup.getRepository(DirectDebitFileRepository.class);
        List<DirectDebitFile> directDebitFileList = ddfRepo.findAll(ids);
        Integer recordNumber = 1;
        for (DirectDebitFile directDebitFile : directDebitFileList) {
            ddfAsCsvBeans.add(new DDFAsCsvBean(directDebitFile, recordNumber++, ddfExportingConfig));
        }

        File csvFile = CsvHelper.generateCsv(ddfAsCsvBeans, DDFAsCsvProjection.class, columns, fileName, ".dds");

        return csvFile;
    }

    private String getNewFileName() {
        DDFExportingConfigRepository repository = Setup.getRepository(DDFExportingConfigRepository.class);
        Date now = new Date();
        String fileName = ddfExportingConfig.getFileIndex() + DateUtil.formatDayNumber(now)
                + DateUtil.formatMonthNumber(now) + DateUtil.formatYearNumber(now) + ddfExportingConfig.getOic()
                + String.format("%08d", ddfExportingConfig.getInitialSeqNumber());

        ddfExportingConfig.setInitialSeqNumber(ddfExportingConfig.getInitialSeqNumber() + 1);
        repository.save(ddfExportingConfig);

        return fileName;
    }

    public DDFList get(String key) {
        return map.get(key);
    }

    public Collection<DDFList> values() {
        return map.values();
    }


    public class DDFList {
        private String fileName;
        private List<Long> ids;

        public DDFList() {
            ids = new ArrayList();
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public List<Long> getIds() {
            return ids;
        }

        public void setIds(List<Long> ids) {
            this.ids = ids;
        }

        private void add(Long id) {
            ids.add(id);
        }

        private Integer size() {
            return ids.size();
        }

    }
}
