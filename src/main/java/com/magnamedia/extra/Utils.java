package com.magnamedia.extra;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.Shortener;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.type.CoreParameter;
import com.magnamedia.entity.AccountingLink;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.helper.ContractPaymentTermHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.service.AccountingLinkService;
import com.magnamedia.service.IncompleteDirectDebitService;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
@Component
public class Utils {
    public static final List<String> perDiscountMonthArabic = Arrays.asList("الاول", "الثاني", "الثالث", "الرابع", "الخامس", "السادس", "السابع", "الثامن", "التاسع", "العاشر", "الحادي عشر", "الثاني عشر", "الثالث عشر");
    public static final List<String> perDiscountMonthEnglish = Arrays.asList("first", "second", "third", "fourth", "fifth", "sixth", "seventh", "eighth", "ninth", "tenth", "eleventh", "twelfth", "thirteenth ");


    private static final Logger logger = Logger.getLogger(Utils.class.getName());

    @Autowired
    private Shortener shortener;

    public Properties getClientEmailSenderProperties(Boolean isMaidVisa) {
        Properties properties = new Properties();
        try {
            String emailConfig = isMaidVisa ?
                    Setup.getParameter(Setup.getModule("sales"),
                            AccountingModule.PARAMETER_CLIENTS_EMAIL_CONFIG_MAID_VISA) :
                    Setup.getParameter(Setup.getModule("sales"),
                            AccountingModule.PARAMETER_CLIENTS_EMAIL_CONFIG);

            ObjectMapper mapper = new ObjectMapper();
            properties.putAll(mapper.readValue(
                    emailConfig, new TypeReference<Map<String, String>>() {}));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return properties;
    }

    // ACC-1435 from here
    public <T extends BaseEntity> List<T> readObjectsFromJson(
            List<JsonNode> nodes,
            BaseRepository<T> repository,
            Class<T> entityClass) {

        if (nodes == null) return null;

        List<T> entities = new ArrayList<>();
        nodes.forEach(jsonNode -> {
            try {
                entities.add(readObjectFromJson(jsonNode, repository, entityClass));
            } catch (IOException e) {
                throw new RuntimeException("error while convect json to entity");
            }
        });
        return entities;
    }

    @Autowired
    private ObjectMapper objectMapper;

    public <T extends BaseEntity> T readObjectFromJson(JsonNode node, BaseRepository<T> repository,
                                                       Class<T> entityClass) throws IOException {
        if (node == null)
            return null;
        objectMapper.setDateFormat(DateUtil.getDashedDateFormatWithTimeV2());
        T entity = null;
        if (node.has("id") && !node.get("id").isNull() && !node.get("id").asText().isEmpty()) {
            entity = repository.findOne(node.get("id").asLong());
            entity = objectMapper.readerForUpdating(entity).readValue(node);
        } else {
            entity = objectMapper.convertValue(node, entityClass);
        }
        return entity;
    }

    public <T> T readObjectFromLinkedHasMap(LinkedHashMap node, Class<T> entityClass) throws IOException {
        if (node == null)
            return null;
        objectMapper.setDateFormat(DateUtil.getDashedDateFormatWithTimeV2());
        objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);

        T entity = objectMapper.convertValue(node, entityClass);
        return entity;
    }

    public String shorteningUrl(String text) {
        StringTokenizer tokenizer = new StringTokenizer(text, " \t\n\r\f", true);
        StringBuilder result = new StringBuilder();
        while (tokenizer.hasMoreTokens()) {
            String token = tokenizer.nextToken();
            if (token.startsWith("http") || token.startsWith("www."))
                token = shortener.shorten(token);
            result.append(token);
        }
        return result.toString();
    }

    public String getSingDDLink(Map<String, Object> map) {
        return getSingDDLink(map, true);
    }

    public String getSingDDLink(Map<String, Object> map, boolean withShortLink) {

        return AccountingLinkService.getSignDdLink(
                map,
                Setup.getApplicationContext()
                        .getBean(ContractPaymentTermHelper.class)
                        .completeBankInfoGetCurrentFlow(map), withShortLink);
    }

    public String getSpouseSingDDLink(String uuid) {
        return Setup.getApplicationContext().getBean(Utils.class)
                .shorteningUrl(Setup.getCoreParameter(CoreParameter.PUBLIC_LINK_BASE)
                        + "/modules/accounting/switching-account/spouse-sign/#!/?uuid=" + uuid);
    }

    public String getSingDDLinkWithWrongSignatureTrue(Map<String, Object> map) {
        return getSingDDLinkWithWrongSignatureTrue(map, true);
    }

    public String getSingDDLinkWithWrongSignatureTrue(Map<String, Object> map, boolean withShortLink) {

        return AccountingLinkService.getSignDdLink(map,
                "&wrongSignature=true" +
                        Setup.getApplicationContext()
                                .getBean(ContractPaymentTermHelper.class)
                                .completeBankInfoGetCurrentFlow(map), withShortLink);
    }

    public String getSingDDLinkWithWrongSignatureTrueAndHideRejectionMessageTrue(Map<String, Object> map) {
        return getSingDDLinkWithWrongSignatureTrueAndHideRejectionMessageTrue(map, true);
    }

    public String getSingDDLinkWithWrongSignatureTrueAndHideRejectionMessageTrue(Map<String, Object> map, boolean withShortLink) {

        return Setup.getApplicationContext()
                .getBean(IncompleteDirectDebitService.class)
                .getSignDdLinkForIncompleteFlowMissingBankInfo(map, withShortLink);
    }

    public Date getSoonestPayrollDate() {
        try {
            java.sql.Date soonestPayrollDate = Setup.getApplicationContext().getBean(InterModuleConnector.class)
                    .get("payroll/monthlyRules/getNextPaymentDate?isMaidVisa=true", java.sql.Date.class);

            logger.log(Level.SEVERE, "Soonest Payroll Date: " + soonestPayrollDate.toString());
            return new Date(soonestPayrollDate.getTime());
        } catch (Exception e) {
            return new Date();
        }
    }

    public String getWordTemplateParamValue(Object value) {

        return getWordTemplateParamValue(value, false);
    }

    public String getWordTemplateParamValue(Object value, boolean hideFractions) {
        if (value == null)
            return "";
        if (value instanceof PicklistItem)
            return ((PicklistItem) value).getName();
        if (value instanceof BaseEntity)
            return ((BaseEntity) value).getLabel();
        if (value instanceof Date)
            return DateUtil.formatDateDashed(value);
        if (value instanceof LabelValueEnum)// use the base enum in your module instead of salesEnum
            return ((LabelValueEnum) value).getLabel();
        if (value instanceof Enum)
            return convertToTitleCase(value.toString().trim());
        if ((Double.class.isInstance(value) && Math.floor((double) value) == (double) value)
                || (Float.class.isInstance(value) && Math.floor((float) value) == (float) value))
            return String.format("%.0f", value);
        if (value instanceof Double && hideFractions)
            return String.valueOf(((Double) value).intValue());
        return value.toString();
    }

    public String getMonthOrder(Integer month, String lang) {
        if (month < 0 || month > 12) return "";

        if (lang.equals("en"))
            return perDiscountMonthEnglish.get(month);
        if (lang.equals("ar"))
            return perDiscountMonthArabic.get(month);

        return "";
    }

    // ACC-6103 get from class Utils from cm
    public static Double roundDownMode(Double value, int places) {
        return round(value, places, RoundingMode.DOWN);
    }

    public static Double round(Double value, int places, RoundingMode roundingMode) {
        if (places < 0 || value == null) return value;

        BigDecimal bd = new BigDecimal(Double.toString(value));
        bd = bd.setScale(places, roundingMode);
        return bd.doubleValue();
    }

    private String convertToTitleCase(String s) {
        String[] words = s.split("_");
        String result = "";
        for (String word : words) {
            if (word.isEmpty())
                continue;
            else if (word.length() == 1)
                result += Character.toUpperCase(word.charAt(0)) + " ";
            else
                result += Character.toUpperCase(word.charAt(0)) + word.substring(1).toLowerCase() + " ";
        }
        return result.trim();
    }

    public String replaceParameters(String text, Map<String, String> parameters) {
        for (String key : parameters.keySet()) {
            text = text.replaceAll("@" + key + "@", parameters.get(key));
        }
        return text;
    }
    //Jirra ACC-1435 to here

    public static boolean isEmpty(Object o) {
        if (o == null)
            return true;
        if (o instanceof String) {
            String s = (String) o;
            return s.trim().isEmpty();
        }
        return false;
    }

    public static boolean isPropertyUpdated(Object newValue, Object oldValue) {
        if (oldValue == null && newValue != null)
            return true;
        if (oldValue != null && newValue == null)
            return true;
        if (oldValue == null)
            return false;
        if (oldValue instanceof String)
            return !((String) oldValue).equalsIgnoreCase((String) newValue);
        if (oldValue instanceof Date)
            return !oldValue.equals(newValue);
        if (oldValue instanceof Double)
            return !oldValue.equals(newValue);
        if (oldValue instanceof Long)
            return !oldValue.equals(newValue);
        if (oldValue instanceof Integer)
            return !oldValue.equals(newValue);
        return oldValue.equals(newValue);
    }

    public InputStream getInputStreamFromAttachmentOrMultiPartFile(Object item) throws IOException {
        if (item == null)
            return null;
        if (item instanceof Attachment)
            return Storage.getStream((Attachment) item);
        if (item instanceof MultipartFile)
            return ((MultipartFile) item).getInputStream();
        throw new RuntimeException("invalid item type");
    }

    public Attachment getAttachmentFromObject(Object item, String tag) {
        try {
            if (item == null) return null;
            if (item instanceof Attachment) return (Attachment) item;

            if (item instanceof MultipartFile) {
                if (tag.equals("temp_signature"))
                    return Storage.storeTemporary((MultipartFile) item, tag, Boolean.FALSE, true);

                return Storage.storeTemporary((MultipartFile) item, tag, Boolean.FALSE);
            }
        } catch (Exception e) {
            new RuntimeException("couldn't store Attachment with tag: " + tag);
        }

        throw new RuntimeException("invalid item type");
    }

    public List<Object> convertMultipartListToObjectList(List<MultipartFile> fromList) {
        if (fromList == null)
            return null;
        List<Object> toList = new ArrayList<>();
        fromList.forEach(sig -> {
            toList.add(sig);
        });
        return toList;
    }

    // ACC-8531
    public static boolean isEmiratiMobileNumber(String mobileNumber) {
        if (mobileNumber == null || mobileNumber.isEmpty()) return false;
        return mobileNumber.startsWith("971");
    }

    // ACC-8954
    public static <T> T parseValue(Object value, Class<T> entityClass) {
        if (value == null) return null;

        ObjectMapper objectMapper = Setup.getApplicationContext().getBean(ObjectMapper.class);
        objectMapper.setDateFormat(DateUtil.getDashedDateFormatWithTimeV2());

        try {
            if (entityClass.equals(Long.class)) {
                if (value instanceof String) {
                    return entityClass.cast(Long.parseLong((String) value));
                } else if (value instanceof Integer) {
                    return entityClass.cast(((Integer) value).longValue());
                } else if (value instanceof Long) {
                    return entityClass.cast(value);
                } else if(value instanceof BigInteger) {
                    return entityClass.cast(((BigInteger) value).longValue());
                }
            } else if (entityClass.equals(Integer.class)) {
                if (value instanceof String) {
                    return entityClass.cast(Integer.parseInt((String) value));
                } else if (value instanceof Integer) {
                    return entityClass.cast(value);
                } else if (value instanceof Long) {
                    return entityClass.cast(((Long) value).intValue());
                } else if(value instanceof BigInteger) {
                    return entityClass.cast(((BigInteger) value).intValue());
                }
            } else if (entityClass.equals(Double.class)) {
                if (value instanceof String) {
                    return entityClass.cast(Double.parseDouble((String) value));
                } else if (value instanceof Number) {
                    return entityClass.cast(((Number) value).doubleValue());
                }
            } else if (entityClass.equals(Date.class)) {
                if (value instanceof String) {
                    return entityClass.cast(DateUtil.parseDateTimeDashed((String) value));
                } else if (value instanceof Date) {
                    return entityClass.cast(value);
                }
            } else {
                return objectMapper.convertValue(value, entityClass);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Failed to parse value to type " + entityClass.getSimpleName(), e);
        }

        throw new RuntimeException("Unsupported conversion from " + value.getClass().getSimpleName() + " to " + entityClass.getSimpleName());
    }
}
