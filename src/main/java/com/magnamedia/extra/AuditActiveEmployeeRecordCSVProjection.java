package com.magnamedia.extra;

import org.springframework.beans.factory.annotation.Value;


public interface AuditActiveEmployeeRecordCSVProjection {

    String getFullName();

    String getPassportNumber();

    @Value("#{target.getInsuranceStartDate() != null ? " +
            "new org.joda.time.LocalDate(target.getInsuranceStartDate()).toString('yyyy-MM-dd') : ''}")
    String getInsuranceStartDate();

    @Value("#{target.getDateOfBirth() != null ? target.getDateOfBirth().toString() : ''}")
    String getDateOfBirth();

    @Value("#{target.getNationality() != null ? target.getNationality().getName() : ''}")
    String getNationality();
}