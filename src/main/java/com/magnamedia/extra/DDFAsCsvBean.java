package com.magnamedia.extra;

import com.magnamedia.core.Setup;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.DDFExportingConfig;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Apr 10, 2020
 *         Jirra ACC-1604
 */

public class DDFAsCsvBean {
    Long id;
    Integer recordNumber;
    String oic;
    String applicationId;
    String customerType;
    String customerIdType;
    String eid;
    String dDAIssuedFor;
    String payerName;
    String payerMobileNumber;
    String payerEmailAddress;
    String fundingAccountType;
    String iban;
    String dDACommencementDate;
    String dDAExpiryDate;
    Integer debitRequestInstanceAllowed;
    String dDAAmountType;
    String paymentFrequency;
    Long dDRDefinedDays;
    Double dDRMaximumAmount;
    Double dDRMinimumAmount;
    String captureMode;
    String scannedDDAFileName;

    public DDFAsCsvBean() {
    }

    public DDFAsCsvBean(DirectDebitFile directDebitFile, Integer recordNumber, DDFExportingConfig ddfExportingConfig) {
        this.id = directDebitFile.getId();
        this.recordNumber = recordNumber;
        this.oic = ddfExportingConfig.getOic();
        this.applicationId = directDebitFile.getApplicationId() != null ? directDebitFile.getApplicationId() : "";
        this.customerType = ddfExportingConfig.getCustomerType();
        this.customerIdType = ddfExportingConfig.getCustomerIdType();
        this.eid = directDebitFile.getEid() != null ? directDebitFile.getEid().replace("-", "") : "";
        this.dDAIssuedFor = ddfExportingConfig.getDdaIssuedFor();
        if (directDebitFile.getDirectDebit() == null || directDebitFile.getDirectDebit().getContractPaymentTerm() == null ||
                directDebitFile.getDirectDebit().getContractPaymentTerm().getContract() == null
                || directDebitFile.getDirectDebit().getContractPaymentTerm().getContract().getClient() == null) {
            this.payerMobileNumber = "";
        } else {
            Client client = directDebitFile.getDirectDebit().getContractPaymentTerm().getContract().getClient();
            this.payerMobileNumber = !Utils.isEmpty(client.getNormalizedMobileNumber()) &&
                    Utils.isEmiratiMobileNumber(client.getNormalizedMobileNumber()) ?
                    ("+" + client.getNormalizedMobileNumber()) : "";
        }
        this.payerName = directDebitFile.getAccountName() != null ? directDebitFile.getAccountName() : "";

        this.payerEmailAddress = "";
        this.fundingAccountType = ddfExportingConfig.getFundingAccountType();
        this.iban = directDebitFile.getIbanNumber() != null ? directDebitFile.getIbanNumber() : "";
        this.dDACommencementDate = directDebitFile.getStartDate() != null ? DateUtil.formatDateDashed(directDebitFile.getStartDate()) : "";
        this.dDAExpiryDate = directDebitFile.getExpiryDate() != null ? DateUtil.formatDateDashed(directDebitFile.getExpiryDate()) : "";
        this.debitRequestInstanceAllowed = 0;
        this.dDAAmountType = ddfExportingConfig.getDdAmountType();
        this.paymentFrequency = directDebitFile.getDdFrequency() != null ? directDebitFile.getDdFrequency().getLabel().substring(0, 1) : "";
        this.dDRDefinedDays = ddfExportingConfig.getDefinedDays();
        this.dDRMaximumAmount = directDebitFile.getAmount() != null ? directDebitFile.getAmount() : 0;
        this.dDRMinimumAmount = this.dDRMaximumAmount;
        this.captureMode = ddfExportingConfig.getCaptureMode();
        this.scannedDDAFileName = directDebitFile.getApplicationId() != null ? directDebitFile.getApplicationId() + ".pdf" : "";
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getRecordNumber() {
        return recordNumber;
    }

    public void setRecordNumber(Integer recordNumber) {
        this.recordNumber = recordNumber;
    }

    public String getOic() {
        return oic;
    }

    public void setOic(String oic) {
        this.oic = oic;
    }

    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getCustomerIdType() {
        return customerIdType;
    }

    public void setCustomerIdType(String customerIdType) {
        this.customerIdType = customerIdType;
    }

    public String getEid() {
        return eid;
    }

    public void setEid(String eid) {
        this.eid = eid;
    }

    public String getdDAIssuedFor() {
        return dDAIssuedFor;
    }

    public void setdDAIssuedFor(String dDAIssuedFor) {
        this.dDAIssuedFor = dDAIssuedFor;
    }

    public String getPayerName() {
        return payerName;
    }

    public void setPayerName(String payerName) {
        this.payerName = payerName;
    }

    public String getPayerMobileNumber() {
        return payerMobileNumber;
    }

    public void setPayerMobileNumber(String payerMobileNumber) {
        this.payerMobileNumber = payerMobileNumber;
    }

    public String getPayerEmailAddress() {
        return payerEmailAddress;
    }

    public void setPayerEmailAddress(String payerEmailAddress) {
        this.payerEmailAddress = payerEmailAddress;
    }

    public String getFundingAccountType() {
        return fundingAccountType;
    }

    public void setFundingAccountType(String fundingAccountType) {
        this.fundingAccountType = fundingAccountType;
    }

    public String getIban() {
        return iban;
    }

    public void setIban(String iban) {
        this.iban = iban;
    }

    public String getdDACommencementDate() {
        return dDACommencementDate;
    }

    public void setdDACommencementDate(String dDACommencementDate) {
        this.dDACommencementDate = dDACommencementDate;
    }

    public String getdDAExpiryDate() {
        return dDAExpiryDate;
    }

    public void setdDAExpiryDate(String dDAExpiryDate) {
        this.dDAExpiryDate = dDAExpiryDate;
    }

    public Integer getDebitRequestInstanceAllowed() {
        return debitRequestInstanceAllowed;
    }

    public void setDebitRequestInstanceAllowed(Integer debitRequestInstanceAllowed) {
        this.debitRequestInstanceAllowed = debitRequestInstanceAllowed;
    }

    public String getDDAAmountType() {
        return dDAAmountType;
    }

    public void setDDAAmountType(String dDAAmountType) {
        this.dDAAmountType = dDAAmountType;
    }

    public String getPaymentFrequency() {
        return paymentFrequency;
    }

    public void setPaymentFrequency(String paymentFrequency) {
        this.paymentFrequency = paymentFrequency;
    }

    public Long getdDRDefinedDays() {
        return dDRDefinedDays;
    }

    public void setdDRDefinedDays(Long dDRDefinedDays) {
        this.dDRDefinedDays = dDRDefinedDays;
    }

    public Double getdDRMaximumAmount() {
        return dDRMaximumAmount;
    }

    public void setdDRMaximumAmount(Double dDRMaximumAmount) {
        this.dDRMaximumAmount = dDRMaximumAmount;
    }

    public Double getdDRMinimumAmount() {
        return dDRMinimumAmount;
    }

    public void setdDRMinimumAmount(Double dDRMinimumAmount) {
        this.dDRMinimumAmount = dDRMinimumAmount;
    }

    public String getCaptureMode() {
        return captureMode;
    }

    public void setCaptureMode(String captureMode) {
        this.captureMode = captureMode;
    }

    public String getScannedDDAFileName() {
        return scannedDDAFileName;
    }

    public void setScannedDDAFileName(String scannedDDAFileName) {
        this.scannedDDAFileName = scannedDDAFileName;
    }
}
