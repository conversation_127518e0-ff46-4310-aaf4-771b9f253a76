/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.extra;

import com.magnamedia.entity.serializer.TransactionSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.entity.Bucket;
import com.magnamedia.entity.CreditCardStatement;
import com.magnamedia.entity.Expense;
import com.magnamedia.entity.Revenue;
import com.magnamedia.entity.Ticket;
import com.magnamedia.entity.Transaction;
import com.magnamedia.module.type.OperationType;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 *
 * <AUTHOR> <<EMAIL>>
 */
public class TicketMatchingLibrary {


    
    //Common words for company matching
    public static final List<String> COMMON_WORLDS
            = Arrays.asList(
                    "AIRWAYS",
                    "AIRLINES",
                    "AIRLINE",
                    "AIRWAY",
                    "AIR"
                    );

    final static Logger logger = LoggerFactory.getLogger(TicketMatchingLibrary.class);
    
    public static MatchingAccountingHelper helper;

    public static Double Compare(CreditCardStatement statement, Ticket ticket) {
        debug("Starting Compare function....");
        debug("**** Comparing ticket " + ticket.getId() + " and Statement " + statement.getId());

        //ticket's card used must be 1483
        try {
            if (ticket.getCardUsed()==null||!ticket.getCardUsed().getName().equals("1483")) {
                debug("Card used isn't 1483");
                return 0.0;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return 0.0;
        }
        //
        boolean isTicketRefunded = ticket.isRefunded() != null ? ticket.isRefunded() : false;
        boolean isPurchaseStatement = statement.getOpertaionType() == OperationType.PURCHASE ? true : false;
        debug("IsPurchase statement " + isPurchaseStatement);

        if (!isTicketRefunded && !isPurchaseStatement) {
            debug("Different Purchase type");
            return 0.0;
        }

        debug("isTicketRefunded" + isTicketRefunded);
        boolean nineDigitsMatch = false;
        try {
            if (ticket.getBookingConfirmationNumber() != null && ticket.getBookingConfirmationNumber() != "" && statement.getBookingConfirmationCode() != null && statement.getBookingConfirmationCode() != "") {
                if (ticket.getBookingConfirmationNumber().equals(statement.getBookingConfirmationCode())) {
                    debug("Confirmation Codes matched. Returining 100%");
                    return 1.0;
                } else if (statement.getCompany() != null && statement.getCompany().equalsIgnoreCase("srilankan")) {
                    //Nothing, algorithm will continue
                } else if (statement.getCompany() != null && statement.getCompany().equalsIgnoreCase("TRIPSTA") && ((ticket.getBookingConfirmationNumber().startsWith("F") && ticket.getBookingConfirmationNumber().substring(1, 7).equals(statement.getBookingConfirmationCode())))
                        || (ticket.getBookingConfirmationNumber().length()>=5&&ticket.getBookingConfirmationNumber().substring(0, 5).equals(statement.getBookingConfirmationCode()))) {
                    //Nothing, algorithm will continue
                } else if (ticket.getBookingConfirmationNumber().length() >= 9 && ticket.getBookingConfirmationNumber().substring(0, 9).equals(statement.getBookingConfirmationCode())) {
                    nineDigitsMatch = true;
                } else {
                    debug("Confirmation Codes mismatch");
                    debug("Returining 0.0");
                    return 0.0;
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        //Comparing dates 
        Double currentResult = 1.0;
        debug("Compairing date....");
        try {
            LocalDate ticketPurchaseDate;
            LocalDate statementOperationDate;
            if(ticket.getPurchaseDate()!=null){
                ticketPurchaseDate=new LocalDate(ticket.getPurchaseDate());
                statementOperationDate = new LocalDate(statement.getOperationDate());
            }
            else{
                debug("Purchase Date mismatch");
                debug("Returining 0.0");
                return 0.0;
            }
            if (isPurchaseStatement && (ticket.getPurchaseDate() == null || !ticketPurchaseDate.equals(statementOperationDate))) {
                debug("Purchase Date mismatch");
                debug("Returining 0.0");
                return 0.0;
            } else if (!isPurchaseStatement) {
                if (statement.getOperationDate() == null) {
                    debug("Statement operation date is null");
                    debug("Returining 0.0");
                    return 0.0;
                }
                if (ticket.getRefundRequestDate()==null) {
                    debug("Ticket refund request date is null");
                    debug("Returining 0.0");
                    return 0.0;
                }

                Integer nineDigitsMargin = nineDigitsMatch ? 0 : 5;
                Integer daysDiff =calculateDuration(statement.getOperationDate(),ticket.getRefundRequestDate());
                debug("DaysDiff "+daysDiff);
                debug("nineDigitsMargin"+nineDigitsMargin);
                if(Math.abs(daysDiff)>nineDigitsMargin){
                    currentResult*=0.75;
                    debug("Date with 1 day difference (or within nineDigitsMargin)");
                }
                else if(Math.abs(daysDiff)>nineDigitsMargin+1){
                    debug("Refunds Date mismatch, more than 1 day (plus 5 days of nineDigitsMargin)");
                    debug("Returining 0.0");
                    return 0.0;
                }
            }

        } catch (Exception e) {
            //if any exception occurred, then no match
            //this comparison is necessary and should succeed
            e.printStackTrace();
            debug("Returining 0.0");
            return 0.0;
        }
        debug("Done comparint date ,currentResult= "+currentResult);
        
        //Fare must be the same with a margin of FARE_MARGI
        debug("Comparing fare....");
        Double BELOW_MIN_RATE = 0.05;
        Double FARE_MIN_MARGIN = 50.0;
        Double FARE_MAX_MARGIN = 150.0;
        
        if(ticket.getAirline()!=null&&ticket.getAirline().getName().toLowerCase().contains("srilankan")){
            FARE_MIN_MARGIN = 100.0;
            FARE_MAX_MARGIN = 200.0;
        }
        Double threshold=0.0;
        try {
            Double amount=null;
            Double ticketAmount=null;
            if(isPurchaseStatement){
                amount=statement.getDebit();
                ticketAmount=ticket.getFareInRefCurrency();
            }
            else{
                amount=statement.getCredit();
                ticketAmount=ticket.getRefundAmount();
            }
            if(ticketAmount==null){
                debug("Fare is null");
                debug("Returining 0.0");
                return 0.0;
            }
            if(amount==null){
                debug("Amount is null");
                debug("Returining 0.0");
                return 0.0;
            }
            debug("Fare: "+ticketAmount);
            
            Double Diff = Math.abs(ticketAmount - amount);
            currentResult *= ((threshold - 1) / (FARE_MAX_MARGIN - FARE_MIN_MARGIN)) * (Diff - FARE_MIN_MARGIN) + 1; //A fuzzy function for a line passing from points (MIN, 1) and (MAX, threshold)
            
            Double goDown=BELOW_MIN_RATE;
            if(Diff<FARE_MIN_MARGIN)
                goDown = (Diff / FARE_MIN_MARGIN) * BELOW_MIN_RATE;
            debug("goDown = "+goDown);
            
            debug("Diff: "+Diff);
            if (currentResult > 1)
                currentResult = 1.0;

            currentResult -= goDown;
            if (currentResult < 0)
                currentResult = 0.0;
            debug("Fare Fuzzy result: "+ currentResult);
            
            if(currentResult==0.0){
                debug("Fare difference too big");
                debug("Returining 0.0");
                return 0.0;
            }
        } catch (Exception e) {
            //if any exception occurred, then no match
            //this comparison is necessary and should succeed
            e.printStackTrace();
            debug("Returining 0.0");
            return 0.0;
        }
        debug("Done compairing fare");
        
        //Company must be the same, the match between 2 companies will be returned
        //as LCS between the 2 strings divided by the minimum length of the 2 strings
        debug("Compairing Company");
        Double SIMILARITY_THRESHOLD = 0.68;
        try {
            Double factor = 0.99;
            //Even if there is full Similarity between the two companies, this doesn't mean
            //that we are 100% sure of the match... Only Confirmation Code gives 100% match
            Double sim;
            if(statement.getCompany()!=null&&statement.getCompany().equalsIgnoreCase("TRIPSTA"))
                sim=1.0;
            else if(ticket.getAirline().getName().equals("Philippine Airlines")&&statement.getCompany().equals("PAL"))
                sim=1.0;
            
            else
                sim=StringLibrary.CustomSimilarityAlgorithm(ticket.getAirline().getName(), statement.getCompany(), COMMON_WORLDS);
            
            Double res=currentResult*factor*sim;
            
            debug("Sime: "+sim);
            debug("res : "+res);
            
            if(sim<SIMILARITY_THRESHOLD){
                debug("Similarity less than threshold!");
                debug("Returining 0.0");
                return 0.0;
            }
            debug("returning res: "+res);
            return res;
        } catch (Exception e) {
            e.printStackTrace();
            debug("Returining 0.0");
            return 0.0;
        }
        
    }

    
    public static List<MatchedTicket> MatchTickets(List<Ticket> tickets,List<CreditCardStatement> statements){
    
        debug("Start MatchTickets function");
        List<MatchedTicket> matchedTickets=new ArrayList<MatchedTicket>();
        Set<Integer> statementsToDelete=new HashSet<Integer>();
        int statementIndex=0;
        
        debug("Searching for similariteies...");
        int round=0;
        while(true){//Repeat until all statements found tickets or can't find any
            debug("round "+ ++round);
            statementsToDelete=new HashSet<Integer>();
            statementIndex=0;
            Double threshold=0.1;
            for(CreditCardStatement statement: statements){
                Double maxSim=-1.0;
                Ticket chosenTicket=null;
                int chosenIndex=-1;
                int i=0;
                for(Ticket ticket: tickets){
                    Double sim=Compare(statement,ticket);
                    if(sim>maxSim){
                        chosenTicket=ticket;
                        maxSim=sim;
                        chosenIndex=i;
                    }
                    i++;
                    
                }
                if(maxSim>=threshold&&chosenTicket!=null&&chosenIndex>=0){
                    //Statement found its best Ticket
                    //but this best Ticket must also have the same statement
                    //as its best
                    boolean isItTheOne=true;
                    for(CreditCardStatement otherStatement : statements){
                        if(otherStatement.getId().equals(statement.getId()))
                            continue;
                        Double sim=Compare(otherStatement,chosenTicket);
                        System.out.println(sim);
                        if(sim>maxSim){//Chosen Ticket found some better statement!
                            isItTheOne=false;
                            break;
                        }
                    }
                    if(isItTheOne){
                        matchedTickets.add(new MatchedTicket(chosenTicket, statement,maxSim));
                        debug("ticket "+chosenTicket.getId()+" and statement "+
                                statement.getId()+ " were added to matches with similarity: "+maxSim);
                        tickets.remove(chosenIndex);
                        statementsToDelete.add(statementIndex);
                    }
                }else{
                    debug("no similarity found for the statement "+statement.getId());
                }
                statementIndex++;
            }
            if(statementsToDelete.size()>0)
                deleteFromList(statements, statementsToDelete);
            else 
                break; // no more matches found
        }
        Collections.sort(matchedTickets);
        return matchedTickets;
    }

    public static void deleteFromList(List<CreditCardStatement> l, Set<Integer> indexes) {
        if (l == null)
            return;
        if (indexes == null)
            return;
        for (Integer i = l.size() - 1; i >= 0; i--) {
            if (indexes.contains(i))
                l.remove(i);
        }
    }
    
    public static Integer calculateDuration(Date d1, Date d2) {
        LocalDate ld1 = LocalDate.fromDateFields(d1);
        LocalDate ld2 = LocalDate.fromDateFields(d2);
        return Days.daysBetween(ld1,
                ld2)
                .getDays();
    }

    
    public static void debug(String s){
        System.out.println(s);
    }
    
    public static class MatchedTicket implements Comparable<MatchedTicket>{
        private Ticket ticket;
        private CreditCardStatement statement;
        @JsonSerialize(using = TransactionSerializer.class)
        private Transaction trans;
        
        private Double percentage;
        private String cardNo;
        
        private String fromBucketCode;
        private String toBucketCode;
        private String expenseCode;
        private String revenueCode;
        
        private List<Bucket> allBucketList;
        private List<Revenue> allRevenueList;
        private List<Expense> allExpenses;

        public MatchedTicket() {
        }
        
        

        public MatchedTicket(Ticket ticket, CreditCardStatement statement, Double similarity) {
            this.ticket = ticket;
            this.statement = statement;
            this.percentage = (similarity*100);
            this.allBucketList=helper.buckets;
            this.allExpenses=helper.expenses;
            this.allRevenueList=helper.revenues;
        }
        
        

        public MatchedTicket(Ticket ticket, CreditCardStatement statement) {
            this.ticket = ticket;
            this.statement = statement;
        }

        public Ticket getTicket() {
            return ticket;
        }

        public void setTicket(Ticket ticket) {
            this.ticket = ticket;
        }

        public CreditCardStatement getStatement() {
            return statement;
        }

        public void setStatement(CreditCardStatement statement) {
            this.statement = statement;
        }

        public Double getPercentage() {
            return percentage;
        }

        public void setPercentage(Double percentage) {
            this.percentage = percentage;
        }

        public String getCardNo() {
            if(statement.getDescription()==null)
                return null;
            String description=statement.getDescription();
            String[] spaceSplitted=description.split(" ");
            String res=null;
            
            boolean isNext=false;
            for(String token: spaceSplitted){
                if(isNext)
                    return token;
                String trimmed=token.trim();
                if(trimmed.contains("*")){
                    isNext=true;
                    continue;
                }
                if(trimmed.startsWith(("NO."))){
                    res=trimmed.substring(3);
                    break;
                }
            }
            if(res==null||res.length()<6)
                return res;
            int len=res.length();
            
            return res.substring(len-6,len);
        }

        public String getFromBucket() {
            return fromBucketCode;
        }

        public String getToBucket() {
            return toBucketCode;
        }

        public String getExpenseCode() {
            return expenseCode;
        }

        public String getRevenueCode() {
            return revenueCode;
        }

        public Transaction getTrans() {
            
            if(trans==null){
                trans=new Transaction();
                
                trans.setAmount(statement.getDebit()!=null?statement.getDebit():statement.getCredit());
                trans.setDate(new java.sql.Date(new LocalDate().toDate().getTime()));
                
                String descriptionSummary="";
                if(ticket.getRequest()!=null&&ticket.getRequest().getOfficeStaff()!=null)
                    descriptionSummary+="Office Staff: "+ticket.getRequest().getOfficeStaff()+"\n";
                if(ticket.getRequest()!=null&&ticket.getRequest().getHousemaid()!=null)
                    descriptionSummary+="Housemaid : "+ticket.getRequest().getHousemaid()+"\n";
                
                descriptionSummary+="Airline Company in SF Ticket: "+ticket.getAirline()+"\n";
                descriptionSummary+="Airline Company in Credit Card Statement: "+statement.getCompany()+"\n";
                descriptionSummary+="Date of Purchase :"+ticket.getPurchaseDate()+"\n";
                descriptionSummary+="-- Auto Inserted by Card Matching System";
                trans.setDescription(descriptionSummary);
                
                if(statement.getOpertaionType()==OperationType.PURCHASE){
                    if(ticket.getCardUsed().getName().equals("0366")||ticket.getCardUsed().getName().equals("2723"))
                        fromBucketCode="BC 23";
                    else  //if(ticket.Card_Used__c == '1483') or empty
                        fromBucketCode="BC 06";
                    
                        if (ticket.getTicketType()==Ticket.TicketType.TO_EXIT)
                            expenseCode = "FT 07";
                        else if (ticket.getTicketType()==Ticket.TicketType.TO_DUBAI)
                            expenseCode = "FT 77";
                        else if (ticket.getTicketType()==Ticket.TicketType.TO_MANILA)
                            expenseCode = "FT 78";
                        else if (ticket.getTicketType()==Ticket.TicketType.TERMINATION)
                            expenseCode = "FT 29";
                        else if (ticket.getTicketType()==Ticket.TicketType.PREWORK_VACATION)
                            expenseCode = "FT 75";
                        else if (ticket.getTicketType()==Ticket.TicketType.VACATION)
                            expenseCode = "FT 88";
                }else{
                    trans.setAmount(-Math.abs(trans.getAmount()));
                    if(ticket.getCardUsed().getName().equals("0036")||ticket.getCardUsed().getName().equals("2723"))
                        fromBucketCode="BC 23";
                    else
                        fromBucketCode="BC 06";
                    
                    expenseCode="FT 76";
                }
                
                
                if(fromBucketCode!=null)
                    for(Bucket b: allBucketList){
                        if(b.getCode().equals(fromBucketCode)){
                            trans.setFromBucket(b);
                            break;
                        }
                    }
                if(expenseCode!=null)
                    for(Expense e: allExpenses){
                        if(e.getCode().equals(expenseCode)){
                            trans.setExpense((e));
                        }
                    }
                
                if(toBucketCode!=null)
                    for(Bucket b:allBucketList){
                        if(b.getCode().equals(toBucketCode)){
                            trans.setToBucket(b);
                        }
                    }
                
                if(revenueCode!=null)
                    for(Revenue r:allRevenueList){
                        if(r.getCode().equals(revenueCode)){
                            trans.setRevenue(r);
                        }
                    }
            }
            
            trans.setAttachments(null);
            return trans;
        }

        public void setTrans(Transaction trans) {
            this.trans = trans;
        }

        public String getFromBucketCode() {
            return fromBucketCode;
        }

//        public List<Bucket> getAllBucketList() {
//            return allBucketList;
//        }
//
//        public List<Revenue> getAllRevenueList() {
//            return allRevenueList;
//        }
//
//        public List<Expense> getAllExpenses() {
//            return allExpenses;
//        }

        @Override
        public int compareTo(MatchedTicket other) {
            

            if (other.getPercentage() == null)
                return -1;

            if (percentage > other.getPercentage())
                return -1;
            else if (percentage < other.getPercentage())
                return 1;

            if (ticket == null || statement == null)
                return 0;
            if (other.getTicket() == null || other.getStatement() == null)
                return 0;
            if (ticket.getPurchaseDate() == null || statement.getOperationDate() == null)
                return 0;
            if (other.getTicket().getPurchaseDate() == null || other.getStatement().getOperationDate() == null)
                return 0;

            LocalDate purchaseDate=new LocalDate(ticket.getPurchaseDate());
            LocalDate otherPurchaseDate =new LocalDate(other.getTicket().getPurchaseDate());
            if (purchaseDate.isAfter(otherPurchaseDate))
                return 1;
            else if (purchaseDate.isBefore(purchaseDate))
                return -1;

            return 0;
        }
        
        
    }
    
}
