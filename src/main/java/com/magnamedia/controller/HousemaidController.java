package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.Searchable;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.workflow.WorkFlowTaskHistory;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.projection.IdLabel;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.type.HousemaidStatus;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.RenewRequest;
import com.magnamedia.entity.projection.HousemaidList;
import com.magnamedia.extra.OnlineCardStatementFileProjection;
import com.magnamedia.extra.SearchableEnumProjection;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.HousemaidType;
import com.magnamedia.repository.HousemaidRepository;
import com.magnamedia.repository.InsuranceAgreementRepository;
import com.magnamedia.repository.RenewVisaRequestRepository;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.stream.Collectors;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 *
 * <AUTHOR>
 */
@RequestMapping("/housemaid")
@RestController
public class HousemaidController extends BaseRepositoryController<Housemaid> {

    @Autowired
    private HousemaidRepository RepHousemaid;

    @Autowired
    private ProjectionFactory projectionFactory;
    
    @Autowired
    private RenewVisaRequestRepository renewVisaRequestRepository;
    
    @Override
    public BaseRepository<Housemaid> getRepository() {
        return RepHousemaid;

    }

    //Jirra ACC-1087
    @PreAuthorize("hasPermission('housemaid','searchHousemaid')")
    @RequestMapping(value = "/searchHousemaid", method = RequestMethod.GET)
    protected ResponseEntity<?> searchHousemaid(
            @RequestParam(name = "search",required = false) String searchQuery,
            @RequestParam(name = "status",required = false) HousemaidStatus status,
            Pageable pageable){
        
        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        if(searchQuery != null && !searchQuery.isEmpty())
            query.filterBy("name", "LIKE", "%"+searchQuery+"%");
        if(searchQuery != null && !searchQuery.isEmpty())
            query.filterBy("status", "=", status);
        query.sortBy("name", true, false);
        
        return new ResponseEntity<>(
                query.execute(pageable)
                .map(maid -> projectionFactory.createProjection(HousemaidProjection.class,
                maid)), HttpStatus.OK);
        }

    //Jirra ACC-1087
    @PreAuthorize("hasPermission('housemaid','searchActiveHousemaid')")
    @RequestMapping(value = "/searchActiveHousemaid", method = RequestMethod.GET)
    protected ResponseEntity<?> searchActiveHousemaid(
            @RequestParam(name = "search",required = false) String searchQuery,
            Pageable pageable){
        
        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        if(searchQuery != null && !searchQuery.isEmpty())
            query.filterBy("name", "LIKE", "%" + searchQuery + "%");
        // ACC-5714
        SelectFilter cc = new SelectFilter("housemaidType", "<>", HousemaidType.MAID_VISA)
                .and("landedInDubaiDate", "IS NOT NULL", null);
        SelectFilter mv = new SelectFilter("housemaidType", "=", HousemaidType.MAID_VISA)
                .and("status", "=", HousemaidStatus.WITH_CLIENT);
        query.filterBy(new SelectFilter(cc).or(mv));
        query.filterBy("status", "<>", HousemaidStatus.EMPLOYEMENT_TERMINATED); // ACC-6826
        query.sortBy("name", true, false);
        
        return new ResponseEntity<>(
                query.execute(pageable)
                .map(maid -> projectionFactory.createProjection(HousemaidProjection.class,
                maid)), HttpStatus.OK);
        }
    
    public interface HousemaidProjection{
        Long getId();
        String getLabel();
    }
    
    @PreAuthorize("hasPermission('housemaid','findByStatus')")
    @RequestMapping(value = "/findbystatus/{status}",
            method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> findByStatus(@PathVariable HousemaidStatus status) {

        List<Housemaid> hs = RepHousemaid.findByStatus(status);
       
        return new ResponseEntity<>(project(hs,IdLabel.class),
                HttpStatus.OK);
    }
    
    @PreAuthorize("hasPermission('housemaid','getallmaidtypes')")
    @RequestMapping(value = "/getallmaidtypes")
    public ResponseEntity<?> getAllMaidTypes(
            @RequestParam(name = "search", required = false) String search) {
        List<SearchableEnumProjection> result =
                Arrays.asList(HousemaidType.class.getEnumConstants())
                        .stream()
                        .map(x -> new SearchableEnumProjection(x.toString()))
                        .filter(x -> search==null
                                || search.isEmpty()
                                || x.getLabel().contains(search))
                        .collect(Collectors.toList());
        return new ResponseEntity<>(
                result,
                HttpStatus.OK);
    }
    
    //Jirra ACC-684 ACC-1515 Jirra ACC-1793
    @PreAuthorize("hasPermission('housemaid','advancesearch')")
    @RequestMapping(value = "/advancesearch/page",
            method = RequestMethod.GET)
    @Searchable(fieldName = "id",
                            label = "Housemaid ID",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "name",
                            label = "Housemaid Name",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "creationDate",
                            label = "Creation Date",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "basicSalary",
                            label = "Basic Salary",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "startDate",
                            label = "Start Date",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "isAgency",
                            label = "Agency Maid",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "freedomMaid",
                            label = "Freedom Maid",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "housemaidType",
                            label = "Housemaid Type",
                            entity = Housemaid.class,
                            valuesApi = "/accounting/housemaid/getallmaidtypes",
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "contracts.status",
                            label = "Contract status",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "contracts.contractProspectType",
                            label = "Contract Type",
                            entity = Housemaid.class,
                            valuesApi = "/public/picklist/items/ProspectType",
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "nationality",
                            label = "Nationality",
                            entity = Housemaid.class,
                            valuesApi = "/public/picklist/items/nationalities",
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "notArabicSpeaker",
                            label = "Not Arabic Speaker",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "freedomOperator",
                            label = "Freedom Operator",
                            entity = Housemaid.class,
                            valuesApi = "/accounting/freedomoperator/search/page",
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "freedomOperator.salaryCutOffDate",
                            label = "Freedom Operator Salary CutOff Date",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "landedInDubaiDate",
                            label = "Landed In Dubai Date",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "isBeingPaid50PercentSalary",
                            label = "Is Being Paid 50 Percent Salary",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "oldRenewalBasicSalary",
                            label = "Old Renewal Basic Salary",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "payrollType",
                            label = "Payroll Type",
                            valuesApi = "/public/picklist/items/PAYROLL_TYPE",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "isMaidsAt",
                            label = "Maid.at Maid",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "isAfricanCandidate",
                            label = "Is African Candidate",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    public ResponseEntity<?> advanceSearch(
            Pageable pageable) {
        
        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        query.filterBy(CurrentRequest.getSearchFilter());
        
        return new ResponseEntity<>(
                query.execute(pageable).map(maid ->
                        projectionFactory.createProjection(HousemaidList.class, maid)),
                HttpStatus.OK);
    }
    
    //Jirra ACC-1862
    @PreAuthorize("hasPermission('housemaid','checktocreatevacationrequest')")
    @RequestMapping(value = "/checktocreatevacationrequest", method = RequestMethod.GET)
    public ResponseEntity<?> checkPaidWorkVacationRequest(
            @RequestParam(name = "maid_id", required = false) Long maid_id) {
        Map<String, Object> map = new HashMap<>();

        if (maid_id == null) {
            map.put("isRenewedMaid", false);
            map.put("isVacationRequestDateValid", false);
            map.put("totalSalary", 0.0);
            return new ResponseEntity<>(map, HttpStatus.OK);
        }
        Housemaid housemaid = getRepository().findOne(maid_id);
        if (housemaid == null) {
            map.put("isRenewedMaid", false);
            map.put("isVacationRequestDateValid", false);
            map.put("totalSalary", 0.0);
            return new ResponseEntity<>(map, HttpStatus.OK);
        }
        boolean isRenewedMaid = false;
        List<WorkFlowTaskHistory> workFlowTaskHistories = null;
        RenewRequest lastCompletedRequest = renewVisaRequestRepository.findFirstOneByHousemaidAndCompletedOrderByCreationDateDesc(housemaid, true);

        if (lastCompletedRequest != null) {
            logger.log(Level.INFO, "lastCompletedRequest fetched id = " + lastCompletedRequest.getId());
            workFlowTaskHistories = Setup.getRepository(InsuranceAgreementRepository.class).findCompleteRenewRequest(lastCompletedRequest.getId());
            isRenewedMaid = workFlowTaskHistories != null && !workFlowTaskHistories.isEmpty();
        }

        boolean isVacationRequestDateValid = false;

        if (isRenewedMaid) {
            logger.log(Level.INFO, "workFlowTaskHistories fetched size = " + workFlowTaskHistories.size());
            logger.log(Level.INFO, "workFlowTaskHistory fetched with id = " + workFlowTaskHistories.get(0).getId());
            logger.log(Level.INFO, "workFlowTaskHistory fetched with taskMoveOutDate = " + workFlowTaskHistories.get(0).getTaskMoveOutDate());
            // number of months after the renewal date in which
            // the maid will be eligible to get paid vacation days
            int eligibleVacationPayingAfterMonths = Integer.parseInt(getParameter(AccountingModule.ELIGIBLE_VACATION_PAYING_DATE));
            DateTime renewedDate = new DateTime(workFlowTaskHistories.get(0).getTaskMoveOutDate());
            logger.log(Level.INFO, "renewedDate = " + workFlowTaskHistories.get(0).getTaskMoveOutDate());
            isVacationRequestDateValid = renewedDate.plusMonths(eligibleVacationPayingAfterMonths).toDate().before(new java.util.Date());
            logger.log(Level.INFO, "isVacationRequestDateValid = " + isVacationRequestDateValid);
        }

        map.put("isRenewedMaid", isRenewedMaid);
        map.put("isVacationRequestDateValid", isVacationRequestDateValid);
        map.put("totalSalary", housemaid.getPrimarySalary());
        return new ResponseEntity<>(map, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('housemaid','getActiveMaids')")
    @GetMapping("/getActiveMaids")
    public ResponseEntity<?> getActiveMaids(
            @RequestParam(name = "search", required = false) String searchQuery, Pageable pageable){
        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);

        if(searchQuery != null && !searchQuery.isEmpty()) {
            query.filterBy("name", "like", "%" + searchQuery + "%");
        }

        query.filterBy("status", "not in",
            Arrays.asList(HousemaidStatus.REJECTED,
                HousemaidStatus.VISA_UNSUCCESSFUL,
                HousemaidStatus.UNREACHABLE_AFTER_EXIT,
                HousemaidStatus.EMPLOYEMENT_TERMINATED,
                HousemaidStatus.PASSED_EXIT,
                HousemaidStatus.UNREACHABLE));
        return ResponseEntity.ok(query.execute(pageable).map(
                maid -> projectionFactory.createProjection(HousemaidList.class, maid)));
    }
}
