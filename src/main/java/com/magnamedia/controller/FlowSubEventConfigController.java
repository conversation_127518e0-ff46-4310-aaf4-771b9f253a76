package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.repository.BaseRepositoryParent;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.repository.FlowSubEventConfigRepository;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR>
 */

@RestController
@RequestMapping("/flowSubEvents")
public class FlowSubEventConfigController extends BaseRepositoryController<FlowSubEventConfig> {

    @Override
    public BaseRepositoryParent<FlowSubEventConfig> getRepository() {
        return Setup.getRepository(FlowSubEventConfigRepository.class);
    }

    @PreAuthorize("hasPermission('flowSubEvents','requiredActions')")
    @GetMapping("/requiredActions")
    public ResponseEntity<?> requiredActions() {

        return new ResponseEntity<>(FlowSubEventConfig.RequiredAction.values(), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('flowSubEvents','requiredDocuments')")
    @GetMapping("/requiredDocuments")
    public ResponseEntity<?> requiredDocuments() {

        return new ResponseEntity<>(FlowSubEventConfig.RequiredDocument.values(), HttpStatus.OK);
    }
}
