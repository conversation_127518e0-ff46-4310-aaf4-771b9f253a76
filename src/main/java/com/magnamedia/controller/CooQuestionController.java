package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.entity.CooQuestion;
import com.magnamedia.entity.SoaManualRecord;
import com.magnamedia.entity.payroll.logging.OfficeStaffPayrollLog;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.entity.workflow.PayrollAccountantTodo;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.*;
import com.magnamedia.service.EmailTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Mamon.Masod on 4/4/2021.
 */

@RequestMapping("/coo-questions")
@RestController
public class CooQuestionController extends BaseRepositoryController<CooQuestion> {

    @Autowired
    private CooQuestionRepository cooQuestionRepository;

    @Autowired
    private EmailTemplateService emailTemplateService;

    @Override
    protected ResponseEntity<?> createEntity(CooQuestion entity) {
        ResponseEntity response = super.createEntity(entity);
        sendEmailUponAsking(entity);

        return response;
    }

    private void sendEmailUponAsking(
            CooQuestion cooQuestion) {

        Map<String, String> params = new HashMap<>();
        params.put("questioned_record", cooQuestion.getQuestionedRecordLabel());
        params.put("link", Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_FRONT_END_URL) +
                "#!/accounting/" + "coo-questions/answer/" + cooQuestion.getId());

        User user = Setup.getRepository(UserRepository.class).findOne(cooQuestion.getTargetUser().getId());

        emailTemplateService.sendExpenseRequestTodoEmail(user.getEmail(), "Question from Mario", "coo_question_added", params);
    }

    private void sendEmailUponAnswering(CooQuestion cooQuestion) {
        if (cooQuestion.getQuestionedPage() == null ||
                !Arrays.asList(CooQuestion.QuestionedPage.COO_APPROVAL,
                        CooQuestion.QuestionedPage.BANK_CONFIRMATION,
                        CooQuestion.QuestionedPage.NIGHT_REVIEW).contains(cooQuestion.getQuestionedPage()))
            return;

        Map<String, String> params = new HashMap();
        params.put("label", cooQuestion.getQuestionedRecordLabel());

        String baseURL = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_FRONT_END_URL) + "#!/accounting/";
        switch (cooQuestion.getQuestionedPage()) {
            case BANK_CONFIRMATION:
                baseURL += "coo-control?tab=BANK_TRANSFER_CONFIRMATION";
                break;
            case NIGHT_REVIEW:
                baseURL += "coo-control?tab=NIGHT_REVIEWS";
                break;
            default:
                baseURL += "coo-control?tab=COO_SCREEN";
        }
        params.put("link", baseURL);
        params.put("user_name", cooQuestion.getTargetUser().getUsername());
        params.put("COO_question", cooQuestion.getQuestion());
        params.put("user_answer", cooQuestion.getAnswer());
        emailTemplateService.sendExpenseRequestTodoEmail(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_COO_SENDING_INQUIRIES),
                cooQuestion.getTargetUser().getUsername() + " replied to your question", "coo_question_answered", params);
    }

    @PreAuthorize("hasPermission('coo-questions','answer-question/{id}')")
    @RequestMapping(value = "/answer-question/{id}", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity answerQuestion(@PathVariable("id") CooQuestion cooQuestion, @RequestBody String answer) {
        
        if (cooQuestion.isAnswered()
                || (cooQuestion.getAnswer() != null && !cooQuestion.getAnswer().isEmpty()))
            throw new RuntimeException("This question is already answered!!"); 
        cooQuestion.setAnswer(answer);
        cooQuestion = getRepository().save(cooQuestion);

        sendEmailUponAnswering(cooQuestion);

        return ResponseEntity.ok(cooQuestion);
    }

    @PreAuthorize("hasPermission('coo-questions','send-rejection-mail')")
    @RequestMapping(value = "/send-rejection-mail", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity sendRejectionMailAPI(@RequestParam String label, @RequestParam String recordInfo,
                                               @RequestParam String rejectionNotes,
                                               @RequestBody List<Long> userIds,
                                               @RequestParam(required = false, defaultValue = "") String entityType,
                                               @RequestParam(required = false, defaultValue = "-1") Long entityId) {
        for (Long userId : userIds) {
            sendRejectionMail(userId, label, recordInfo, rejectionNotes);
        }

        if (entityType.equals("") || entityId == -1)
            return okResponse();

        switch (entityType) {
            case "PayrollAccountantTodo":
                PayrollAccountantTodoRepository payrollAccountantTodoRepository = Setup.getRepository(PayrollAccountantTodoRepository.class);
                PayrollAccountantTodo payrollAccountantTodo = payrollAccountantTodoRepository.findOne(entityId);
                if (payrollAccountantTodo != null) {
                    payrollAccountantTodo.setRejectionNotes(rejectionNotes);
                    payrollAccountantTodoRepository.save(payrollAccountantTodo);
                }
                break;
            case "ClientRefundToDo":
                ClientRefundTodoRepository clientRefundTodoRepository = Setup.getRepository(ClientRefundTodoRepository.class);
                ClientRefundToDo clientRefundToDo = clientRefundTodoRepository.findOne(entityId);
                if (clientRefundToDo != null) {
                    clientRefundToDo.setRejectionNotes(rejectionNotes);
                    clientRefundTodoRepository.save(clientRefundToDo);
                }
                break;
            case "ExpenseRequestTodo":
                ExpenseRequestTodoRepository expenseRequestTodoRepository = Setup.getRepository(ExpenseRequestTodoRepository.class);
                ExpenseRequestTodo expenseRequestTodo = expenseRequestTodoRepository.findOne(entityId);
                if (expenseRequestTodo != null) {
                    expenseRequestTodo.setRejectionNotes(rejectionNotes);
                    expenseRequestTodoRepository.save(expenseRequestTodo);
                }
                break;
            case "OfficeStaffPayrollLog":
                OfficeStaffPayrollLogRepository officeStaffPayrollLogRepository = Setup.getRepository(OfficeStaffPayrollLogRepository.class);
                OfficeStaffPayrollLog officeStaffPayrollLog = officeStaffPayrollLogRepository.findOne(entityId);
                if (officeStaffPayrollLog != null) {
                    officeStaffPayrollLog.setRejectionNotes(rejectionNotes);
                    officeStaffPayrollLogRepository.save(officeStaffPayrollLog);
                }
                break;
                case "SoaManualRecord":
                SoaManualRecordRepository soaManualRecordRepository = Setup.getRepository(SoaManualRecordRepository.class);
                SoaManualRecord soaManualRecord = soaManualRecordRepository.findOne(entityId);
                if (soaManualRecord != null) {
                    soaManualRecord.setRejectionNotes(rejectionNotes);
                    soaManualRecordRepository.save(soaManualRecord);
                }
                break;
        }

        return okResponse();
    }

    public void sendRejectionMail(Long userId, String label, String recordInfo, String rejectionNotes) {
        User user = Setup.getRepository(UserRepository.class).findOne(userId);

        String subject = label + " Rejected";

        Map<String, String> params = new HashMap();
        params.put("label", label);
        params.put("record_info", recordInfo);
        params.put("rejection_notes", rejectionNotes);

        emailTemplateService.sendExpenseRequestTodoEmail(user.getEmail(), subject, "coo_rejected_record", params);
    }

    @Override
    public BaseRepository<CooQuestion> getRepository() {
        return cooQuestionRepository;
    }
}
