package com.magnamedia.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.JwtSecured;
import com.magnamedia.core.annotation.Searchable;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.entity.BackgroundTaskStatus;
import com.magnamedia.core.entity.Picklist;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.core.security.ViewScope;
import com.magnamedia.entity.*;
import com.magnamedia.entity.projection.BankDirectDebitActivationFileProjection;
import com.magnamedia.entity.projection.BankDirectDebitActivationRecordCsvProjection;
import com.magnamedia.entity.projection.BankDirectDebitActivationRecordProjection;
import com.magnamedia.extra.*;
import com.magnamedia.extra.annotations.UsedBy;
import com.magnamedia.helper.BackgroundTaskHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.DDActivationFileStatus;
import com.magnamedia.module.type.DirectDebitFileStatus;
import com.magnamedia.module.type.DirectDebitSignatureStatus;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.report.NewDDRejectionReasonsReport;
import com.magnamedia.repository.*;
import com.magnamedia.service.*;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Dec 17, 2018
 *         Jirra ACC-330
 */
@RequestMapping("/bddactivationfiles")
@RestController
public class BankDirectDebitActivationFileController
        extends BaseRepositoryController<BankDirectDebitActivationFile> {

    public static final String BANK_RESPONSE_ACCEPTED_STATUS = "ACCEPTED";
    public static final String BANK_RESPONSE_REJECTED_STATUS = "REJECTED";

    @Autowired
    private BankDirectDebitActivationFileRepository bankDirectDebitActivationFileRepository;
    @Autowired
    private BankDirectDebitActivationRecordRepository bankDirectDebitActivationRecordRepository;
    @Autowired
    private DirectDebitFileRepository directDebitFileRepository;
    @Autowired
    private PicklistRepository picklistRepository;
    @Autowired
    private DirectDebitCancelationToDoRepository directDebitCancelationToDoRepository;

    @Autowired
    private BankDirectDebitActivationFileController selfCtrl;
    @Autowired
    private ProjectionFactory projectionFactory;
    @Autowired
    private AccountingEntityPropertyRepository accountingEntityPropertyRepository;
    @Autowired
    private DirectDebitSignatureService directDebitSignatureService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private BankDirectDebitActivationRecordService bankDirectDebitActivationRecordService;

    @Override
    public BaseRepository<BankDirectDebitActivationFile> getRepository() {
        return bankDirectDebitActivationFileRepository;
    }

    // ACC-9004
    @UsedBy(others = UsedBy.Others.Rpa)
    @PreAuthorize("hasPermission('bddactivationfiles','parseUploadedFileByRPA')")
    @PostMapping(value = "/parseUploadedFileByRPA")
    @Transactional
    public ResponseEntity<?> parseUploadedFileByRPA(@RequestBody BankDirectDebitActivationFile bankDirectDebitActivationFile) {
        if (bankDirectDebitActivationFile.getAttachments() == null || bankDirectDebitActivationFile.getAttachments().isEmpty()) {
            return new ResponseEntity<>("Attachment is required", HttpStatus.BAD_REQUEST);
        }

        bankDirectDebitActivationRecordService.uploadFileByRPA(bankDirectDebitActivationFile, CurrentRequest.getUser());
        return ResponseEntity.ok("the file is being parsed.");
    }

    @Transactional
    public void parseUploadedFileByRPA(Map<String, Object> payload) {
        logger.info("parse file using RPA with id : " + payload.getOrDefault("entityId", null));

        payload.put("fromRPA", true);
        parseUploadedFile(payload);
    }

    public void addNewPropertyToSendEmail(BankDirectDebitActivationFile file) {
        try {
            AccountingEntityProperty a = new AccountingEntityProperty();
            a.setOrigin(file);
            a.setKey(AccountingModule.SEND_REPORT_EMAIL_AFTER_BACKGROUND_TASKS_FINISHED);
            a.setPurpose("BankDirectDebitActivationFile_Confirm_DD_" + file.getId());

            // Add Email parameters for the property
            Long matchedAndAcceptedCount = bankDirectDebitActivationRecordRepository.countMatchedRecordsByStatus(
                    file.getId(), BANK_RESPONSE_ACCEPTED_STATUS);

            Long matchedAndRejectedCount = bankDirectDebitActivationRecordRepository.countMatchedRecordsByStatus(
                    file.getId(), BANK_RESPONSE_REJECTED_STATUS);

            Long unMatchedCount = bankDirectDebitActivationRecordRepository.countUnMatchedRecords(
                    file.getId(), Arrays.asList(BANK_RESPONSE_ACCEPTED_STATUS, BANK_RESPONSE_REJECTED_STATUS));

            a.setValue(objectMapper.writeValueAsString(new HashMap<String, String>() {{
                put("fileId", file.getId().toString());
                put("templateName", "dd_300_activation_report");
                put("matchedAndAcceptedCount", Long.toString(matchedAndAcceptedCount));
                put("matchedAndRejectedCount", Long.toString(matchedAndRejectedCount));
                put("unMatchedCount", Long.toString(unMatchedCount));
            }}));
            accountingEntityPropertyRepository.save(a);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //Jirra ACC-1400
    @PreAuthorize("hasPermission('bddactivationfiles','parse-file')")
    @PostMapping(value = "/parse-file")
    @Transactional
    public ResponseEntity parseUploadedFileApi(@RequestBody BankDirectDebitActivationFile bankDirectDebitActivationFile) {

        if (QueryService.existsEntity(BackgroundTask.class, "e.name = :p0 and e.status not in :p1",
                new Object[]{UploadStatementEntityType.BankDirectDebitActivationByRPAFile.toString(),
                        Arrays.asList(BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed)}) ||
                QueryService.existsEntity(AccountingEntityProperty.class, "e.key = :p0 and e.purpose = :p1",
                        new Object[]{AccountingModule.RUN_BACKGROUND_TASK_IN_SEQUENTIAL,
                                UploadStatementEntityType.BankDirectDebitActivationByRPAFile.toString()})) {
            throw new BusinessException("There’s another file under parsing, please wait …");
        }

        ResponseEntity<?> responseEntity = super.create(bankDirectDebitActivationFile);

        if (responseEntity.getStatusCode().equals(HttpStatus.OK) && responseEntity.getBody() instanceof BankDirectDebitActivationFile) {

            BankDirectDebitActivationFile file = (BankDirectDebitActivationFile) responseEntity.getBody();

            Map<String, Object> payload = new HashMap<>();
            payload.put("entityId", file.getId());
            payload.put("emails", CurrentRequest.getUser() != null ? CurrentRequest.getUser().getEmail() : null);

            BackgroundTaskHelper.createBGTParsingStatementUploaded(UploadStatementEntityType.BankDirectDebitActivationFile,
                    UploadStatementEntityType.BankDirectDebitActivationFile.toString(),
                    payload);
            return ResponseEntity.ok("the file is being parsed. We will send you an email once done.!");
        }
        return new ResponseEntity<>("Id must be null.", HttpStatus.BAD_REQUEST);
    }
    
    @Transactional
    public void parseUploadedFile(Map<String, Object> payload) {
        String email = (String) payload.getOrDefault("emails", "");
        boolean fromRPA = (boolean) payload.getOrDefault("fromRPA", false);

        BankDirectDebitActivationFile file = getRepository().findOne(Long.parseLong(payload.get("entityId").toString()));
        if (file == null) {
            throw new BusinessException("File is required");
        }

        try {
            bankDirectDebitActivationRecordService.parseRecords(file);
            file.setFileParsed(true);

            // Check if auto-confirmation is enabled
            String autoConfirmParam = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_AUTO_CONFIRM_DD_RPA_RECORDS);
            logger.info("autoConfirmParam : " + autoConfirmParam);
            if (Boolean.parseBoolean(autoConfirmParam)) {
                if (fromRPA) {
                    file.setConfirmedByRPA(true);
                }
                logger.info("start auto confirm for file : " + file.getId());
                addNewPropertyToSendEmail(file);
                bankDirectDebitActivationRecordService.confirmDDsFromActivationFile(
                        bankDirectDebitActivationRecordRepository
                                .findMatchedRecordsIdsByFileId(file.getId()), fromRPA);

            }
            bankDirectDebitActivationFileRepository.save(file);

            /*if (email != null && !email.isEmpty()) {

                String frontEndUrl = Setup.getParameter(
                        Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_FRONT_END_URL);

                HashMap<String, String> parameters = new HashMap<>();
                parameters.put("frontEndUrl", frontEndUrl);
                parameters.put("file_id", bankDirectDebitActivationFile.getId().toString());

                Setup.getApplicationContext()
                        .getBean(MessagingService.class)
                        .sendEmailToOfficeStaff("Bank_Direct_Debit_Activation_File_Mail",
                                parameters, email,
                                "Bank Direct Debit Activation File");
            }*/
        } catch (Exception e) {
            logger.severe("Error while parsing file : " + file.getId() + " uploaded by RPA");

            e.printStackTrace();
            if (email != null && !email.isEmpty()) {
                Setup.getApplicationContext()
                        .getBean(MessagingService.class)
                        .sendEmailToOfficeStaff("Bank_Direct_Debit_Activation_File_Error_Mail",
                                new HashMap<>(), email,
                                "Bank Direct Debit Activation File");
            }
        }
    }

    private DDActivationFileStatus getDDActivationFileStatus(BankDirectDebitActivationFile bankDirectDebitActivationFile) {
        boolean existsMatchedConfirmedRecords = bankDirectDebitActivationRecordService
                .existsRecords(bankDirectDebitActivationFile, recordMatched.MATCHED, false);

        boolean existsUnMatchedConfirmedRecords = bankDirectDebitActivationRecordService
                .existsRecords(bankDirectDebitActivationFile, recordMatched.NOT_MATCHED, false);

        boolean existsMatchedRejectedRecords = bankDirectDebitActivationRecordService
                .existsRecords(bankDirectDebitActivationFile, recordMatched.MATCHED, true);

        boolean existsUnMatchedRejectedRecords = bankDirectDebitActivationRecordService
                .existsRecords(bankDirectDebitActivationFile, recordMatched.NOT_MATCHED, true);


        if(!existsMatchedRejectedRecords && !existsMatchedConfirmedRecords
                && !existsUnMatchedConfirmedRecords && !existsUnMatchedRejectedRecords) {

            return DDActivationFileStatus.DONE;
        }
        else if(!existsMatchedRejectedRecords && !existsMatchedConfirmedRecords
                && existsUnMatchedConfirmedRecords && existsUnMatchedRejectedRecords) {
            return DDActivationFileStatus.UNMATCHED;
        } else {
            return DDActivationFileStatus.PENDING;
        }
    }

    @PreAuthorize("hasPermission('bddactivationfiles','matchDirectDebitFile')")
    @GetMapping(value = "/matchdirectdebitfile/{bddarId}/{ddfId}")
    public ResponseEntity<?> matchDirectDebitFile(
            @PathVariable("bddarId") BankDirectDebitActivationRecord bankDirectDebitActivationRecord,
            @PathVariable("ddfId") DirectDebitFile directDebitFile) {

        if (bankDirectDebitActivationRecord == null)
            throw new RuntimeException("Bank Direct Debit Activation Record not found");
        if (directDebitFile == null)
            throw new RuntimeException("Direct Debit File not found");

        if (directDebitFile.getDdStatus() != null && (directDebitFile.getDdStatus().equals(DirectDebitStatus.REJECTED)
                || directDebitFile.getDdStatus().equals(DirectDebitStatus.PENDING))) {
            bankDirectDebitActivationRecord.setDirectDebitFile(directDebitFile);
            bankDirectDebitActivationRecordRepository.save(bankDirectDebitActivationRecord);
        } else
            throw new RuntimeException("invalid direct debit file status");
        return new ResponseEntity<>(HttpStatus.OK);
    }

    // ACC-331
    @PreAuthorize("hasPermission('bddactivationfiles','list')")
    @GetMapping(value = "/projectedlist")
    @JsonView(ViewScope.Normal.class)
    public ResponseEntity<?> projectedList(Pageable pageable) {

        SelectQuery<BankDirectDebitActivationFile> query =
                new SelectQuery<>(BankDirectDebitActivationFile.class);
        query.filterBy("hidden", "=", false);
        query.sortBy("creationDate", false, false); // ACC-404

        return new ResponseEntity<>(
                query.execute(pageable).map(
                        obj -> projectionFactory.createProjection(
                                BankDirectDebitActivationFileProjection.class, obj)),
                HttpStatus.OK);
    }

    // ACC-2909
    @PreAuthorize("hasPermission('bddactivationfiles','rpa/confirmdd')")
    @PostMapping(value = "/rpa/confirmdd/{fileId}")
    public ResponseEntity confirmDD_RPA(
            @PathVariable("fileId") BankDirectDebitActivationFile file) {

        List<BankDirectDebitActivationRecord> matchedConfirmedRecords = bankDirectDebitActivationRecordService
                .getRecords(file, recordMatched.MATCHED, false, null).getContent();

        List<BankDirectDebitActivationRecord> unMatchedConfirmedRecords = bankDirectDebitActivationRecordService
                .getRecords(file, recordMatched.NOT_MATCHED, false, null).getContent();

        List<BankDirectDebitActivationRecord> matchedRejectedRecords = bankDirectDebitActivationRecordService
                .getRecords(file, recordMatched.MATCHED, true, null).getContent();

        List<BankDirectDebitActivationRecord> unMatchedRejectedRecords = bankDirectDebitActivationRecordService
                .getRecords(file, recordMatched.NOT_MATCHED, true, null).getContent();

        String mail = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DDS_ACTIVATION_RPA_MAIL);
        bankDirectDebitActivationRecordService.preConfirmDDsAPI(matchedConfirmedRecords, true, mail);
        bankDirectDebitActivationRecordService.preConfirmDDsAPI(matchedRejectedRecords, true, mail);

        file.setConfirmedByRPA(true);
        getRepository().save(file);

        Integer matchedConfirmedCount = matchedConfirmedRecords.size();
        Integer unMatchedConfirmedCount = unMatchedConfirmedRecords.size();
        Integer matchedRejectedCount = matchedRejectedRecords.size();
        Integer unMatchedRejectedCount = unMatchedRejectedRecords.size();

        Map<String, String> parameters = new HashMap();
        String fileLink = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_FRONT_END_URL) + "#!/"+
                "accounting/payments-automation/importing-file/ddFile/" + file.getId();

        parameters.put("file_link", fileLink);

        parameters.put("matched_confirmed", matchedConfirmedCount.toString());
        parameters.put("matched_confirmed_needs_action", matchedConfirmedCount > 0 ? " (Already confirmed by RPA no action required)" : "");

        parameters.put("unmatched_confirmed", unMatchedConfirmedCount.toString());
        parameters.put("unmatched_confirmed_needs_action", unMatchedConfirmedCount > 0 ? " (Needs your action)" : "");

        parameters.put("matched_rejected", matchedRejectedCount.toString());
        parameters.put("matched_rejected_needs_action", matchedRejectedCount > 0 ? " (Already confirmed by RPA no action required)" : "");

        parameters.put("unmatched_rejected", unMatchedRejectedCount.toString());
        parameters.put("unmatched_rejected_needs_action", unMatchedRejectedCount > 0 ? " (Needs your action)" : "");

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff("dd_activation_rpa",
                        parameters, mail,
                        "New (DD 300) file has been uploaded and proceed by RPA on " + DateUtil.formatDateDashedV2(new Date()));

        return okResponse();
    }

    //Jirra ACC-331 ACC-475
    @PreAuthorize("hasPermission('bddactivationfiles','confirmdd')")
    @PostMapping(value = "/confirmdd")
    @JsonView(ViewScope.Normal.class)
    public ResponseEntity<?> confirmDDsAPI(@RequestBody List<Long> recordsIDs) {
        if (QueryService.existsEntity(BackgroundTask.class, "e.relatedEntityType = :p0 and e.status not in :p1",
                new Object[] {"BankDirectDebitActivationFile", Arrays.asList(
                        BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed)})) {
            throw new RuntimeException("another request is under processing right now");
        }

        bankDirectDebitActivationRecordService
                .confirmMatchedRecords(bankDirectDebitActivationRecordRepository.findAllById(
                        recordsIDs), false);

        return ResponseEntity.ok("records is being processed. We will send you an email once done.!");
    }

    @Transactional
    public void confirmDDs(List<String> recordsIds, Boolean fromRPA, String email) {

        if (recordsIds == null || recordsIds.isEmpty()) return;

        List<BankDirectDebitActivationRecord> records = bankDirectDebitActivationRecordRepository
                .findByIdInOrderByDirectDebitFile_DdMethodDesc(
                        recordsIds.stream().map(record -> Long.parseLong(record))
                                .collect(Collectors.toList()));
        Long fileId = records.get(0).getBankDirectDebitActivationFile().getId();
        BankDirectDebitActivationFile file = bankDirectDebitActivationFileRepository.findOne(fileId);
        file.setStatus(DDActivationFileStatus.UNDER_PROCESSING);
        bankDirectDebitActivationFileRepository.save(file);
        try {
            logger.log(Level.INFO, "BankDirectDebitActivationFileController confirming records");
            for (BankDirectDebitActivationRecord r : records) {
                logger.info("record id : " + r.getId() + " , record status : " + r.getStatus());
                selfCtrl.processRecord(r);
                r = bankDirectDebitActivationRecordRepository.findOne(r.getId());
                r.setConfirmed(true);
                r.setConfirmedByRPA(fromRPA);
                r.setProcessing(false);
                bankDirectDebitActivationRecordRepository.save(r);
            }


            file.setStatus(getDDActivationFileStatus(file));
            bankDirectDebitActivationFileRepository.save(file);

        } catch (Exception e) {
            logger.log(Level.SEVERE, "BankDirectDebitActivationFileController", e);

            List<BankDirectDebitActivationRecord> result = new ArrayList<>();
            for (BankDirectDebitActivationRecord r : records) {
                r = bankDirectDebitActivationRecordRepository.findOne(r.getId());
                r.setProcessing(false);
                result.add(r);
            }
            bankDirectDebitActivationRecordRepository.save(result);

            EmailHelper.sendEmailByText(email, "Bank Direct Debit Activation File, records confirm",
                    "records was not processed successfully, an error occurred while processing " + e.getMessage());
            throw e;
        }
    }

    @Transactional
    public BankDirectDebitActivationRecord processRecord(BankDirectDebitActivationRecord r) {
        if (r.isConfirmed() || r.getDirectDebitFile() == null ||
                r.getContract() == null || r.getDirectDebitFile().getApplicationId() == null ||
                !r.getContract().equals(r.getDirectDebitFile().getApplicationId())) {
            
            return r;
        }
        
        DirectDebitFile ddf = r.getDirectDebitFile();

        if (r.getStatus().toUpperCase().equals("ACCEPTED") &&
                (!ddf.isOldByApplicationId() ||
                        (ddf.isOldByApplicationId() && ddf.getStatus().equals(DirectDebitFileStatus.SENT)))) {

            ddf.setPresentmentDate(r.getPresentmentDate());
            ddf.setDdaRefNo(r.getDdaRefNo()); // ACC-429 ACC-475
            ddf.setStatus(DirectDebitFileStatus.APPROVED);
            directDebitFileRepository.save(ddf);

            if(!ddf.getDdStatus().equals(DirectDebitStatus.CANCELED)) {
                ddf.setDdStatus(DirectDebitStatus.CONFIRMED);
            }

            if(!ddf.getDdStatus().equals(DirectDebitStatus.CANCELED) &&
                    !directDebitCancelationToDoRepository.existsByDirectDebitFileAndCompletedFalseAndStoppedFalse(ddf)) {
                
                Setup.getApplicationContext().getBean(DirectDebitStatusChangeService.class)
                        .ddFileApproved(ddf);
            }

            directDebitSignatureService.updateSignatureStatus(ddf, DirectDebitSignatureStatus.APPROVED);
        }

        // Jirra ACC-817
        if (!ddf.getDdStatus().equals(DirectDebitStatus.CONFIRMED)
                && (r.getStatus().toUpperCase().equals("REJECTED") ||
                r.getStatus().toUpperCase().equals("DISCARDED")) &&
                (!ddf.isOldByApplicationId() ||
                        (ddf.isOldByApplicationId() && ddf.getStatus().equals(DirectDebitFileStatus.SENT)))) {

            // Jirra ACC-1587
            ddf.setStatus(DirectDebitFileStatus.REJECTED);
            ddf.setDdStatus(DirectDebitStatus.REJECTED);
            ddf.setRejectCategory(r.getRejectCategory());

            //Jirra SAL-1200
            ddf.setRejectionReason(r.getRejectionReason());
            
            switch (ddf.getRejectCategory()) {
                case Signature:
                    directDebitSignatureService.updateSignatureStatus(ddf, DirectDebitSignatureStatus.REJECTED);
                    break;
                case EID: // ACC-3989
                    DirectDebitSignatureRepository directDebitSignatureRepository =
                        Setup.getRepository(DirectDebitSignatureRepository.class);
                    List<DirectDebitSignature> signatures = (List<DirectDebitSignature>) directDebitSignatureService
                        .getLastSignatureType(ddf.getDirectDebit().getContractPaymentTerm(),
                            true, false).get("currentSignatures");
                    if (signatures != null)
                        signatures.forEach(s -> {
                            s.setEid(null);
                            directDebitSignatureRepository.save(s);
                        });
                    break;
            }
            
            ddf = Setup.getApplicationContext().getBean(DirectDebitStatusChangeService.class)
                    .ddFileRejected(ddf);
            directDebitFileRepository.save(ddf);
        }
        
        return r;
    }

    public enum recordMatched {
        MATCHED, NOT_MATCHED, PREV_MATCHED
    }


    @PreAuthorize("hasPermission('bankStatementFile','getProcessSummary')")
    @RequestMapping("/{id}/getProcessSummary")
    public ResponseEntity getProcessSummary(@PathVariable("id") BankDirectDebitActivationFile file) {
        file = bankDirectDebitActivationFileRepository.findOne(file.getId());
        if (file == null)
            return new ResponseEntity("no stored file with passed (id)!", HttpStatus.BAD_REQUEST);

        HashMap<String, Object> res = new HashMap<>();

        int confirmedCountByProcessingTimeStamp = 0;
        int countByProcessingTimeStamp = 0;
        boolean processing = false;

        Long byMaxProcessingTimeStamp = bankDirectDebitActivationRecordRepository.findByMaxProcessingTimeStamp(file);
        if (byMaxProcessingTimeStamp != null) {
            processing = true;
            confirmedCountByProcessingTimeStamp = bankDirectDebitActivationRecordRepository.findConfirmedCountByProcessingTimeStamp(byMaxProcessingTimeStamp, file);
            countByProcessingTimeStamp = bankDirectDebitActivationRecordRepository.findCountByProcessingTimeStamp(byMaxProcessingTimeStamp, file);
        }

        res.put("processing", processing);
        res.put("totalRecords", countByProcessingTimeStamp);
        res.put("processedRecords", confirmedCountByProcessingTimeStamp);

        return new ResponseEntity<>(res, HttpStatus.OK);
    }

    // Jirra ACC-612 ACC-778
    @PreAuthorize("hasPermission('bddactivationfiles','get')")
    @RequestMapping(value = "/getrecords/{id}", method = RequestMethod.GET)
    @ResponseBody
    @Searchable(fieldName = "clientName",
            label = "Family Name",
            entity = BankDirectDebitActivationRecord.class,
            apiKey = "importing_bank_direct_debit_activation_file")
    @Searchable(fieldName = "contract",
            label = "Contract",
            entity = BankDirectDebitActivationRecord.class,
            apiKey = "importing_bank_direct_debit_activation_file")
    @Searchable(fieldName = "amount",
            label = "Amount",
            entity = BankDirectDebitActivationRecord.class,
            apiKey = "importing_bank_direct_debit_activation_file")
    @Searchable(fieldName = "startDate",
            label = "Start Date",
            entity = BankDirectDebitActivationRecord.class,
            apiKey = "importing_bank_direct_debit_activation_file")
    @Searchable(fieldName = "expiryDate",
            label = "Expiry Date",
            entity = BankDirectDebitActivationRecord.class,
            apiKey = "importing_bank_direct_debit_activation_file")
    @Searchable(fieldName = "presentmentDate",
            label = "Presentment Date",
            entity = BankDirectDebitActivationRecord.class,
            apiKey = "importing_bank_direct_debit_activation_file")
    @Searchable(fieldName = "status",
            label = "Status",
            entity = BankDirectDebitActivationRecord.class,
            apiKey = "importing_bank_direct_debit_activation_file")
    @Searchable(fieldName = "rejectionReason",
            label = "Rejection Reason",
            entity = BankDirectDebitActivationRecord.class,
            apiKey = "importing_bank_direct_debit_activation_file")
    public ResponseEntity<?> getRecordsAPI(
            @PathVariable("id") BankDirectDebitActivationFile file,
            @RequestParam(required = false, value = "matched") recordMatched matched,
            @RequestParam(required = false, value = "rejected", defaultValue = "false") boolean rejected,
            Pageable pageable) {

        return ResponseEntity.ok(
                bankDirectDebitActivationRecordService.getRecords(file, matched, rejected, pageable)
                        .map(obj -> projectionFactory.createProjection(BankDirectDebitActivationRecordProjection.class, obj)));
    }


    @PreAuthorize("hasPermission('bddactivationfiles','get')")
    @RequestMapping(value = "/getrecords/csv/{id}", method = RequestMethod.GET)
    @ResponseBody
    public void getRecordsCSV(
            HttpServletResponse response,
            @PathVariable("id") BankDirectDebitActivationFile file,
            @RequestParam(required = false, value = "matched") recordMatched matched,
            @RequestParam(required = false, value = "rejected", defaultValue = "false") boolean rejected,
            Sort sort,
            @RequestParam(name = "limit", required = false) Integer limit) {

        SelectQuery<BankDirectDebitActivationRecord> query =
                new SelectQuery<>(BankDirectDebitActivationRecord.class);
        query.filterBy(CurrentRequest.getSearchFilter());
        query.filterBy("bankDirectDebitActivationFile", "=", file);
        switch (matched) {
            case MATCHED:
                query.filterBy("status", "LIKE", rejected ? BANK_RESPONSE_REJECTED_STATUS : BANK_RESPONSE_ACCEPTED_STATUS);
                query.filterBy("directDebitFileId", "IS NOT NULL", null);
                query.filterBy("ddStatus", "=", DirectDebitStatus.PENDING);
                break;
            case NOT_MATCHED:
                query.filterBy("status", "LIKE", rejected ? BANK_RESPONSE_REJECTED_STATUS : BANK_RESPONSE_ACCEPTED_STATUS);
                query.filterBy("directDebitFileId", "IS NULL", null);
                break;
            case PREV_MATCHED:
                query.filterBy("directDebitFileId", "IS NOT NULL", null);
                query.filterBy("ddStatus", "!=", DirectDebitStatus.PENDING);
                break;
        }
        //Sorting
        if (sort != null) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        }
        InputStream is = null;
        
        try {
            List<String> headerList = new ArrayList<>();
            if (matched.equals(recordMatched.MATCHED)) {
                headerList = rejected
                        ? Arrays.asList("clientName", "contract", "account", "amount", "ddType", "startDate",
                        "expiryDate", "nextAction", "rejectionReason")
                        : Arrays.asList("clientName", "contract", "account", "amount", "ddType", "startDate",
                        "expiryDate", "nextAction");
            }
            if (matched.equals(recordMatched.NOT_MATCHED)) {
                headerList = rejected
                        ? Arrays.asList("account", "bank", "contract", "amount", "ddType", "startDate",
                        "expiryDate", "nextAction", "rejectionReason")
                        : Arrays.asList("account", "bank", "contract", "amount", "ddType", "startDate",
                        "expiryDate", "nextAction");
            }
            if (matched.equals(recordMatched.PREV_MATCHED)) {
                headerList = Arrays.asList("clientName", "contract", "amount", "ddType", "presentmentDate", "startDate",
                        "expiryDate", "status", "rejectionReason");
            }

            is = generateCsv(query.execute(), BankDirectDebitActivationRecordCsvProjection.class, headerList.stream().toArray(String[]::new));

            createDownloadResponse(response,
                    "DD_Records.csv",
                    is);

        } catch (IOException ex) {
            logger.log(Level.SEVERE,
                    ex.getMessage(),
                    ex);
        } finally {
            StreamsUtil.closeStream(is);
        }
    }

    @UsedBy(others = UsedBy.Others.Rpa)
    @JwtSecured
    @GetMapping(value = "/allowAddNewActivationFileFromRpa")
    public boolean allowAddNewActivationFileFromRpa() {

        return !QueryService.existsEntity(BackgroundTask.class, "e.name in :p0 and e.status not in :p1",
                new Object[]{
                        Arrays.asList(
                                UploadStatementEntityType.BankDirectDebitActivationByRPAFile.toString(),
                                UploadStatementEntityType.BankDirectDebitActivationFile.toString()),
                        Arrays.asList(BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed)}) &&

                !QueryService.existsEntity(BackgroundTask.class, "e.name like :p0 and e.status not in :p1",
                        new Object[]{"BankDirectDebitActivationFile_Confirm_DD_%",
                                Arrays.asList(BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed)}) &&

                !QueryService.existsEntity(AccountingEntityProperty.class, "e.key = :p0 and e.purpose = :p1",
                        new Object[]{AccountingModule.RUN_BACKGROUND_TASK_IN_SEQUENTIAL,
                        UploadStatementEntityType.BankDirectDebitActivationByRPAFile.toString()});
    }
}
