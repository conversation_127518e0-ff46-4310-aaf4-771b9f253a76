package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.BaseAdditionalInfo;
import com.magnamedia.core.entity.BaseEntityWithAdditionalInfo;
import com.magnamedia.core.repository.BaseAdditionalInfoRepository;
import com.magnamedia.core.repository.BaseRepository;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

@RestController
@RequestMapping("/baseadditionalinfo")
public class BaseAdditionalInfoController extends BaseRepositoryController<BaseAdditionalInfo> {
    private static final Logger logger = Logger.getLogger(BaseAdditionalInfoController.class.getName());

    @Autowired
    private BaseAdditionalInfoRepository baseAdditionalInfoRepository;

    @Override
    public BaseRepository<BaseAdditionalInfo> getRepository() { return baseAdditionalInfoRepository; }

    @PreAuthorize("hasPermission('baseadditionalinfo','importFromExcel')")
    @PostMapping("/importFromExcel")
    public ResponseEntity<?> importFromExcel(MultipartFile file,
         @RequestParam(name = "packageName", required = false, defaultValue = "com.magnamedia.entity") String packageName) throws IOException {

        return ResponseEntity.ok(readExcelFile(file, packageName));

    }

    private Map<String, Object> readExcelFile(MultipartFile file, String packageName)
            throws IOException {

        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);

        // Get header row to find column indices
        Row headerRow = sheet.getRow(0);
        int infoKeyIndex = -1;
        int infoValueIndex = -1;
        int ownerIdIndex = -1;
        int ownerTypeIndex = -1;

        for (int i = 0; i < headerRow.getLastCellNum(); i++) {
            Cell cell = headerRow.getCell(i);
            if (cell != null) {
                String headerValue = cell.getStringCellValue().trim().toUpperCase();
                switch (headerValue) {
                    case "INFO_KEY":
                        infoKeyIndex = i;
                        break;
                    case "INFO_VALUE":
                        infoValueIndex = i;
                        break;
                    case "OWNER_ID":
                        ownerIdIndex = i;
                        break;
                    case "OWNER_TYPE":
                        ownerTypeIndex = i;
                        break;
                }
            }
        }

        // Validate that all required columns are found
        if (infoKeyIndex == -1 || infoValueIndex == -1 || ownerIdIndex == -1 || ownerTypeIndex == -1) {
            throw new IllegalArgumentException("Excel file must contain columns: INFO_KEY, INFO_VALUE, OWNER_ID, OWNER_TYPE");
        }

        // Process data rows
        List<String> errors = new ArrayList<>();

        for (int i = 1; i <= sheet.getLastRowNum(); i++) {

            Row row = sheet.getRow(i);
            try {

                BaseAdditionalInfo entity = new BaseAdditionalInfo();

                // Get OWNER_TYPE
                if (row.getCell(ownerTypeIndex).getStringCellValue() != null &&
                        !row.getCell(ownerTypeIndex).getStringCellValue().isEmpty()) {

                    Class<?> entityClass = Class.forName(packageName + "." + row.getCell(ownerTypeIndex).getStringCellValue().trim());

                    // Check if it extends BaseEntityWithAdditionalInfo
                    if (!BaseEntityWithAdditionalInfo.class.isAssignableFrom(entityClass)) {
                        errors.add("Row " + (i + 1) + ": OWNER_TYPE " + row.getCell(ownerTypeIndex).getStringCellValue().trim() + " does not support additional info");
                        continue;
                    }

                    entity.setOwnerType(row.getCell(ownerTypeIndex).getStringCellValue());
                }

                // Get INFO_KEY
                if (row.getCell(infoKeyIndex).getStringCellValue() != null &&
                        !row.getCell(infoKeyIndex).getStringCellValue().isEmpty()) {
                    entity.setInfoKey(row.getCell(infoKeyIndex).getStringCellValue().trim());
                } else {
                    errors.add("Row " + (i + 1) + ": INFO_KEY is missing");
                    continue;
                }

                // Get INFO_VALUE
                if (row.getCell(infoValueIndex).getStringCellValue() != null &&
                        !row.getCell(infoValueIndex).getStringCellValue().isEmpty()) {
                    entity.setInfoValue(row.getCell(infoValueIndex).getStringCellValue().trim());
                } else {
                    errors.add("Row " + (i + 1) + ": INFO_VALUE is missing");
                    continue;
                }

                // Get OWNER_ID
                long ownerIdCell = (long) row.getCell(ownerIdIndex).getNumericCellValue();
                entity.setOwnerId(ownerIdCell);

                baseAdditionalInfoRepository.save(entity);

            } catch (Exception e) {
                e.printStackTrace();
                errors.add("Row " + (i + 1) + ": " + e.getMessage());
            }
        }

        return new HashMap<String, Object>() {{
                put("errors", errors);
        }};
    }
}
