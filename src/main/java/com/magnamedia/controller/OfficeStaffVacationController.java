///*
// * To change this license header, choose License Headers in Project Properties.
// * To change this template file, choose Tools | Templates
// * and open the template in the editor.
// */
//package com.magnamedia.controller;
//
//import com.magnamedia.core.controller.BaseRepositoryController;
//import com.magnamedia.core.repository.BaseRepository;
//import com.magnamedia.entity.OfficeStaff;
//import com.magnamedia.entity.OfficeStaffVacation;
//import com.magnamedia.repository.OfficeStaffRepository;
//import com.magnamedia.repository.OfficeStaffVacationRepository;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.PathVariable;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
///**
// *
// * <AUTHOR> <<EMAIL>>
// */
//
//@RequestMapping("OfficeStaffVacation")
//@RestController
//public class OfficeStaffVacationController extends BaseRepositoryController<OfficeStaffVacation> {
//
//    @Autowired
//    private OfficeStaffVacationRepository vacationRep;
//
//    @Autowired
//    private OfficeStaffRepository staffRep;
//
//    @Override
//    public BaseRepository<OfficeStaffVacation> getRepository() {
//        return vacationRep;
//    }
//
//    @RequestMapping("/getOfficeStaffVacations/{id}")
//    public ResponseEntity<?> getOfficeStaffVacations(@PathVariable Long id){
//        OfficeStaff staff=staffRep.findOne(id);
//        if(staff!=null)
//            return new ResponseEntity<>(vacationRep.findByOfficeStaff(staff),HttpStatus.OK);
//        return new ResponseEntity<>("Please check the passed OfficeStaff's id",HttpStatus.BAD_REQUEST);
//
//    }
//}
