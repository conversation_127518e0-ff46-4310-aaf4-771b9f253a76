package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseController;
import com.magnamedia.entity.Item;
import com.magnamedia.entity.PurchaseItem;
import com.magnamedia.entity.PurchasingToDo;
import com.magnamedia.entity.dto.PurchaseItemDtoForConfirmRequest;
import com.magnamedia.entity.dto.PurchaseItemDtoForGetSupplier;
import com.magnamedia.entity.dto.PurchasingAuditorListDto;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.type.MaintenanceRequestType;
import com.magnamedia.module.type.PurchaseItemSupplierStatus;
import com.magnamedia.repository.*;
import com.magnamedia.scheduledjobs.MaidInAccommodationStatisticsJob;
import com.magnamedia.workflow.service.purchasingsteps.GetBestSupplierStep;
import com.magnamedia.workflow.type.PurchasingToDoType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Mohammad Nosairat (Feb 03, 2021)
 */

@RequestMapping("/purchasing-auditor")
@RestController
public class PurchasingAuditorController extends BaseController {
    @Autowired
    PurchasingToDoRepository purchasingToDoRepository;
    @Autowired
    PurchaseItemRepository purchaseItemRepository;
    @Autowired
    SupplierRepository supplierRepository;
    @Autowired
    GetBestSupplierStep getBestSupplierStep;
    @Autowired
    MaintenanceRequestRepository maintenanceRequestRepository;

    @NoPermission
    @RequestMapping("/get-auditor-requests-list")
    public ResponseEntity<?> getAuditorRequestsList() {
        List<PurchasingAuditorListDto> purchasingToDos = purchasingToDoRepository
                .findByCompletedFalseAndStoppedFalseAndTaskNameIgnoreCaseIn(Arrays.asList(
                        PurchasingToDoType.PA_CONFIRM_PURCHASING_REQUEST.toString(),
                        PurchasingToDoType.PA_CONFIRM_BEST_SUPPLIER.toString()
                )).stream().map(PurchasingAuditorListDto::new).collect(Collectors.toList());

        List<PurchasingAuditorListDto> maintenances = maintenanceRequestRepository
                .findByCompletedFalseAndStoppedFalseAndTaskNameIgnoreCaseIn(Collections.singletonList(
                        MaintenanceRequestType.PURCHASE_AUDITOR_APPROVE_REQUEST.toString()
                )).stream().map(PurchasingAuditorListDto::new).collect(Collectors.toList());
        purchasingToDos.addAll(maintenances);
        List<PurchasingAuditorListDto> items = purchasingToDos.stream().sorted(Comparator.comparing(PurchasingAuditorListDto::getCreationDate)).collect(Collectors.toList());
        return ResponseEntity.ok(items);
    }

    @NoPermission
    @RequestMapping("/get-confirm-request-items/{purchasingToDoId}")
    public ResponseEntity<?> getConfirmRequestsList(@PathVariable("purchasingToDoId") PurchasingToDo purchasingToDo) {
        List<PurchaseItem> items = purchaseItemRepository.findByPurchasingToDo(purchasingToDo);
        List<PurchaseItemDtoForConfirmRequest> purchaseItemDtoForConfirmRequests = items.stream().map(PurchaseItemDtoForConfirmRequest::new).collect(Collectors.toList());
        return ResponseEntity.ok(purchaseItemDtoForConfirmRequests);
    }

    @PreAuthorize("hasPermission('purchasing-auditor','get-confirm-prices-supplier-grids')")
    @RequestMapping("/get-confirm-prices-supplier-grids/{purchasingToDoId}")
    public ResponseEntity<?> getConfirmPricesSupplierGrids(@PathVariable("purchasingToDoId") PurchasingToDo purchasingToDo) {
        List<PurchaseItemDtoForGetSupplier> items = purchaseItemRepository
                .findByPurchasingToDoAndSupplierStatus(purchasingToDo, PurchaseItemSupplierStatus.PENDING)
                .stream().map(PurchaseItemDtoForGetSupplier::new)
                .collect(Collectors.toList());
        return ResponseEntity.ok(items);
    }

    @PreAuthorize("hasPermission('purchasing-auditor','get-confirm-prices-review-grids')")
    @RequestMapping("/get-confirm-prices-review-grids/{purchasingToDoId}")
    public ResponseEntity<?> getConfirmPricesReviewGrid(@PathVariable("purchasingToDoId") PurchasingToDo purchasingToDo) {
        List<PurchaseItemDtoForGetSupplier> items = purchaseItemRepository
                .findByPurchasingToDoAndSupplierStatusNotIn(purchasingToDo, Arrays.asList(PurchaseItemSupplierStatus.PENDING, PurchaseItemSupplierStatus.CONFIRMED))
                .stream().map(PurchaseItemDtoForGetSupplier::new)
                .collect(Collectors.toList());
        return ResponseEntity.ok(items);
    }

    @Transactional
    @PreAuthorize("hasPermission('purchasing-auditor','confirm-price-action-approve')")
    @RequestMapping("/confirm-price-action-approve/{purchaseItemId}")
    public ResponseEntity<?> confirmPriceActionApprove(@PathVariable("purchaseItemId") PurchaseItem purchaseItem) {
        purchaseItem.setSupplierStatus(PurchaseItemSupplierStatus.APPROVED);
        purchaseItemRepository.save(purchaseItem);
        return ResponseEntity.ok().build();
    }

    @Transactional
    @PreAuthorize("hasPermission('purchasing-auditor','confirm-price-action-cancel')")
    @RequestMapping("/confirm-price-action-cancel/{purchaseItemId}")
    public ResponseEntity<?> confirmPriceActionCancel(@PathVariable("purchaseItemId") PurchaseItem purchaseItem) {
        purchaseItemRepository.delete(purchaseItem);
        return ResponseEntity.ok().build();
    }

    @Transactional
    @PreAuthorize("hasPermission('purchasing-auditor','confirm-price-action-get-better-prices')")
    @PostMapping("/confirm-price-action-get-better-prices/{purchaseItemId}")
    public ResponseEntity<?> confirmPriceActionGetBetterPrices(@PathVariable("purchaseItemId") PurchaseItem purchaseItem, @RequestBody PurchaseItem purchaseItemReq) {
        purchaseItem.setSendToGetBetterPriceNote(purchaseItemReq.getSendToGetBetterPriceNote());
        purchaseItem.setSupplierStatus(PurchaseItemSupplierStatus.SEND_GET_BETTER_PRICES);
        purchaseItemRepository.save(purchaseItem);
        return ResponseEntity.ok().build();
    }

    @Transactional
    @PreAuthorize("hasPermission('purchasing-auditor','confirm-price-action-undo')")
    @RequestMapping("/confirm-price-action-undo/{purchaseItemId}")
    public ResponseEntity<?> confirmPriceActionUndo(@PathVariable("purchaseItemId") PurchaseItem purchaseItem) {
        purchaseItem.setSupplierStatus(PurchaseItemSupplierStatus.PENDING);
        purchaseItemRepository.save(purchaseItem);
        return ResponseEntity.ok().build();
    }

    @Autowired
    ItemRepository itemRepository;

    @Transactional
    @PreAuthorize("hasPermission('purchasing-auditor','update-consumption-rate')")
    @PostMapping("/update-consumption-rate/{purchaseItemId}")
    public ResponseEntity<?> updateConsumptionRate(@PathVariable("purchaseItemId") PurchaseItem purchaseItem, @RequestBody PurchaseItem purchaseItemReq) {
        Item item = purchaseItem.getItem();
        item.setConsumptionRate(purchaseItemReq.getConsumptionRate());
        itemRepository.save(item);
        purchaseItem.setUpdatedConsumptionRate(purchaseItemReq.getConsumptionRate());
        purchaseItem.setUpdatedInitialCycleInventory(item.getInitialCycleInventory());
        purchaseItem.setQuantity(item.getQuantityToOrder());
        purchaseItemRepository.save(purchaseItem);
        return ResponseEntity.ok().build();
    }

    @RequestMapping("/purchase-flow-manager-notes")
    public ResponseEntity<?> managerNotes() {
        int size = Setup.getRepository(MaidInAccommodationStatisticsRepository.class).findAll().size();
        if (size != 0) return ResponseEntity.ok().build();

        Date end = new Date();
        Date begin = DateUtil.addDays(end, -60);
        MaidInAccommodationStatisticsJob job = new MaidInAccommodationStatisticsJob();
        while (end.compareTo(begin) > 0) {
            job.runJob(begin);
            begin = DateUtil.addDays(begin, 1);
        }
        return ResponseEntity.ok().build();
    }

}
