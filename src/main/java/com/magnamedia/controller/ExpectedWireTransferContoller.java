package com.magnamedia.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.JwtSecured;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.security.ViewScope;
import com.magnamedia.entity.*;
import com.magnamedia.extra.FilterItem;
import com.magnamedia.repository.ClientTransferDetailsRepository;
import com.magnamedia.repository.ExpectedWireTransferRepository;
import com.magnamedia.repository.PaymentRepository;

import java.util.List;

import com.magnamedia.service.PaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Jan 23, 2019
 *         ACC-373
 */
@RequestMapping("/expectedwiretransfers")
@RestController
public class ExpectedWireTransferContoller extends BaseRepositoryController<ExpectedWireTransfer> {

    @Autowired
    private ExpectedWireTransferRepository expectedWireTransferRepository;

    //Jirra CM-442
    @Autowired
    private ClientTransferDetailsRepository clientTransferDetailsRepository;

    @Autowired
    private PaymentRepository paymentRepository;

    @Override
    public BaseRepository<ExpectedWireTransfer> getRepository() {
        return expectedWireTransferRepository;
    }

    @JwtSecured
    @RequestMapping(value = "/secure_create", method = RequestMethod.POST)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    @Transactional
    public ResponseEntity<?> secureCreate(
            @RequestBody ExpectedWireTransfer entity) {
        
        if (entity.getId() != null) {
            return new ResponseEntity<>(new Response("Id must be null"),
                    HttpStatus.BAD_REQUEST);
        }
        return createEntity(entity);
    }
    
    @PreAuthorize("hasPermission('expectedwiretransfers','advancesearch')")
    @RequestMapping(value = "/advancesearch/page",
            method = RequestMethod.POST)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    public ResponseEntity<?> advanceSearch(
            @RequestBody List<FilterItem> filters,
            Pageable pageable
    ) {
        SelectQuery<ExpectedWireTransfer> query =
                new SelectQuery<>(ExpectedWireTransfer.class);
        //Process Filters
        SelectFilter selectFilter = new SelectFilter();
        for (FilterItem filter : filters) {
            selectFilter = selectFilter.and(filter.getSelectFilter(ExpectedWireTransfer.class));
        }
        query.filterBy(selectFilter);

        //Sorting
        if (pageable.getSort() != null) {
            for (Sort.Order order : pageable.getSort()) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        } else {
            query.sortBy("expectedDate", true, false);
        }

        return new ResponseEntity<>(
                query.execute(pageable),
                HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('expectedwiretransfers','createpayment')")
    @RequestMapping(value = "/createpayment/{wireid}/{transactionid}",
            method = RequestMethod.POST)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    @Transactional
    public ResponseEntity<?> createPayment(
            @RequestBody Payment payment,
            @PathVariable(name = "wireid") ExpectedWireTransfer expectedWireTransfer,
            @PathVariable(name = "transactionid") Transaction transaction) {

        payment.setContract(((ContractTransaction) transaction.getContracts().toArray()[0]).getContract());
        Setup.getApplicationContext()
                .getBean(PaymentService.class)
                .forceUpdatePayment(payment);
        if (payment.getId() != null) {
            expectedWireTransfer.setPayment(payment);
            expectedWireTransfer.setTransaction(transaction);
            expectedWireTransferRepository.save(expectedWireTransfer);
            return new ResponseEntity<>(
                    "Payment added successfully.",
                    HttpStatus.OK);
        }
        return new ResponseEntity<>(
                "Payment could not be added.",
                HttpStatus.BAD_REQUEST);

    }

    //Jirra CM-442
    @NoPermission
    @RequestMapping(
            value = "/public/create", method = RequestMethod.POST)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    @Transactional
    public ResponseEntity<?> publicCreate(
            @RequestBody ExpectedWireTransfer entity) {
        if (entity.getId() != null) {
            return new ResponseEntity<>(new Response("Id must be null"), HttpStatus.BAD_REQUEST);
        }
        ExpectedWireTransfer expectedWireTransfer = new ExpectedWireTransfer();
        expectedWireTransfer.setAmount(entity.getAmount());
        expectedWireTransfer.setAttachments(entity.getAttachments());
        expectedWireTransfer.setClient(entity.getClient());
        expectedWireTransfer.setExpectedDate(entity.getExpectedDate());
        expectedWireTransfer.setTransactionNumber(entity.getTransactionNumber());
        expectedWireTransfer.setNotes("Automatically Added - Client Transfer Proof SMS");
        // Jirra CM-442
        if (entity.getClienttransferDetailsID() != null) {
            ClientTransferDetails clientTransferDetails =
                    clientTransferDetailsRepository.findOne(entity.getClienttransferDetailsID());
            if (clientTransferDetails != null) {
                clientTransferDetails.setConfirmed(Boolean.TRUE);
                clientTransferDetailsRepository.save(clientTransferDetails);
            }
        }
        return createEntity(expectedWireTransfer);
    }
}
