package com.magnamedia.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.mail.TextEmail;
import com.magnamedia.core.security.ViewScope;
import com.magnamedia.entity.AdhocVariableNode;
import com.magnamedia.entity.BasePLNode;
import com.magnamedia.entity.BasePLVariableBucket;
import com.magnamedia.entity.PLVariableNode;
import com.magnamedia.entity.interfaces.BasePLVariableNode;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.repository.AdhocVariableBucketRepository;
import com.magnamedia.repository.BasePLVariableBucketRepository;
import com.magnamedia.repository.BasePLVariableRepository;
import com.magnamedia.repository.PLVariableBucketRepository;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.reflect.ParameterizedType;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Masod <<EMAIL>>
 *         Created on Jul 20, 2020
 *         Jirra ACC-644
 */

public class BasePLVariableController<T extends BasePLNode> extends BaseRepositoryController<T> {

    public BasePLVariableBucketRepository getBasePLVariableBucketRepository() {
        return null;
    }

    @Override
    public ResponseEntity<?> createEntity(T entity) {
        entity.validate();
        List<BasePLVariableBucket> pLVariableBuckets = ((BasePLVariableNode) entity).getpLVariableBuckets();
        ((BasePLVariableNode) entity).setpLVariableBuckets(new ArrayList());
        entity = getRepository().save(entity);
        for (BasePLVariableBucket pLVariableBucket : pLVariableBuckets)
            pLVariableBucket.setpLVariable(((BasePLVariableNode) entity));
        ((BasePLVariableNode) entity).setpLVariableBuckets(getBasePLVariableBucketRepository().save(pLVariableBuckets));
        return new ResponseEntity<>(entity, HttpStatus.OK);
    }

    @RequestMapping(value = "/updatevariable", method = RequestMethod.POST)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    @Transactional
    public ResponseEntity<?> updateVariable(@RequestBody T entity) {
        if (!checkPermission("update")) {
            return unauthorizedReponse();
        }
        if (entity.getId() == null) {
            throw new RuntimeException("Id could not be null.");
        }

        T pLVariableToUpdate = getRepository().findOne(entity.getId());
        if (entity.getName() != null)
            pLVariableToUpdate.setName(entity.getName());
        if (entity.getNodeOrder() != null)
            pLVariableToUpdate.setNodeOrder(entity.getNodeOrder());

        if (((BasePLVariableNode) entity).getpLVariableBuckets() != null) {
            List<BasePLVariableBucket> pLVariableBuckets = new ArrayList<>();
            for (BasePLVariableBucket pLVariableBucket : ((List<BasePLVariableBucket>) ((BasePLVariableNode) entity).getpLVariableBuckets())) {
                pLVariableBucket.setpLVariable(((BasePLVariableNode) pLVariableToUpdate));
                if (pLVariableBucket.getId() != null) {
                    BasePLVariableBucket pLVariableBucketToUpdate = (BasePLVariableBucket) getBasePLVariableBucketRepository()
                            .findOne(pLVariableBucket.getId());
                    pLVariableBucketToUpdate.setWieght(pLVariableBucket.getWieght());
                    pLVariableBucketToUpdate.setpLVariable(((BasePLVariableNode) pLVariableToUpdate));
                    pLVariableBucketToUpdate.setExpense(pLVariableBucket.getExpense());
                    pLVariableBucketToUpdate.setRevenue(pLVariableBucket.getRevenue());
                    pLVariableBuckets.add(pLVariableBucketToUpdate);
                } else
                    pLVariableBuckets.add(pLVariableBucket);
            }
            //if (!pLVariableBuckets.isEmpty())
            ((BasePLVariableNode) pLVariableToUpdate).setpLVariableBuckets(pLVariableBuckets);
        }
        pLVariableToUpdate.validate();

        List<BasePLVariableBucket> newPLVariableBuckets = ((BasePLVariableNode) pLVariableToUpdate).getpLVariableBuckets();
        ((BasePLVariableNode) pLVariableToUpdate).setpLVariableBuckets(new ArrayList<>());
        pLVariableToUpdate = getRepository().save(pLVariableToUpdate);

        List<BasePLVariableBucket> oldPLVariableBuckets = getBasePLVariableBucketRepository().findByPLVariable(((BasePLVariableNode) pLVariableToUpdate));

        List<Long> ids = newPLVariableBuckets.stream().filter(x -> x.getId() != null).map(xx -> xx.getId())
                .collect(Collectors.toList());

        List<BasePLVariableBucket> oldPLVariableBucketsToDelete = oldPLVariableBuckets.stream()
                .filter(x -> !ids.contains(x.getId())).collect(Collectors.toList());
        getBasePLVariableBucketRepository().delete(oldPLVariableBucketsToDelete);
        for (BasePLVariableBucket pLVariableBucket : newPLVariableBuckets) {
            pLVariableBucket.setpLVariable(((BasePLVariableNode) pLVariableToUpdate));
            if (pLVariableBucket.getId() != null) {
                BasePLVariableBucket pLVariableBucketToUpdate = (BasePLVariableBucket) getBasePLVariableBucketRepository()
                        .findOne(pLVariableBucket.getId());
                pLVariableBucketToUpdate.setWieght(pLVariableBucket.getWieght());
                pLVariableBucketToUpdate.setpLVariable(pLVariableBucket.getpLVariable());
                pLVariableBucketToUpdate.setExpense(pLVariableBucket.getExpense());
                pLVariableBucketToUpdate.setRevenue(pLVariableBucket.getRevenue());
                getBasePLVariableBucketRepository().save(pLVariableBucketToUpdate);
            } else
                getBasePLVariableBucketRepository().save(pLVariableBucket);
        }
        return new ResponseEntity<>(new Response("Updated"), HttpStatus.OK);
    }

    @Override
    public BasePLVariableRepository<T> getRepository() {
        return null;
    }
}