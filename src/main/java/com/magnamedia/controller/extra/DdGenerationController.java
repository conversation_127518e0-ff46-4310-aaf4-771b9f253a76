package com.magnamedia.controller.extra;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.core.controller.BaseController;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.extra.DD_OneTime_CSV;
import com.magnamedia.extra.Utils;
import com.magnamedia.helper.CsvHelper;
import com.magnamedia.repository.ContractPaymentTermRepository;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.service.ContractPaymentTermServiceNew;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Dec 17, 2020
 *         Jirra ACC-2906
 */

@RequestMapping("/dd-generation")
@RestController
public class DdGenerationController extends BaseController {
    protected static final Logger logger = Logger.getLogger(DdGenerationController.class.getName());

    @Autowired
    private ContractPaymentTermServiceNew contractPaymentTermServiceNew;
    @Autowired
    private ContractRepository contractRepo;
    @Autowired
    private BackgroundTaskService backgroundTaskService;
    @Autowired
    private Utils utils;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ContractPaymentTermRepository cptRepo;

    //Jirra ACC-2906
    @PreAuthorize("hasPermission('dd-generation','generateNewDDs/One_Time')")
    @RequestMapping(value = "/generateNewDDs/One_Time", method = RequestMethod.POST)
    public ResponseEntity generateNewOneTimeDDs(@RequestParam(name = "file", required = false) MultipartFile csvFile) throws Exception {
        String[] columns = new String[]{"Contract ID", "DD start date", "DD end date", "DD Amount", "DD type", "Payment type"};
        List<DD_OneTime_CSV> ddConfigList = CsvHelper.readCsv(utils.getInputStreamFromAttachmentOrMultiPartFile(csvFile), DD_OneTime_CSV.class, columns, true);

        if (ddConfigList == null) throw new RuntimeException("no DDs to be generated");

        Long time = new Date().getTime();

        for (DD_OneTime_CSV ddConfig : ddConfigList) {
            backgroundTaskService.addDirectCallBackgroundTaskForEntity(
                    "Accounting_DD_Generating_OneTime_DDs" + ddConfig.getContractId() + "_" + time,
                    "ddGenerationController",
                    "accounting",
                    "processOneTime",
                    "Contract",
                    ddConfig.getContractId(),
                    true,
                    false,
                    new Class<?>[]{Map.class},
                    new Object[]{objectMapper.convertValue(ddConfig, Map.class)});
        }

        return okResponse();
    }
}