package com.magnamedia.controller.extra;

import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseController;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.repository.*;
import com.magnamedia.entity.*;
import com.magnamedia.extra.Utils;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.repository.DDMessagingRepository;
import com.magnamedia.repository.DirectDebitConfigurationRepository;
import com.magnamedia.service.SwitchingNationalityService;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by Mamon.Masod on 4/24/2021.
 */

@RequestMapping("/release-data-migration")
@RestController
public class ReleaseDataMigrationController extends BaseController {
    protected static final Logger logger = Logger.getLogger(ReleaseDataMigrationController.class.getName());

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private DDMessagingRepository ddMessagingRepository;

    @Autowired
    private TemplateRepository templateRepository;

    @Autowired
    private Utils utils;

    @Autowired
    private TemplateUtil templateUtil;

    @Autowired
    private DDMessagingRepository dDMessagingRepository;

    @Autowired
    private TemplateTranslationRepository templateTranslationRepository;

    @PreAuthorize("hasPermission('release-data-migration','version-8-6-0')")
    @RequestMapping(value = "/version-8-6-0", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity release_V_8_6_0() {
        //Jirra ACC-3296
        DirectDebitConfigurationRepository ddConfigurationRepo = Setup.getRepository(DirectDebitConfigurationRepository.class);
        PicklistRepository pickListRepo = Setup.getRepository(PicklistRepository.class);
        Picklist nationalitiesPickList = pickListRepo.findByCode(AccountingModule.PICKLIST_BANK_NAME);

        List<PicklistItem> bankItems = nationalitiesPickList.getItems();

        for (PicklistItem bank : bankItems) {
            if (ddConfigurationRepo.findFirstByBank(bank) != null) continue;

            DirectDebitConfiguration ddConfiguration = DirectDebitConfiguration.newInstance(bank);
            ddConfigurationRepo.save(ddConfiguration);
        }

        return okResponse();
    }

    @PreAuthorize("hasPermission('release-data-migration','version-9-2-0')")
    @RequestMapping(value = "/version-9-2-0", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity release_V_9_2_0() {
        //Jirra ACC-3487
        SelectQuery<ContractPayment> query = new SelectQuery(ContractPayment.class);
        SelectFilter selectFilter = new SelectFilter("paymentMethod", "<>", PaymentMethod.DIRECT_DEBIT);
        selectFilter.and("confirmed", "=", Boolean.FALSE);
        query.filterBy(selectFilter);

        Page<ContractPayment> page;
        Integer pageIndex = 0;
        do {
            page = query.execute(PageRequest.of(pageIndex++, 100));
            for (ContractPayment contractPayment : page.getContent()) {
                ContractPaymentConfirmationToDo toDo = new ContractPaymentConfirmationToDo();
                toDo.setContractPaymentTerm(contractPayment.getContractPaymentTerm());
                toDo.setPaymentType(contractPayment.getPaymentType());
                toDo.setPaymentMethod(contractPayment.getPaymentMethod());
                toDo.setSource(ContractPaymentConfirmationToDo.Source.ERP);

                ContractPaymentWrapper contractPaymentWrapper = new ContractPaymentWrapper();
                contractPaymentWrapper.setContractPaymentConfirmationToDo(toDo);
                contractPaymentWrapper.setContractPayment(contractPayment);
                contractPaymentWrapper.setPaymentDate(contractPayment.getDate());
                contractPaymentWrapper.setProrated(contractPayment.getIsProRated());
                contractPaymentWrapper.setInitial(contractPayment.getIsInitial());
                contractPaymentWrapper.setAmount(contractPayment.getAmount());
                contractPaymentWrapper.setActualReceivedAmount(contractPayment.getAmount());
                contractPaymentWrapper.setDescription(contractPayment.getDescription());

                toDo.getContractPaymentList().add(contractPaymentWrapper);

                entityManager.merge(toDo);
            }
        } while (page.hasNext());

        return okResponse();
    }

    @PreAuthorize("hasPermission('release-data-migration','yaya-app')")
    @RequestMapping(path = "/yaya-app", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity release_V_Yaya_App() {
        int index = 0;
        Page<DDMessaging> pa;

        do {
            pa = ddMessagingRepository.findByMaidTemplateIsNotNullOrMaidWhenRetractCancellationTemplateIsNotNull(PageRequest.of(index++, 100));
            for (DDMessaging ddMessaging : pa.getContent()) {
                if (ddMessaging.getMaidTemplate() != null)
                    processTemplate(ddMessaging.getMaidTemplate(), ddMessaging);
                if (ddMessaging.getMaidWhenRetractCancellationTemplate() != null)
                    processTemplate(ddMessaging.getMaidWhenRetractCancellationTemplate(), ddMessaging);
            }
        } while (pa.hasNext());

        return new ResponseEntity("Done", HttpStatus.OK);
    }

    @Autowired
    TemplateParameterRepository paramRepository;

    public void processTemplate(Template maidTemplate, DDMessaging ddMessaging){

        maidTemplate = templateRepository.findOne(maidTemplate.getId());
        maidTemplate.setText(maidTemplate.getText().replace("@greetings@", "@maid_name@"));
        Set<TemplateParameter> originParameters = paramRepository.findByTemplate(maidTemplate);
        for(TemplateParameter tp : originParameters)
            if(tp.getName() != null && tp.getName().equals("greetings")){
                tp.setName("maid_name");
                paramRepository.save(tp);
                break;
            }

        Template smsTemplate = new Template("", "", "", "");
        BeanUtils.copyProperties(maidTemplate, smsTemplate);
        smsTemplate.setId(null);

        String smsTemplateUniqueName = "Accounting_dd_messaging_setup_maid_" + ddMessaging.getEvent().toString();
        Template smsByNameIgnoreCase = templateRepository.findByNameIgnoreCase(smsTemplateUniqueName);
        int y = 0;
        while (smsByNameIgnoreCase != null) {
            y++;
            smsByNameIgnoreCase = templateRepository.findByNameIgnoreCase(smsTemplateUniqueName + y);
        }

        smsTemplate.setName(y == 0 ? (smsTemplateUniqueName) : smsTemplateUniqueName + y);
        TemplateUtil.createTemplate(smsTemplate, new HashMap<>());

        maidTemplate.setNotificationSmsTemplateName(smsTemplate.getName());
        maidTemplate.setDescription("this is a notification template that is linked with sms template:" + smsTemplate.getId());

        templateUtil.updateTemplate(maidTemplate, new HashMap<>());
    }

    @PreAuthorize("hasPermission('release-data-migration','version-yaya-app-translate')")
    @RequestMapping(value = "/version-yaya-app-translate", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity release_yaya_app(
            @RequestParam(name = "file", required = false) MultipartFile xlsxFile) throws Exception {

        PicklistItem hindiPicklistItem = Setup.getItem("template_languages", "hi");
        PicklistItem amhPicklistItem = Setup.getItem("template_languages", "am");
        PicklistItem omPicklistItem = Setup.getItem("template_languages", "az");
        PicklistItem tlPicklistItem = Setup.getItem("template_languages", "tl");

        if (hindiPicklistItem == null || amhPicklistItem == null || omPicklistItem == null || tlPicklistItem == null)
            throw new RuntimeException("Yaya Languages is not setup yet.");

        Workbook workbook = new XSSFWorkbook(utils.getInputStreamFromAttachmentOrMultiPartFile(xlsxFile));

        Sheet sheet = workbook.getSheetAt(0);

        Iterator<Row> rowIterator = sheet.iterator();
        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            try{
                if (row.getRowNum() == 0)
                    continue;
                DataFormatter formatter = new DataFormatter();
                String val = formatter.formatCellValue(row.getCell(0));
                if (val.isEmpty())
                    break;
                Long ddMessaingId = new Double(Double.parseDouble(val)).longValue();
                DDMessaging dDMessaging = dDMessagingRepository.findOne(ddMessaingId);
                if (dDMessaging == null)
                    continue;
                Template template = dDMessaging.getMaidTemplate();

                String hindiTranslation = row.getCell(24).getStringCellValue();
                String amhTranslation = row.getCell(25).getStringCellValue();
                String omTranslation = row.getCell(26).getStringCellValue();
                String tlTranslation = row.getCell(27).getStringCellValue();

                TemplateTranslation hindiTemplateTranslation = new TemplateTranslation();
                hindiTemplateTranslation.setTemplate(template);
                hindiTemplateTranslation.setLanguage(hindiPicklistItem);
                hindiTemplateTranslation.setTranslation(hindiTranslation);
                templateTranslationRepository.save(hindiTemplateTranslation);

                TemplateTranslation amhTemplateTranslation = new TemplateTranslation();
                amhTemplateTranslation.setTemplate(template);
                amhTemplateTranslation.setLanguage(amhPicklistItem);
                amhTemplateTranslation.setTranslation(amhTranslation);
                templateTranslationRepository.save(amhTemplateTranslation);

                TemplateTranslation omTemplateTranslation = new TemplateTranslation();
                omTemplateTranslation.setTemplate(template);
                omTemplateTranslation.setLanguage(omPicklistItem);
                omTemplateTranslation.setTranslation(omTranslation);
                templateTranslationRepository.save(omTemplateTranslation);

                TemplateTranslation tlTemplateTranslation = new TemplateTranslation();
                tlTemplateTranslation.setTemplate(template);
                tlTemplateTranslation.setLanguage(tlPicklistItem);
                tlTemplateTranslation.setTranslation(tlTranslation);
                templateTranslationRepository.save(tlTemplateTranslation);
            }
            catch(Exception ex){
                throw new RuntimeException("row.getRowNum(): " + row.getRowNum() + " --- " + ex.getMessage());
            }
        }


        return new ResponseEntity("Done", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('release-data-migration','version-yaya-app-3775')")
    @RequestMapping(value = "/version-yaya-app-3775", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity release_yaya_app_3775(
            @RequestParam(name = "file", required = false) MultipartFile xlsxFile) throws Exception {

        PicklistItem hindiPicklistItem = Setup.getItem("template_languages", "hi");
        PicklistItem amhPicklistItem = Setup.getItem("template_languages", "am");
        PicklistItem omPicklistItem = Setup.getItem("template_languages", "az");
        PicklistItem tlPicklistItem = Setup.getItem("template_languages", "tl");

        if (hindiPicklistItem == null || amhPicklistItem == null || omPicklistItem == null || tlPicklistItem == null)
            throw new RuntimeException("Yaya Languages is not setup yet.");

        Workbook workbook = new XSSFWorkbook(utils.getInputStreamFromAttachmentOrMultiPartFile(xlsxFile));

        Sheet sheet = workbook.getSheetAt(1);

        Iterator<Row> rowIterator = sheet.iterator();
        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            try{
                if (row.getRowNum() == 0)
                    continue;
                DataFormatter formatter = new DataFormatter();
                String val = formatter.formatCellValue(row.getCell(0));
                if (val.isEmpty())
                    break;
                logger.log(Level.SEVERE, "row.getCell(28).getBooleanCellValue()" + row.getCell(28).getNumericCellValue());
                if (row.getCell(28).getNumericCellValue() != 1D)
                    continue;
                Long ddMessaingId = new Double(Double.parseDouble(val)).longValue();
                DDMessaging dDMessaging = dDMessagingRepository.findOne(ddMessaingId);
                if (dDMessaging == null)
                    continue;

                Template template = dDMessaging.getMaidTemplate();

                template.setText(row.getCell(17).getStringCellValue());
                templateUtil.updateTemplate(template, new HashMap<>());

                Template smsTemplate =  templateRepository.findByNameIgnoreCase(template.getNotificationSmsTemplateName());
                if (smsTemplate != null){
                    smsTemplate.setText(row.getCell(17).getStringCellValue());
                    templateUtil.updateTemplate(template, new HashMap<>());
                }
                //            List<TemplateParameter> originParameters = paramRepository.findByTemplate(template);
                //            TemplateParameter tp = new TemplateParameter();
                //            tp.setTemplate(template);
                //            tp.setName("");
                //            tp.setValue(val);
                //            tp.setExpression(false);
                //            paramRepository.save(tp);

                String hindiTranslation = row.getCell(24).getStringCellValue();
                String amhTranslation = row.getCell(25).getStringCellValue();
                String omTranslation = row.getCell(26).getStringCellValue();
                String tlTranslation = row.getCell(27).getStringCellValue();

                TemplateTranslation hindiTemplateTranslation = templateTranslationRepository.findByLanguageAndTemplate(hindiPicklistItem, template);
                if(hindiTemplateTranslation == null){
                    hindiTemplateTranslation = new TemplateTranslation();
                    hindiTemplateTranslation.setTemplate(template);
                    hindiTemplateTranslation.setLanguage(hindiPicklistItem);
                    hindiTemplateTranslation.setTranslation(hindiTranslation);
                }
                else
                    hindiTemplateTranslation.setTranslation(hindiTranslation);
                templateTranslationRepository.save(hindiTemplateTranslation);

                TemplateTranslation amhTemplateTranslation = templateTranslationRepository.findByLanguageAndTemplate(amhPicklistItem, template);
                if(amhTemplateTranslation == null){
                    amhTemplateTranslation = new TemplateTranslation();
                    amhTemplateTranslation.setTemplate(template);
                    amhTemplateTranslation.setLanguage(amhPicklistItem);
                    amhTemplateTranslation.setTranslation(amhTranslation);
                    templateTranslationRepository.save(amhTemplateTranslation);
                }
                else
                    amhTemplateTranslation.setTranslation(amhTranslation);
                templateTranslationRepository.save(amhTemplateTranslation);

                TemplateTranslation omTemplateTranslation = templateTranslationRepository.findByLanguageAndTemplate(omPicklistItem, template);
                if(omTemplateTranslation == null){
                    omTemplateTranslation = new TemplateTranslation();
                    omTemplateTranslation.setTemplate(template);
                    omTemplateTranslation.setLanguage(omPicklistItem);
                    omTemplateTranslation.setTranslation(omTranslation);
                    templateTranslationRepository.save(omTemplateTranslation);
                }
                else
                    omTemplateTranslation.setTranslation(omTranslation);
                templateTranslationRepository.save(omTemplateTranslation);

                TemplateTranslation tlTemplateTranslation = templateTranslationRepository.findByLanguageAndTemplate(tlPicklistItem, template);
                if(tlTemplateTranslation == null){
                    tlTemplateTranslation = new TemplateTranslation();
                    tlTemplateTranslation.setTemplate(template);
                    tlTemplateTranslation.setLanguage(tlPicklistItem);
                    tlTemplateTranslation.setTranslation(tlTranslation);
                    templateTranslationRepository.save(tlTemplateTranslation);
                }
                else
                    tlTemplateTranslation.setTranslation(tlTranslation);
                templateTranslationRepository.save(tlTemplateTranslation);
            }catch(Exception ex){
                throw new RuntimeException("row.getRowNum(): " + row.getRowNum() + " --- " + ex.getMessage());
            }
        }
        return new ResponseEntity("Done", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('release-data-migration','version-cc-app-3850')")
    @RequestMapping(value = "/version-cc-app-3850", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity release_cc_app_3850(
            @RequestParam(name = "file", required = false) MultipartFile xlsxFile) throws Exception {

        Workbook workbook = new XSSFWorkbook(utils.getInputStreamFromAttachmentOrMultiPartFile(xlsxFile));

        for (int i=0; i<4; i++){
            Sheet sheet = workbook.getSheetAt(i);
            Iterator<Row> rowIterator = sheet.iterator();
            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                try{
                    if (row.getRowNum() == 0)
                        continue;
                    DataFormatter formatter = new DataFormatter();
                    String val = formatter.formatCellValue(row.getCell(27));
                    if (val.isEmpty())
                        break;
                    logger.log(Level.SEVERE, "row.getCell(28).getNumericCellValue()" + val);
                    Long ddMessaingId = new Double(Double.parseDouble(val)).longValue();
                    DDMessaging dDMessaging = dDMessagingRepository.findOne(ddMessaingId);
                    if (dDMessaging == null)
                        continue;

                    Template template = dDMessaging.getClientTemplate();
//                template.setText(row.getCell(11).getStringCellValue());
//                templateUtil.updateTemplate(template, new HashMap<>());

                    Template smsTemplate =  templateRepository.findByNameIgnoreCase(template.getNotificationSmsTemplateName());
                    if (smsTemplate != null){
                        smsTemplate.setText(row.getCell(17).getStringCellValue());
                        templateUtil.updateTemplate(template, new HashMap<>());
                    }

                    //Maid visa
//                val = formatter.formatCellValue(row.getCell(28));
//                if (val.isEmpty())
//                    break;
//                logger.log(Level.SEVERE, "row.getCell(28).getNumericCellValue()" + val);
//                ddMessaingId = new Double(Double.parseDouble(val)).longValue();
//                dDMessaging = dDMessagingRepository.findOne(ddMessaingId);
//                if (dDMessaging == null)
//                    continue;
//
//                template = dDMessaging.getClientTemplate();
////                template.setText(row.getCell(11).getStringCellValue());
////                templateUtil.updateTemplate(template, new HashMap<>());
//
//                smsTemplate =  templateRepository.findByNameIgnoreCase(template.getNotificationSmsTemplateName());
//                if (smsTemplate != null){
//                    smsTemplate.setText(row.getCell(17).getStringCellValue());
//                    templateUtil.updateTemplate(template, new HashMap<>());
//                }
                } catch(Exception ex){
                    throw new RuntimeException("row.getRowNum(): " + row.getRowNum() + " --- " + ex.getMessage());
                }
            }
        }


        return new ResponseEntity("Done", HttpStatus.OK);
    }

    //16 MAIN text
    //8 HINDI translation
    //4 AMHARIC translation
    //2 OROMO translation
    //1 TAGALOG translation
    @PreAuthorize("hasPermission('release-data-migration','version-yaya-app-yb-684')")
    @RequestMapping(value = "/version-yaya-app-yb-684", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity release_yaya_app_yb_684(
            @RequestParam(name = "file", required = false) MultipartFile xlsxFile) throws Exception {

        PicklistItem hindiPicklistItem = Setup.getItem("template_languages", "hi");
        PicklistItem amhPicklistItem = Setup.getItem("template_languages", "am");
        PicklistItem omPicklistItem = Setup.getItem("template_languages", "az");
        PicklistItem tlPicklistItem = Setup.getItem("template_languages", "tl");

        if (hindiPicklistItem == null || amhPicklistItem == null || omPicklistItem == null || tlPicklistItem == null)
            throw new RuntimeException("Yaya Languages is not setup yet.");

        Workbook workbook = new XSSFWorkbook(utils.getInputStreamFromAttachmentOrMultiPartFile(xlsxFile));

        Sheet sheet = workbook.getSheetAt(1);

        Iterator<Row> rowIterator = sheet.iterator();
        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            try{
                if (row.getRowNum() == 0)
                    continue;
                logger.log(Level.SEVERE, "row.getRowNum(): " + row.getRowNum());
                DataFormatter formatter = new DataFormatter();
                String val = formatter.formatCellValue(row.getCell(0));
                if (val.isEmpty())
                    break;
                Double formate = row.getCell(28).getNumericCellValue();
                logger.log(Level.SEVERE, "formate: " + formate);
                if (formate == 0D)
                    continue;
                Long ddMessaingId = new Double(Double.parseDouble(val)).longValue();
                DDMessaging dDMessaging = dDMessagingRepository.findOne(ddMessaingId);
                if (dDMessaging == null)
                    continue;

                Template template = dDMessaging.getMaidTemplate();

                if (formate > 15){
                    logger.log(Level.SEVERE, "update 17");
                    template.setText(row.getCell(17).getStringCellValue());
                    templateUtil.updateTemplate(template, new HashMap<>());

                    Template smsTemplate =  templateRepository.findByNameIgnoreCase(template.getNotificationSmsTemplateName());
                    if (smsTemplate != null){
                        smsTemplate.setText(row.getCell(17).getStringCellValue());
                        templateUtil.updateTemplate(template, new HashMap<>());
                    }
                    formate -= 16;
                }
                if (formate == 0D)
                    continue;

                String hindiTranslation = row.getCell(24).getStringCellValue();
                String amhTranslation = row.getCell(25).getStringCellValue();
                String omTranslation = row.getCell(26).getStringCellValue();
                String tlTranslation = row.getCell(27).getStringCellValue();

                if (formate > 7){
                    logger.log(Level.SEVERE, "update 24");
                    TemplateTranslation hindiTemplateTranslation = templateTranslationRepository.findByLanguageAndTemplate(hindiPicklistItem, template);
                    if(hindiTemplateTranslation == null){
                        hindiTemplateTranslation = new TemplateTranslation();
                        hindiTemplateTranslation.setTemplate(template);
                        hindiTemplateTranslation.setLanguage(hindiPicklistItem);
                        hindiTemplateTranslation.setTranslation(hindiTranslation);
                    }
                    else
                        hindiTemplateTranslation.setTranslation(hindiTranslation);
                    templateTranslationRepository.save(hindiTemplateTranslation);
                    formate -= 8;
                }
                if (formate == 0D)
                    continue;

                if (formate > 3){
                    logger.log(Level.SEVERE, "update 25");
                    TemplateTranslation amhTemplateTranslation = templateTranslationRepository.findByLanguageAndTemplate(amhPicklistItem, template);
                    if(amhTemplateTranslation == null){
                        amhTemplateTranslation = new TemplateTranslation();
                        amhTemplateTranslation.setTemplate(template);
                        amhTemplateTranslation.setLanguage(amhPicklistItem);
                        amhTemplateTranslation.setTranslation(amhTranslation);
                        templateTranslationRepository.save(amhTemplateTranslation);
                    }
                    else
                        amhTemplateTranslation.setTranslation(amhTranslation);
                    templateTranslationRepository.save(amhTemplateTranslation);
                    formate -= 4;
                }
                if (formate == 0D)
                    continue;

                if (formate > 1){
                    logger.log(Level.SEVERE, "update 26");
                    TemplateTranslation omTemplateTranslation = templateTranslationRepository.findByLanguageAndTemplate(omPicklistItem, template);
                    if(omTemplateTranslation == null){
                        omTemplateTranslation = new TemplateTranslation();
                        omTemplateTranslation.setTemplate(template);
                        omTemplateTranslation.setLanguage(omPicklistItem);
                        omTemplateTranslation.setTranslation(omTranslation);
                        templateTranslationRepository.save(omTemplateTranslation);
                    }
                    else
                        omTemplateTranslation.setTranslation(omTranslation);
                    templateTranslationRepository.save(omTemplateTranslation);
                    formate -= 2;
                }

                if (formate > 0){
                    logger.log(Level.SEVERE, "update 27");
                    TemplateTranslation tlTemplateTranslation = templateTranslationRepository.findByLanguageAndTemplate(tlPicklistItem, template);
                    if(tlTemplateTranslation == null){
                        tlTemplateTranslation = new TemplateTranslation();
                        tlTemplateTranslation.setTemplate(template);
                        tlTemplateTranslation.setLanguage(tlPicklistItem);
                        tlTemplateTranslation.setTranslation(tlTranslation);
                        templateTranslationRepository.save(tlTemplateTranslation);
                    }
                    else
                        tlTemplateTranslation.setTranslation(tlTranslation);
                    templateTranslationRepository.save(tlTemplateTranslation);
                    formate -= 1;
                }
            }catch(Exception ex){
                throw new RuntimeException("row.getRowNum(): " + row.getRowNum() + " --- " + ex.getMessage());
            }
        }
        return new ResponseEntity("Done", HttpStatus.OK);
    }
}