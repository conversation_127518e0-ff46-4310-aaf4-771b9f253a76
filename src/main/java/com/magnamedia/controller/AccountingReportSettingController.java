package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.AccountingReportSetting;
import com.magnamedia.repository.AccountingReportSettingRepository;
import com.magnamedia.service.AccountingReportSettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@RestController
@RequestMapping("/accountingReportSetting")
public class AccountingReportSettingController extends BaseRepositoryController<AccountingReportSetting> {

    @Autowired
    private AccountingReportSettingRepository accountingReportSettingRepository;
    @Autowired
    private AccountingReportSettingService accountingReportSettingService;

    @Override
    public BaseRepository<AccountingReportSetting> getRepository() {
        return accountingReportSettingRepository;
    }

    @PreAuthorize("hasPermission('accountingReportSetting','updateQueries')")
    @PostMapping(value = "/updateQueries")
    public ResponseEntity<?> updateQueries(MultipartFile file, AccountingReportSetting.ReportCategory reportCategory) throws IOException {
        accountingReportSettingService.updateQueries(file, reportCategory);
        return ResponseEntity.ok("Done ^_^");
    }

    @PreAuthorize("hasPermission('accountingReportSetting','saveNewQuery')")
    @PostMapping(value = "/saveNewQuery")
    public ResponseEntity<?> saveNewQuery(
            AccountingReportSetting.ReportCategory reportCategory,
            AccountingReportSetting.QueryCategory queryCategory,
            String query) {

        accountingReportSettingService.saveNewQuery(reportCategory, queryCategory, query);
        return ResponseEntity.ok("Done ^_^");
    }
}
