package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.entity.TelecomPhone;
import com.magnamedia.entity.TelecomPhoneBill;
import com.magnamedia.entity.projection.TelecomPhoneCsvProjection;
import com.magnamedia.entity.projection.TelecomPhoneProjection;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.repository.TelecomPhoneBillRepository;
import com.magnamedia.repository.TelecomPhoneRepository;

import java.io.IOException;
import java.io.InputStream;
import java.util.logging.Level;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Kanaan <<EMAIL>>
 * Created on Mar 1, 2018
 */
@RequestMapping("/telecomphone")
@RestController
public class TelecomPhoneController extends BaseRepositoryController<TelecomPhone> {


    @Autowired
    private TelecomPhoneRepository RepHousemaid;

    @Autowired
    private ProjectionFactory projectionFactory;

    @Autowired
    private TelecomPhoneBillRepository billRepository;

    @Override
    public TelecomPhoneRepository getRepository() {
        return RepHousemaid;

    }

    @PreAuthorize("hasPermission('telecomphone','search')")
    @RequestMapping(value = "/page/search", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> searchTelecomPhone(Pageable pageable,
                                                Sort sort,
                                                @RequestParam(name = "search",
                                                        required = false) String queryString,
                                                @RequestParam(name = "active",
                                                        required = false) Boolean active,
                                                @RequestParam(name = "deleted",
                                                        required = false) Boolean deleted) {

//        SelectQuery<TelecomPhone> query = new SelectQuery<>(TelecomPhone.class);
//        
//        query.filterBy(
//                    new SelectFilter("name", "like", "%" + queryString + "%")
//                            .or("number", "like", "%" + queryString + "%"));
//        
//        
//        //Sorting
//        if (sort != null) {
//            for (Sort.Order order : sort) {
//                query.sortBy(order.getProperty(),
//                        order.isAscending(),
//                        !order.isAscending());
//            }
//        } else {
//            query.sortBy("name", true, false);
//        }

        //Jirra ACC-303
        Page<Object[]> page = null;
        if (active != null && deleted != null) {
            page = (Page<Object[]>) getRepository().
                    getTelecomeByNameOrNumberOrUsageAndActiveAndDeleted(queryString, queryString, queryString, active, deleted, pageable);
        } else if (active != null) {
            page = (Page<Object[]>) getRepository().
                    getTelecomeByNameOrNumberOrUsageAndActive(queryString, queryString, queryString, active, pageable);
        } else if (deleted != null) {
            page = (Page<Object[]>) getRepository().
                    getTelecomeByNameOrNumberOrUsageAndDeleted(queryString, queryString, queryString, deleted, pageable);
        } else {
            page = (Page<Object[]>) getRepository().
                    getTelecomeByNameOrNumberOrUsage(queryString, queryString, queryString, pageable);
        }
        return new ResponseEntity<>(
                page.map(x -> (TelecomPhone) x[0])
                        .map(obj ->
                                projectionFactory.createProjection(TelecomPhoneProjection.class,
                                        obj)),
                HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('telecomphone','downloadcsv')")
    @RequestMapping(value = "/search/csv", method = RequestMethod.GET)
    @ResponseBody
    public void downloadCsv(
            HttpServletResponse response,
            Sort sort,
            @RequestParam(name = "search", required = false) String queryString,
            @RequestParam(name = "active",
                    required = false) Boolean active,
            @RequestParam(name = "deleted",
                    required = false) Boolean deleted) {

        SelectQuery<TelecomPhone> query = new SelectQuery<>(TelecomPhone.class);

        query.filterBy(
                new SelectFilter("name", "like", "%" + queryString + "%")
                        .or("number", "like", "%" + queryString + "%")
                        .or("usageText", "like", "%" + queryString + "%"));

        if (active != null)
            query.filterBy("active", "=", active);
        if (deleted != null)
            query.filterBy("deleted", "=", deleted);


        //Sorting
        if (sort != null) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(),
                        order.isAscending(),
                        !order.isAscending());
            }
        } else {
            query.sortBy("name", true, false);
        }

        InputStream is = null;
        try {
            String[] namesOrdared = {"name", "number", "primaryExpense",
                    "secondryExpense", "dueEvery", "holders", "lastBill", "lastBillDate",
                    "usageText", "serviceType", "paymentMethod", "notes"};
            is = generateCsv(query, TelecomPhoneCsvProjection.class,
                    namesOrdared, 10000);
            createDownloadResponse(response, "Phones.csv", is);
        } catch (IOException ex) {
            logger.log(Level.SEVERE,
                    ex.getMessage(),
                    ex);
        } finally {
            StreamsUtil.closeStream(is);
        }
    }

    @Override
    protected ResponseEntity<?> deleteEntity(TelecomPhone entity) {
        entity.setDeleted(true);
        RepHousemaid.save(entity);
        return new ResponseEntity<>("TelecomPhone deleted successfully.",
                HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('telecomphone','addbill')")
    @RequestMapping(value = "/addbill/{id}", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<?> addBill(
            @PathVariable("id") TelecomPhone phone,
            @RequestBody TelecomPhoneBill bill) {
        if ((phone != null) && (bill != null)) {
            bill.setPhone(phone);
            billRepository.save(bill);
            return new ResponseEntity<>("TelecomPhone has updated successfully.",
                    HttpStatus.OK);
        }
        return new ResponseEntity<>("TelecomPhone has not found.",
                HttpStatus.BAD_REQUEST);
    }

}
