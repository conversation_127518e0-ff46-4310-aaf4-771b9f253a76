/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR>
 */
@RequestMapping("/notificationsTest")
@RestController
public class NotificationTestController {

//    @RequestMapping("/sendtype")
//    public String sendNotification(@RequestParam(required = true, value = "title") String title,
//            @RequestParam(required = true, value = "body") String body,
//            @RequestParam(required = true, value = "type") String type) {
//
//        Setup.getApplicationContext().getBean(NotificationUtils.class).sendNotificationOfType(title, body, type);
//        return "Sent";
//
//    }
//
//    @RequestMapping("/senduser")
//    public String sendNotificationUser(@RequestParam(required = true, value = "title") String title,
//            @RequestParam(required = true, value = "body") String body,
//            @RequestParam(required = true, value = "userId") Long userId) {
//
//        Setup.getApplicationContext().getBean(NotificationUtils.class).sendNotification(title, body, userId);
//        return "Sent";
//    }
}
