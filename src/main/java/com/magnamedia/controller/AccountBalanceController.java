package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.AccountBalance;
import com.magnamedia.entity.Bucket;
import com.magnamedia.entity.Transaction;
import com.magnamedia.extra.AccountBalanceCSVProjection;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.helper.CsvHelper;
import com.magnamedia.report.AccountBalanceReport;
import com.magnamedia.repository.AccountBalanceRepository;
import com.magnamedia.repository.BucketRepository;
import com.magnamedia.service.AccountBalanceService;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Masod <<EMAIL>>
 *         Created on Oct 14, 2020
 *         Jirra ACC-2522
 */

@RestController
@RequestMapping("/account-balance")
public class AccountBalanceController extends BaseRepositoryController<AccountBalance> {

    @Autowired
    private AccountBalanceRepository repository;
    @Autowired
    private BucketRepository bucketRepository;

    @Autowired
    private AccountBalanceService accountBalanceService;

    public enum Format {
        HTML, EXCEL, PDF
    }

    @PreAuthorize("hasPermission('account-balance','get-balance-report')")
    @RequestMapping(value = "/get-balance-report/{format}", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity getBalanceReport(
            @RequestBody AccountBalance searchCriteria,
           @PathVariable(value = "format") Format format,
           HttpServletResponse response) {

        if (format == null) format = Format.HTML;

        try {
            Map data = accountBalanceService.getBalance(searchCriteria);
            Double transactionSum = (Double) data.get("transactionSum");
            Double initBalance = (Double) data.get("initBalance");
            List<Transaction> transactions = (List<Transaction>) data.get("transactions");

            Bucket bucket = searchCriteria.getBucket();


            if(bucket != null) {
              //accountBalanceService.addAccountBalance(bucket);
              //bucket = bucketRepository.findOne(bucket.getId());
              bucket = accountBalanceService.setBucketBalanceBasedOnTransaction(bucket);
            }

            AccountBalanceReport report = new AccountBalanceReport(
                    initBalance, transactionSum, bucket, transactions,
                    bucket == null ? 0.0 : (double) Math.round(bucket.getBalance() * 100) / 100);

            switch (format) {
                case PDF:
                    response.setContentType(getMimeType("pdf"));
                    response.addHeader("Content-Disposition",
                            "attachment; filename=\"AccountBalance.pdf\"");
                    report.exportPdf(response.getOutputStream());
                    response.flushBuffer();
                    break;
                case HTML:
                    return new ResponseEntity(report.render(), HttpStatus.OK);
                case EXCEL:
                    InputStream inputStream = null;
                    try {
                        for (Transaction transaction : transactions) {
                            if (bucket != null) {
                                transaction.setBucketPreBalance(initBalance.toString());

                                if (transaction.getFromBucket() != null && transaction.getFromBucket().getCode() != null && transaction.getFromBucket().getCode().equals(bucket.getCode())) {
                                    initBalance -= transaction.getAmount();
                                }

                                if (transaction.getToBucket() != null && transaction.getToBucket().getCode() != null && transaction.getToBucket().getCode().equals(bucket.getCode())) {
                                    initBalance += transaction.getAmount();
                                }
                                transaction.setBucketBalance(initBalance.toString());
                            }
                        }
                        String[] namesOrdered = {"id", "fromBucket", "revenue", "expense", "toBucket", "description", "amount",
                                "date", "creationDate", "bucketPreBalance", "bucketBalance"};
                        String[] fileHeader = {"Bucket Balance: " + (bucket == null ? 0.0 : bucket.getBalance()),
                                "Transaction Sum: " + transactionSum,
                                "Record Count: " + (transactions != null ? transactions.size() : 0)};

                        File excelFile = CsvHelper.generateCsv(transactions, AccountBalanceCSVProjection.class,
                                bucket != null ? report.h : Arrays.copyOf(report.h, report.h.length-2),
                                bucket != null ? namesOrdered : Arrays.copyOf(namesOrdered, namesOrdered.length-2),
                                "AccountBalance.csv", "csv",
                                bucket != null ? fileHeader : Arrays.copyOfRange(fileHeader, 1, fileHeader.length));
                        inputStream = new FileInputStream(excelFile);
                        createDownloadResponse(response, "AccountBalance.csv", inputStream);
                    } finally {
                        StreamsUtil.closeStream(inputStream);
                    }
                    break;
            }
        } catch (Exception e) {
            return new ResponseEntity(ExceptionUtils.getStackTrace(e), HttpStatus.INTERNAL_SERVER_ERROR);
        }

        return null;
    }

    @Override
    public BaseRepository<AccountBalance> getRepository() {
        return repository;
    }
}
