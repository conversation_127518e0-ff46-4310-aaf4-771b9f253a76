package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.TemplateRepository;
import com.magnamedia.entity.DDMessaging;
import com.magnamedia.entity.DDMessagingToDo;
import com.magnamedia.module.type.DDMessagingType;
import com.magnamedia.repository.DDMessagingRepository;
import com.magnamedia.repository.DDMessagingToDoRepository;
import com.magnamedia.service.DDMessagingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on 15-4-2020
 *         Jirra ACC-1611
 */
@RestController(value = "ddMessagingToDoController")
@RequestMapping("/ddMessagingTodo")
public class DDMessagingToDoController extends BaseRepositoryController<DDMessagingToDo> {

    @Autowired
    private DDMessagingToDoRepository ddMessagingToDoRepository;
    @Autowired
    private DDMessagingRepository ddMessagingRepository;
    @Autowired
    private DDMessagingService ddMessagingService;

    @Autowired
    private TemplateRepository templateRepository;

    @Override
    public BaseRepository<DDMessagingToDo> getRepository() {
        return ddMessagingToDoRepository;
    }

    public void closeRelatedToDos(String contractUUID) {
        List<DDMessagingToDo> toDos = ddMessagingToDoRepository.findByContractUuidAndIsSent(contractUUID, Boolean.FALSE);
        closeToDos(toDos);
    }

    public void closeRelatedToDos(String contractUUID, List<DDMessagingType> events) {
        List<DDMessagingToDo> toDos = ddMessagingToDoRepository.findByContractUuidAndIsSentAndEventIn(contractUUID, Boolean.FALSE, events);
        closeToDos(toDos);
    }

    public void closeRelatedToDos(Long paymentId, List<DDMessagingType> events) {
        events.stream()
                .forEach(e -> {
                    List<DDMessagingToDo> toDos = ddMessagingToDoRepository.findByPaymentIdAndIsSentAndEvent(
                            paymentId, Boolean.FALSE, e);
                    if(e.equals(DDMessagingType.BouncedPayment)) {
                        toDos = toDos.stream().filter(t -> {
                            if(t.getDdMessagingConfigId() == null) return true;
                            DDMessaging dm = ddMessagingRepository.findOne(t.getDdMessagingConfigId());
                            String bouncedStatus = dm.getBouncedPaymentStatus() == null ?
                                    "" : dm.getBouncedPaymentStatus().getCode();
                            return !bouncedStatus.equals("bounced_payment_received") &&
                                    !bouncedStatus.equals("bounced_payment_received_-_not_including_worker_salary");
                        }).collect(Collectors.toList());
                    }
                    closeToDos(toDos);
                });
    }

    private void closeToDos(List<DDMessagingToDo> toDos) {
        if (toDos == null) return;

        for (DDMessagingToDo toDo : toDos) {
            toDo.setActive(Boolean.FALSE);
            ddMessagingToDoRepository.save(toDo);
        }
    }
}