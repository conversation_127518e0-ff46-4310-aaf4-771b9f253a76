
package com.magnamedia.controller;

import com.google.api.client.util.Strings;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.EnableSwaggerMethod;
import com.magnamedia.core.annotation.JwtSecured;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.annotation.caching.ApiCacheable;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.*;
import com.magnamedia.core.helper.paytabs.PaytabsTransaction;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.type.CoreParameter;
import com.magnamedia.entity.*;
import com.magnamedia.entity.ContractPaymentConfirmationToDo.Source;
import com.magnamedia.entity.dto.ContractPaymentConfirmationToDoDto;
import com.magnamedia.entity.dto.ContractPaymentConfirmationToDoFromGPTDto;
import com.magnamedia.entity.dto.ContractPaymentWrapperDto;
import com.magnamedia.entity.dto.ExistingPaymentFromGPTDto;
import com.magnamedia.entity.projection.ContractPaymentConfirmationSearchProjectionCsv;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.extra.CcAppCmsTemplate;
import com.magnamedia.extra.FilterItem;
import com.magnamedia.extra.PaymentHelper;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.extra.Utils;
import com.magnamedia.extra.annotations.UsedBy;
import com.magnamedia.helper.PayTabsHelper;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.module.type.TransactionEntityType;
import com.magnamedia.repository.*;
import com.magnamedia.service.*;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.sql.Date;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by Mamon.Masod on 7/4/2021.
 */

@RestController
@RequestMapping("/contract-payment-confirmation")
public class ContractPaymentConfirmationToDoController extends BaseRepositoryController<ContractPaymentConfirmationToDo> {

    private final Logger logger = Logger.getLogger(ContractPaymentConfirmationToDoController.class.getName());
    public static final String RELATIVE_REDIRECT_LINK = "/accounting/contract-payment-confirmation/paid-via-paytabs";
    public static final String ALLOW_ADD_DISCOUNT_TO_PAYMENT_FOR_APPROVAL_POSITION = "allow_add_discount_to_payment_for_approval_position";

    @Autowired
    private ContractPaymentConfirmationToDoRepository repository;
    @Autowired
    private PaymentController paymentCtrl;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private PaymentRepository paymentRepo;
    @Autowired
    private ContractRepository contractRepo;
    @Autowired
    private ContractPaymentRepository contractPaymentRepo;
    @Autowired
    private ContractPaymentTermRepository cptRepo;
    @Autowired
    private TransactionRepository transactionRepo;
    @Autowired
    private TransactionsController transactionsController;
    @Autowired
    private ContractPaymentWrapperRepository contractPaymentWrapperRepository;
    @Autowired
    private PayTabsHelper payTabsHelper;
    @Autowired
    private ContractPaymentConfirmationToDoService confirmationService;
    @Autowired
    private ClientPayingViaCreditCardService clientPayingViaCreditCardService;
    @Autowired
    private AccountingEPaymentService accountingEPaymentService;
    @Autowired
    private FlowProcessorService flowProcessorService;
    @Autowired
    private FlowProcessorEntityRepository flowProcessorEntityRepository;

    @Override
    public BaseRepository<ContractPaymentConfirmationToDo> getRepository() {
        return repository;
    }

    // ACC-8120
    @PreAuthorize("hasPermission('contract-payment-confirmation','proceedStopRelatedFlowAndCreateToDo')")
    @PostMapping(value = "/proceedStopRelatedFlowAndCreateToDo/{matchedToDoId}/{id}")
    @Transactional
    public ResponseEntity<?> proceedStopRelatedFlowAndCreateToDo(
            @PathVariable("matchedToDoId") ContractPaymentConfirmationToDo matchedTodo,
            @PathVariable("id") FlowProcessorEntity entity,
            @RequestBody ContractPaymentConfirmationToDo toDo) {


        ResponseEntity responseEntity = createFromErpAPI(toDo);

        if (!matchedTodo.isDisabled()) {
            matchedTodo.setDisabled(true);
            repository.silentSave(matchedTodo);
        }

        entity = flowProcessorEntityRepository.findOne(entity.getId());
        entity.setStopped(true);
        entity.setCausedTermination(false);
        entity.setStoppedDueContractTerminated(false);
        flowProcessorEntityRepository.silentSave(entity);

        return responseEntity;
    }

    @NoPermission
    @PostMapping(value = "/erp/create")
    @ResponseBody
    @Transactional
    public ResponseEntity createFromErpAPI(@RequestBody ContractPaymentConfirmationToDo toDo) {
        if (checkPermission("create")) {
            CurrentRequest.authorize();
            toDo.getContractPaymentList().forEach(w -> w.setAddedFromPaymentForApproval(true));

            confirmationService.validateCreation(toDo);

            // ACC-8650 When Using By Iframe From Sales: stop the old Online Reminder flow and Delete the old PRE_PDP
            if (toDo.getClientTodoInfo() != null) {
                Setup.getApplicationContext()
                        .getBean(ClientToDoService.class).
                        stopOldOnlineReminderFlowAndDeletedOldPayment(toDo.getClientTodoInfo());
            }

            if (toDo.isPayingViaCreditCard()) {

                logger.info("check if there is running IPAM flow , contract id : " + toDo.getContractPaymentTerm().getContract().getId());
                flowProcessorService.stopRunningFlow(toDo.getContractPaymentTerm().getContract(),
                        FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED);

                // ACC-8662
                Setup.getApplicationContext()
                        .getBean(ContractService.class)
                        .stopPreventCreateOtherDdsIfHas(toDo.getContractPaymentTerm().getContract());

                logger.info("check if there is running IPAM flow , contract id : " + toDo.getContractPaymentTerm().getContract().getId());
                flowProcessorService.stopRunningFlow(toDo.getContractPaymentTerm().getContract(),
                        FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED);
                clientPayingViaCreditCardService.handleAddConfirmationToDoFromErp(toDo);
            } else {
                confirmationService.createToDoERPSource(toDo);
            }

            return okResponse();
        } else {
            return unauthorizedReponse();
        }
    }

    @JwtSecured
    @UsedBy(others = UsedBy.Others.CC_App)
    @PreAuthorize("hasPermission('contract-payment-confirmation','createFromAppAPI')")
    @RequestMapping(value = "/ccApp/create", method = RequestMethod.POST)
    @ResponseBody
    @Transactional
    public ResponseEntity createFromAppAPI(@RequestBody ContractPaymentConfirmationToDo todo) {
        CurrentRequest.authorize();

        logger.info("Authorization code: " + todo.getAuthorizationCode());

        todo.setSource(Source.CC_APP);
        confirmationService.validateCreation(todo);
        return ResponseEntity.ok(confirmationService.createConfirmationToDo(todo));
    }

    @Transactional
    public ResponseEntity<?> createConfirmationToDo(ContractPaymentConfirmationToDo toDo) {
        return ResponseEntity.ok(confirmationService.createConfirmationToDo(toDo));
    }

    @PreAuthorize("hasPermission('contract-payment-confirmation','search')")
    @PostMapping(value = "/search")
    public ResponseEntity searchAPI(
            @RequestBody List<FilterItem> filters, Pageable pageable) {

        return ResponseEntity.ok(Setup.getApplicationContext()
                .getBean(QueryService.class)
                .contractPaymentConfirmationTodoSearch(filters, pageable));
    }

    @PreAuthorize("hasPermission('contract-payment-confirmation','search/csv')")
    @PostMapping(value = "/search/csv")
    public void isConfirmedCsv(
            @RequestBody List<FilterItem> filters,
            Sort sort,
            @RequestParam(name = "limit", required = false) Integer limit,
            HttpServletResponse response) {

        if (limit == null) limit = 1000;

        Page<ContractPaymentConfirmationToDoDto> p =
                Setup.getApplicationContext()
                        .getBean(QueryService.class)
                        .contractPaymentConfirmationTodoSearch(filters, PageRequest.of(0, limit));
        InputStream is = null;

        try {
            String[] namesOrdered =
                    {"client", "housemaid", "contractId", "contractType", "nationality", "dateChangedToReceived", "amount", "transferReference",
                            "expectedDate", "paymentType", "paymentMethod", "source", "description", "authorizationCode","isActive"};
            is = generateCsv(p.getContent(), ContractPaymentConfirmationSearchProjectionCsv.class, namesOrdered);

            createDownloadResponse(response, "Contract Payment Confirmation ToDos.csv", is);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            StreamsUtil.closeStream(is);
        }
    }

    @PreAuthorize("hasPermission('contract-payment-confirmation','confirm')")
    @GetMapping(value = "/confirm/{id}")
    @Transactional
    public ResponseEntity confirmAPI(
            @PathVariable("id") ContractPaymentConfirmationToDo toDo) throws Exception {

        confirmToDo(toDo);
        return okResponse();
    }

    @PreAuthorize("hasPermission('contract-payment-confirmation','confirm//wire-transfer')")
    @PostMapping(value = "/confirm/wire-transfer/{id}")
    @Transactional
    public ResponseEntity confirmWireTransferAPI(
            @PathVariable("id") ContractPaymentConfirmationToDo toDo,
            @RequestBody List<ContractPaymentWrapperDto> payments) throws Exception {

        if (toDo.getContractPaymentList() != null
                && !toDo.getContractPaymentList().isEmpty()
                && payments != null && !payments.isEmpty()){

            for(ContractPaymentWrapperDto dto : payments){
                for(ContractPaymentWrapper wrapper : toDo.getContractPaymentList()){
                    if (dto.getId().equals(wrapper.getId())){
                        dto.updateContractPaymentWrapperFromDto(wrapper);
                        contractPaymentWrapperRepository.save(wrapper);
                        break;
                    }
                }
            }
        }

        confirmToDo(toDo);
        return okResponse();
    }

    @Transactional
    public void confirmToDo(ContractPaymentConfirmationToDo toDo) throws Exception {
        if (toDo == null) throw new RuntimeException("ToDo not Found");

        ContractPaymentTerm cpt = cptRepo.findOne(toDo.getContractPaymentTerm().getId());

        int idx = 0;
        Optional<Transaction> oldTransaction = toDo.getContractPaymentList().stream()
                .filter(w -> w.getTransactionId() != null)
                .map(w -> transactionRepo.findOne(w.getTransactionId()))
                .findAny();

        String oldDescription = oldTransaction.isPresent() ? oldTransaction.get().getDescription() : "";

        for (ContractPaymentWrapper w : toDo.getContractPaymentList()) {
            ContractPaymentWrapper cpw = contractPaymentWrapperRepository.findOne(w.getId());

            cpw.setCreator(w.getCreator() != null && w.getCreator().getUsername() != null ?
                    Setup.getUser(w.getCreator().getUsername()) : null);

            logger.info("Payment Method: " + toDo.getPaymentMethod());
            Map newPayment = null;
            Long newPaymentId = null;
            Long transactionId = null;

            switch (toDo.getPaymentMethod()) {
                case WIRE_TRANSFER:
                    if (idx == 0 && oldTransaction.isPresent()) {
                        logger.info(String.format("Update Related Transaction (%s)", w.getTransactionId()));
                        transactionId = oldTransaction.get().getId();
                    }

                    if (w.isReplacementOfBouncedPayment()) {
                        logger.info("Replacement of Bounced Payment -> call replacing payment API from ClientMgmt Module");
                        Payment bouncedPayment = paymentRepo.findOne(w.getReplacedBouncedPaymentId());
                        newPaymentId = confirmationService.addReplacementPayment(bouncedPayment, cpw, true).getId();
                        logger.info(String.format("Payment ID is (%s)", newPaymentId));

                    } else if (w.isReplacementOfFuturePayment()) {
                        logger.info("Replacement of Future PRE_PDP/PDP Payment -> call receiving Payment API from ClientMgmt Module");
                        Payment actualPayment = paymentRepo.findOne(w.getReplacedFuturePaymentId());

                        if(actualPayment.getMethodOfPayment() != null && actualPayment.getMethodOfPayment().equals(PaymentMethod.DIRECT_DEBIT)){
                            logger.info("Creation of new Payment -> call creating new Payment API from ClientMgmt Module");
                            newPaymentId = confirmationService.createPayment(cpw, toDo.getAttachments(), PaymentStatus.RECEIVED, false, true);
                        } else{
                            actualPayment.setStatus(PaymentStatus.RECEIVED);
                            actualPayment.setAttachments(toDo.getAttachments());
                            // ACC-8166
                            actualPayment.setIgnorePostingEngineBR(true);
                            paymentService.forceUpdatePayment(actualPayment);
                            newPaymentId = w.getReplacedFuturePaymentId();
                            logger.info(String.format("Payment ID is (%s)", w.getReplacedFuturePaymentId()));
                        }
                    } else if (w.isCreationOfNewPayment()) {
                        logger.info("Creation of new Payment -> call creating new Payment API from ClientMgmt Module");
                        newPaymentId = confirmationService.createPayment(cpw, toDo.getAttachments(), PaymentStatus.RECEIVED, false, true);
                    }
                    createOrUpdateTransaction(w, transactionId, oldTransaction, newPaymentId, newPayment, oldDescription);
                    break;

                case CARD:
                    if(toDo.isPayingOnline()) {
                        confirmationService.confirmOnlineCardPayment(w);
                        break;
                    }

                default:
                    confirmationService.confirmNonOnlineAndCashPayment(cpw);
                    break;
            }

            idx++;
        }

        // Email Difference should be calculated before updating the transaction amount
        if (toDo.getPaymentMethod().equals(PaymentMethod.WIRE_TRANSFER)) {
            List<ContractPaymentWrapper> wrappers = contractPaymentWrapperRepository.findAll(
                    toDo.getContractPaymentList().stream().map(w -> w.getId()).collect(Collectors.toList()));

            Double totalTransactions = wrappers.stream().filter(w -> w.getTransactionId() != null)
                    .map(w -> {
                        Transaction transaction = transactionRepo.findOne(w.getTransactionId());
                        logger.log(Level.SEVERE, "Contract transaction.getAmount(): " + transaction.getAmount());

                        return transaction.getAmount();
                    }).reduce(0.0, Double::sum);

            logger.log(Level.SEVERE, "Contract toDo.getTotalAmount(): " + toDo.getMainTotalAmount());
            Double amountDifference = toDo.getMainTotalAmount() - totalTransactions;

            Double amountDifferenceThreshold = Double.parseDouble(
                    Setup.getParameter(Setup.getCurrentModule(),
                            AccountingModule.PARAMETER_WIRE_TRANSFER_AMOUNT_DIFFERENCE_THRESHOLD));

            if (amountDifference > amountDifferenceThreshold) {
                List<ContractPaymentWrapper> paymentList = wrappers.stream()
                        .filter(payment -> !payment.getAmount().equals(payment.getActualReceivedAmount()))
                        .collect(Collectors.toList());

                confirmationService.sendWireTransferAmountDifferenceEmail(cpt.getContract(), amountDifference, paymentList);
            }
        }

        toDo = repository.findOne(toDo.getId());
        toDo.setConfirmed(true);
        repository.save(toDo);

        // if(to Do.isPayingOnline()) return; ACC-8333

        Setup.getApplicationContext()
                .getBean(FlowProcessorService.class)
                .retractContractTermination(toDo.getContractPaymentTerm(), toDo);
    }

    @UsedBy(modules = UsedBy.Modules.Client_Management)
    public Boolean isTherePendingUserConfirmationToDo(Long contractId) {
        Contract contract = contractId != null ? contractRepo.findOne(contractId) : null;
        if (contractId == null) throw new RuntimeException("Contract not Found");

        return repository.existsByContractPaymentTerm_ContractAndConfirmedAndShowOnERP(
                contract, false, true);
    }


    public Long createOrUpdateTransaction(
            ContractPaymentWrapper contractPaymentWrapper,
            Long transactionId,
            Optional<Transaction> oldTransaction,
            Long newPaymentId,
            Map newPayment,
            String oldDescription) {

        if (transactionId == null) {//clone old transaction and set the details
            transactionId = createTransaction(contractPaymentWrapper, newPaymentId);
            logger.info(String.format("Generated Transaction ID is (%s)", transactionId));
        } else {
            logger.info(String.format("Transaction with ID (%s) already exists", transactionId));
        }

        Transaction transaction = transactionRepo.findOne(transactionId);
        setTransactionDetails(transaction, oldTransaction, contractPaymentWrapper, newPaymentId, newPayment, oldDescription);
        transactionsController.updateEntity(transaction);

        contractPaymentWrapper = contractPaymentWrapperRepository.findOne(contractPaymentWrapper.getId());
        contractPaymentWrapper.setTransactionId(transactionId);
        contractPaymentWrapperRepository.save(contractPaymentWrapper);

        return transactionId;
    }

    public Long createTransaction(
            ContractPaymentWrapper contractPaymentWrapper,
            Long newPaymentId) {

        Transaction transaction = new Transaction();
        setTransactionDetails(transaction, null, contractPaymentWrapper, newPaymentId, null, "");

        return ((Transaction)transactionsController.createEntity(transaction).getBody()).getId();//transactionRepo.save(transaction).getId();
    }

    public void setTransactionDetails(
            Transaction transaction,
            Optional<Transaction> oldTransaction,
            ContractPaymentWrapper w,
            Long paymentId,
            Map newPayment,
            String oldDescription) {

        ContractPaymentConfirmationToDo todo = repository.findOne(w.getContractPaymentConfirmationToDo().getId());
        Payment payment = paymentRepo.findOne(paymentId);
        Contract contract = todo.getContractPaymentTerm().getContract();

        transaction.setPaymentId(paymentId);
        transaction.setPaymentType(w.getPaymentMethod());
        transaction.setToBucket(w.getToBucket());
        transaction.setRevenue(w.getRevenue());

        if(newPayment != null && newPayment.containsKey("amountOfPayment")) {
            transaction.setAmount(Double.parseDouble(newPayment.get("amountOfPayment").toString()));
        } else if(payment != null) {
            transaction.setAmount(payment.getAmountOfPayment());
        } else {
            transaction.setAmount(w.getActualReceivedAmount());
        }

        if(oldTransaction == null || !oldTransaction.isPresent()) {
            transaction.setDate(new Date(todo.getExpectedDate().getTime()));
            transaction.setPnlValueDate(new Date(todo.getExpectedDate().getTime()));
            transaction.setDescription("Payment via wire transfer "
                    + contract.getClient().getName() + " /C.ID: " + contract.getId() + " /P.Id: " + paymentId);
        } else {
            transaction.setDate(oldTransaction.get().getDate());
            transaction.setPnlValueDate(oldTransaction.get().getPnlValueDate());

            transaction.setDescription("Payment via wire transfer "
                    + contract.getClient().getName() + " /C.ID: " + contract.getId() + " /P.Id: " + paymentId
                    + " \n" + oldDescription);
        }

        if (todo.getId().equals(getItem("TypeOfPayment", "monthly_payment").getId()) && contract != null) {
            transaction.setTransactionType(TransactionEntityType.CLIENT);

            ClientTransaction clientTransaction = new ClientTransaction();
            clientTransaction.setClient(contract.getClient());
            clientTransaction.setTransaction(transaction);

            List<ClientTransaction> clientTransactionList = new ArrayList();
            clientTransactionList.add(clientTransaction);

            transaction.setClients(clientTransactionList);
        }

        if(w.isVatPaidByClient()){
            if(newPayment != null && newPayment.containsKey("vat")) {
                transaction.setVatAmount(Double.parseDouble(newPayment.get("vat").toString()));
            } else {
                transaction.setVatAmount(payment != null ? payment.getVat() : null);
            }
            transaction.setVatType(w.getVatType());
            transaction.setLicense(PicklistHelper.getItemNoException(
                    AccountingModule.PICKLIST_TRANSACTION_LICENSE, AccountingModule.PICKLIST_TRANSACTION_LICENSE_MUSTAQEEM_ITEM));
        } else{
            transaction.setVatType(null);
            transaction.setVatAmount(null);
            transaction.setLicense(PicklistHelper.getItemNoException(
                    AccountingModule.PICKLIST_TRANSACTION_LICENSE,
                    AccountingModule.PICKLIST_TRANSACTION_LICENSE_NO_VAT_ITEM));

            ContractPayment cp = contractPaymentRepo.findOne(
                    w.getContractPayment().getId());
            cp.setVatPaidByClient(false);
            cp.setVat(0.0);
            contractPaymentRepo.save(cp);
        }

        //copy needed data from old transaction
        if (oldTransaction != null && oldTransaction.isPresent()) {
            for (Attachment att : oldTransaction.get().getAttachments()){
                transaction.addAttachment(Storage.cloneTemporary(att, att.getTag()));
            }
        }
    }

    @NoPermission
    @UsedBy(others = UsedBy.Others.CC_App)
    @RequestMapping(value = "/paid-via-paytabs", method = RequestMethod.POST, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    @Transactional
    @Deprecated
    public ResponseEntity<?> paidViaPayTabs(@RequestParam Map<String, Object> body) throws URISyntaxException {
        for (String s : body.keySet()){
            logger.info( s + " " + body.get(s).toString());
        }
        String entityUuid = payTabsHelper.extractUUID(body);
        logger.info("paid-via-paytabs API, UUID: " + entityUuid);

        ContractPaymentConfirmationToDo toDo = getRepository().findByUuid(entityUuid);
        String relativeRedirectPage = null;

        if (toDo != null) {
            SelectQuery<BackgroundTask> tasksQuery = new SelectQuery<>(BackgroundTask.class);
            tasksQuery.filterBy("relatedEntityId", "=", toDo.getId());
            tasksQuery.filterBy("relatedEntityType", "=", "ContractPaymentConfirmationToDo");
            tasksQuery.filterBy("targetMethod", "=", "paidViaPayTabs");
            tasksQuery.filterBy("parameters", "like", "%paidSuccess%");

            if (!tasksQuery.execute().isEmpty()) relativeRedirectPage = "2";
        }

        // ACC-5587
        if (toDo != null && body.containsKey("respCode")) {
            toDo.setAuthorizationCode((String) body.get("respCode"));
            getRepository().save(toDo);
        }

        if(relativeRedirectPage == null) {
            relativeRedirectPage = accountingEPaymentService.paidViaPayTabs(
                    toDo,
                    body.getOrDefault("respMessage", "").toString(),
                    body.getOrDefault("respStatus", "").toString().equals("A"));
        }

        URI redirectPage = new URI(Setup.getCoreParameter(CoreParameter.PUBLIC_LINK_BASE) + "/" + relativeRedirectPage);

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setLocation(redirectPage);

        return new ResponseEntity(httpHeaders, HttpStatus.SEE_OTHER);
    }

    @NoPermission
    @GetMapping(value = "/replaced-paytabs-public-link/{uuId}")
    @Transactional
    public ResponseEntity<?> replacedPaytabsPublicLink(
            @PathVariable(name = "uuId") String uuId) throws URISyntaxException {

        if(Strings.isNullOrEmpty(uuId)){
             throw new RuntimeException("Error: uuId cannot be null or empty");
        }

        logger.info("replaced-paytabs-public-link API, UUID: " + uuId);
        ContractPaymentConfirmationToDo toDo = getRepository().findByUuid(uuId);

        boolean disableLink = confirmationService.isTodoExpired(toDo);

        String redirectPageAction = "4";
        URI redirectPage = null;

        if(toDo == null || disableLink ||
                (!Strings.isNullOrEmpty(toDo.getPayTabsResponseMessage()) && toDo.isShowOnERP())) {

            if(toDo == null) {
                redirectPageAction = "3"; // Not Found
            }
            else if (disableLink) {
                redirectPageAction = "5"; // Expired link
            } else {
                redirectPageAction = "2";
            }

            redirectPage = new URI(Setup.getCoreParameter(CoreParameter.PUBLIC_LINK_BASE) + "/" +
                    Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_PAYTAB_PAYMENT_REDIRECT_AFTER_ACTION_PAGE) +
                    "?action=" + redirectPageAction);
        } else if (Arrays.asList(Source.ERP,
                Source.AFTER_CASH_FLOW,
                Source.CLIENT_PAYING_VIA_Credit_Card,
                Source.ONE_MONTH_AGREEMENT).contains(toDo.getSource())){

            // create new transaction and generate paytabs link
            PaytabsTransaction paytabsTransaction = payTabsHelper.generatePayTabsLink(
                    toDo.getUuid(), toDo.getSource().toString(), toDo.getTotalAmount(), RELATIVE_REDIRECT_LINK);
            String transactionString = payTabsHelper.convertToString(paytabsTransaction);
            logger.info("PaytabsTransaction Object: " + transactionString);
            String redirectURL = paytabsTransaction.getRedirect_url();


            toDo.setCreditCardInfo(transactionString);
            getRepository().save(toDo);

            redirectPage = new URI(redirectURL);
        }

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setLocation(redirectPage);

        return new ResponseEntity(httpHeaders, HttpStatus.SEE_OTHER);
    }

    @PreAuthorize("hasPermission('contract-payment-confirmation','AddPaymentForApprovalInfo')")
    @PostMapping(value = "/AddPaymentForApprovalInfo/{id}")
    @Transactional
    public ResponseEntity AddPaymentForApprovalInfo(@PathVariable("id") Contract contract) {
        List<PaymentStatus> statuses = Arrays.asList(PaymentStatus.PDC,
                PaymentStatus.PRE_PDP, PaymentStatus.RECEIVED);

        Integer allowedProRatedPayments = 0;
        if (contract.getContractProspectType().getCode().equals("maids.cc_prospect")) {
            List<Payment> proRatedPayments = paymentRepo.findByContractAndIsProRatedAndStatusIn(contract, true,  statuses);
            allowedProRatedPayments = proRatedPayments.isEmpty() ?
                    2 : (proRatedPayments.size() == 1 ? 1 : 0);
        }

        List<Payment> initialPayments = paymentRepo.findByContractAndIsInitialAndStatusIn(contract, true,  statuses);

        Map<String, Object> info = new HashMap<>();
        info.put("allowedProRatedPayments", allowedProRatedPayments);
        info.put("allowedInitialPayments", initialPayments.size() == 0 ? 2 : initialPayments.size() == 1 ? 1 : 0);
        info.put("payingViaCreditCard", contract.isPayingViaCreditCard());
        info.put("allowedWaivedPayments", true);
        info.put("showWarningMessageForIPAM", flowProcessorService.existsRunningFlows(contract,
                Collections.singletonList(FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED)));
        return new ResponseEntity(info, HttpStatus.OK);
    }

    @NoPermission
    @GetMapping(value = "/getNextMonthAmountAndPaidEndDate")
    public ResponseEntity<?> getNextMonthAmountAndPaidEndDate(String uuid) {
        Contract contract = contractRepo.findByUuid(uuid);
        if (contract == null)
            return new ResponseEntity<>("Invalid contract id", HttpStatus.BAD_REQUEST);

        Map<String, Object> map = new HashMap<>();
        map.put("date", "");
        map.put("amount", "");
        map.put("paymentsInfo", "");
        map.put("paytabsLink", "");
        map.put("paid", true);

        SelectQuery<ContractPaymentConfirmationToDo> query = new SelectQuery<>(ContractPaymentConfirmationToDo.class);
        query.filterBy("contractPaymentTerm.contract", "=", contract);
        query.filterBy("disabled", "=", false);
        query.filterBy("source", "in", Arrays.asList(
                Source.CLIENT_PAYING_VIA_Credit_Card,
                Source.ONE_MONTH_AGREEMENT));
        query.sortBy("creationDate", false);

        List<ContractPaymentConfirmationToDo> toDos = query.execute();
        if(toDos.isEmpty()) return new ResponseEntity<>(map, HttpStatus.OK);

        logger.log(Level.INFO, "toDo id: {0}", toDos.get(0).getId());

        List<ContractPaymentWrapper> monthly = toDos.get(0).getContractPaymentList()
                .stream().filter(t -> t.getPaymentType().getCode().equals("monthly_payment"))
                .sorted(Comparator.comparing(ContractPaymentWrapper::getPaymentDate))
                .collect(Collectors.toList());

        DateTime paidEndDate = monthly.isEmpty() ?
                paymentService.getLastReceivedMonthlyPaymentDate(contract) :
                new DateTime(monthly.get(monthly.size()-1).getPaymentDate());
        if(paidEndDate == null) paidEndDate = new DateTime(contract.getPaidEndDate());
        int d = new LocalDate(contract.getPaidEndDate()).getDayOfMonth();

        map.put("paid", toDos.get(0).isShowOnERP());
        map.put("amount", String.valueOf(toDos.get(0).getTotalAmount().intValue()));
        map.put("date", contract.isOneMonthAgreement() ?
                (d == 31 ?
                        paidEndDate.plusMonths(1).withDayOfMonth(30).toString("dd MMM yyyy") :
                        paidEndDate.plusMonths(1).withDayOfMonth(d).toString("dd MMM yyyy")) :
                paidEndDate.dayOfMonth().withMaximumValue().toString("dd MMM yyyy"));


        map.put("paymentsInfo", confirmationService.getPaymentsDetails(toDos.get(0)));

        boolean recurring = ContractPaymentConfirmationToDoService.isEligibleForTokenizationViaConfirmationToDO(toDos.get(0));

        String paytabsLink = confirmationService.getPayingViaCreditCardLink(toDos.get(0), false, recurring);
        logger.info("todo id: " + toDos.get(0).getId() + "; paytabsLink: " + paytabsLink);
        map.put("paytabsLink", paytabsLink);

        return new ResponseEntity<>(map, HttpStatus.OK);
    }

    // ACC-8742
    @JwtSecured
    @UsedBy(others = UsedBy.Others.New_GPT)
    @Transactional
    @PostMapping(value = "/createFromGPTAPIForExistingPayment")
    public ResponseEntity<?> createFromGPTAPIForExistingPayment(@RequestBody ExistingPaymentFromGPTDto dto) {
        return ResponseEntity.ok(confirmationService.createFromGPTAPIForExistingPayment(dto));
    }

    // ACC-8120
    @PreAuthorize("hasPermission('contract-payment-confirmation','checkMatchedToDoIfRelatedToRunningFlow')")
    @GetMapping(value = "/checkMatchedToDoIfRelatedToRunningFlow/{id}")
    public ResponseEntity<?> checkMatchedToDoIfRelatedToRunningFlow(@PathVariable("id") Long todo) {
        return ResponseEntity.ok(confirmationService.checkMatchedToDoIfRelatedToRunningFlow(todo));
    }

    // ACC-8741
    @JwtSecured
    @UsedBy(others = UsedBy.Others.New_GPT, modules = UsedBy.Modules.Staff_Management)
    @Transactional
    @PostMapping(value = "/createFromGPTAPI")
    public ResponseEntity<?> createFromGPTAPI(@RequestBody ContractPaymentConfirmationToDoFromGPTDto dto) {
        return ResponseEntity.ok(confirmationService.createFromGPTAPI(dto));
    }

    // ACC-6596
    @PreAuthorize("hasPermission('contract-payment-confirmation','validateCreateToDoFromErp')")
    @PostMapping(value = "/validateCreateToDoFromErp")
    public ResponseEntity<?> validateCreateToDoFromErp(@RequestBody ContractPaymentConfirmationToDo toDo) {
        confirmationService.validateCreation(toDo);

        return ResponseEntity.ok(confirmationService.validateCreateToDoFromErp(toDo));
    }

    // ACC-6596
    @PreAuthorize("hasPermission('contract-payment-confirmation','sendPayTabsLinkViaMessage')")
    @GetMapping(value = "/sendPayTabsLinkViaMessage")
    public ResponseEntity<?> sendPayTabsLinkViaMessage(String todoUuid) {

        ContractPaymentConfirmationToDo toDo = getRepository().findByUuid(todoUuid);
        if (toDo == null) return badRequestResponse();

        if (toDo.getContractPaymentTerm().getContract().isEnded() && !toDo.isReactivationPayment()) {
            toDo.setReactivationPayment(true);
            repository.save(toDo);
        }

        confirmationService.sendPayTabsLinkViaMessage(toDo);
        return okResponse();
    }

    @PreAuthorize("hasPermission('contract-payment-confirmation','setReactivationPaymentForTodo')")
    @GetMapping(value = "/setReactivationPaymentForTodo/{uuid}")
    public ResponseEntity<?> setReactivationPaymentForTodo(@PathVariable("uuid") String todoUuid) {

        ContractPaymentConfirmationToDo toDo = getRepository().findByUuid(todoUuid);
        if (toDo == null) return badRequestResponse();

        if (!toDo.getContractPaymentTerm().getContract().isEnded() || toDo.isReactivationPayment()) return okResponse();

        toDo.setReactivationPayment(true);
        repository.save(toDo);
        return okResponse();
    }

    @PreAuthorize("hasPermission('contract-payment-confirmation','getPayingViaCreditCardPayment')")
    @GetMapping(value = "/getPayingViaCreditCardPayment/{id}")
    public ResponseEntity<?> getPayingViaCreditCardPayment(@PathVariable("id") Contract contract) {

        AtomicInteger i = new AtomicInteger(1);
        return ResponseEntity.ok(clientPayingViaCreditCardService.getInitialFlowPaymentsForErp(contract)
                .stream()
                .sorted(Comparator.comparing(ContractPayment::getDate))
                .map(c -> {
                    Map<String, Object> m = new HashMap<>();
                    m.put("paymentType", new HashMap<String, Object>() {{
                        put("id", c.getPaymentType().getId());
                        put("code", c.getPaymentType().getCode());
                        put("label", c.getPaymentType().getName());
                    }});
                    m.put("dateOfPayment", new LocalDate(c.getDate()).toString("yyyy-MM-dd"));
                    m.put("amount", c.getAmount());
                    m.put("id", i.getAndIncrement());
                    List<Object[]> l = paymentRepo.findStatusByContractAndTypeOfPaymentAndDateOfPayment(
                            contract, c.getPaymentType().getCode(),
                            new LocalDate(c.getDate()).dayOfMonth().withMinimumValue().toDate(),
                            new LocalDate(c.getDate()).dayOfMonth().withMaximumValue().toDate());
                    m.put("status", l.isEmpty() ? "" : l.get(0)[0]);
                    return m;
                }).collect(Collectors.toList()));
    }

    @PreAuthorize("hasPermission('contract-payment-confirmation','allowAddDiscountToPaymentForApproval')")
    @GetMapping(value = "/allowAddDiscountToPaymentForApproval")
    public boolean allowAddDiscountToPaymentForApproval() {

        return CurrentRequest.getUser() != null &&
                CurrentRequest.getUser().hasPosition(ALLOW_ADD_DISCOUNT_TO_PAYMENT_FOR_APPROVAL_POSITION);
    }

    @PreAuthorize("hasPermission('contract-payment-confirmation','checkIfUserForgetToMarkPaymentAsProrated')")
    @PostMapping(value = "/checkIfUserForgetToMarkPaymentAsProrated/{id}")
    public boolean checkIfUserForgetToMarkPaymentAsProrated(
            @PathVariable("id") Contract contract, @RequestBody ContractPaymentConfirmationToDo todo) {

        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
        PicklistItem monthlyPayment = PicklistHelper.getItem("TypeOfPayment", "monthly_payment");
        CalculateDiscountsWithVatService calculateDiscountsWithVatService = Setup.getApplicationContext().getBean(CalculateDiscountsWithVatService.class);
        return todo.getContractPaymentList()
                .stream()
                .filter(cw -> cw.getPaymentType() != null && cw.getPaymentType().getId() != null &&
                        cw.getPaymentType().getId().equals(monthlyPayment.getId()) &&
                        cw.getReplacedFuturePaymentId() == null &&
                        cw.getReplacedBouncedPaymentId() == null)
                .anyMatch(cw ->
                        !calculateDiscountsWithVatService.getAmountOfMonthlyPaymentAtTimeWithDiscounts(cpt, new LocalDate(cw.getPaymentDate())).get("amount")
                                .equals(cw.getAmount()) && !cw.isProrated()
                );
    }

    @JwtSecured
    @UsedBy(others = UsedBy.Others.CC_App)
    @GetMapping(value = "/isLinkExpired/{uuId}")
    public ResponseEntity<?> isLinkExpired(@PathVariable(name = "uuId") String uuId) {

        ContractPaymentConfirmationToDo toDo = getRepository().findByUuid(uuId);
        boolean isExpire = toDo == null || confirmationService.isTodoExpired(toDo) ||
                (!Strings.isNullOrEmpty(toDo.getPayTabsResponseMessage()) && toDo.isShowOnERP());

        return ResponseEntity.ok(new HashMap<String, Object>(){{
            put("isExpire", isExpire);
        }});
    }

    // ACC-6513
    @JwtSecured
    @UsedBy(others = UsedBy.Others.CC_App)
    @PostMapping(value = "/ccApp/getTodoUuidViaPayment")
    public ResponseEntity<?> getTodoUuid(@RequestBody Map<String, Object> body) {
        ContractPaymentConfirmationToDo toDo = null;

        if (body.containsKey("replacedBouncedPaymentId")) {
            Payment p = paymentRepo.findOne(Utils.parseValue(body.get("replacedBouncedPaymentId"), Long.class));
            toDo = paymentService.createToDoIfNotExists(p, p.getContract().getActiveContractPaymentTerm(), Source.BOUNCED_PAYMENT_FLOW);
        } else if (body.containsKey("replacedFuturePaymentId")) {
            Payment p = paymentRepo.findOne(Utils.parseValue(body.get("replacedFuturePaymentId"), Long.class));
            toDo = paymentService.createToDoIfNotExists(p, p.getContract().getActiveContractPaymentTerm(), Source.FAQ);
        } else if (body.containsKey("purpose") && body.get("purpose").equals(Source.VISA_OVERSTAY_FEE.toString()) && body.containsKey("contractId")) {
            toDo = confirmationService.createToDoForVisaOverStayFeePurpose(
                    body,
                    contractRepo.findOne(Utils.parseValue(body.get("contractId"), Long.class))
                            .getActiveContractPaymentTerm());
        } else if (body.containsKey("payments") && body.containsKey("contractId")) {

            ContractPaymentConfirmationToDo.Purpose purpose = ContractPaymentConfirmationToDo.Purpose.CC_APP_SWITCH_NATIONALITY;
            Contract contract = contractRepo.findOne(Utils.parseValue(body.get("contractId"), Long.class));

            // Check if the link and notification is still active
            if (body.containsKey("switchLiveOutStatus")) {

                if (!Setup.getApplicationContext()
                        .getBean(SwitchingNationalityService.class)
                        .validationSwitchMaidLiveStatus(contract, false)) {
                    throw new RuntimeException("This action is no longer available.");
                }
                purpose = ContractPaymentConfirmationToDo.Purpose.CC_APP_SWITCH_LIVE_OUT_STATUS;
            }

            toDo = confirmationService.createToDoForSwitchNationalityPurpose(
                    (List<Map<String, Object>>) body.get("payments"),
                    contract.getActiveContractPaymentTerm(), purpose);
        } else if (body.containsKey("contractId") && body.getOrDefault("purpose", "").equals(ContractPaymentConfirmationToDo.Purpose.CC_APP_ADD_NEW_CARD.toString())) {
            Contract contract = contractRepo.findOne(Utils.parseValue(body.get("contractId"), Long.class));
            List<ContractPaymentConfirmationToDo> toDos = repository.findByContractPaymentTerm_ContractAndSourceAndDisabledFalseAndPurpose(
                            contract, Source.AUTHORIZATION, ContractPaymentConfirmationToDo.Purpose.CC_APP_ADD_NEW_CARD);
            toDo = !toDos.isEmpty() ?
                    toDos.get(0) :
                    confirmationService.generateConfirmationToDoForAuthorization(contract.getActiveContractPaymentTerm(),
                                    ContractPaymentConfirmationToDo.Purpose.CC_APP_ADD_NEW_CARD);
        } else if (body.containsKey("todoUuid") && body.getOrDefault("purpose", "").equals(ContractPaymentConfirmationToDo.Purpose.CC_APP_RECURRING_CLIENT_INITIATED.toString())) {
            toDo = repository.findByUuid(String.valueOf(body.get("todoUuid")));
            toDo.setPurpose(ContractPaymentConfirmationToDo.Purpose.CC_APP_RECURRING_CLIENT_INITIATED);
            repository.save(toDo);
        }

        Map<String, Object> m = new HashMap<>();
        m.put("todoUuid", toDo == null ? "" : toDo.getUuid());
        return ResponseEntity.ok(m);
    }

    // ACC-7278
    @PreAuthorize("hasPermission('contract-payment-confirmation','checkClientEnchanterTodoPaymentStatus')")
    @GetMapping(value = "/checkClientEnchanterTodoPaymentStatus/{id}")
    public ResponseEntity<?> checkClientEnchanterTodoPaymentStatus(@PathVariable("id") Long id) {
        Map<String, Object> r = new HashMap<String, Object>() {{
           put("received", false);
           put("exists", false);
        }};

        SelectQuery<ContractPaymentConfirmationToDo> q =  new SelectQuery<>(ContractPaymentConfirmationToDo.class);
        q.filterBy("relatedEntityId", "=", id);
        q.filterBy("relatedEntityType", "=", "ClientEnchanterTodo");

        List<ContractPaymentConfirmationToDo> l = q.execute();
        if (l.isEmpty()) return ResponseEntity.ok(r);
        r.put("exists", true);
        r.put("received", l.stream().allMatch(t -> t.isConfirmed() ||
                (t.getPaymentMethod().equals(PaymentMethod.CARD) &&
                        t.isPayingOnline() &&
                        t.isShowOnERP())));

        return ResponseEntity.ok(r);
    }

    // ACC-7156
    @PreAuthorize("hasPermission('contract-payment-confirmation','getOnlineCreditCardLinkViaPayment')")
    @PostMapping(value = "/getOnlineCreditCardLinkViaPayment")
    public ResponseEntity<?> getOnlineCreditCardLinkViaPayment(@RequestBody long[] paymentIds) {

        return ResponseEntity.ok(confirmationService.getOnlineCreditCardLinkViaPayment(paymentIds));
    }

    // SD-48957
    @PreAuthorize("hasPermission('contract-payment-confirmation','applyOnRefundedBusinessLogic')")
    @PostMapping(value = "/applyOnRefundedBusinessLogic")
    public ResponseEntity<?> applyOnRefundedBusinessLogic(@RequestBody long[] transactionIds) {

        for (long transactionId : transactionIds) {
            try {
                accountingEPaymentService.applyOnRefundedBusinessLogic(transactionId);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('contract-payment-confirmation','createPaymentFromWrapper')")
    @GetMapping(value = "/createPaymentFromWrapper/{id}")
    public ResponseEntity<?> createPaymentFromWrapper(@PathVariable("id") ContractPaymentWrapper w) throws Exception {

        return ResponseEntity.ok(confirmationService.createPayment(
                w, w.getContractPaymentConfirmationToDo().getAttachments(), PaymentStatus.RECEIVED, true));
    }

    @PreAuthorize("hasPermission('contract-payment-confirmation','applyBusinessLogic')")
    @PostMapping(value = "/applyBusinessLogic")
    public ResponseEntity<?> applyBusinessLogic(@RequestBody long[] transactionIds) {

        for (long transactionId : transactionIds) {
            try {
                accountingEPaymentService.applyBusinessLogic(transactionId);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return ResponseEntity.ok("Done");
    }

    @NoPermission
    @GetMapping(value = "/getFlowTerminationMessageInfo")
    public ResponseEntity<?> getFlowTerminationMessageInfo(@RequestParam String uuid) {

        Map<String, Object> response = new HashMap<>();
        response.put("title", "");
        response.put("text", "");
        response.put("payTabsLink", "");
        response.put("paymentsInfo", "");
        response.put("paid", true);

        AccountingLink accountingLink = Setup.getRepository(AccountingLinkRepository.class).findByUuid(uuid);
        if (accountingLink == null) {
            return ResponseEntity.ok(response);
        }

        boolean recurring = false;
        ContractPaymentConfirmationToDo todo = null;
        ContractPaymentTerm cpt = Setup.getRepository(ContractPaymentTermRepository.class)
                .findOne(accountingLink.getCptId());
        String templateCode = null;
        Map<String, Object> parameters = new HashMap<>();
        switch (accountingLink.getRelatedFlowEntityType()) {
            case "FlowProcessorEntity":
                if (!accountingLink.getAdditionalInfo().containsKey("sendFromFlowTerminationMessage") ||
                        !(boolean) accountingLink.getAdditionalInfo().get("sendFromFlowTerminationMessage")) {
                    return ResponseEntity.ok(response);
                }

                FlowProcessorEntity flow = Setup.getRepository(FlowProcessorEntityRepository.class)
                        .findOne(accountingLink.getRelatedFlowId());

                todo = flow.getContractPaymentConfirmationToDo();
                if (todo == null || confirmationService.isTodoExpired(todo) ||
                        (!Strings.isNullOrEmpty(todo.getPayTabsResponseMessage()) && todo.isShowOnERP())) {
                    return ResponseEntity.ok(response);
                }

                recurring = ContractPaymentConfirmationToDoService.isEligibleForTokenizationViaConfirmationToDO(todo);
                break;
            case "DirectDebitRejectionToDo":
            case "DirectDebit":
                Map<String, Object> m = new HashMap<>();
                m.put("relatedFlowId", accountingLink.getRelatedFlowId());
                m.put("relatedFlowEntityType", accountingLink.getRelatedFlowEntityType());

                todo = Setup.getApplicationContext()
                        .getBean(DirectDebitRejectionFlowService.class)
                        .getContractPaymentConfirmationToDoIfExistOrCreate(cpt, m);
                break;
        }

        if (todo == null) {
            return ResponseEntity.ok(response);
        }

        parameters.put("total_amount", String.valueOf(todo.getTotalAmount().intValue()));
        templateCode = CcAppCmsTemplate.FLOW_PROCESSOR_ENTITY_TERMINATION_MESSAGE_DETAILS.toString();
        response.put("text", TemplateUtil.compileTemplate(templateCode, todo.getContractPaymentTerm().getContract(), parameters));

        response.put("paymentsInfo", todo.getContractPaymentList().size() > 1 ?
                // Multiple payments
                confirmationService.getPaymentsDetails(todo) :
                // One payment
                "");

        String payTabsLink = confirmationService.getPayingViaCreditCardLink(todo, false, recurring);
        response.put("payTabsLink", payTabsLink);
        response.put("paid", todo.isShowOnERP());

        Setup.getApplicationContext()
                .getBean(AccountingAPILogService.class)
                .loggingFlowTerminationMessageInfoByClientApi(accountingLink, response);
        return ResponseEntity.ok(response);
    }

    @PreAuthorize("hasPermission('contract-payment-confirmation','checkForExistingTokenizedPayments')")
    @PostMapping(value = "/checkForExistingTokenizedPayments")
    public ResponseEntity<?> checkForExistingTokenizedPayments(@RequestBody ContractPaymentConfirmationToDo todo) {

        return ResponseEntity.ok(confirmationService.checkForExistingTokenizedPayments(todo));
    }
}