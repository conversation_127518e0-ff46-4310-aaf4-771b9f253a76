package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.helper.Aggregate;
import com.magnamedia.core.helper.AggregateQuery;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.BankStatementFile;
import com.magnamedia.entity.BankStatementTransaction;
import com.magnamedia.module.type.BankTransactionStatus;
import com.magnamedia.module.type.BankTransactionType;
import com.magnamedia.repository.BankStatementTransactionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.logging.Level;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At Apr 18, 2020
 **/


@RestController
@RequestMapping("/bankStatementTransaction")
public class BankStatementTransactionController extends BaseRepositoryController<BankStatementTransaction> {

    @Autowired
    private BankStatementTransactionRepository bankStatementTransactionRepository;


    @Override
    public BaseRepository<BankStatementTransaction> getRepository() {
        return bankStatementTransactionRepository;
    }



}
