package com.magnamedia.controller;

import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.projection.ClientContractsProjection;
import com.magnamedia.entity.projection.ContractProjection;
import com.magnamedia.repository.ClientRepository;
import com.magnamedia.repository.ContractRepository;

import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Sep 11, 2019
 * Jirra Acc-837
 */
@RequestMapping("/clients")
@RestController
public class ClientController extends BaseRepositoryController<Client> {

    @Autowired
    private ClientRepository clientRepository;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private ProjectionFactory projectionFactory;

    @Override
    public BaseRepository<Client> getRepository() {
        return clientRepository;
    }

    //Jirra ACC-1087
    @PreAuthorize("hasPermission('clients','clientcontracts')")
    @RequestMapping(value = "/clientcontracts/{id}",
            method = RequestMethod.GET)
    public ResponseEntity<?> getClientContracts(
            @PathVariable("id") Client client) {

        return new ResponseEntity<>(
                contractRepository.findByClientOrderByCreationDateDesc(client)
                        .stream().map(x -> projectionFactory.createProjection(ContractProjection.class, x))
                        .collect(Collectors.toList()),
                HttpStatus.OK);
    }

    //Jirra ACC-2307
    @PreAuthorize("hasPermission('clients','clientcontractsWithStatus')")
    @RequestMapping(value = "/clientcontractsWithStatus/{id}",
            method = RequestMethod.GET)
    public ResponseEntity<?> clientcontractsWithStatus(
            @PathVariable("id") Client client) {

        return new ResponseEntity<>(
                contractRepository.findByClientOrderByCreationDateDesc(client)
                        .stream().map(x -> projectionFactory.createProjection(ClientContractsProjection.class, x)).collect(Collectors.toList()),
                HttpStatus.OK);
    }

    //Jirra ACC-2154
//    @PreAuthorize("hasPermission('clients','clientsByName')")
    @NoPermission
    @RequestMapping(value = "/clients/name",
            method = RequestMethod.GET)
    public ResponseEntity<?> getClientsByName(
            @RequestParam(name = "search", required = false) String search, Pageable pageable) {

        return new ResponseEntity<>(
                clientRepository.findByNameContaining(search, pageable),
                HttpStatus.OK);
    }

    //Jirra ACC-1087
    @PreAuthorize("hasPermission('clients','clientsbymobile')")
    @RequestMapping(value = "/clientsbymobile/{mobile}",
            method = RequestMethod.GET)
    public ResponseEntity<?> getClientsByMobileNumber(
            @PathVariable("mobile") String mobile) {

        return new ResponseEntity<>(
                clientRepository.findByMobileNumber(mobile),
                HttpStatus.OK);
    }
    
    @PreAuthorize("hasPermission('clients','clientinfo')")
    @RequestMapping(value = "/clientinfo/{id}",
            method = RequestMethod.GET)
    public ResponseEntity<?> getClientInfo(
            @PathVariable("id") Contract c) {

        Client client = c.getClient();
        if (client == null)
            throw new RuntimeException("No Client is found!");
        client.setContractPaymentTermInfo(c);
        return new ResponseEntity<>(client, HttpStatus.OK);
    }
}
