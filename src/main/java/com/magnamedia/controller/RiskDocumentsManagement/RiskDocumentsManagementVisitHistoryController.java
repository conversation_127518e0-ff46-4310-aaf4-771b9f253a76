package com.magnamedia.controller.RiskDocumentsManagement;


import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagementVisitHistory;
import com.magnamedia.repository.RiskDocumentsManagement.RiskDocumentsManagementVisitHistoryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/riskDocumentsManagementVisitHistory")
public class RiskDocumentsManagementVisitHistoryController extends BaseRepositoryController<RiskDocumentsManagementVisitHistory> {

    @Autowired
    private RiskDocumentsManagementVisitHistoryRepository  riskDocumentsManagementVisitHistoryRepository;

    @Override
    public BaseRepository<RiskDocumentsManagementVisitHistory> getRepository() {
        return riskDocumentsManagementVisitHistoryRepository;
    }
}