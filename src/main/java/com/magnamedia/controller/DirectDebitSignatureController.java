package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Shortener;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.type.CoreParameter;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.DirectDebitSignature;
import com.magnamedia.entity.DirectDebitSignatureApprovalLink;
import com.magnamedia.repository.DirectDebitSignatureApprovalLinkRepository;
import com.magnamedia.repository.DirectDebitSignatureRepository;
import com.magnamedia.service.DirectDebitSignatureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;

@RestController
@RequestMapping("/directDebitSignature")
public class DirectDebitSignatureController extends BaseRepositoryController<DirectDebitSignature> {

    @Autowired
    private DirectDebitSignatureRepository directDebitSignatureRepository;
    @Autowired
    private DirectDebitSignatureService directDebitSignatureService;
    @Autowired
    private Shortener shortener;
    @Autowired
    private DirectDebitSignatureApprovalLinkRepository directDebitSignatureApprovalLinkRepository;

    @Override
    public BaseRepository<DirectDebitSignature> getRepository() {
        return directDebitSignatureRepository;
    }

    // ACC-6207
    @PreAuthorize("hasPermission('directDebitSignature', 'generateSignatureApprovalLink')")
    @GetMapping(value = "/generateSignatureApprovalLink/{id}")
    public ResponseEntity<?> generateSignatureApprovalLink(@PathVariable("id") Contract contract) {

        SelectQuery<DirectDebitSignatureApprovalLink> q = new SelectQuery<>(DirectDebitSignatureApprovalLink.class);
        q.filterBy("contract", "=", contract);
        q.filterBy("expired", "=", false);
        q.sortBy("creationDate", false, true);
        q.setLimit(1);
        List<DirectDebitSignatureApprovalLink> l = q.execute();

        if (!l.isEmpty()) return ResponseEntity.ok(l.get(0).getLink());

        DirectDebitSignatureApprovalLink approvalLink = new DirectDebitSignatureApprovalLink();
        approvalLink.setContract(contract);
        directDebitSignatureApprovalLinkRepository.save(approvalLink);

        approvalLink = directDebitSignatureApprovalLinkRepository.findOne(approvalLink.getId());
        approvalLink.setLink(shortener.shorten(Setup.getCoreParameter(CoreParameter.PUBLIC_LINK_BASE)
                + "/modules/accounting/signature-approval-link/#!/?uid=" + approvalLink.getUuid()));
        directDebitSignatureApprovalLinkRepository.save(approvalLink);

        return ResponseEntity.ok(approvalLink.getLink());
    }

    // ACC-6207
    @NoPermission
    @GetMapping(value = "/isGenerateSignatureApprovalLinkExpired")
    public ResponseEntity<?> isGenerateSignatureApprovalLinkExpired(String uuid) {

        DirectDebitSignatureApprovalLink link = directDebitSignatureApprovalLinkRepository.findByUuid(uuid);

        if(link == null) return notFoundResponse();

        return new ResponseEntity<>(new HashMap<String, Object>() {{
            put("expired", link.isExpired());
            put("contractUuid", link.getContract().getUuid());
        }}, HttpStatus.OK);
    }
}
