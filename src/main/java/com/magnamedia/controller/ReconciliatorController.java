package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseController;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.Position;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.projection.IdLabel;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.core.repository.PositionRepository;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.entity.Bucket;
import com.magnamedia.entity.CashBox;
import com.magnamedia.entity.CooQuestion;
import com.magnamedia.entity.Transaction;
import com.magnamedia.entity.dto.ReconciliatorDto;
import com.magnamedia.entity.projection.ExpensePaymentNightReviewProjection;
import com.magnamedia.entity.projection.IdLabelNameCodeProjection;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.extra.PaymentWithOriginal;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.BucketType;
import com.magnamedia.module.type.ExpensePaymentMethod;
import com.magnamedia.module.type.ExpensePaymentStatus;
import com.magnamedia.repository.BucketRepository;
import com.magnamedia.repository.CashBoxRepository;
import com.magnamedia.repository.ExpensePaymentRepository;
import com.magnamedia.repository.ExpenseRepository;
import com.magnamedia.service.AccountBalanceService;
import com.magnamedia.workflow.type.AttachmentTag;
import com.magnamedia.workflow.type.ExpensePaymentToDoType;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Mohammad Nosairat (Jan 27, 2021)
 */
@RequestMapping("/reconciliator")
@RestController
@Transactional
public class ReconciliatorController extends BaseController {

    @Autowired
    CashBoxRepository cashBoxRepository;
    @Autowired
    ExpensePaymentRepository expensePaymentRepository;
    @Autowired
    AttachementRepository attachementRepository;
    @Autowired
    BucketRepository bucketRepository;
    @Autowired
    AccountBalanceService accountBalanceService;
    @Autowired
    private ReconciliatorController selfCtrl;

    @Autowired
    private TransactionsController transactionsController;

    @NoPermission
    @RequestMapping("/get-today-cash-box")
    public ResponseEntity<?> getTodayCashBox() {
        List<CashBox> list = new ArrayList<>();
        List<Bucket> buckets = bucketRepository.findByBucketType(BucketType.CASH_BOX);

        for (Bucket bucket : buckets) {
            if(bucket.getBalance() == null || !bucket.getIsActive() || bucket.getBalance().equals((double) 0))
                continue;

            CashBox cashBox = cashBoxRepository.findTop1ByBucketOrderByLastCloseDateDesc(bucket);

            if (cashBox == null) {
                cashBox = new CashBox();
                cashBox.setUser(bucket.getHolder());
                cashBox.setBucketBalance(bucket.getBalance());
                list.add(cashBox);
                continue;
            }
            cashBox.setBucketBalance(bucket.getBalance());
            list.add(cashBox);
        }

        List<CashBox> cashBoxes = list.stream().filter(t -> t.getLastCloseDate() != null)
                .sorted(Comparator.comparing(CashBox::getLastCloseDate).reversed())
                .collect(Collectors.toList());
        cashBoxes.addAll(list.stream()
                .filter(t -> t.getLastCloseDate() == null)
                .collect(Collectors.toList()));

        return ResponseEntity.ok(cashBoxes);
    }

    @NoPermission
    @RequestMapping("/get-complete-expenses-pending-confirmation")
    public ResponseEntity<?> getCompleteExpensesPendingConfirmation(@RequestParam(name = "filterBy", required = false) String filterBy) {
        List<ReconciliatorDto> list = expensePaymentRepository
                .findByCompletedFalseAndStoppedFalseAndTaskNameIgnoreCaseContaining(
                        ExpensePaymentToDoType.TO_DO_IN_RECONCILIATOR_CONFIRMATION_SCREEN.toString())
                .stream().filter(t -> {
                    if (filterBy == null)
                        return (t.getMethod().equals(ExpensePaymentMethod.CASH)
                                || t.getMethod().equals(ExpensePaymentMethod.SALARY));
                    else if (filterBy.equals("cash")) {
                        return (t.getMethod().equals(ExpensePaymentMethod.CASH));
                    } else if (filterBy.equals("salary")) {
                        return (t.getMethod().equals(ExpensePaymentMethod.SALARY));
                    }
                    throw new RuntimeException("filter " + filterBy + "is not recognized");
                }).filter(t -> t.getStatus().equals(ExpensePaymentStatus.PAID)
                        && (t.getConfirmed() == null || t.getConfirmed().equals(Boolean.FALSE)))
                .map(e -> new ReconciliatorDto("Confirm", e)).collect(Collectors.toList());

        return ResponseEntity.ok(list);
    }

    @NoPermission
    @RequestMapping("/get-expenses-pending-invoices")
    public ResponseEntity<?> getExpensesPendingInvoices() {
        List<ReconciliatorDto> expensePayments = expensePaymentRepository
                .findByCompletedFalseAndStoppedFalseAndTaskNameIgnoreCaseContaining(
                        ExpensePaymentToDoType.TO_DO_IN_PENDING_INVOICE_SCREEN.toString()).stream()
                .filter(ep -> (ep.getMethod().equals(ExpensePaymentMethod.CASH) || ep.getMethod().equals(ExpensePaymentMethod.SALARY))
                        && ep.getStatus().equals(ExpensePaymentStatus.PAID_PENDING_INVOICE)
                        && (ep.getConfirmed() == null || ep.getConfirmed().equals(Boolean.FALSE)))
                .map(e -> new ReconciliatorDto("Collect invoice", e))
                .collect(Collectors.toList());

        return ResponseEntity.ok(expensePayments);
    }

    @NoPermission
    @RequestMapping("/select-as-invoice/{expensePaymentId}/{attachmentId}")
    public ResponseEntity<?> selectAsInvoice(@PathVariable("expensePaymentId") ExpensePayment expensePayment,
                                             @PathVariable("attachmentId") Attachment attachment) {
        Attachment paymentInvoice = Storage.cloneTemporary(
                attachment, AttachmentTag.EXPENSE_PAYMENT_INVOICE.toString());
        expensePayment.addAttachment(paymentInvoice);
        expensePaymentRepository.save(expensePayment);
        addAttachmentToAllTransaction(expensePayment, attachment);
        return ResponseEntity.ok().build();
    }

    @NoPermission
    @RequestMapping("/add-invoice/{expensePaymentId}/{attachmentId}")
    @Transactional
    public ResponseEntity<?> addInvoice(@PathVariable("expensePaymentId") ExpensePayment expensePayment,
                                        @PathVariable("attachmentId") Attachment attachment) {

        expensePayment.addAttachment(attachment);
        expensePaymentRepository.save(expensePayment);

        addAttachmentToAllTransaction(expensePayment, attachment);

        return ResponseEntity.ok().build();
    }
    private void addAttachmentToAllTransaction(ExpensePayment expensePayment, Attachment attachment) {

        List<Transaction> transactions = expensePayment.getTransactions();
        for (Transaction trans : transactions){
            if (attachment.getTag().equals(AttachmentTag.EXPENSE_PAYMENT_VAT_INVOICE.toString())) {
                Attachment transactionVatInvoice = Storage.cloneTemporary(attachment, AttachmentTag.VAT_TRANSACTION_INVOICE.toString());
                List<Attachment> atts = trans.getAttachments();
                atts.add(transactionVatInvoice);
                trans.setAttachments(atts);
            } else {
                List<Attachment> atts = trans.getAttachments();
                atts.add(attachment);
                trans.setAttachments(atts);
            }
            trans.checkChangeStatusPermission();
            transactionsController.updateEntity(trans);
        }
    }

    @PreAuthorize("hasPermission('reconciliator','delete-invoice')")
    @RequestMapping("/delete-invoice/{attachmentId}")
    @Transactional
    public ResponseEntity<?> deleteInvoice(@PathVariable("attachmentId") Attachment attachment) {
        if (attachment.getTag().equals(AttachmentTag.EXPENSE_PAYMENT_VAT_INVOICE.toString())) {
            removeVatInvoiceFromTransaction(attachment);
        }else{
            removeInvoiceFromTransaction(attachment);
        }
        attachementRepository.delete(attachment);
        return ResponseEntity.ok().build();
    }

    private void removeInvoiceFromTransaction(Attachment attachment) {
        ExpensePayment expensePayment = expensePaymentRepository.findOne(attachment.getOwnerId());
        List<Transaction> transactions = expensePayment.getTransactions();
        if (transactions == null || transactions.isEmpty()) return;
        for (Transaction transaction : transactions){
            Attachment invoiceAttachment = transaction.getAttachment(AttachmentTag.EXPENSE_PAYMENT_INVOICE.toString());
            if(invoiceAttachment != null)
                attachementRepository.delete(invoiceAttachment);
        }
    }

    private void removeVatInvoiceFromTransaction(Attachment expensePaymentVatAttachment) {
        ExpensePayment expensePayment = expensePaymentRepository.findOne(expensePaymentVatAttachment.getOwnerId());
        List<Transaction> transactions = expensePayment.getTransactions();
        if (transactions == null || transactions.isEmpty()) return;
        for (Transaction transaction : transactions){
            Attachment vatAttachment = transaction.getAttachment(AttachmentTag.VAT_TRANSACTION_INVOICE.toString());
            if(vatAttachment != null)
                attachementRepository.delete(vatAttachment);
        }
    }

    @NoPermission
    @RequestMapping("/select-as-vat-invoice/{expensePaymentId}/{attachmentId}")
    public ResponseEntity<?> selectAsVatInvoice(@PathVariable("expensePaymentId") ExpensePayment expensePayment,
                                                @PathVariable("attachmentId") Attachment attachment) {
        Attachment paymentVatInvoice = Storage.cloneTemporary(
                attachment, AttachmentTag.EXPENSE_PAYMENT_VAT_INVOICE.toString());
        expensePayment.addAttachment(paymentVatInvoice);
        expensePaymentRepository.save(expensePayment);
        addAttachmentToAllTransaction(expensePayment, paymentVatInvoice);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasPermission('reconciliator','getDoneByReconciliator')")
    @GetMapping("/getDoneByReconciliator")
    public ResponseEntity<?> getDoneByReconciliatortest(@RequestParam("date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        Date dayStart = DateUtil.getDayEnd(DateUtil.addDays(date, -1));
        Date dayEnd = DateUtil.getDayStart(DateUtil.addDays(date, 1));

        List<ExpensePaymentRepository.IdAndRevision> execute = expensePaymentRepository.findConfirmedByReconciliatorBetweenDates(dayStart, dayEnd);

        List<Map> withOriginals = new ArrayList<>();
        for (ExpensePaymentRepository.IdAndRevision obj : execute) {
            withOriginals.add(projectAuditManagerReports(obj));
        }

        return new ResponseEntity(withOriginals, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('reconciliator','missingInvoiceDismissed')")
    @GetMapping("/missingInvoiceDismissed")
    public ResponseEntity<?> missingInvoiceDismissed(@RequestParam("date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        Position position = Setup.getRepository(PositionRepository.class).findByCode(AccountingModule.EXPENSE_RECONCILITOR_USER_POSITION);
        List<User> users = Setup.getRepository(UserRepository.class).findActiveBy(position);
        List<Long> userIds = users.stream().map(user -> user.getId()).collect(Collectors.toList());
        Date dayStart = DateUtil.getDayEnd(DateUtil.addDays(date, -1));
        Date dayEnd = DateUtil.getDayStart(DateUtil.addDays(date, 1));

        List<ExpensePaymentRepository.IdAndRevision> execute = expensePaymentRepository.findmissingInvoiceDismissedByUsersBetweenDates(userIds, dayStart, dayEnd, ExpensePaymentStatus.PAID_PENDING_INVOICE.toString());
        List<Map> result = new ArrayList();
        for (ExpensePaymentRepository.IdAndRevision obj : execute) {
            result.add(projectAuditManagerReports(obj));
        }

        return new ResponseEntity(result, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('reconciliator','missingTaxInvoice')")
    @GetMapping("/missingTaxInvoice")
    public ResponseEntity<?> missingTaxInvoice(@RequestParam("date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        Position position = Setup.getRepository(PositionRepository.class).findByCode(AccountingModule.EXPENSE_RECONCILITOR_USER_POSITION);
        List<User> users = Setup.getRepository(UserRepository.class).findActiveBy(position);
        List<Long> userIds = users.stream().map(user -> user.getId()).collect(Collectors.toList());
        Date dayStart = DateUtil.getDayEnd(DateUtil.addDays(date, -1));
        Date dayEnd = DateUtil.getDayStart(DateUtil.addDays(date, 1));

        List<ExpensePaymentRepository.IdAndRevision> execute = expensePaymentRepository.findmissingTaxInvoiceByUsersBetweenDates(userIds, dayStart, dayEnd);
        List<Map> result = new ArrayList();
        for (ExpensePaymentRepository.IdAndRevision obj : execute) {
            result.add(projectAuditManagerReports(obj));
        }

        return new ResponseEntity(result, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('reconciliator','get-reconciliator-actions/questioned-expenses')")
    @RequestMapping("/get-reconciliator-actions/questioned-expenses")
    public ResponseEntity getQuestionedReconciliatorActionsAPI(Pageable pageable) {
        Date toDate = DateUtil.getDateAtHour(new Date(), Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_COO_NIGHT_REVIEW_TIME_HOUR)));
        Date fromDate = new DateTime(toDate).minusDays(14).toDate();

        Page<Map> result = expensePaymentRepository.getQuestionedReconciliatorActions(fromDate, toDate, pageable)
                .map(expensePaymentRev -> {
                    ExpensePayment editedPayment = expensePaymentRepository.findOne(expensePaymentRev.getId());
                    PaymentWithOriginal originalPayment = expensePaymentRepository.findLastEditedByIdAndRevision(expensePaymentRev.getId(), expensePaymentRev.getRevision());

                    editedPayment.setCooQuestionedPage(CooQuestion.QuestionedPage.NIGHT_REVIEW);

                    Map item = new HashMap();
                    item.put("originalPayment", originalPayment);
                    item.put("editedPayment", project(editedPayment, ExpensePaymentNightReviewProjection.class));

                    return item;

                });

        return ResponseEntity.ok(result);
    }


    @PreAuthorize("hasPermission('reconciliator','get-reconciliator-actions')")
    @RequestMapping("/get-reconciliator-actions")
    public ResponseEntity getReconciliatorActionsAPI(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
                                                     @RequestParam boolean archive,
                                                     Pageable pageable) {
        Page result = getReconciliatorActions(date, archive, pageable);
        return ResponseEntity.ok(result);
    }

    public Page<Map> getReconciliatorActions(Date date, boolean archive, Pageable pageable) {
        Date toDate = DateUtil.getDateAtHour(date, Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_COO_NIGHT_REVIEW_TIME_HOUR)));
        Date fromDate = new DateTime(toDate).minusDays(1).toDate();

        Page<Map> result = expensePaymentRepository.getReconciliatorActions(archive, fromDate, toDate, pageable)
                .map(expensePaymentRev -> {
                    ExpensePayment editedPayment = expensePaymentRepository.findOne(expensePaymentRev.getId());
                    PaymentWithOriginal originalPayment = expensePaymentRepository.findLastEditedByIdAndRevision(expensePaymentRev.getId(), expensePaymentRev.getRevision());

                    editedPayment.setCooQuestionedPage(CooQuestion.QuestionedPage.NIGHT_REVIEW);

                    Map item = new HashMap();
                    item.put("originalPayment", originalPayment);
                    item.put("editedPayment", project(editedPayment, ExpensePaymentNightReviewProjection.class));

                    return item;
                });

        return result;
    }

    @PreAuthorize("hasPermission('reconciliator','mark-as-done-by-coo')")
    @RequestMapping(value = "/mark-as-done-by-coo", method = RequestMethod.POST)
    public ResponseEntity markAsDoneByCooAPI(@RequestBody Long id) {

        selfCtrl.markAsDoneByCoo(id);

        return okResponse();
    }

    @PreAuthorize("hasPermission('reconciliator','mark-all-as-done-by-coo')")
    @RequestMapping(value = "/mark-all-as-done-by-coo", method = RequestMethod.POST)
    public ResponseEntity markAllAsDoneByCooAPI(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
                                                @RequestParam boolean archive) {
        List<Long> ids = getReconciliatorActions(date, archive, PageRequest.of(0, Integer.MAX_VALUE))
                .map(element -> ((ExpensePaymentNightReviewProjection) element.get("editedPayment")).getId()).getContent();

        markAsDoneByCoo(ids);

        return okResponse();
    }

    @PreAuthorize("hasPermission('reconciliator','mark-all-as-done-by-coo')")
    @RequestMapping(value = "/mark-list-as-done-by-coo", method = RequestMethod.POST)
    public ResponseEntity markListAsDoneByCooAPI(@RequestBody List<Long> ids) {
        markAsDoneByCoo(ids);

        return okResponse();
    }

    private void markAsDoneByCoo(List<Long> ids) {
        if (ids == null) return;

        for (Long id : ids) {
            selfCtrl.markAsDoneByCoo(id);
        }
    }

    @Transactional
    public void markAsDoneByCoo(Long id) {
        if (id == null) return;

        ExpensePayment expensePayment = expensePaymentRepository.findOne(id);
        expensePayment.setDoneByCoo(true);

        expensePaymentRepository.save(expensePayment);
    }

    private Map projectAuditManagerReports(ExpensePaymentRepository.IdAndRevision idAndRevision) {
        ExpensePayment payment = expensePaymentRepository.findOne(idAndRevision.getId());

        PaymentWithOriginal withOriginal = expensePaymentRepository.findLastEditedByIdAndRevision(idAndRevision.getId(), idAndRevision.getRevision());

        Map map = new HashMap();

        if (withOriginal != null) {
            map.put("transactionId", withOriginal.getTransactionId());

            map.put("expenseToPost", withOriginal.getExpenseToPostId() != null ? project(Setup.getRepository(ExpenseRepository.class).findOne(withOriginal.getExpenseToPostId()), IdLabel.class)
                    : null);
            map.put("fromBucket", withOriginal.getFromBucketId() != null ? project(Setup.getRepository(BucketRepository.class).findOne(withOriginal.getFromBucketId()), IdLabelNameCodeProjection.class)
                    : null);

            map.put("beneficiaryName", payment.getBeneficiaryName() != null ? payment.getBeneficiaryName() : "");

            map.put("paymentMethod", withOriginal.getPaymentMethod() != null ? withOriginal.getPaymentMethod().getLabel() : null);

            map.put("amount", withOriginal.getAmount() != null ? withOriginal.getAmount() : 0.0);

            map.put("vatAmount", withOriginal.getVatAmount() != null ? withOriginal.getVatAmount() : 0.0);

            map.put("description", withOriginal.getDescription() != null ? withOriginal.getDescription() : "");
        }

        map.put("newValue", payment);

        return map;
    }
}
