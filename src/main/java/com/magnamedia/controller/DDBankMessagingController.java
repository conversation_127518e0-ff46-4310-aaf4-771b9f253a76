package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.DDBankMessaging;
import com.magnamedia.entity.DDMessaging;
import com.magnamedia.repository.DDBankMessagingRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> <PERSON>ud
 *         Created on Oct 23, 2023
 *         ACC-6544
 */
@RequestMapping("/ddBankMessaging")
@RestController
public class DDBankMessagingController extends BaseRepositoryController<DDBankMessaging> {

    @Autowired
    private DDBankMessagingRepository ddBankMessagingRepository;
    @Autowired
    private ProjectionFactory projectionFactory;
    @Override
    public BaseRepository<DDBankMessaging> getRepository() {
        return ddBankMessagingRepository;
    }

    @PreAuthorize("hasPermission('DDBankMessagingController','getDdBankMessaging')")
    @GetMapping("/getDdBankMessaging/{id}")
    public ResponseEntity<?> getDdBankMessaging(@PathVariable("id") DDMessaging ddMessaging)  {

        SelectQuery<DDBankMessaging> q = new SelectQuery<>(DDBankMessaging.class);
        q.filterBy("ddMessaging", "=", ddMessaging);
        return new ResponseEntity<>(q.execute(), HttpStatus.OK);
    }
}