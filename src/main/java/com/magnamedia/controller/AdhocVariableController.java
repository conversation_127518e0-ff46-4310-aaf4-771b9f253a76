package com.magnamedia.controller;


import com.magnamedia.core.Setup;
import com.magnamedia.entity.AdhocVariableNode;
import com.magnamedia.repository.AdhocVariableBucketRepository;
import com.magnamedia.repository.AdhocVariableNodeRepository;
import com.magnamedia.repository.BasePLVariableBucketRepository;
import com.magnamedia.repository.BasePLVariableRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jul 20, 2020
 *         Jirra ACC-644
 */

@RequestMapping("/adhocvariables")
@RestController
public class AdhocVariableController extends BasePLVariableController<AdhocVariableNode> {

    @Autowired
    private AdhocVariableNodeRepository adhocVariableNodeRepository;

    @Override
    public BasePLVariableRepository<AdhocVariableNode> getRepository() {
        return adhocVariableNodeRepository;
    }

    @Override
    public BasePLVariableBucketRepository getBasePLVariableBucketRepository() {
        return Setup.getRepository(AdhocVariableBucketRepository.class);
    }
}
