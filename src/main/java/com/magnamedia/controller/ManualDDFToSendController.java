package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.entity.ManualDDFToSend;
import com.magnamedia.entity.Payment;
import com.magnamedia.module.type.ManualDDFToSendStatus;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.DirectDebitFileRepository;
import com.magnamedia.repository.ManualDDFToSendRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Masod <<EMAIL>>
 *         Created on Apr 13, 2020
 *         Jirra ACC-1598
 */
@RequestMapping("/manualddftosend")
@RestController
public class ManualDDFToSendController extends BaseRepositoryController<ManualDDFToSend> {

    @Autowired
    private ManualDDFToSendRepository manualDDFToSendRepository;
    @Autowired
    private DirectDebitFileController ddfController;
    @Autowired
    private DirectDebitFileRepository ddfRepository;
    @Autowired
    private PaymentController paymentController;
    @Autowired
    private ManualDDFToSendController selfController;

    @PreAuthorize("hasPermission('manualddftosend','notsent')")
    @RequestMapping(value = "/notsent", method = RequestMethod.GET)
    public ResponseEntity getNotSent(Pageable pageable) {
        SelectQuery<ManualDDFToSend> selectQuery = new SelectQuery(ManualDDFToSend.class);
        selectQuery.filterBy("status", "=", ManualDDFToSendStatus.NOT_SENT);

        return new ResponseEntity(selectQuery.execute(pageable), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('manualddftosend','confirmsenttobank')")
    @RequestMapping(value = "/confirmsenttobank/{id}", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity confirmSentToBank(
            @PathVariable("id") ManualDDFToSend manualDDFToSend) throws Exception {

        manualDDFToSend.setStatus(ManualDDFToSendStatus.SENT);
        manualDDFToSendRepository.save(manualDDFToSend);
        
        List<Long> ids = Arrays.stream(manualDDFToSend.getIds()
                .split(","))
                .map(x -> Long.parseLong(x))
                .collect(Collectors.toList());

        Set<Long> updatedPayments = new HashSet<>(Arrays.asList(-1L));
        List<String> pL = new ArrayList<>();
        List<String> dfL = new ArrayList<>();

        //Jirra ACC-1811 #11 ACC-5048
        for (Long id : ids) {
            DirectDebitFile ddf = ddfRepository.findOne(id);

            SelectQuery<Payment> bouncedPaymentQuery = new SelectQuery(Payment.class);
            bouncedPaymentQuery.filterBy("directDebitId", "=", ddf.getDirectDebit().getId());
            bouncedPaymentQuery.filterBy("status", "=", PaymentStatus.BOUNCED);
            bouncedPaymentQuery.filterBy("id", "not in", updatedPayments);

            for (Payment p : bouncedPaymentQuery.execute()) {
                if(p.getDirectDebitFileId() == ddf.getId()) {
                    updatedPayments.add(p.getId());
                }

                if(updatedPayments.contains(p.getId())) {
                    continue;
                }

                pL.add(p.getId().toString());
                dfL.add(ddf.getId().toString());

                if(pL.size() >= 5) {
                    Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                            .create(new BackgroundTask.builder(
                                    "updateDirectDebitFile_" + p.getId(),
                                    "accounting",
                                    "paymentService",
                                    "updateDirectDebitFile")
                                    .withRelatedEntity(manualDDFToSend.getEntityType(), manualDDFToSend.getId())
                                    .withParameters(
                                            new Class[]{List.class, List.class},
                                            new Object[]{pL, dfL})
                                    .withQueue(BackgroundTaskQueues.SequentialQueue)
                                    .build());

                    pL = new ArrayList<>();
                    dfL = new ArrayList<>();
                }

                updatedPayments.add(p.getId());
            }
        }

        if(!pL.isEmpty()) {
            Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                    .create(new BackgroundTask.builder(
                            "updateDirectDebitFile_" + pL.get(0),
                            "accounting",
                            "paymentService",
                            "updateDirectDebitFile")
                            .withRelatedEntity(manualDDFToSend.getEntityType(), manualDDFToSend.getId())
                            .withParameters(
                                    new Class[]{List.class, List.class},
                                    new Object[]{pL, dfL})
                            .withQueue(BackgroundTaskQueues.SequentialQueue)
                            .build());
        }
        
        return new ResponseEntity("Done", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('manualddftosend','confirmsenttobank')")
    @RequestMapping(value = "/confirmsenttobank-all", method = RequestMethod.POST)
    public ResponseEntity confirmSentToBankAll(@RequestBody List<Long> manualDDFToSendIds) throws Exception {
        Integer initialNumber = manualDDFToSendIds.size(), processedSuccessfully = 0;
        for (Long id : manualDDFToSendIds) {
            try {
                ManualDDFToSend manualDDFToSend = manualDDFToSendRepository.findOne(id);
                selfController.confirmSentToBank(manualDDFToSend);
                processedSuccessfully++;
            } catch (Exception e) {
                logger.log(Level.SEVERE, e.getMessage());
            }
        }
        return new ResponseEntity("Done, " + processedSuccessfully + " from " + initialNumber + " Confirmed", HttpStatus.OK);
    }

    @Override
    public BaseRepository<ManualDDFToSend> getRepository() {
        return manualDDFToSendRepository;
    }
}
