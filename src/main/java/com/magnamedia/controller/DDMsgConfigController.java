/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.DDMsgConfig;
import com.magnamedia.repository.DDMsgConfigRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Feb 29, 2020
 *         Jirra ACC-1435
 */

@RestController
@RequestMapping("/ddmsgconfig")
public class DDMsgConfigController extends BaseRepositoryController<DDMsgConfig> {

    @Autowired
    private DDMsgConfigRepository ddMsgConfigRepository;

    @Override
    public BaseRepository<DDMsgConfig> getRepository() {
       return ddMsgConfigRepository;
    }
}
