package com.magnamedia.controller.OnlineCardStatement;

import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.BackgroundTaskStatus;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.ContractPaymentConfirmationToDo;
import com.magnamedia.entity.OnlineCardStatement.OnlineCardStatementFile;
import com.magnamedia.entity.OnlineCardStatement.OnlineCardStatementRecord;
import com.magnamedia.extra.AccountingPage;
import com.magnamedia.repository.ContractPaymentConfirmationToDoRepository;
import com.magnamedia.repository.OnlineCardStatement.OnlineCardStatementFileRepository;
import com.magnamedia.repository.OnlineCardStatement.OnlineCardStatementRecordRepository;
import com.magnamedia.service.OnlineCardStatementService;
import com.magnamedia.service.QueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Mahfoud
 **/

// ACC-5587
@RestController
@RequestMapping("/onlineCardStatementRecord")
public class OnlineCardStatementRecordController extends BaseRepositoryController<OnlineCardStatementRecord> {

    @Autowired
    private OnlineCardStatementService onlineCardStatementService;
    @Autowired
    private OnlineCardStatementRecordRepository onlineCardStatementRecordRepository;
    @Autowired
    private ContractPaymentConfirmationToDoRepository contractPaymentConfirmationToDoRepository;

    @Override
    public BaseRepository<OnlineCardStatementRecord> getRepository() {
        return Setup.getRepository(OnlineCardStatementRecordRepository.class);
    }

    @PreAuthorize("hasPermission('onlineCardStatementRecord','viewResult')")
    @GetMapping(value = "/viewResult/{id}")
    public ResponseEntity<?> viewResult(
            @PathVariable("id") OnlineCardStatementFile onlineCardStatementFile,
            @RequestParam(name = "clientName", required = false) String clientName,
            @RequestParam(name = "contractId", required = false) Long contractId,
            @RequestParam(name = "paymentId", required = false) Long paymentId,
            @RequestParam(name = "paymentType", required = false) PicklistItem paymentType,
            @RequestParam(name = "authorizationCode", required = false) String authorizationCode,
            @RequestParam(name = "transferReference", required = false) String transferReference,
            Pageable pageable) {

        Map<String, Object> map = new HashMap<String, Object>() {{
            put("gridName", "MatchedOnlineCardRecord");
            put("clientName", clientName);
            put("contractId", contractId);
            put("paymentId", paymentId);
            put("paymentType", paymentType);
            put("authorizationCode", authorizationCode);
            put("transferReference", transferReference);
        }};

        AccountingPage matchedOnlineCardRecord =
                onlineCardStatementService.searchByFileStatementAndGridName(onlineCardStatementFile, pageable, map);

        map.put("gridName", "UnmatchedOnlineCardRecord");
        AccountingPage unmatchedOnlineCardRecord =
                onlineCardStatementService.searchByFileStatementAndGridName(onlineCardStatementFile, pageable, map);

        map.put("gridName", "UnmatchedInErpOnly");
        AccountingPage unmatchedRecordsInErpOnly =
                onlineCardStatementService.searchByFileStatementAndGridName(onlineCardStatementFile, pageable, map);

        map.put("gridName", "UnmatchedRefundOnlineCardRecord");
        AccountingPage unmatchedRefundOnlineCardRecord =
                onlineCardStatementService.searchByFileStatementAndGridName(onlineCardStatementFile, pageable, map);

        map.put("gridName", "MatchedClientRefunds");
        AccountingPage matchedClientRefund =
                onlineCardStatementService.searchByFileStatementAndGridName(onlineCardStatementFile, pageable, map);

        Map<String, Object> response = new HashMap<>();
        response.put("matchedOnlineCardRecord", matchedOnlineCardRecord);
        response.put("unmatchedOnlineCardRecord", unmatchedOnlineCardRecord);
        response.put("unmatchedRecordsInErpOnly", unmatchedRecordsInErpOnly);
        response.put("matchedClientRefunds", matchedClientRefund);
        response.put("unmatchedRefundOnlyInBank", unmatchedRefundOnlineCardRecord);

        response.put("totalTransaction", onlineCardStatementFile.getTotalTransactions());

        response.put("totalMatchPayment", matchedOnlineCardRecord.getTotalElements());

        response.put("totalUnMatchPaymentOnlineCardRecord", unmatchedOnlineCardRecord.getTotalElements());

        response.put("totalUnMatchPaymentInErpOnly", unmatchedRecordsInErpOnly.getTotalElements());

        response.put("totalMatchedClientRefunds", matchedClientRefund.getTotalElements());

        response.put("totalUnmatchedRefund", unmatchedRefundOnlineCardRecord.getTotalElements());

        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('onlineCardStatementRecord','searchByGrid')")
    @GetMapping(value = "/searchByGrid/{id}")
    public ResponseEntity<?> searchByGrid(
            @PathVariable("id") OnlineCardStatementFile onlineCardStatementFile,
            @RequestParam(name = "gridName") String gridName,
            @RequestParam(name = "clientName", required = false) String clientName,
            @RequestParam(name = "contractId", required = false) Long contractId,
            @RequestParam(name = "paymentId", required = false) Long paymentId,
            @RequestParam(name = "paymentType", required = false) PicklistItem paymentType,
            @RequestParam(name = "authorizationCode", required = false) String authorizationCode,
            Pageable pageable) {

        Map<String, Object> map = new HashMap<String, Object>() {{
            put("gridName", gridName);
            put("clientName", clientName);
            put("contractId", contractId);
            put("paymentId", paymentId);
            put("paymentType", paymentType);
            put("authorizationCode", authorizationCode);
        }};

        return new ResponseEntity<>(
                onlineCardStatementService.searchByFileStatementAndGridName(
                        onlineCardStatementFile, pageable, map), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('onlineCardStatementRecord','getTodosForMatchOnlineStatement')")
    @GetMapping(value = "/getTodosForMatchOnlineStatement/{id}")
    public ResponseEntity<?> getTodosForMatchOnlineStatement(
            @PathVariable("id") OnlineCardStatementRecord r,
            Pageable pageable) {

        r = onlineCardStatementRecordRepository.findOne(r.getId());
        return new ResponseEntity<>(
                Setup.getApplicationContext()
                        .getBean(QueryService.class)
                        .getTodosForMatchOnlineStatement(r.getAmount(), pageable),
                HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('onlineCardStatementRecord','getTodosForMatchOnlineStatementForRefundPayment')")
    @GetMapping(value = "/getTodosForMatchOnlineStatementForRefundPayment/{id}")
    public ResponseEntity<?> getTodosForMatchOnlineStatementForRefundPayment(
            @PathVariable("id") OnlineCardStatementRecord r,
            Pageable pageable) {

        r = onlineCardStatementRecordRepository.findOne(r.getId());
        return new ResponseEntity<>(
                contractPaymentConfirmationToDoRepository.getTodosForMatchOnlineStatementForRefundPayment(Math.abs(r.getAmount()), pageable),
                HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('onlineCardStatementRecord','confirmTodoManually')")
    @GetMapping(value = "/confirmTodoManually/{rId}/{todoId}")
    public ResponseEntity<?> confirmTodoManually(
            @PathVariable("rId") OnlineCardStatementRecord r,
            @PathVariable("todoId") ContractPaymentConfirmationToDo toDo,
            @RequestParam(name = "forceUpdateTransRef", required = false, defaultValue = "false") boolean forceUpdateTransRef) {

        toDo = contractPaymentConfirmationToDoRepository.findOne(toDo.getId());
        r = onlineCardStatementRecordRepository.findOne(r.getId());
        r.setMatchedManually(true);

        onlineCardStatementService.confirmTodo(r, toDo, forceUpdateTransRef);

        return new ResponseEntity<>("Done", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('onlineCardStatementRecord','generateCsv')")
    @GetMapping(value = "/generateCsv/{id}")
    public void generateCsv(
            @PathVariable("id") OnlineCardStatementFile f,
            @RequestParam(name = "gridName", required = false) String gridName,
            @RequestParam(name = "clientName", required = false) String clientName,
            @RequestParam(name = "contractId", required = false) Long contractId,
            @RequestParam(name = "paymentId", required = false) Long paymentId,
            @RequestParam(name = "paymentType", required = false) PicklistItem paymentType,
            @RequestParam(name = "authorizationCode", required = false) String authorizationCode,
            @RequestParam(name = "transferReference", required = false) String transferReference,
            @RequestParam(name = "exportAll", required = false, defaultValue = "false") boolean exportAll,
            HttpServletResponse response) throws IOException {

        Map<String, Object> map = new HashMap<String, Object>() {{
            put("clientName", clientName);
            put("contractId", contractId);
            put("paymentId", paymentId);
            put("paymentType", paymentType);
            put("authorizationCode", authorizationCode);
            put("transferReference", transferReference);
        }};

        onlineCardStatementService.generateExcelReport(
                response, f, gridName, exportAll, map);
    }
}
