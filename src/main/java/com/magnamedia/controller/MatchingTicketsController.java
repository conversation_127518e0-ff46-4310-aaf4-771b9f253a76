/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.CreditCardStatement;
import com.magnamedia.entity.Ticket;
import com.magnamedia.extra.*;
import com.magnamedia.module.type.OperationType;
import com.magnamedia.repository.*;
import com.opencsv.CSVReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */

@RequestMapping("/CreditCardStatement")
@RestController
public class MatchingTicketsController extends BaseRepositoryController<CreditCardStatement>{

    @Autowired
    private CreditCardStatementRepository statementRep;
    
    @Autowired
    private BucketRepository bucketRep;
    
    @Autowired
    private RevenueRepository revenueRep;
    
    @Autowired
    private ExpenseRepository expenseRep;
    
    @Autowired
    private TicketRepository ticketRep;
    
    @Autowired
    private TransactionsController transCont;
    
    
    @Override
    public BaseRepository<CreditCardStatement> getRepository() {
        return statementRep;
    }

    @PreAuthorize("hasPermission('CreditCardStatement','importStatements')")
    @RequestMapping(method = RequestMethod.GET,
            value = "/importStatements/{attid}")
    public ResponseEntity<?> getData(@PathVariable("attid") Attachment attachment){
        return new ResponseEntity<>(getDataFromCsv(attachment),HttpStatus.OK);
    }
    
    public  CreditCardStatementHelper getDataFromCsv(Attachment attachment){
        FileInputStream fis = null;
        Integer csvLines=0;
        CreditCardStatementHelper statementList = new CreditCardStatementHelper();
        List<StatementCand> lines=new ArrayList<StatementCand>();
        
        try {
            InputStream attachmentInputStream = Storage.getStream(attachment);
            CSVReader reader = new CSVReader(new InputStreamReader(attachmentInputStream));
            String[] nextLine;
            reader.readNext();
            
            while ((nextLine = reader.readNext()) != null) {
                if(nextLine[0]==null||nextLine[0]==""||nextLine[1]==""||nextLine[2]==""||nextLine[3]==""||
                        nextLine[1]==null||nextLine[2]==null||nextLine[3]==null||nextLine[4]==""||nextLine[4]==null)
                  throw new RuntimeException ("Please upload a valid file");  
                StatementCand newLine= new StatementCand(nextLine[0], nextLine[1], nextLine[2], nextLine[3],nextLine[4]);
                StatementCand tempLine=StatementsCSVParsingLibrary.parsLine(newLine);
                if(isDuplicated(tempLine))
                    tempLine.setIsDuplicated(Boolean.TRUE);
                else
                    tempLine.setIsDuplicated(Boolean.FALSE);
                System.out.println("Test");
               
                lines.add(tempLine);
                csvLines++;
            }
            
            statementList.setStatements(lines);
            statementList.setCsvLines(csvLines);
        } catch (FileNotFoundException ex) {
           throw new RuntimeException("An error occurred when importing row number: " + csvLines);
        } catch (IOException ex) {
            throw new RuntimeException("An error occurred when importing row number: " + csvLines);
        } finally {
            try {
                if (fis != null) {
                    fis.close();
                }
            } catch (IOException ex) {
                throw new RuntimeException(ex.getStackTrace().toString());
            }
        }
        
        return statementList;
    }

    @PreAuthorize("hasPermission('CreditCardStatement','insertNewRows')")
    @RequestMapping(value="/insertNewRows/{attid}",method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> insertNewRows(@PathVariable("attid") Attachment attachment){

        List<StatementCand> statements =getDataFromCsv(attachment).getStatements();
        List<CreditCardStatement> credits=new ArrayList<CreditCardStatement>();
        for(StatementCand temp:statements){
            if(temp.getIsValid()&&!isDuplicated(temp)){
                CreditCardStatement credit=new CreditCardStatement();
                if(temp.getBalance()!=null)
                    credit.setBalance(temp.getBalance());
                if(temp.getBookingConfirmationCode()!=null)
                    credit.setBookingConfirmationCode(temp.getBookingConfirmationCode());
                if(temp.getCompany()!=null)
                    credit.setCompany(temp.getCompany());
                if(temp.getCredit()!=null)
                    credit.setCredit(temp.getCredit());
                else
                    credit.setDebit(temp.getDebit());
                if(temp.getOperationDate()!=null)
                    credit.setOperationDate(temp.getOperationDate());
                if(temp.getOpertaionType()!=null)
                    credit.setOpertaionType(temp.getOpertaionType());
                if(temp.getTransactionDate()!=null)
                    credit.setTransactionDate(temp.getTransactionDate());
                if(temp.getDescription()!=null)
                    credit.setDescription(temp.getDescription());
                credits.add(credit);
            } 
        }
        System.out.println(credits.size());
        return new ResponseEntity<>(statementRep.save(credits),HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('CreditCardStatement','GetTicketsAndPurchaseCards')")
    @RequestMapping(value="/GetTicketsAndPurchaseCards",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<?> GetTicketsAndPurchaseCards(@RequestBody MatchedTicketSearchHelper search){
        
        MatchingAccountingHelper helper=new MatchingAccountingHelper();
        helper.buckets=bucketRep.findAll();
        helper.expenses=expenseRep.findAll();
        helper.revenues=revenueRep.findAll();   
        TicketMatchingLibrary.helper=helper;
        
        List<Ticket> tickets=getTickets(search);
        List<CreditCardStatement> statements=getStatements(search);
        List<TicketMatchingLibrary.MatchedTicket> unMatchedTickets=TicketMatchingLibrary.MatchTickets(tickets, statements);
        
        List<MatchingResultHelper> finalResult=new ArrayList<MatchingResultHelper>();
        
        List<Ticket> ticketsToCompare=new ArrayList<Ticket>();
        List<CreditCardStatement> statementsToCompare=new ArrayList<CreditCardStatement>();
        Double sumFare=0.0;
        int totalRecords=0;
        for(TicketMatchingLibrary.MatchedTicket temp:unMatchedTickets){
            sumFare+=temp.getTicket()==null?0:(temp.getTicket().getFareInRefCurrency()!=null?temp.getTicket().getFareInRefCurrency():0);
            totalRecords++;
            ticketsToCompare.add(temp.getTicket());
            statementsToCompare.add(temp.getStatement());
        }
        MatchingResultHelper result=new MatchingResultHelper();
        result.setFareSum(sumFare);
        result.setTotalRecords(totalRecords);
        result.setMatchedTickets(unMatchedTickets);
//        result.setAccountingLists(helper);
        finalResult.add(result);
        
        
        
        //Tickets
        Double sumFareTickets=0.0;
        int totalRecordsTickets=0;
        List<Ticket> tempTicketsList=tickets;
        for(Ticket temp:tickets){
            if(ticketsToCompare.contains(temp)){
                tempTicketsList.remove(temp);
                continue;
            }
            sumFareTickets+=temp.getFareInRefCurrency()!=null?temp.getFareInRefCurrency():0;
            totalRecordsTickets++;
        }
        MatchingResultHelper TicketsResult=new MatchingResultHelper();
        TicketsResult.setFareSum(sumFareTickets);
        TicketsResult.setTotalRecords(totalRecordsTickets);
        TicketsResult.setTickets(tempTicketsList);
        finalResult.add(TicketsResult);
        
        //Statements
        Double sumDebit=0.0;
        Double sumCredit=0.0;
        int totalRecordsStatements=0;
        List<CreditCardStatement> tempStatementList=new ArrayList<CreditCardStatement>();
        for(CreditCardStatement temp: statements){
            if(statementsToCompare.contains(temp)){
                tempStatementList.add(temp);
                continue;
            }
            sumDebit+=temp.getDebit()!=null?temp.getDebit():0;
            sumCredit+=temp.getCredit()!=null?temp.getCredit():0;
            totalRecordsStatements++;
        }
        statements.removeAll(tempStatementList);
        MatchingResultHelper resultStatements=new MatchingResultHelper();
        resultStatements.setCreditSum(sumCredit);
        resultStatements.setDebitSum(sumDebit);
        resultStatements.setTotalRecords(totalRecordsStatements);
        resultStatements.setStatements(statements);
        finalResult.add(resultStatements);
        
        return new ResponseEntity<>(finalResult,HttpStatus.OK);
    }
    
//    @RequestMapping(value="/getUnmatchedTickets",method = RequestMethod.POST)
//    @ResponseBody
//    public ResponseEntity<?> getUnmatchedTickets(@RequestBody MatchedTicketSearchHelper search){
//        List<Ticket> tickets=getTickets(search);
//        Double sumFare=0.0;
//        int totalRecords=0;
//        for(Ticket temp:tickets){
//            sumFare+=temp.getFareInRefCurrency()!=null?temp.getFareInRefCurrency():0;
//            totalRecords++;
//        }
//        MatchingResultHelper result=new MatchingResultHelper();
//        result.setFareSum(sumFare);
//        result.setTotalRecords(totalRecords);
//        result.setTickets(tickets);
//        return new ResponseEntity<>(result,HttpStatus.OK);
//    }
//    
//    @RequestMapping(value="/getUnmatchedStatements",method = RequestMethod.POST)
//    @ResponseBody
//    public ResponseEntity<?> getUnmatchedStatements(@RequestBody MatchedTicketSearchHelper search){
//        List<CreditCardStatement> statements=getStatements(search);
//        Double sumDebit=0.0;
//        Double sumCredit=0.0;
//        int totalRecords=0;
//        for(CreditCardStatement temp: statements){
//            sumDebit+=temp.getDebit()!=null?temp.getDebit():0;
//            sumCredit+=temp.getCredit()!=null?temp.getCredit():0;
//            totalRecords++;
//        }
//        MatchingResultHelper result=new MatchingResultHelper();
//        result.setCreditSum(sumCredit);
//        result.setDebitSum(sumDebit);
//        result.setTotalRecords(totalRecords);
//        result.setStatements(statements);
//        return new ResponseEntity<>(result,HttpStatus.OK);
//    }

    @PreAuthorize("hasPermission('CreditCardStatement','matchSingleTicket')")
    @RequestMapping(value="/matchSingleTicket",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<?> matchSingleTicket(@RequestBody TicketMatchingLibrary.MatchedTicket temp){
        CreditCardStatement statement=statementRep.findOne(temp.getStatement().getId());
        Ticket ticket=ticketRep.findOne(temp.getTicket().getId());
        if(statement.getOpertaionType()==OperationType.PURCHASE){
//            statement.setMatchToPurchaseDecision(CreditCardStatement.MatchingDecision.MATCHED);
            ticket.setMatchToPurchaseDecision(Ticket.MatchingDecision.MATCHED);
           
        }
        else{
//            statement.setMatchToRefundsDecision(CreditCardStatement.MatchingDecision.MATCHED);
            ticket.setMatchToRefundsDecision(Ticket.MatchingDecision.MATCHED);
        }
        statement.setMatchDecision(CreditCardStatement.MatchingDecision.MATCHED);
        
        ticketRep.save(ticket);
        
        transCont.createEntity(temp.getTrans());
        
        statement.setTransaction(temp.getTrans());
        statement.setMatchedTicket(ticket);
        statementRep.save(statement);
        
        
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('CreditCardStatement','matchMultipleTickets')")
    @RequestMapping(value="/matchMultipleTickets",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<?> matchMultipleTickets(@RequestBody List<TicketMatchingLibrary.MatchedTicket> tempList){
        List<Ticket> ticketsToUpdate=new ArrayList<Ticket>();
        List<CreditCardStatement> statementsToUpdate=new ArrayList<CreditCardStatement>();
        for(TicketMatchingLibrary.MatchedTicket temp:tempList){            
            CreditCardStatement statement=statementRep.findOne(temp.getStatement().getId());
            Ticket ticket=ticketRep.findOne(temp.getTicket().getId());
            if(statement.getOpertaionType()==OperationType.PURCHASE){
//                statement.setMatchToPurchaseDecision(CreditCardStatement.MatchingDecision.MATCHED);
                ticket.setMatchToPurchaseDecision(Ticket.MatchingDecision.MATCHED);

            }
            else{
//                statement.setMatchToRefundsDecision(CreditCardStatement.MatchingDecision.MATCHED);
                ticket.setMatchToRefundsDecision(Ticket.MatchingDecision.MATCHED);
            }

            statement.setMatchDecision(CreditCardStatement.MatchingDecision.MATCHED);
            ticketsToUpdate.add(ticket);
            transCont.createEntity(temp.getTrans());

            statement.setTransaction(temp.getTrans());
            statement.setMatchedTicket(ticket);
            statementsToUpdate.add(statement);
        
        }
        ticketRep.save(ticketsToUpdate);
        statementRep.save(statementsToUpdate);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('CreditCardStatement','dismissPurchase')")
    @RequestMapping(value="/dismissPurchase",method = RequestMethod.GET)
    public ResponseEntity<?> dismissPurchase(@RequestParam (value="ticketId",required = false) Long ticketId,
            @RequestParam (value="statementId",required = false) Long statementId){
        Ticket ticket;
        CreditCardStatement statement;
        if(ticketId!=null){
            ticket=ticketRep.findOne(ticketId);
            ticket.setMatchToPurchaseDecision(Ticket.MatchingDecision.DISMISSED);
            return new ResponseEntity<>(ticketRep.save(ticket),HttpStatus.OK); 
        }
        if(statementId!=null){
            statement=statementRep.findOne(statementId);
//            statement.setMatchToPurchaseDecision(CreditCardStatement.MatchingDecision.DISMISSED);
            statement.setMatchDecision(CreditCardStatement.MatchingDecision.DISMISSED);
            return new ResponseEntity<>(statementRep.save(statement),HttpStatus.OK); 
        }
        
        return  new ResponseEntity<>("Please check the passed Id",HttpStatus.OK);
        
    }

    @PreAuthorize("hasPermission('CreditCardStatement','dismissPurchaseMultiple')")
    @RequestMapping(value="/dismissPurchaseMultiple",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<?> dismissPurchaseMultiple(@RequestBody MatchingResultHelper helper){
        if(helper.getTickets()!=null&&helper.getTickets().size()>0){
            List<Ticket> ticketsToUpdate=new ArrayList<Ticket>();
            for(Ticket temp:helper.getTickets()){
                Ticket ticket=ticketRep.findOne(temp.getId());
                ticket.setMatchToPurchaseDecision(Ticket.MatchingDecision.DISMISSED);
                ticketsToUpdate.add(ticket);
            }
            return new ResponseEntity<>(ticketRep.save(ticketsToUpdate),HttpStatus.OK);   
        }
        if(helper.getStatements()!=null&&helper.getStatements().size()>0){
            List<CreditCardStatement> statementsToUpdate=new ArrayList<CreditCardStatement>();
            for(CreditCardStatement temp:helper.getStatements()){
                CreditCardStatement statement=statementRep.findOne(temp.getId());
//                statement.setMatchToPurchaseDecision(CreditCardStatement.MatchingDecision.DISMISSED);
                statement.setMatchDecision(CreditCardStatement.MatchingDecision.DISMISSED);
                statementsToUpdate.add(statement);    
            }
            return new ResponseEntity<>(statementRep.save(statementsToUpdate),HttpStatus.OK);
        }
        return  new ResponseEntity<>("Please check the passed data",HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('CreditCardStatement','dismissRefunds')")
    @RequestMapping(value="/dismissRefunds",method = RequestMethod.GET)
    public ResponseEntity<?> dismissRefunds(@RequestParam (value="ticketId",required = false) Long ticketId,
            @RequestParam (value="statementId",required = false) Long statementId){
        Ticket ticket;
        CreditCardStatement statement;
        if(ticketId!=null){
            ticket=ticketRep.findOne(ticketId);
            ticket.setMatchToRefundsDecision(Ticket.MatchingDecision.DISMISSED);
            return new ResponseEntity<>(ticketRep.save(ticket),HttpStatus.OK); 
        }
        if(statementId!=null){
            statement=statementRep.findOne(statementId);
//            statement.setMatchToRefundsDecision(CreditCardStatement.MatchingDecision.DISMISSED);
            statement.setMatchDecision(CreditCardStatement.MatchingDecision.DISMISSED);
            return new ResponseEntity<>(statementRep.save(statement),HttpStatus.OK); 
        }
        
        return  new ResponseEntity<>("Please check the passed Id",HttpStatus.OK);
        
    }

    @PreAuthorize("hasPermission('CreditCardStatement','dismissRefundsMultiple')")
    @RequestMapping(value="/dismissRefundsMultiple",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<?> dismissRefundsMultiple(@RequestBody MatchingResultHelper helper){
        if(helper.getTickets()!=null&&helper.getTickets().size()>0){
            List<Ticket> ticketsToUpdate=new ArrayList<Ticket>();
            for(Ticket temp:helper.getTickets()){
                Ticket ticket=ticketRep.findOne(temp.getId());
                ticket.setMatchToRefundsDecision(Ticket.MatchingDecision.DISMISSED);
                ticketsToUpdate.add(ticket);
            }
            return new ResponseEntity<>(ticketRep.save(ticketsToUpdate),HttpStatus.OK);   
        }
        if(helper.getStatements()!=null&&helper.getStatements().size()>0){
            List<CreditCardStatement> statementsToUpdate=new ArrayList<CreditCardStatement>();
            for(CreditCardStatement temp:helper.getStatements()){
                CreditCardStatement statement=statementRep.findOne(temp.getId());
//                statement.setMatchToRefundsDecision(CreditCardStatement.MatchingDecision.DISMISSED);
                statement.setMatchDecision(CreditCardStatement.MatchingDecision.DISMISSED);
                statementsToUpdate.add(statement);    
            }
            return new ResponseEntity<>(statementRep.save(statementsToUpdate),HttpStatus.OK);
        }
        return  new ResponseEntity<>("Please check the passed data",HttpStatus.OK);
    }
    
    public Boolean isDuplicated(StatementCand statement){
        List<CreditCardStatement> statements=new ArrayList<CreditCardStatement>();
        SelectQuery<CreditCardStatement> query=new SelectQuery<>(CreditCardStatement.class);
        query.filterBy("transactionDate", "=", statement.getTransactionDate());
        query.filterBy("description", "=", statement.getDescription());
        if(statement.getDebit()!=null)
            query.filterBy("debit","=",statement.getDebit());
        else
            query.filterBy("credit", "=", statement.getCredit());
        query.filterBy("balance", "=", statement.getBalance());
        if(query.execute().size()>0)
            return true;
        return false;
    }
    
    public List<Ticket> getTickets(MatchedTicketSearchHelper search){
        SelectQuery<Ticket> query=new SelectQuery<>(Ticket.class);
        if(search.getStatementType()==OperationType.PURCHASE){
            query.filterBy("matchToPurchaseDecision", "IS NULL", null);
            query.filterBy("matchToRefundsDecision", "IS NULL", null);
            
        }
        if(search.getStatementType()==OperationType.REFUNDS){
            query.filterBy("matchToPurchaseDecision", "IS NULL", null);
            query.filterBy("matchToRefundsDecision", "IS NULL", null);
            query.filterBy("refunded", "=", true);
        }
        
        if(search.getPurchaseDate()!=null)
            query.filterBy("purchaseDate", "=", search.getPurchaseDate());
        if(search.getTicketType()!=null)
            query.filterBy("ticketType", "=", search.getTicketType());
        if(search.getFare()!=null){
            if(search.getMargin()==null)
                search.setMargin(0.0);
            
            Double maximum=search.getFare()+search.getMargin();
            Double minimum=search.getFare()-search.getMargin();
            query.filterBy("fareInRefCurrency",">=",minimum);
            query.filterBy("fareInRefCurrency","<=",maximum);
            }
        if(search.getCardUsed()!=null)
            query.filterBy("cardUsed","=",search.getCardUsed());
        if(search.getAirLineCompany()!=null)
            query.filterBy("airline","=",search.getAirLineCompany());
        return query.execute();
        }   
    
     public List<CreditCardStatement> getStatements(MatchedTicketSearchHelper search){
         SelectQuery<CreditCardStatement> query=new SelectQuery<>(CreditCardStatement.class);
         if(search.getStatementType()!=null){
//            query.filterBy("matchToPurchaseDecision", "IS NULL", null);
//            query.filterBy("matchToRefundsDecision", "IS NULL", null);
             query.filterBy("matchDecision","IS NULL", null);
            
         }
         if(search.getOperationDate()!=null)
             query.filterBy("operationDate", "=", search.getOperationDate());
         if(search.getCompany()!=null)
             query.filterBy("company","=", search.getCompany());
         if(search.getAmount()!=null){
            if(search.getMargin()==null)
                search.setMargin(0.0);
            
            Double maximum=search.getAmount()+search.getMargin();
            Double minimum=search.getAmount()-search.getMargin();
            query.filterBy("amount",">=",minimum);
            query.filterBy("amount","<=",maximum);
            }
         if(search.getStatementType()!=null)
            query.filterBy("opertaionType","=",search.getStatementType());
         return query.execute();
     }
    }


    

