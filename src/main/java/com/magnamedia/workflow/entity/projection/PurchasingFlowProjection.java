package com.magnamedia.workflow.entity.projection;

import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.workflow.Task;
import com.magnamedia.entity.PurchasingToDo;
import com.magnamedia.entity.workflow.ExpensePayment;
import org.springframework.data.rest.core.config.Projection;

import java.util.List;

/**
 * <PERSON> (Jan 31, 2021)
 */
@Projection(types = PurchasingToDo.class)
public interface PurchasingFlowProjection extends Task {
    Long getId();

    List<Attachment> getAttachments();

}
