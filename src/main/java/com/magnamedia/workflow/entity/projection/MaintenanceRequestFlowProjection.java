package com.magnamedia.workflow.entity.projection;

import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.workflow.Task;
import com.magnamedia.entity.MaintenanceRequest;
import org.springframework.data.rest.core.config.Projection;

import java.util.List;

/**
 * <PERSON> (Feb 07, 2021)
 */
@Projection(types = MaintenanceRequest.class)
public interface MaintenanceRequestFlowProjection extends Task {
    Long getId();

    List<Attachment> getAttachments();

    String getSupplierName();

    String getDescription();

    String getSendToGetBetterPriceNote();

    Double getCost();
}