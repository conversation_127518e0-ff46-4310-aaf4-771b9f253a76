package com.magnamedia.workflow.service.expensepayment;

import com.magnamedia.core.repository.workflow.WorkflowRepository;
import com.magnamedia.core.workflow.ManualTask;
import com.magnamedia.entity.workflow.ExpensePayment;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

/**
 * <PERSON> (Jan 19, 2021)
 */
abstract public class ExpensePaymentAbstractStep<T extends ExpensePayment> extends ManualTask<T> {

    @Autowired
    private WorkflowRepository<T> expensePaymentRepository;

    @Override
    public WorkflowRepository<T> getRepository() {
        return expensePaymentRepository;
    }

    @Override
    public void onSave(T t) {
    }

    @Override
    public List<String> getTaskHeader() {
        return Arrays.asList("beneficiary", "description", "amount");
    }

}
