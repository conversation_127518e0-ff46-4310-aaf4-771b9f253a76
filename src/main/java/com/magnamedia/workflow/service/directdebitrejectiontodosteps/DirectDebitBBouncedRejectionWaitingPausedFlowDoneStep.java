package com.magnamedia.workflow.service.directdebitrejectiontodosteps;

import com.magnamedia.controller.DirectDebitController;
import com.magnamedia.core.Setup;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.DirectDebitMethod;
import com.magnamedia.module.type.DirectDebitRejectionToDoType;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.repository.DirectDebitFileRepository;
import com.magnamedia.service.BouncingFlowService;
import com.magnamedia.service.ContractService;
import com.magnamedia.service.DirectDebitRejectionFlowService;
import com.magnamedia.service.DirectDebitSignatureService;
import com.magnamedia.workflow.service.DirectDebitRejectionToDoManualStep;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on 24-8-2020
 *         Jirra ACC-CC_APP
 */
@Service
public class DirectDebitBBouncedRejectionWaitingPausedFlowDoneStep
        extends DirectDebitRejectionToDoManualStep<DirectDebitRejectionToDo> {

    @Autowired
    private DirectDebitController directDebitCtrl;
    @Autowired
    private DirectDebitRejectionFlowService directDebitRejectionFlowService;
    @Autowired
    private BouncingFlowService bouncingFlowService;
    @Autowired
    private DirectDebitFileRepository ddfRepository;
    @Autowired
    private DirectDebitSignatureService directDebitSignatureService;
    
    private static final Logger logger =
            Logger.getLogger(DirectDebitBBouncedRejectionWaitingPausedFlowDoneStep.class.getName());

    public DirectDebitBBouncedRejectionWaitingPausedFlowDoneStep() {
        this.setId(DirectDebitRejectionToDoType.WAITING_FLOW_PAUSE_B_BOUNCED.toString());
    }

    @Override
    public void onSave(DirectDebitRejectionToDo entity) {
    }

    @Override
    public void postSave(DirectDebitRejectionToDo entity) {
    }

    @Override
    public void onDone(DirectDebitRejectionToDo entity) {
        boolean needAccountantAction = false;
        DirectDebitRejectionToDoType nextStep = null;
        DirectDebit lastRejected = entity.getLastDirectDebit();

        if (lastRejected != null) {
            ContractPaymentTerm contractPaymentTerm = lastRejected.getContractPaymentTerm();
            logger.log(Level.SEVERE, "DirectDebitBBouncedRejectionWaitingBankResponseStep onDone dd id:" + lastRejected.getId());
            logger.log(Level.SEVERE, "DirectDebitBBouncedRejectionWaitingBankResponseStep onDone dd getBouncingRejectCategory:" + lastRejected.getBouncingRejectCategory());

            boolean allManualRejected = true;

            long manualCount = lastRejected.getDirectDebitFiles().stream()
                    .filter(ddf -> ddf.getDdMethod() == DirectDebitMethod.MANUAL).count();
            if (manualCount == 0)
                allManualRejected = false;
            else {
                for (DirectDebitFile ddf : lastRejected.getDirectDebitFiles())
                    if (ddf.getDdMethod() == DirectDebitMethod.MANUAL &&
                            (!ddf.getDdStatus().equals(DirectDebitStatus.REJECTED)
                                    && !ddf.getDdStatus().equals(DirectDebitStatus.CANCELED))) {
                        allManualRejected = false;
                        break;
                    }
            }

            logger.log(Level.SEVERE, "DirectDebitBBouncedRejectionWaitingBankResponseStep onDone dd allManualRejected:" + allManualRejected);

            if (allManualRejected) {
                Integer maxTrials = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_DD_MAX_TRIALS));
                /*boolean createExpertTodo = false;
                String reasonToCall = "";
                String initialNotes = "";*/
                boolean scheduleForTermination = false;

                if (lastRejected.getBouncingRejectCategory() != entity.getLastRejectCategory()) {
                    logger.log(Level.SEVERE, "DirectDebitBBouncedRejectionWaitingBankResponseStep make trials 0 because of change rejection category" +
                            " to : " + lastRejected.getRejectCategory() + " from : " + entity.getLastRejectCategory());
                    entity.setTrials(0);
                }

                List<DirectDebitSignature> signatures;
                if (null != lastRejected.getBouncingRejectCategory()) switch (lastRejected.getBouncingRejectCategory()) {
                    case Compliance:
                    case Other:
                        Map<String, Object> lastSignatureType = directDebitSignatureService
                                .getLastSignatureType(lastRejected.getContractPaymentTerm(), true, false);
                        signatures = (List<DirectDebitSignature>) lastSignatureType.get("currentSignatures");

                        try {
                            Map<String, Object> map = new HashMap<>();
                            map.put("signatures", signatures);
                            bouncingFlowService.createManualDDsForBouncedFlow(lastRejected, map);
                        } catch (Exception e) {
                            throw new RuntimeException(e.getMessage());
                        }
                        nextStep = DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B_BOUNCED;
                        break;
                    
                    case Signature:
                        Integer maxReSignTrials =
                                Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_MAX_RE_SIGN_TRIALS));
                        logger.log(Level.SEVERE, "DirectDebitARejectionWaitingBankResponseStep maxReSignTrials: " + maxReSignTrials);
                        logger.log(Level.SEVERE, "DirectDebitARejectionWaitingBankResponseStep entity.getReSignTrials(): " + entity.getReSignTrials());
                        Map<String, Object> signatureType = directDebitSignatureService
                                .getLastSignatureType(lastRejected.getContractPaymentTerm(), false, false);
                        signatures = (List<DirectDebitSignature>) signatureType.get("currentSignatures");

                        if ((Boolean) signatureType.get("useApprovedSignature")) {
                            entity.setSameSignTrials(entity.getSameSignTrials() + 1);
                        } else if (directDebitRejectionFlowService.flowStoppedAfterIncreaseReSignTrials(entity)) return; // ACC-4715

                        if (entity.getReSignTrials() > maxReSignTrials) {
                            scheduleForTermination = true;
                        } else if (entity.getReSignTrials() == maxReSignTrials) {
                            bouncingFlowService.createManualDDsForBouncedFlow(lastRejected, new HashMap<>());
                            
                            entity.setReminder(0);
                            
                            /*createExpertTodo = true;
                            reasonToCall = "Client signature is rejected from bank side for " + maxReSignTrials + " time";*/
                            
                            // make the rejection flow waits for client resign
                            addNewTask(entity, DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_BOUNCED.toString());
                            
                        } else if (entity.getReSignTrials() < maxReSignTrials) {
                            
                            if ((Boolean) signatureType.get("useApprovedSignature")) {
                                
                                Integer maxSameSignatureTrials =
                                        Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_MAX_SAME_SIGNATURE_TRIALS));
                                
                                if (entity.getSameSignTrials() <= maxSameSignatureTrials) {
                                    try {
                                        Map<String, Object> map = new HashMap<>();
                                        map.put("signatures", signatures);
                                        bouncingFlowService.createManualDDsForBouncedFlow(lastRejected, map);
                                    } catch (Exception e) {
                                        throw new RuntimeException(e.getMessage());
                                    }
                                    
                                    nextStep = DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B_BOUNCED;
                                    
                                } else if (entity.getSameSignTrials() > maxSameSignatureTrials) {
                                    bouncingFlowService.createManualDDsForBouncedFlow(lastRejected, new HashMap<>());
                                    
                                    entity.setReminder(0);
                                    //ACC-4715
                                    if (directDebitRejectionFlowService.flowStoppedAfterIncreaseReSignTrials(entity)) return;

                                    entity.setReminderDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
                                    
                                    // make the rejection flow waits for client resign
                                    nextStep = DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_BOUNCED;
                                }
                            } else { // not using same signature
                                bouncingFlowService.createManualDDsForBouncedFlow(lastRejected, new HashMap<>());

                                entity.setReminderDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
                                
                                nextStep = DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_BOUNCED;
                            }
                        }
                        break;
                    /*case Account:
                    case EID:
                        // this if is when rejected for account or eid in second,third... times    
                        lastRejected.setMStatus(DirectDebitStatus.PENDING_DATA_ENTRY);
                        List<DirectDebitFile> manualsForBouncing = lastRejected.getDirectDebitFiles()
                                .stream()
                                .filter(ddf -> ddf.getDdMethod() == DirectDebitMethod.MANUAL && ddf.getDdStatus().equals(DirectDebitStatus.REJECTED)
                                        && ddf.getForBouncingPayment() != null && ddf.getForBouncingPayment())
                                .collect(Collectors.toList());
                        
                        directDebitRejectionFlowService.sendDDFsBackToAccountant(manualsForBouncing);
                        
                        nextStep = DirectDebitRejectionToDoType.WAITING_ACCOUNTANT_ACTION_B_BOUNCED;
                        needAccountantAction = true;
                        break;*/
                    case Account:
                    case EID:
                    case Invalid_Account:
                    case Authorization:
                        bouncingFlowService.createManualDDsForBouncedFlow(lastRejected, new HashMap<>());
                        
                        entity.setTrials(entity.getTrials() + 1);
                        entity.setReminder(0);
                        entity.setReminderDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
                        
                        if (entity.getTrials() > maxTrials) {
                            scheduleForTermination = true;
                        }
                        /*if (entity.getTrials() == (maxTrials - 1)) {
                            createExpertTodo = true;
                            reasonToCall = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_AUTH_REJECT_EXPERT_TODO_REASON_TO_CALL);
                            initialNotes = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_AUTH_REJECT_EXPERT_TODO_INITIAL_NOTES);
                        }*/
                        
                        // make the rejection flow waits for client to provide new info
                        nextStep = DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_BOUNCED;
                        break;
                    
                    /*case Invalid_Account:
                        bouncingFlowService.createManualDDsForBouncedFlow(lastRejected, new HashMap<>());
                        
                        entity.setTrials(entity.getTrials() + 1);
                        entity.setReminder(0);
                        entity.setReminderDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
                        if (entity.getTrials() >= maxTrials) {
                            scheduleForTermination = true;
                        }
                        
                        // make the rejection flow waits for client resign
                        nextStep = DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_BOUNCED;
                        break;*/
                    default:
                        break;
                }

                // save last reject reason and update the flow with new values
                if (needAccountantAction) {
                    entity.setDontSendDdMessage(true);
                } else {
                    entity.setDontSendDdMessage(false);
                }
                logger.log(Level.SEVERE, "DirectDebitBBouncedRejectionWaitingBankResponseStep onDone entity id:" + entity.getId());
                logger.log(Level.SEVERE, "DirectDebitBBouncedRejectionWaitingBankResponseStep onDone setDontSendDdMessage:" + entity.getDontSendDdMessage());

                if (scheduleForTermination) {
                    Contract contract = lastRejected.getContractPaymentTerm().getContract();
                    
                    if (contract.isTerminateContractDueRejection() || !entity.isDdAddedByOecFlow()) {
                        logger.info("contract.terminateContractDueRejection: " + contract.isTerminateContractDueRejection() +
                                "; entity.isDdAddedByOecFlow: " + entity.isDdAddedByOecFlow());

                        entity.setContractScheduleDateOfTermination(
                                directDebitRejectionFlowService
                                        .setContractForTermination(
                                                lastRejected.getContractPaymentTerm(),
                                                "direct_debit_rejection_type_b_maxsignaturetrialsb_reached",
                                                entity));
                        entity.setLeadingRejectionFlow(true);
                    }
                    entity.setCompleted(true);
                    entity.setStopped(true);
                }

                /*if (createExpertTodo) {
                    entity.setVoiceResolverTodoId(directDebitRejectionFlowService.createExpertTodo(lastRejected, reasonToCall, initialNotes));
                    entity.setLastTrialDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
                }*/

                entity.setLastRejectCategory(lastRejected.getBouncingRejectCategory());
                super.onDone(entity);
                if (nextStep != null) {
                    addNewTask(entity, nextStep.toString());
                } else {
                    throw new RuntimeException("DirectDebitBBouncedRejectionWaitingBankResponseStep next step cannot be null");
                }
            }
        }
    }


    @Override
    public void postDone(DirectDebitRejectionToDo entity) {

    }
}
