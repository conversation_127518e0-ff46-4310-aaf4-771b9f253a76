package com.magnamedia.workflow.service.directdebitrejectiontodosteps;

import com.magnamedia.core.Setup;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.DirectDebitFileRepository;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.service.*;
import com.magnamedia.workflow.service.DirectDebitRejectionToDoManualStep;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on 5-3-2020
 *         Jirra ACC-1777
 */
@Service
public class DirectDebitBBouncedDRejectionWaitingAccountantActionStep
        extends DirectDebitRejectionToDoManualStep<DirectDebitRejectionToDo> {

    @Autowired
    private DirectDebitRepository directDebitRepository;

    @Autowired
    private DirectDebitFileRepository directDebitFileRepository;

    @Autowired
    private DirectDebitSignatureService directDebitSignatureService;

    private static final Logger logger =
            Logger.getLogger(DirectDebitBBouncedDRejectionWaitingAccountantActionStep.class.getName());

    public DirectDebitBBouncedDRejectionWaitingAccountantActionStep() {
        this.setId(DirectDebitRejectionToDoType.WAITING_ACCOUNTANT_ACTION_B_BOUNCED.toString());
    }

    @Override
    public void onSave(DirectDebitRejectionToDo entity) {
    }

    @Override
    public void postSave(DirectDebitRejectionToDo entity) {
    }

    @Override
    public void onDone(DirectDebitRejectionToDo entity) {
        DirectDebit rejectedDirectDebit = entity.getLastDirectDebit();
        if (rejectedDirectDebit != null) {
            rejectedDirectDebit = directDebitRepository.getOne(rejectedDirectDebit.getId());
            if (entity.getSendToClient()) {

                entity.setTrials(entity.getTrials() + 1);
                entity.setReminder(0);
                entity.setDontSendDdMessage(false);
                entity.setReminderDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());

                List<DirectDebitFile> manualsForBouncing = rejectedDirectDebit.getDirectDebitFiles()
                        .stream()
                        .filter(ddf -> ddf.getDdMethod() == DirectDebitMethod.MANUAL
                                && ddf.getForBouncingPayment() != null && ddf.getForBouncingPayment())
                        .collect(Collectors.toList());

                for (DirectDebitFile directDebitFile : manualsForBouncing) {
                    directDebitFile.setStatus(DirectDebitFileStatus.REJECTED);
                    directDebitFile.setDdStatus(DirectDebitStatus.REJECTED);
                    directDebitFile.setNeedAccountantReConfirmation(false);
                    directDebitFile.setIsFromAccountantAction(true);
                    if (directDebitFile.getRejectCategory().equals(DirectDebitRejectCategory.Signature))
                        directDebitSignatureService.updateSignatureStatus(directDebitFile, DirectDebitSignatureStatus.REJECTED);
                    directDebitFile = Setup.getApplicationContext().getBean(DirectDebitStatusChangeService.class)
                            .ddFileRejected(directDebitFile);
                    directDebitFileRepository.save(directDebitFile);
                }

                BouncingFlowService bouncingFlowService = Setup.getApplicationContext().getBean(BouncingFlowService.class);
                bouncingFlowService.createManualDDsForBouncedFlow(rejectedDirectDebit, new HashMap<>());
//                bouncingFlowService.increaseDDPaymentsTrials(rejectedDirectDebit);


                addNewTask(entity, DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_BOUNCED.toString());


                Integer maxTrials =
                        Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_MAX_TRIALS));

                if (entity.getTrials() > maxTrials) {
                    Contract contract = rejectedDirectDebit.getContractPaymentTerm().getContract();
                    
                    if (contract.isTerminateContractDueRejection() || !entity.isDdAddedByOecFlow()) {
                        logger.info("contract.terminateContractDueRejection: " + contract.isTerminateContractDueRejection() +
                                "; entity.isDdAddedByOecFlow: " + entity.isDdAddedByOecFlow());

                        entity.setContractScheduleDateOfTermination(
                                Setup.getApplicationContext()
                                        .getBean(DirectDebitRejectionFlowService.class)
                                        .setContractForTermination(
                                                rejectedDirectDebit.getContractPaymentTerm(),
                                                "direct_debit_rejection_type_b_bank_info_max_trials_reached",
                                                entity));
                        entity.setLeadingRejectionFlow(true);
                    }
                    entity.setCompleted(true);
                    entity.setStopped(true);
                }

            } else {
                if (entity.getEid() != null) {
                    rejectedDirectDebit.setEid(entity.getEid());
                } else if (entity.getAccountName() != null) {
                    rejectedDirectDebit.setAccountName(entity.getAccountName());
                } else {
                    logger.log(Level.SEVERE, "eid and account name is null");
                    return;
                }
                rejectedDirectDebit.setMStatus(DirectDebitStatus.PENDING);
                rejectedDirectDebit.setConfirmedBankInfo(true);

                List<DirectDebitFile> manualsForBouncing = rejectedDirectDebit.getDirectDebitFiles()
                        .stream()
                        .filter(ddf -> ddf.getDdMethod() == DirectDebitMethod.MANUAL
                                && ddf.getForBouncingPayment() != null && ddf.getForBouncingPayment())
                        .collect(Collectors.toList());


                for (DirectDebitFile f : manualsForBouncing) {
                    f.setNeedAccountantReConfirmation(false);
                    f.setStatus(DirectDebitFileStatus.NOT_SENT);
                    f.setDdStatus(DirectDebitStatus.PENDING);
                    f.setConfirmedBankInfo(true);
                    if (entity.getEid() != null) {
                        f.setEid(entity.getEid());
                    } else if (entity.getAccountName() != null) {
                        f.setAccountName(entity.getAccountName());
                    }

                    if (!Setup.getApplicationContext().getBean(DirectDebitService.class)
                            .createDirectDebitActivationAttachmentIfNotExist(f)) {

                        directDebitFileRepository.save(f);
                        directDebitSignatureService.updateSignatureStatus(f, DirectDebitSignatureStatus.UNUSED);
                    }

                }

                entity.setDontSendDdMessage(true);
                addNewTask(entity, DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B_BOUNCED.toString());
                directDebitRepository.save(rejectedDirectDebit);
            }
        } else {
            throw new RuntimeException("lastDirectDebit can't be null");
        }
        super.onDone(entity);
//        no need for save since core complete task do the save
    }

    @Override
    public void postDone(DirectDebitRejectionToDo entity) {
    }
}
