package com.magnamedia.workflow.service.purchasingsteps;

import com.magnamedia.entity.PurchasingToDo;
import com.magnamedia.workflow.type.PurchasingToDoType;
import org.springframework.stereotype.Service;

/**
 * <PERSON> (Feb 01, 2021)
 */
@Service
public class PurchasingStep extends PurchasingAbstractStep<PurchasingToDo> {
    public PurchasingStep() {
        this.setId(PurchasingToDoType.PM_PURCHASE.toString());
    }

    @Override
    public void onDone(PurchasingToDo entity) {
        super.onDone(entity);
        entity.setCompleted(Boolean.TRUE);
    }
}
