package com.magnamedia.workflow.service.purchasingsteps;

import com.magnamedia.entity.PurchaseItem;
import com.magnamedia.entity.PurchaseOrder;
import com.magnamedia.entity.PurchasingToDo;
import com.magnamedia.module.type.PurchaseItemInOrderStatus;
import com.magnamedia.module.type.PurchaseItemSupplierStatus;
import com.magnamedia.repository.PurchaseItemRepository;
import com.magnamedia.repository.PurchaseOrderRepository;
import com.magnamedia.service.ExpenseNotificationService;
import com.magnamedia.workflow.type.PurchasingToDoType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <PERSON> (Feb 01, 2021)
 */
@Service
public class ConfirmBestSupplierStep extends PurchasingAbstractStep<PurchasingToDo> {
    public ConfirmBestSupplierStep() {
        this.setId(PurchasingToDoType.PA_CONFIRM_BEST_SUPPLIER.toString());
    }

    @Autowired
    private PurchaseItemRepository purchaseItemRepository;

    @Autowired
    private PurchaseOrderRepository purchaseOrderRepository;

    @Autowired
    private ExpenseNotificationService expenseNotificationService;

    @Override
    public void onDone(PurchasingToDo entity) {
        List<PurchaseItem> items = purchaseItemRepository.findByPurchasingToDo(entity);
        if (items.stream().anyMatch(t -> t.getSupplierStatus().equals(PurchaseItemSupplierStatus.PENDING)))
            throw new RuntimeException("all items must be not pending");

        List<PurchaseItem> getBetterSupplierItems = items.stream().filter(t -> t.getSupplierStatus().equals(PurchaseItemSupplierStatus.SEND_GET_BETTER_PRICES)).collect(Collectors.toList());
        confirmItems(items);

        List<PurchaseItem> confirmedItems = items.stream().filter(t -> t.getSupplierStatus().equals(PurchaseItemSupplierStatus.CONFIRMED)).collect(Collectors.toList());

        if (getBetterSupplierItems.size() > 0) {
            addNewTask(entity, PurchasingToDoType.PM_GET_BETTER_SUPPLIER.toString());
            expenseNotificationService.expenseToDoCreatedEmail(entity.getEntityType(), entity.getTaskName());
        } else if (confirmedItems.size() > 0) {
            addNewTask(entity, PurchasingToDoType.PM_PURCHASE.toString());
            expenseNotificationService.expenseToDoCreatedEmail(entity.getEntityType(), entity.getTaskName());
            createPurchasingObjects(items);
        }
        super.onDone(entity);
    }

    private void createPurchasingObjects(List<PurchaseItem> allItems) {
        List<PurchaseItem> confirmedItems = allItems.stream().filter(t -> t.getSupplierStatus().equals(PurchaseItemSupplierStatus.CONFIRMED)).collect(Collectors.toList());
        Map<Long, List<PurchaseItem>> suppliersItems = new HashMap<>();
        for (PurchaseItem item : confirmedItems) {
            suppliersItems.computeIfAbsent(item.getCurrentSupplier().getId(), t -> new ArrayList<>());
            suppliersItems.get(item.getCurrentSupplier().getId()).add(item);
        }

        suppliersItems.forEach((key, value) -> createPO(value));
    }

    private void createPO(List<PurchaseItem> purchaseItems) {
        PurchaseOrder purchaseOrder = new PurchaseOrder();
        purchaseOrder.setSupplier(purchaseItems.get(0).getCurrentSupplier());
        purchaseOrder.setPurchasingToDo(purchaseItems.get(0).getPurchasingToDo());
        purchaseOrderRepository.save(purchaseOrder);
        for (PurchaseItem purchaseItem : purchaseItems) {
            purchaseItem.setPurchaseOrder(purchaseOrder);
            purchaseItem.setItemInOrderStatus(PurchaseItemInOrderStatus.INVOLVED);
            purchaseItemRepository.save(purchaseItem);
        }
    }

    private void confirmItems(List<PurchaseItem> allItems) {
        List<PurchaseItem> approvedItems = allItems.stream().filter(t -> t.getSupplierStatus().equals(PurchaseItemSupplierStatus.APPROVED)).collect(Collectors.toList());
        for (PurchaseItem purchaseItem : approvedItems) {
            purchaseItem.setSupplierStatus(PurchaseItemSupplierStatus.CONFIRMED);
            purchaseItemRepository.save(purchaseItem);
        }
    }
}
