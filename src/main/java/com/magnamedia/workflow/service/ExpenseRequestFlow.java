package com.magnamedia.workflow.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SearchField;
import com.magnamedia.core.workflow.RoleBasedWorkflow;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.workflow.service.expenserequesttodosteps.ExpenseRequestPaymentObjectCreatedStep;
import com.magnamedia.workflow.service.expenserequesttodosteps.ExpenseRequestWaitingCooApprovalStep;
import com.magnamedia.workflow.service.expenserequesttodosteps.ExpenseRequestWaitingManagerApprovalStep;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR> <PERSON>
 * Created on Jan 18, 2021
 * Jirra ACC-2913
 */
@Service(value = "expenseRequestFlow")
public class ExpenseRequestFlow extends RoleBasedWorkflow<ExpenseRequestTodo> {

    @Autowired
    ExpenseRequestWaitingManagerApprovalStep expenseRequestWaitingManagerApprovalStep;

    @Autowired
    ExpenseRequestWaitingCooApprovalStep expenseRequestWaitingCooApprovalStep;

    @Autowired
    ExpenseRequestPaymentObjectCreatedStep expenseRequestPaymentObjectCreatedStep;

    public ExpenseRequestFlow(ExpenseRequestWaitingManagerApprovalStep expenseRequestWaitingManagerApprovalStep,
                              ExpenseRequestWaitingCooApprovalStep expenseRequestWaitingCooApprovalStep,
                              ExpenseRequestPaymentObjectCreatedStep expenseRequestPaymentObjectCreatedStep) {
        setId("expenseRequestFlow");

        this.expenseRequestWaitingManagerApprovalStep=expenseRequestWaitingManagerApprovalStep;
        this.expenseRequestWaitingCooApprovalStep = expenseRequestWaitingCooApprovalStep;
        this.expenseRequestPaymentObjectCreatedStep=expenseRequestPaymentObjectCreatedStep;

        this.getWorkflowSteps().addAll(Arrays.asList(
                this.expenseRequestWaitingManagerApprovalStep,
                this.expenseRequestWaitingCooApprovalStep,
                this.expenseRequestPaymentObjectCreatedStep
        ));

        this.getWorkflowSteps().forEach((workflowStep) -> {
            this.idStepMap.put(workflowStep.getId(), workflowStep);
        });
    }

    @Override
    public List<SearchField> fillSearchFields() {
        List<SearchField> fields = new ArrayList();

        return fields;
    }

    @Override
    public Map<String, String> getTableHeader() {
        Map<String, String> tableHeader = new HashMap();
        return tableHeader;
    }

    @Override
    public Map<String, String> getTableColumnTypes() {
        Map<String, String> tableColumnTypes = new HashMap();

        return tableColumnTypes;
    }

    public List<SearchField> getDefaultSearchableFields() {
        return Arrays.asList(
                new SearchField("creationDate", "Creation Date", "timestamp",
                        Setup.DATE_OPERATIONS),
                new SearchField("lastModificationDate", "Last Update Date", "timestamp",
                        Setup.DATE_OPERATIONS)
        );
    }

    @Override
    public String getWorkflowHeader() {
        return "Expense Request Flow";
    }
}
