package com.magnamedia.workflow.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SearchField;
import com.magnamedia.core.workflow.RoleBasedWorkflow;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.workflow.service.expensepayment.*;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * <PERSON> (Jan 19, 2021)
 */
@Service
public class ExpensePaymentFlow extends RoleBasedWorkflow<ExpensePayment> {

    final ExpensePaymentInPendingPaymentCashierStep expensePaymentInPendingPaymentCashierStep;
    final ExpensePaymentBankTransferStep expensePaymentBankTransferStep;
    final ExpensePaymentInCreditCardHolderStep expensePaymentInCreditCardHolderStep;
    final ExpensePaymentInPendingInvoiceStep expensePaymentInPendingInvoiceStep;
    final ExpensePaymentInReconciliatorConfirmationStep expensePaymentInReconciliatorConfirmationStep;

    public static final String flowName="expensePaymentFlow";

    public ExpensePaymentFlow(
            ExpensePaymentInPendingPaymentCashierStep expensePaymentInPendingPaymentCashierStep,
            ExpensePaymentBankTransferStep expensePaymentBankTransferStep,
            ExpensePaymentInCreditCardHolderStep expensePaymentInCreditCardHolderStep,
            ExpensePaymentInPendingInvoiceStep expensePaymentInPendingInvoiceStep,
            ExpensePaymentInReconciliatorConfirmationStep expensePaymentInReconciliatorConfirmationStep) {
        this.expensePaymentInPendingPaymentCashierStep = expensePaymentInPendingPaymentCashierStep;
        this.expensePaymentBankTransferStep = expensePaymentBankTransferStep;
        this.expensePaymentInCreditCardHolderStep = expensePaymentInCreditCardHolderStep;
        this.expensePaymentInPendingInvoiceStep = expensePaymentInPendingInvoiceStep;
        this.expensePaymentInReconciliatorConfirmationStep = expensePaymentInReconciliatorConfirmationStep;

        setId(flowName);

        this.getWorkflowSteps().addAll(
                Arrays.asList(
                        this.expensePaymentInPendingPaymentCashierStep,
                        this.expensePaymentBankTransferStep,
                        this.expensePaymentInCreditCardHolderStep,
                        this.expensePaymentInPendingInvoiceStep,
                        this.expensePaymentInReconciliatorConfirmationStep
                ));

        this.getWorkflowSteps().forEach((workflowStep) -> {
            this.idStepMap.put(workflowStep.getId(), workflowStep);
        });
    }

    public List<SearchField> getDefaultSearchableFields() {
        return Arrays.asList(
                new SearchField("creationDate", "Creation Date", "timestamp",
                        Setup.DATE_OPERATIONS),
                new SearchField("lastModificationDate", "Last Update Date", "timestamp",
                        Setup.DATE_OPERATIONS)
        );
    }
}