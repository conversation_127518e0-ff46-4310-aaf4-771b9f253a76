package com.magnamedia.workflow.service.bucketreplenishmentsteps;

import com.magnamedia.controller.ExpensePaymentController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.entity.Bucket;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.workflow.BucketReplenishmentTodo;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.BucketRepository;
import com.magnamedia.repository.OfficeStaffRepository;
import com.magnamedia.service.CurrencyExchangeSevice;
import com.magnamedia.workflow.service.BucketReplenishmentManualStep;
import com.magnamedia.workflow.type.BucketReplenishmentTodoType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 * @date 1/21/2021
 */
@Service
public class BucketReplenishmentApproveStep extends BucketReplenishmentManualStep<BucketReplenishmentTodo> {

    @Autowired
    private CurrencyExchangeSevice currencyExchangeSevice;

    private static final Logger logger =
            Logger.getLogger(BucketReplenishmentApproveStep.class.getName());

    public BucketReplenishmentApproveStep() {
        this.setId(BucketReplenishmentTodoType.BUCKET_REPLENISHMENT_WAITING_FOR_APPROVE.toString());
    }

    @Override
    public void onSave(BucketReplenishmentTodo entity) {
    }

    @Override
    public void postSave(BucketReplenishmentTodo entity) {
    }

    @Override
    public void onDone(BucketReplenishmentTodo entity) {

        logger.log(Level.INFO, "BucketReplenishmentApproveStep onDone Started");
        if (/*entity.getStatus()==BucketReplenishmentTodoStatus.PENDING &&*/
                entity.isAuditManagerApproved() &&
                        entity.getBucket().getBucketType() == BucketType.CASH_BOX &&
                        entity.getFillFrom().getBucketType() == BucketType.BANK_ACCOUNT &&
                        entity.isTransGuardService() &&
                        !entity.getTransGuardDone()) {
            entity.setStatus(BucketReplenishmentTodoStatus.APPROVED);
            return;
        }
        ExpensePaymentController expensePaymentController = Setup.getApplicationContext().getBean(ExpensePaymentController.class);
        super.onDone(entity);
        if (entity.isAuditManagerApproved()) {
            logger.log(Level.SEVERE, "Audit manager approved on " + entity.getId());
            entity.setStatus(BucketReplenishmentTodoStatus.APPROVED);

            //general payment object
            ExpensePayment payment = new ExpensePayment();

            User user = entity.getBucket().getHolder();
            String relatedEntityType = user != null ? user.getRelatedEntityType() : null;
            Long relatedEntityId = user != null ? user.getRelatedEntityId() : null;

            if (relatedEntityType != null && relatedEntityType.equalsIgnoreCase("OfficeStaff")) {
                OfficeStaff officeStaff = Setup.getRepository(OfficeStaffRepository.class).findOne(relatedEntityId);
                if (officeStaff != null) {
                    payment.setBeneficiaryName(officeStaff.getName());
                    payment.setBeneficiaryId(officeStaff.getId());
                    payment.setBeneficiaryType(ExpenseBeneficiaryType.OFFICE_STAFF);
                }
            }
            if (payment.getBeneficiaryId() == null && user != null)
                payment.setBeneficiaryName(user.getFullName());

            payment.setReplenishmentTodo(entity);
            payment.setAmount(entity.getAmount());
            payment.setStatus(ExpensePaymentStatus.PENDING);
            payment.setDescription("RPx" + entity.getId() + 
                    "\n Replenishment of " + entity.getBucket().getName() + 
                    (entity.getCreator() == null ? "" : " requested by " + entity.getCreator().getName()));
            payment.setFromBucket(entity.getFillFrom());
            payment.setRequiresInvoice(false);
            payment.setCurrency(currencyExchangeSevice.getLocalCurrency());
            payment.setToBucket(entity.getBucket());
            payment.setCashier(entity.getCashier());
            payment.setRequester(entity.getCreator());
            if (CurrentRequest.getUser() != null)
                payment.setApprovedBy(CurrentRequest.getUser().getFullName());

            if (null == entity.getBucket().getBucketType()) {
                throw new RuntimeException("Bucket type is null");
            }
            
            switch (entity.getBucket().getBucketType()) {
                case CREDIT_CARD:
                    logger.log(Level.SEVERE, "Create PAY_4 expense payment");
                    //pay-4
                    payment.setMethod(ExpensePaymentMethod.BANK_TRANSFER);
                    payment.setType(ExpensePaymentType.PAY);
                    payment = (ExpensePayment) expensePaymentController.createEntity(payment).getBody();
                    entity.getExpensePayments().add(payment);
                    break;
                case CASH_BOX:
                    if (null == entity.getFillFrom().getBucketType()) {
                        throw new RuntimeException("Bucket type to filled from is null");
                    }
                    switch (entity.getFillFrom().getBucketType()) {
                        case CASH_BOX:
                            logger.log(Level.SEVERE, "Create PAY_3 expense payment");
                            //pay-3
                            payment.setMethod(ExpensePaymentMethod.CASH);
                            payment.setType(ExpensePaymentType.PAY_TO_BUCKET);
                            payment = (ExpensePayment) expensePaymentController.createEntity(payment).getBody();
                            entity.getExpensePayments().add(payment);
                            break;
                        case BANK_ACCOUNT:
                            if (entity.isTransGuardService()) {
                                logger.log(Level.SEVERE, "Create PAY_1 expense payment");
                                //ACC-3163
                                payment.setMethod(ExpensePaymentMethod.CASH);
                                payment.setType(ExpensePaymentType.COLLECT);

                                payment = (ExpensePayment) expensePaymentController.createEntity(payment).getBody();
                                entity.getExpensePayments().add(payment);
                            } else {
                                Bucket moneyTransferBucket = Setup.getRepository(BucketRepository.class).findFirstByBucketType(BucketType.MONEY_TRANSFER);
                                if (moneyTransferBucket == null) {
                                    throw new RuntimeException("no money transfer bucket exists");
                                }

                                logger.log(Level.SEVERE, "Create PAY_1 expense payment");
                                //pay-1
                                payment.setMethod(ExpensePaymentMethod.MONEY_TRANSFER);
                                payment.setType(ExpensePaymentType.PAY);
                                payment.setToBucket(moneyTransferBucket);
                                payment = (ExpensePayment) expensePaymentController.createEntity(payment).getBody();

                                entity.getExpensePayments().add(payment);
                                logger.log(Level.SEVERE, "Create PAY_2 expense payment");

                                //pay-2
                                ExpensePayment payment2 = new ExpensePayment();
                                payment2.setMethod(ExpensePaymentMethod.CASH);

                                payment2.setReplenishmentTodo(entity);

                                payment2.setBeneficiaryName(payment.getBeneficiaryName());
                                payment2.setBeneficiaryId(payment.getBeneficiaryId());
                                payment2.setBeneficiaryType(payment.getBeneficiaryType());
                                payment2.setAmount(entity.getAmount());
                                payment2.setType(ExpensePaymentType.COLLECT);
                                payment2.setToBucket(entity.getBucket());
                                payment2.setStatus(ExpensePaymentStatus.PENDING);
                                payment2.setDescription("RPx" + entity.getId() + "\nReplenishment of " + entity.getBucket().getName() + " requested by " + entity.getCreator().getName());

                                payment2.setFromBucket(moneyTransferBucket);
                                payment2.setRequester(entity.getCreator());
                                if (CurrentRequest.getUser() != null)
                                    payment.setApprovedBy(CurrentRequest.getUser().getFullName());

                                payment2.setRequiresInvoice(false);
                                payment2 = (ExpensePayment) expensePaymentController.createEntity(payment2).getBody();

                                entity.getExpensePayments().add(payment2);
                            }
                            break;
                        default:
                            throw new RuntimeException("unsupported bucket type to filled from");
                    }
                    break;

                default:
                    throw new RuntimeException("unsupported bucket type");
            }
        } else {
            logger.log(Level.SEVERE, "Audit manager rejected " + entity.getId());
            entity.setStatus(BucketReplenishmentTodoStatus.REJECTED);
        }

        entity.setCompleted(true);
        entity.setStopped(true);
    }

    @Override
    public void postDone(BucketReplenishmentTodo entity) {
    }

    @Override
    public List<String> getTaskHeader() {
        return new ArrayList<>();
    }
}
