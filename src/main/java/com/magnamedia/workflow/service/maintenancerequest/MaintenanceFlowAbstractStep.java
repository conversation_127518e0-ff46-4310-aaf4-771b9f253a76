package com.magnamedia.workflow.service.maintenancerequest;

import com.magnamedia.core.repository.workflow.WorkflowRepository;
import com.magnamedia.core.workflow.ManualTask;
import com.magnamedia.entity.MaintenanceRequest;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <PERSON> (Feb 07, 2021)
 */
public abstract class MaintenanceFlowAbstractStep<T extends MaintenanceRequest> extends ManualTask<T> {
    @Autowired
    WorkflowRepository<T> maintenanceRequestRepository;

    @Override
    public WorkflowRepository<T> getRepository() {
        return maintenanceRequestRepository;
    }

    @Override
    public void onSave(T t) {
    }

    @Override
    public List<String> getTaskHeader() {
        return new ArrayList<>();
    }

}
