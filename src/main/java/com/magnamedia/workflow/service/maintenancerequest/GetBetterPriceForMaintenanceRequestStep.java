package com.magnamedia.workflow.service.maintenancerequest;

import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.entity.MaintenanceRequest;
import com.magnamedia.module.type.MaintenanceRequestType;
import com.magnamedia.repository.SupplierRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <PERSON> (Feb 07, 2021)
 */
@Service
public class GetBetterPriceForMaintenanceRequestStep extends MaintenanceFlowAbstractStep<MaintenanceRequest> {
    public GetBetterPriceForMaintenanceRequestStep() {
        this.setId(MaintenanceRequestType.PURCHASE_MANAGER_GET_BETTER_PRICE.toString());
    }

    @Autowired
    SupplierRepository supplierRepository;

    @Override
    public void onDone(MaintenanceRequest entity) {
        addNewTask(entity, MaintenanceRequestType.PURCHASE_AUDITOR_APPROVE_REQUEST.toString());
        entity.setSupplier(supplierRepository.getOne(entity.getSupplier().getId()));
        if(entity.getGetPriceNote() != null && !entity.getGetPriceNote().isEmpty())
            entity.addMaintenanceRequestNote(entity.getGetPriceNote());
        super.onDone(entity);
    }
}
