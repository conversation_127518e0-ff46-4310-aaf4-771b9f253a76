package com.magnamedia.workflow.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SearchField;
import com.magnamedia.core.workflow.RoleBasedWorkflow;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.workflow.service.directdebitrejectiontodosteps.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on 4-4-2020
 *         Jirra ACC-1595
 */

@Service(value = "directDebitRejectionToDoFlow")
public class DirectDebitRejectionToDoFlow
        extends RoleBasedWorkflow<DirectDebitRejectionToDo> {

    private DirectDebitARejectionWaitingClientReSignStep directDebitARejectionWaitingClientReSignStep;

    private DirectDebitARejectionWaitingBankResponseStep directDebitARejectionWaitingBankResponseStep;

    private DirectDebitBCaseDRejectionWaitingBankResponseStep directDebitBCaseDRejectionWaitingBankResponseStep;

    private DirectDebitBCaseDRejectionWaitingClientReSignStep directDebitBCaseDRejectionWaitingClientReSignStep;

    private DirectDebitBRejectionWaitingBankResponseStep directDebitBRejectionWaitingBankResponseStep;

    private DirectDebitBRejectionWaitingReScheduleStep directDebitBRejectionWaitingReScheduleStep;

    private DirectDebitARejectionWaitingAccountantActionStep directDebitARejectionWaitingAccountantActionStep;

    private DirectDebitBCaseDRejectionWaitingAccountantActionStep directDebitBCaseDRejectionWaitingAccountantActionStep;

    private DirectDebitBBouncedDRejectionWaitingAccountantActionStep directDebitBBouncedDRejectionWaitingAccountantActionStep;

    private DirectDebitBBouncedRejectionWaitingBankResponseStep directDebitBBouncedRejectionWaitingBankResponseStep;

    private DirectDebitBBouncedRejectionWaitingClientReSignStep directDebitBBouncedRejectionWaitingClientReSignStep;

    @Autowired
    private DirectDebitBBouncedRejectionWaitingPausedFlowDoneStep directDebitBBouncedRejectionWaitingPausedFlowDoneStep;


    @Autowired
    public DirectDebitRejectionToDoFlow(

            DirectDebitARejectionWaitingClientReSignStep directDebitARejectionWaitingClientReSignStep,
            DirectDebitARejectionWaitingBankResponseStep directDebitARejectionWaitingBankResponseStep,
            DirectDebitBCaseDRejectionWaitingBankResponseStep directDebitBCaseDRejectionWaitingBankResponseStep,
            DirectDebitBCaseDRejectionWaitingClientReSignStep directDebitBCaseDRejectionWaitingClientReSignStep,
            DirectDebitBRejectionWaitingBankResponseStep directDebitBRejectionWaitingBankResponseStep,
            DirectDebitBRejectionWaitingReScheduleStep directDebitBRejectionWaitingReScheduleStep,
            DirectDebitARejectionWaitingAccountantActionStep directDebitARejectionWaitingAccountantActionStep,
            DirectDebitBCaseDRejectionWaitingAccountantActionStep directDebitBCaseDRejectionWaitingAccountantActionStep,
            DirectDebitBBouncedDRejectionWaitingAccountantActionStep directDebitBBouncedDRejectionWaitingAccountantActionStep,
            DirectDebitBBouncedRejectionWaitingBankResponseStep directDebitBBouncedRejectionWaitingBankResponseStep,
            DirectDebitBBouncedRejectionWaitingClientReSignStep directDebitBBouncedRejectionWaitingClientReSignStep,
            DirectDebitBBouncedRejectionWaitingPausedFlowDoneStep directDebitBBouncedRejectionWaitingPausedFlowDoneStep

    ) {

        setId("directDebitRejectionToDoFlow");
        this.directDebitARejectionWaitingClientReSignStep = directDebitARejectionWaitingClientReSignStep;
        this.directDebitARejectionWaitingBankResponseStep = directDebitARejectionWaitingBankResponseStep;
        this.directDebitBCaseDRejectionWaitingBankResponseStep = directDebitBCaseDRejectionWaitingBankResponseStep;
        this.directDebitBCaseDRejectionWaitingClientReSignStep = directDebitBCaseDRejectionWaitingClientReSignStep;
        this.directDebitBRejectionWaitingBankResponseStep = directDebitBRejectionWaitingBankResponseStep;
        this.directDebitBRejectionWaitingReScheduleStep = directDebitBRejectionWaitingReScheduleStep;
        this.directDebitARejectionWaitingAccountantActionStep = directDebitARejectionWaitingAccountantActionStep;
        this.directDebitBCaseDRejectionWaitingAccountantActionStep = directDebitBCaseDRejectionWaitingAccountantActionStep;
        this.directDebitBBouncedDRejectionWaitingAccountantActionStep = directDebitBBouncedDRejectionWaitingAccountantActionStep;
        this.directDebitBBouncedRejectionWaitingBankResponseStep = directDebitBBouncedRejectionWaitingBankResponseStep;
        this.directDebitBBouncedRejectionWaitingClientReSignStep = directDebitBBouncedRejectionWaitingClientReSignStep;
        this.directDebitBBouncedRejectionWaitingPausedFlowDoneStep = directDebitBBouncedRejectionWaitingPausedFlowDoneStep;

        this.getWorkflowSteps().addAll(
                Arrays.asList(
                        this.directDebitARejectionWaitingClientReSignStep,
                        this.directDebitARejectionWaitingBankResponseStep,
                        this.directDebitBCaseDRejectionWaitingBankResponseStep,
                        this.directDebitBCaseDRejectionWaitingClientReSignStep,
                        this.directDebitBRejectionWaitingBankResponseStep,
                        this.directDebitBRejectionWaitingReScheduleStep,
                        this.directDebitARejectionWaitingAccountantActionStep,
                        this.directDebitBCaseDRejectionWaitingAccountantActionStep,
                        this.directDebitBBouncedDRejectionWaitingAccountantActionStep,
                        this.directDebitBBouncedRejectionWaitingBankResponseStep,
                        this.directDebitBBouncedRejectionWaitingClientReSignStep,
                        this.directDebitBBouncedRejectionWaitingPausedFlowDoneStep

                ));
        this.getWorkflowSteps().forEach((workflowStep) -> {
            this.idStepMap.put(workflowStep.getId(), workflowStep);
        });
    }

    @Override
    public List<SearchField> fillSearchFields() {

        List<SearchField> fields = new ArrayList<>();

        return fields;
    }

    @Override
    public Map<String, String> getTableHeader() {

        Map<String, String> tableHeader = new HashMap<>();
        tableHeader.put("id", "ID");
        return tableHeader;
    }

    @Override
    public Map<String, String> getTableColumnTypes() {

        Map<String, String> tableColumnTypes = new HashMap<>();
        tableColumnTypes.put("id", "Double");
        return tableColumnTypes;
    }

    public List<SearchField> getDefaultSearchableFields() {
        return Arrays.asList(
                new SearchField("creationDate", "Creation Date", "timestamp",
                        Setup.DATE_OPERATIONS),
                new SearchField("lastModificationDate", "Last Update Date", "timestamp",
                        Setup.DATE_OPERATIONS)
        );
    }

    @Override
    public String getWorkflowHeader() {

        return "Direct Debit Rejection ToDo Flow";
    }
}
