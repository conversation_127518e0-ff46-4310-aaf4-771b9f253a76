package com.magnamedia.workflow.service.clientrefundtodosteps;


import com.magnamedia.controller.PayrollAccountantTodoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.entity.ClientRefundSetup;
import com.magnamedia.entity.ContractPaymentConfirmationToDo;
import com.magnamedia.entity.Payment;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.entity.workflow.PaymentRequestPurpose;
import com.magnamedia.entity.workflow.PayrollAccountantTodo;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.ClientRefundTodoRepository;
import com.magnamedia.repository.ContractPaymentConfirmationToDoRepository;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.repository.PaymentRequestPurposeRepository;
import com.magnamedia.service.ClientRefundService;
import com.magnamedia.workflow.service.ClientRefundManualStep;
import com.magnamedia.workflow.type.ClientRefundPaymentMethod;
import com.magnamedia.workflow.type.ClientRefundStatus;
import com.magnamedia.workflow.type.ClientRefundTodoType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Ketrawi
 *         Created on Dec 12, 2020
 *         ACC-2847
 */

@Service
public class ClientRefundWaitingManagerApprovalStep extends ClientRefundManualStep<ClientRefundToDo> {
    private static final Logger logger = Logger.getLogger(ClientRefundWaitingManagerApprovalStep.class.getName());

    @Autowired
    private ClientRefundTodoRepository clientRefundTodoRepository;

    public ClientRefundWaitingManagerApprovalStep() {
        this.setId(ClientRefundTodoType.WAITING_MANAGER_APPROVAL.toString());
    }

    @Override
    public void onSave(ClientRefundToDo entity) {
    }

    @Override
    public void postSave(ClientRefundToDo entity) {
    }

    @Override
    public void onDone(ClientRefundToDo entity) {
        logger.log(Level.INFO, "Client Refund Manager Approval Step Started");

        if (entity.getManagerAction() == null)
            throw new RuntimeException("Manager Action can't be null");

        if (entity.getPurpose() == null)
            throw new RuntimeException("Purpose Can't be null");

        if (!entity.getStatus().equals(ClientRefundStatus.PENDING))
            throw new RuntimeException("Status is not pending you can't do this action");

        PaymentRequestPurpose purpose = Setup.getRepository(PaymentRequestPurposeRepository.class)
                .findOne(entity.getPurpose().getId());

        ClientRefundSetup setup = purpose.getUniquePurposeSetup();
        if (setup == null) throw new RuntimeException("This Purpose doesn't have setup");

        boolean bankTransferCreated = false;
        boolean creditCardTransferCreated = false;
        List<ContractPaymentConfirmationToDo> toDos = new ArrayList<>();
        ContractPaymentConfirmationToDoRepository confirmationToDoRepository = Setup.getRepository(ContractPaymentConfirmationToDoRepository.class);

        switch (entity.getManagerAction()) {
            case APPROVE:
                if (setup.doesNeedCeoApproval(entity.getAmount())) {
                    addNewTask(entity, ClientRefundTodoType.WAITING_COO_APPROVAL.toString());
                } else if(entity.getMethodOfPayment().equals(ClientRefundPaymentMethod.CREDIT_CARD)) {
                    creditCardTransferCreated = true;
                    addNewTask(entity, ClientRefundTodoType.CREDIT_CARD_TRANSFER_CREATED.toString());
                } else {
                    bankTransferCreated = true;
                    addNewTask(entity, ClientRefundTodoType.BANK_TRANSFER_CREATED.toString());
                }

                if (entity.getMethodOfPayment().equals(ClientRefundPaymentMethod.CREDIT_CARD)) {
                    toDos = confirmationToDoRepository.findTodosByPayment(entity.getRelatedPaymentId());
                    linkRelatedPayment(entity, purpose, toDos);
                }
                break;
            case REJECT:
                entity.setStopped(true);
                entity.setCompleted(true);
                entity.setStatus(ClientRefundStatus.REJECTED);
                entity.setStatusChangeDate(new java.sql.Date(new java.util.Date().getTime()));

                Setup.getApplicationContext()
                        .getBean(ClientRefundService.class)
                            .sendEmailForRejectedRefundRequests(entity);
                break;
            default:
                throw new RuntimeException("Manager Action has unexpected value");
        }


        if (bankTransferCreated) {
            PayrollAccountantTodo payrollAccountantTodo = new PayrollAccountantTodo();
            payrollAccountantTodo.setClientRefundToDo(entity);
            Setup.getApplicationContext().getBean(PayrollAccountantTodoController.class)
                    .createEntity(payrollAccountantTodo);
        } else if (creditCardTransferCreated) {
            if (entity.isForceUpdateTransferRefOfPayment()) {
                if (!toDos.isEmpty() && (toDos.get(0).getTransferReference() == null || toDos.get(0).getTransferReference().isEmpty())) {
                    toDos.get(0).setTransferReference(entity.getTransferReference());
                    confirmationToDoRepository.saveAndFlush(toDos.get(0));
                }
            }

            if (!Setup.getApplicationContext()
                    .getBean(ClientRefundService.class)
                    .approveCreditCardRefund(entity)) return;
        }

        entity.setManagerActionBy(CurrentRequest.getUser());
        super.onDone(entity);
    }

    @Override
    public void postDone(ClientRefundToDo entity) {
    }

    @Override
    public List<String> getTaskHeader() {
        return Arrays.asList("client.label", "purpose.label", "methodOfPayment.label", "amount", "detail", "transferReference");
    }

    public boolean checkUserAction(ClientRefundSetup setup) {
        boolean result = false;
        User approval = setup.getApprovedBy();
        if (approval != null) {
            if (approval.getId().equals(CurrentRequest.getUser().getId())) {
                result = true;
            }
        }
        return result;
    }

    private void linkRelatedPayment(ClientRefundToDo entity, PaymentRequestPurpose purpose, List<ContractPaymentConfirmationToDo> toDos ) {
        if (toDos.isEmpty()) return;

        logger.info("refund todo id : " + entity.getId());
        Payment relatedPayment;
        List<Payment> payments = Setup.getRepository(PaymentRepository.class)
                .findPaymentsByTodo(toDos.get(0).getId());

        if (payments == null || payments.isEmpty()) return;
        logger.info("payments ids  : " + payments.stream().map(x -> x.getId()).collect(Collectors.toList()));

        String purposePaymentTypeCode = purpose.getTypeOfPayment().hasTag("refund_for") ?
                purpose.getTypeOfPayment().getTagValue("refund_for").getValue() : null;
        List<Payment> typeMatchedPayments = payments.stream()
                .filter(p -> purposePaymentTypeCode != null &&
                        p.getTypeOfPayment().getCode().equals(purposePaymentTypeCode))
                .collect(Collectors.toList());

        logger.info("purposePaymentTypeCode : " + purposePaymentTypeCode);
        logger.info("typeMatchedPayments ids  : " + typeMatchedPayments.stream().map(x -> x.getId()).collect(Collectors.toList()));

        if (!typeMatchedPayments.isEmpty()) {
            if (typeMatchedPayments.size() == 1) {
                relatedPayment = typeMatchedPayments.get(0);
                logger.info("same type -> link relatedPayment id : " + relatedPayment.getId());
            } else {
                logger.info("payment idس : " + (typeMatchedPayments.stream()
                        .anyMatch(p -> entity.getAmount().equals(p.getAmountOfPayment())) ? typeMatchedPayments.stream()
                        .filter(p -> entity.getAmount().equals(p.getAmountOfPayment()))
                        .findFirst().get().getId() : null));

                relatedPayment = typeMatchedPayments.stream()
                        .filter(p -> entity.getAmount().equals(p.getAmountOfPayment()))
                        .findFirst()
                        .orElse(typeMatchedPayments.get(0));
                logger.info("same type -> more than payment found -> link relatedPayment id : " + relatedPayment.getId());
            }
        } else {
            logger.info("same type payment not found -> try to link with same amount, refund id :" + entity.getId());
            logger.info("payment id : " + (payments.stream()
                    .anyMatch(p -> entity.getAmount().equals(p.getAmountOfPayment())) ? payments.stream()
                    .filter(p -> entity.getAmount().equals(p.getAmountOfPayment()))
                    .findFirst().get().getId() : null));
            relatedPayment = payments.stream()
                    .filter(p -> entity.getAmount().equals(p.getAmountOfPayment()))
                    .findFirst()
                    .orElse(payments.get(0));
        }

        if (relatedPayment != null) {
            entity.setRelatedPaymentId(relatedPayment.getId());
        }
    }
}