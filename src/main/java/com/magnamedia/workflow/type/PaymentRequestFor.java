package com.magnamedia.workflow.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Sep 15, 2019
 * ACC-837
 */
public enum PaymentRequestFor implements LabelValueEnum {
    
    HOUSEMAID("Housemaid"), 
    CLIENT("Client");

    private final String label;

    private PaymentRequestFor(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
