package com.magnamedia.workflow.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Dec 06, 2020
 *         Jirra ACC-2845
 */
public enum ClientRefundStatus implements LabelValueEnum {
    PENDING("Pending"),
    REJECTED("Rejected"),
    PAID("Paid"),
    STOPPED("Stopped");

    private final String label;

    ClientRefundStatus(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
