package com.magnamedia.workflow.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Dec 06, 2020
 *         Jirra ACC-2845
 */
public enum ClientRefundPaymentMethod implements LabelValueEnum {
    CASH("Cash", true),
    MONEY_TRANSFER("Money Transfer", true),
    BANK_TRANSFER("Bank Transfer"),
    CREDIT_CARD("Credit Card");

    private final String label;
    private final boolean deprecated;

    @Override
    public String getLabel() {
        return label;
    }


    ClientRefundPaymentMethod(String label, boolean deprecated) {
        this.label = label; this.deprecated = deprecated;
    }

    ClientRefundPaymentMethod(String label) {
        this.label = label; this.deprecated = false;
    }

    public boolean isDeprecated() {
        return deprecated;
    }
}
