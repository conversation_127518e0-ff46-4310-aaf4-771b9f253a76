package com.magnamedia.scheduledjobs;

import com.magnamedia.controller.ContractPaymentTermController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.AccountingEntityProperty;
import com.magnamedia.entity.Contract;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.repository.AccountingEntityPropertyRepository;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.joda.time.DateTime;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.Map;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jan 06, 2021
 *         ACC-1689
 * 
 */

public class SwitchingNationalityFromFilipinoEmailSJ implements MagnamediaJob {
    private static final Logger logger = Logger.getLogger(SwitchingNationalityFromFilipinoEmailSJ.class.getName());


    private ContractPaymentTermController cptCtrl;
    private AccountingEntityPropertyRepository accountingEntityPropertyRepository;

    public SwitchingNationalityFromFilipinoEmailSJ() {
        cptCtrl = Setup.getApplicationContext().getBean(ContractPaymentTermController.class);
        accountingEntityPropertyRepository = Setup.getRepository(AccountingEntityPropertyRepository.class);
    }

    @Override
    public void run(Map<String, ?> map) {
        this.runJob();
    }

    private void runJob() {
        logger.info("Job Started");

        Page<AccountingEntityProperty> page;
        int index = 0;
        do {
            page = accountingEntityPropertyRepository.findByKeyAndOriginTypeAndIsDeletedFalse(Contract.DOWNGRADING_NATIONALITY_DATE, Contract.class.getSimpleName(), PageRequest.of(index++, 100));

            for (AccountingEntityProperty a : page.getContent()) {
                Contract contract = (Contract) a.getOrigin();

                if (a.getValue() == null) continue;
                if (!contract.isActive()) {
                    logger.info("contract not active");
                    a.setIsDeleted(true);
                    accountingEntityPropertyRepository.save(a);
                    continue;
                }

                try {
                    DateTime replacementDate = new DateTime(DateUtil.parseDateDashed(a.getValue()));
                    DateTime now = DateTime.now();
                    logger.info("replacementDate: " + replacementDate.toString("yyyy-MM-dd"));

                    if (replacementDate.getMonthOfYear() == now.getMonthOfYear()) {
                        logger.info("Month of switching date = Current month, -> do nothing");
                        continue;
                    }

                    logger.info("sending amending DDs email, Contract#" + contract.getId());
                    boolean emailSentSuccessfully = cptCtrl.sendSwitchNationalityAmendingDirectDebitFormsEmail(
                            contract.getId(), null);
                    logger.info("Email Sending Result: " + emailSentSuccessfully);
                } catch (Exception e) {
                    logger.severe("Exception while processing Switching Nationality Email From F, Contract#" + contract.getId());
                    logger.severe("Exception: " + ExceptionUtils.getStackTrace(e));
                }
            }
        } while (page.hasNext());
    }
}