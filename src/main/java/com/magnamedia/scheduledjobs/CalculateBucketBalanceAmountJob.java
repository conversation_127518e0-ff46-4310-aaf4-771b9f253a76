package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.Bucket;
import com.magnamedia.service.AccountBalanceService;
import org.joda.time.LocalDateTime;

import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class CalculateBucketBalanceAmountJob implements MagnamediaJob {
    private static final Logger logger = Logger.getLogger(CalculateBucketBalanceAmountJob.class.getName());

    @Override
    public void run(Map<String, ?> map) {
        logger.log(Level.INFO, "Job started time : {0}", new LocalDateTime());
        AccountBalanceService accountBalanceService = Setup.getApplicationContext().getBean(AccountBalanceService.class);

        List<Bucket> buckets = new SelectQuery(Bucket.class).execute();

        buckets.forEach(bucket -> {
            logger.log(Level.INFO, "Bucket id : {0}", bucket.getId());
            accountBalanceService.addAccountBalance(bucket);
        });

        logger.log(Level.INFO, "Job finished time : {0}", new LocalDateTime());
    }
}