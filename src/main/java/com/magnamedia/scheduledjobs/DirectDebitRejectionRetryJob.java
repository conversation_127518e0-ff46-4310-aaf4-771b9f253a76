package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.helper.BackgroundTaskHelper;
import com.magnamedia.module.type.DirectDebitRejectionToDoType;
import com.magnamedia.repository.DirectDebitRejectionToDoRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Feb 29, 2020
 *         Jirra ACC-1435
 */

public class DirectDebitRejectionRetryJob implements MagnamediaJob {

    @Override
    public void run(Map<String, ?> map) {
        generateNewDDsToRetry();
    }

    public void generateNewDDsToRetry() {

        DirectDebitRejectionToDoRepository repository = Setup.getRepository(DirectDebitRejectionToDoRepository.class);

        List<String> taskNames = new ArrayList<>();
        taskNames.add(DirectDebitRejectionToDoType.WAITING_RE_SCHEDULE_B.toString());
        Page<DirectDebitRejectionToDo> page;
        int pageIndex = 0;
        List<Long> processedIDs = new ArrayList();
        processedIDs.add(-1L);
        do {
            page = repository.findByTaskNameInAndIdNotIn(taskNames, processedIDs, PageRequest.of(pageIndex, 100));
            List<DirectDebitRejectionToDo> allInWaitingReschedule = page.getContent();

            for (DirectDebitRejectionToDo directDebitRejectionToDo : allInWaitingReschedule) {
                try {
                    processedIDs.add(directDebitRejectionToDo.getId());
                    BackgroundTaskHelper.createBackGroundTask(DirectDebitRejectionRetryJob.class.getName(),
                            "directDebitRejectionToDoController", "processWaitingReScheduleStepForDDB",
                            directDebitRejectionToDo, BackgroundTaskQueues.NormalOperationsQueue);
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.log(Level.SEVERE, "DirectDebitRejectionRetryJob exception happened");
                }
            }
        } while (page.hasNext());
    }


}
