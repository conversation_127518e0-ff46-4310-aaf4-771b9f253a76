//
//package com.magnamedia.scheduledjobs;
//
//import com.magnamedia.core.Setup;
//import com.magnamedia.core.helper.SelectQuery;
//import com.magnamedia.core.schedule.MagnamediaJob;
//import com.magnamedia.entity.Housemaid;
//import com.magnamedia.helper.DateUtil;
//import com.magnamedia.module.type.HousemaidType;
//import com.magnamedia.repository.HousemaidRepository;
//
//import java.util.Date;
//import java.util.List;
//import java.util.Map;
//import java.util.logging.Level;
//import java.util.logging.Logger;
//
///**
// *
// * <AUTHOR> <PERSON>z
// * @reason: PAY-63
// */
//public class MaidVisaSalaryHoldJob implements MagnamediaJob {
//    
//    private static final Logger LOGGER
//            = Logger.getLogger(MaidVisaSalaryHoldJob.class.getName());
//    
//    private HousemaidRepository housemaidRepository;
//   
//    public MaidVisaSalaryHoldJob(){
//     housemaidRepository = Setup.getRepository(HousemaidRepository.class);
//    }
//
//    @Override
//    public void run(Map<String, ?> map) {
//        logger.log(Level.SEVERE, "MaidVisaSalaryHoldJob running.");
//
//        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
//        query.filterBy("replacementSalaryStartDate", ">=", DateUtil.getDayStart(new Date())).
//                and("replacementSalaryStartDate", "<=", DateUtil.getDayEnd(new Date())).
//                and("housemaidType", "=", HousemaidType.MAID_VISA)
//                .and("excludedFromPayroll", "=", true);
//
//        List<Housemaid> housemaids = query.execute();
//        if (housemaids.size() > 0) {
//            housemaids.forEach(housemaid -> {
//                housemaid.setExcludedFromPayroll(false);
//            });
//
//            housemaidRepository.save(housemaids);
//        }
//    }
//}
