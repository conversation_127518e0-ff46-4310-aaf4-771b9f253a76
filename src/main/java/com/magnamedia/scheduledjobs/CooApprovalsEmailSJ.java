package com.magnamedia.scheduledjobs;


import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.service.MessagingService;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Feb 29, 2020
 *         Jirra ACC-1435
 */

public class CooApprovalsEmailSJ implements MagnamediaJob {

    @Override
    public void run(Map<String, ?> map) {

        Map<String, String> parameters = new HashMap();
        parameters.put("link", Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_FRONT_END_URL) +
                "#!/accounting/" + "coo-control?tab=COO_SCREEN");

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff("pending_coo_approval",
                        parameters, Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_REMINDING_THE_COO_OF_APPROVAL_TASKS),
                        "Required COO Approvals");
    }
}
