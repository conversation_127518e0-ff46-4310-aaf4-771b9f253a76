package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PushNotification;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.ContractPaymentConfirmationToDo;
import com.magnamedia.repository.ContractPaymentConfirmationToDoRepository;
import com.magnamedia.repository.DisablePushNotificationRepository;
import com.magnamedia.service.MessagingService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;
import java.util.Map;
import java.util.logging.Logger;
import java.util.stream.Collectors;

public class FlowProcessorMainJob implements MagnamediaJob {
    private final Logger logger = Logger.getLogger(FlowProcessorMainJob.class.toString());

    private ContractPaymentConfirmationToDoRepository contractPaymentConfirmationToDoRepository;
    private DisablePushNotificationRepository disablePushNotificationRepository;
    private MessagingService messagingService;

    @Override
    public void run(Map<String, ?> parameters) {
        logger.info("started");
        contractPaymentConfirmationToDoRepository = Setup.getRepository(ContractPaymentConfirmationToDoRepository.class);
        disablePushNotificationRepository = Setup.getRepository(DisablePushNotificationRepository.class);
        messagingService = Setup.getApplicationContext().getBean(MessagingService.class);

        disableConfirmationToDoUponFlowFinished();

        logger.info("end");
    }

    private void disableConfirmationToDoUponFlowFinished() {
        Long lastId = -1L;
        Page<ContractPaymentConfirmationToDo> p = contractPaymentConfirmationToDoRepository
                .findToDoWithFinishedFlows(lastId, PageRequest.of(0, 200));

        while (!p.isEmpty()) {
            p.getContent()
                    .forEach(todo -> {
                        try {
                            logger.info("todo id: " + todo.getId());
                            List<PushNotification> notifications = disablePushNotificationRepository
                                    .findActiveNotificationsByOwner(todo.getId(), todo.getEntityType());

                            messagingService.createDisableNotificationBGT(
                                    notifications.stream().map(BaseEntity::getId).collect(Collectors.toList()),
                                    "Disable notifications on flow finished");

                            todo.setDisabled(true);
                            contractPaymentConfirmationToDoRepository.silentSave(todo);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    });

            lastId = p.getContent().get(p.getContent().size() - 1).getId();

            p = contractPaymentConfirmationToDoRepository
                    .findToDoWithFinishedFlows(lastId, PageRequest.of(0, 200));
        }
    }
}