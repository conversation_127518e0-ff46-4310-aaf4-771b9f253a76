package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.entity.Contract;
import com.magnamedia.helper.BackgroundTaskHelper;
import com.magnamedia.repository.ContractRepository;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Dec 19, 2019
 *         ACC-1241
 */

public class ReferralDiscountScheduledJob implements MagnamediaJob {

    private ContractRepository contractRepository;

    private static final Logger logger =
            Logger.getLogger(ReferralDiscountScheduledJob.class.getName());

    public ReferralDiscountScheduledJob() {
        contractRepository = Setup.getRepository(ContractRepository.class);
    }

    @Override
    public void run(Map<String, ?> map) {
        this.createClientPaymentRequests();
    }

    public void createClientPaymentRequests() {

        List<Long> processedContractsIDs = new ArrayList();
        processedContractsIDs.add(-1L);

        Page<Contract> page;
        int pageIndex = 0;
        do {
            page = contractRepository.findByDiscountCodeNotNullAndClientNotNullAndAttachmentsTagEquals("contract_signature", processedContractsIDs, PageRequest.of(pageIndex, 100));
            List<Contract> contracts = page.getContent();
            for (Contract contract : contracts) {
                try {
                    processedContractsIDs.add(contract.getId());
                    BackgroundTaskHelper.createBackGroundTask(IncompleteDDInfoSJ.class.getName(),
                            "jobExecutionService", "runReferralDiscountScheduledJob", contract, BackgroundTaskQueues.NormalOperationsQueue);
                } catch (Exception e) {
                    logger.log(Level.SEVERE, "exception while running on Contract#" + contract.getId());
                    logger.log(Level.SEVERE, "Exception: " + ExceptionUtils.getStackTrace(e));
                }
            }
        } while (page.hasNext());
    }
}
