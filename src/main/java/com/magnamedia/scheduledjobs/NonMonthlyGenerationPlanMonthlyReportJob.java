package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.DirectDebitGenerationPlan;
import com.magnamedia.entity.projection.DirectDebitGenerationPlanMonthlyReportProjection;
import com.magnamedia.extra.DirectDebitGenerationPlanTemplate;
import com.magnamedia.helper.CsvHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.service.DirectDebitGenerationPlanService;
import com.magnamedia.service.MessagingService;
import org.joda.time.LocalDate;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;

public class NonMonthlyGenerationPlanMonthlyReportJob implements MagnamediaJob {

    private DirectDebitGenerationPlanService directDebitGenerationPlanService;

    @Override
    public void run(Map<String, ?> parameters) {
        logger.info( "Start");
        directDebitGenerationPlanService = Setup.getApplicationContext().getBean(DirectDebitGenerationPlanService.class);

        sendReport();

        logger.info( "Finish");
    }

    private void sendReport() {
        try {
            String emails = Setup.getParameter(Setup.getCurrentModule(),
                    AccountingModule.PARAMETER_NON_MONTHLY_POSTPONED_DDS_GENERATION_REPORT_RECIPIENT);
            if (emails == null || emails.isEmpty()) return;

            directDebitGenerationPlanService.updatePlansUponActiveCptChanged();

            SelectQuery<DirectDebitGenerationPlan> q = new SelectQuery<>(DirectDebitGenerationPlan.class);
            q.filterBy("ddGenerationPlanStatus", "=", DirectDebitGenerationPlan.DdGenerationPlanStatus.PENDING);
            q.filterBy("ddCloned", "is null", null);
            q.filterBy("ddID", "is null", null);
            q.filterBy("contractPaymentType.type.code", "<>", "monthly_payment");
            q.filterBy("contract.status", "not in", Arrays.asList(ContractStatus.CANCELLED, ContractStatus.EXPIRED));
            q.filterBy("ddGenerationDate", ">=",
                    java.sql.Date.valueOf(new LocalDate().plusMonths(1).dayOfMonth().withMinimumValue().toString("yyyy-MM-dd")));
            q.filterBy("ddGenerationDate", "<=",
                    java.sql.Date.valueOf(new LocalDate().plusMonths(1).dayOfMonth().withMaximumValue().toString("yyyy-MM-dd")));

            List<DirectDebitGenerationPlan> l = q.execute();

            if (l.isEmpty()) return;

            logger.info( "l size: " + l.size());

            String nextMonth = new LocalDate().plusMonths(1).toString("MMMM yyyy");

            String[] namesOrdered = { "clientId", "clientName", "contractId", "contractStartDate",
                    "amount", "ddType", "ddSendDate", "cptName" };

            String[] headers = {"Client ID", "Client Name", "Contract ID", "Contract start date",
                    "Amount Of DD", "Type of DD ", "DD start date ", "CPT Name"};

            File file = CsvHelper.generateCsv(l, DirectDebitGenerationPlanMonthlyReportProjection.class,
                    headers, namesOrdered,
                    "Scheduled Postponed DDs for the month of " + nextMonth, ".csv");

            Setup.getApplicationContext()
                    .getBean(MessagingService.class)
                    .sendEmailToOfficeStaffWithAttachments(DirectDebitGenerationPlanTemplate.NON_MONTHLY_POSTPONED_DDS_GENERATION_REPORT.toString(),
                            new HashMap<>(), emails, Collections.singletonList(Storage.storeTemporary(file.getName(),
                                    new FileInputStream(file), null, false)),
                            "Scheduled Postponed DDs for the month of " + nextMonth);

        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}