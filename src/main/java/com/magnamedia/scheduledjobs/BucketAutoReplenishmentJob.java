package com.magnamedia.scheduledjobs;

import com.magnamedia.controller.BucketReplenishmentController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.Bucket;
import com.magnamedia.module.type.BucketType;
import com.magnamedia.repository.BucketReplenishmentTodoRepository;
import com.magnamedia.repository.BucketRepository;
import com.magnamedia.service.AccountBalanceService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Nov 20, 2021
 */
public class BucketAutoReplenishmentJob implements MagnamediaJob {
    
    BucketRepository bucketRepository;
    AccountBalanceService balanceService;
    BucketReplenishmentController bucketReplenishmentController;
    BucketReplenishmentTodoRepository bucketReplenishmentTodoRepo;
    
    public BucketAutoReplenishmentJob(){
        
        bucketRepository = Setup.getRepository(BucketRepository.class);
        balanceService = Setup.getApplicationContext().getBean(AccountBalanceService.class);
        bucketReplenishmentController = Setup.getApplicationContext().getBean(BucketReplenishmentController.class);
        bucketReplenishmentTodoRepo = Setup.getRepository(BucketReplenishmentTodoRepository.class);
    }
    
    private static final Logger logger =
            Logger.getLogger(BucketAutoReplenishmentJob.class.getName());
    
    @Override
    public void run(Map<String, ?> map) {
        this.runJob();
    }
    
    @Transactional
    private void runJob() {
        Page<Bucket> page;
        Integer pageIndex = 0;
        
        do {
            page = bucketRepository.findByBucketTypeInAndIsActiveAndAutoReplenishment(
                    Arrays.asList(BucketType.CASH_BOX,BucketType.CREDIT_CARD),
                    true, true, PageRequest.of(pageIndex++, 100));
            
            logger.log(Level.INFO, "BucketAutoReplenishmentJob page index: " + pageIndex);
            logger.log(Level.INFO, "BucketAutoReplenishmentJob page: " + page.getNumber());
            logger.log(Level.INFO, "BucketAutoReplenishmentJob page elements: " + page.getNumberOfElements());
            
            for(Bucket bucket : page.getContent()){
                try{
                    Long numOfPendingToDos = bucketReplenishmentTodoRepo.countByBucketAndApprovedAndTransactionAddedFalseOrNotApprovedAndNotStoppedAndNotCompleted(bucket);
                    //                     Double bucketBalance = balanceService.getBucketBalanceAmount(bucket, new Date(), null);
                    bucket = balanceService.setBucketBalanceBasedOnTransaction(bucket);

                    if ((bucket != null && bucket.getBucketType() != null && bucket.getAutoReplenishment() != null &&
                            (bucket.getBucketType() == BucketType.CREDIT_CARD || bucket.getBucketType() == BucketType.CASH_BOX)
                            && bucket.getAutoReplenishment()
                            && bucket.getReplenishmentLevel() != null
                            && bucket.getBalance() < bucket.getReplenishmentLevel()
                            && bucket.getIsActive())
                            && numOfPendingToDos.equals(0L)) {
                        bucketReplenishmentController.createBucketReplenishmentRequestIfNeeded(bucket, bucket.getBalance());
                    }
                } catch(Exception ex){
                    logger.log(Level.SEVERE, "BucketAutoReplenishmentJob: " + ex.getMessage());
                    ex.printStackTrace();
                }
            }
        } while (page.hasNext());
    }
}
