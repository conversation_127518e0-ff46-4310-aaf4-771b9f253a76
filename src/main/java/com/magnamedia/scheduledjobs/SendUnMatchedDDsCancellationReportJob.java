package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.AccountingEntityProperty;
import com.magnamedia.entity.BankDirectDebitCancelationFile;
import com.magnamedia.extra.JobUtils;
import com.magnamedia.extra.Utils;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.AccountingEntityPropertyRepository;
import com.magnamedia.service.MessagingService;
import org.joda.time.LocalDateTime;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 * created on 2025-03-24
 * ACC-9005
 */
public class SendUnMatchedDDsCancellationReportJob implements MagnamediaJob {

    private static final Logger logger = Logger.getLogger(AccountingModuleMainJob.class.getName());
    private static final String ACCOUNTING_PROPERTY_UNMATCHED_DDS_JOB_TIME = "accounting_property_unmatched_dds_job_time";

    @Override
    public void run(Map<String, ?> parameters) {
        AccountingEntityProperty accountingEntityProperty = Setup.getRepository(AccountingEntityPropertyRepository .class)
                .findByKeyAndIsDeletedFalse(ACCOUNTING_PROPERTY_UNMATCHED_DDS_JOB_TIME);

        MessagingService messagingService = Setup.getApplicationContext().getBean(MessagingService.class);
        Date lastRunDate = JobUtils.getJobLastRunDate(accountingEntityProperty, DateUtil.now());
        JobUtils.setJobLastRunDate(accountingEntityProperty, ACCOUNTING_PROPERTY_UNMATCHED_DDS_JOB_TIME, new LocalDateTime());

        SelectQuery<BankDirectDebitCancelationFile> query = new SelectQuery<>(BankDirectDebitCancelationFile.class);
        query.filterBy("date", ">=", lastRunDate);
        query.filterBy("hidden", "=", false);
        List<BankDirectDebitCancelationFile> recentFiles = query.execute();

        if (recentFiles.isEmpty()) return;

        StringBuilder reportsHtml = new StringBuilder();
        int reportCount = 1;

        for (BankDirectDebitCancelationFile file : recentFiles) {
            String q = "select count(b.id) from BankDirectDebitCancelationRecord b " +
                    "left join b.directDebitFile ddf " +
                    "where b.bankDirectDebitCancelationFile.id = :fileId and (ddf is null or ddf.ddStatus not in ('PENDING_FOR_CANCELLATION', 'CANCELED'))";

            long unmatchedCount = new SelectQuery<>(q, "", Long.class,
                    new HashMap<String, Object>() {{
                        put("fileId", file.getId());
                    }})
                    .execute().get(0);
            logger.info("unmatchedCount : " + unmatchedCount + " for file : " + file.getId());

            if (unmatchedCount > 0) {

                reportsHtml.append("<ul>");
                reportsHtml
                        .append("<li>Report ").append(reportCount).append(" name: ").append(file.getAttachments().isEmpty() ?
                                "No Attachment" : file.getAttachments().get(0).getName()).append("</li>")
                        .append("<li>Number of unmatched DDs: ").append(unmatchedCount).append("</li>")
                        .append("<li>Details Link: <a href='").append(Setup.getApplicationContext()
                                .getBean(Utils.class)
                                .shorteningUrl(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_FRONT_END_URL) +
                                        "#!/accounting/importing-bank-dd-cancellation-file/" + file.getId())).append("'>click here</a></li>");
                reportsHtml.append("</ul></br>");

                reportCount++;
            }
        }

        if (reportCount > 1) {
            messagingService.sendEmailToOfficeStaff("unmatched_dd_cancellation_report",
                    new HashMap<String, String>() {{
                        put("reports", reportsHtml.toString());
                    }},
                    Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DDS_CANCELLATION_REPORT_RPA_MAIL),
                    "Unmatched Cases in DD 400 Cancellation Report");
        }
    }
}