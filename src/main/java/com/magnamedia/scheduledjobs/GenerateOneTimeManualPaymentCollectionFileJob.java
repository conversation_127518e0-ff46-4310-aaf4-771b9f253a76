package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.service.SendingApprovedManualDDToBankService;

import java.util.Map;

public class GenerateOneTimeManualPaymentCollectionFileJob implements MagnamediaJob {

    @Override
    public void run(Map<String, ?> parameters) {
        Setup.getApplicationContext().getBean(SendingApprovedManualDDToBankService.class)
                .sendCollectionFile(PaymentStatus.PDC);
    }
}