package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.CooExpenseNotification;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.repository.CooExpenseNotificationRepository;

import java.util.Date;
import java.util.List;
import java.util.Map;

public class CooExpenseNotificationDismissJob implements MagnamediaJob {


    @Override
    public void run(Map<String, ?> map) {
        dismiss24HourNotifications();
    }

    public void dismiss24HourNotifications() {
        List<CooExpenseNotification> notifications = get24HoursActiveNotifications();
        CooExpenseNotificationRepository cooExpenseNotificationRepository = Setup.getRepository(CooExpenseNotificationRepository.class);
        for (CooExpenseNotification cooExpenseNotification:notifications) {
            cooExpenseNotification.setHidden(true);
            cooExpenseNotificationRepository.save(cooExpenseNotification);
        }
    }

    public List<CooExpenseNotification> get24HoursActiveNotifications(){
        SelectQuery<CooExpenseNotification> query = new SelectQuery<>(CooExpenseNotification.class);
        Date now = new Date();
        Date before24Hour = DateUtil.addDays(now ,-1);
        query.filterBy(new SelectFilter("hidden" , "=" , false)
                .or("hidden", "IS NULL", null));
        query.filterBy("creationDate", "<=" ,before24Hour);
        return query.execute();
    }
}
