package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.service.SalesBinderService;

import java.util.Map;

/**
 * <PERSON> (Jan 26, 2021)
 */
//0 53 0/1 ? * * *
public class SalesBinderUpdateDataJob implements MagnamediaJob {
    @Override
    public void run(Map<String, ?> map) {
        try {
            runJob();
        } catch (Exception ex) {
            throw ex;
        }
    }

    public void runJob() {
        SalesBinderService salesBinderService = Setup.getApplicationContext().getBean(SalesBinderService.class);

        salesBinderService.updateSuppliers();
        salesBinderService.updateCategories();
        salesBinderService.updateItems();
    }
}
