package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseAdditionalInfo;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.Contract;
import com.magnamedia.extra.CcNotificationTemplateCode;
import com.magnamedia.extra.MvNotificationTemplateCode;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.service.BouncingFlowService;
import com.magnamedia.service.DirectDebitService;
import com.magnamedia.service.MessagingService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.*;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 * @created 26/04/2025 - 3:03 PM
 * ACC-9011
 * @implNote  Daily job that runs at 7 AM to check for cancelled contracts with active direct debits
 * due to overdue payments and send notifications to clients who haven't received this notification before.
 */
public class CancelledContractWithActiveDDJob implements MagnamediaJob {

    private static final Logger logger = Logger.getLogger(CancelledContractWithActiveDDJob.class.getName());

    private final ContractRepository contractRepository;
    private final BouncingFlowService bouncingFlowService;
    private final MessagingService messagingService;

    public CancelledContractWithActiveDDJob() {
        contractRepository = Setup.getRepository(ContractRepository.class);
        bouncingFlowService = Setup.getApplicationContext().getBean(BouncingFlowService.class);
        messagingService = Setup.getApplicationContext().getBean(MessagingService.class);
    }

    @Override
    public void run(Map<String, ?> parameters) {
        logger.info("Starting");

        List<Long> handledContractsActiveDDs = new ArrayList<>();
        processContractsCanceledWithOverduePayments(handledContractsActiveDDs);
        processContractsPendingDDsAfterCancellation(handledContractsActiveDDs);

        logger.info("Completed");
    }

    private void processContractsCanceledWithOverduePayments(List<Long> handledContractsActiveDDs) {
        Page<Contract> l;
        Long lastId = -1L;
        try {
            do {
                // Get All cancelled contracts that have not sent the active dd messages and
                //    has active direct debits (DDs) due to overdue payments.
                l = contractRepository.findAllByActiveDdAndNotReceivedAboutPaymentDeductionBefore(
                        lastId,
                        Arrays.asList(ContractStatus.CANCELLED, ContractStatus.EXPIRED),
                        DirectDebitService.notAllowedStatusesWithNotApplicable, DirectDebitService.notAllowedStatuses,
                        Contract.ADDITIONAL_INFO_NOTIFY_ABOUT_OVERDUE_PAYMENTS_AFTER_CANCELLATION,
                        PageRequest.of(0, 200));

                l.getContent().forEach(contract -> {
                    try {
                        logger.info("contract id: " + contract.getId());
                        if (!bouncingFlowService.doWeWantMoneyFromClient(contract, null)) {
                            logger.info("We Want Money From Client -> exiting");
                            return;
                        }
                        handledContractsActiveDDs.add(contract.getId());
                        sendMessage(
                                contract,
                                contract.isMaidCc() ?
                                        CcNotificationTemplateCode.CC_INFORM_CANCELED_CLIENTS_WITH_OVERDUE_PAYMENTS_WA.toString() :
                                        MvNotificationTemplateCode.MV_INFORM_CANCELED_CLIENTS_WITH_OVERDUE_PAYMENTS_WA.toString(),
                                Contract.ADDITIONAL_INFO_NOTIFY_ABOUT_OVERDUE_PAYMENTS_AFTER_CANCELLATION);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });

                if (!l.getContent().isEmpty()) {
                    lastId = l.getContent().get(l.getContent().size() - 1).getId();
                }
            } while (!l.getContent().isEmpty());
        } catch (Exception e) {
            e.printStackTrace();
        }
        logger.info("finish");
    }

    private void processContractsPendingDDsAfterCancellation(List<Long> handledContractsActiveDDs) {

        Page<Contract> l;
        Long lastId = -1L;
        try {
            do {

                // Get all cancelled contracts that have not sent any messages (active dd or pending-sent) and
                //   has at least one direct debit (DD) with the status pending_sent to bank.
                l = contractRepository.findAllByPendingDDsAndNotReceivedAboutPaymentDeduction(
                        lastId,
                        handledContractsActiveDDs,
                        Arrays.asList(ContractStatus.CANCELLED, ContractStatus.EXPIRED),
                        Contract.ADDITIONAL_INFO_NOTIFY_PENDING_DD_AFTER_CANCELLATION,
                        PageRequest.of(0, 200));

                l.getContent().forEach(contract -> {
                    try {
                        logger.info("contract id: " + contract.getId());
                        if (bouncingFlowService.doWeWantMoneyFromClient(contract, null)) {
                            logger.info("We Want Money From Client -> exiting");
                            return;
                        }
                        sendMessage(
                                contract,
                                contract.isMaidCc() ?
                                CcNotificationTemplateCode.CC_INFORM_CLIENTS_ABOUT_PENDING_DIRECT_DEBITS_AFTER_CANCELLATION_WA.toString() :
                                MvNotificationTemplateCode.MV_INFORM_CLIENTS_ABOUT_PENDING_DIRECT_DEBITS_AFTER_CANCELLATION_WA.toString(),
                                Contract.ADDITIONAL_INFO_NOTIFY_PENDING_DD_AFTER_CANCELLATION);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });

                if (!l.getContent().isEmpty()) {
                    lastId = l.getContent().get(l.getContent().size() - 1).getId();
                }
            } while (!l.getContent().isEmpty());
        } catch (Exception e) {
            e.printStackTrace();
        }
        logger.info("finish");
    }

    private void sendMessage(Contract contract, String templateName, String additionalInfoKey) {
        logger.info("Contract ID: " + contract);

        messagingService.sendMessageToClient(
                contract,
                new HashMap<>(),
                new HashMap<>(),
                contract.getId(),
                contract.getEntityType(),
                TemplateUtil.getTemplate(templateName));

        contract.addBaseAdditionalInfo(new BaseAdditionalInfo(
                additionalInfoKey,
                "true",
                contract.getEntityType(),
                contract.getId()));
        contractRepository.silentSave(contract);
    }
}