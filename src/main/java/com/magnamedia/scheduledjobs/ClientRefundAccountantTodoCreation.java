package com.magnamedia.scheduledjobs;


import com.magnamedia.core.Setup;
import com.magnamedia.core.mail.TextEmail;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.repository.ClientRefundTodoRepository;
import com.magnamedia.service.ClientRefundService;
import com.magnamedia.workflow.type.ClientRefundStatus;
import com.magnamedia.workflow.type.ClientRefundTodoType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Arrays;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <PERSON>
 * Jirra ACC-2826
 */

public class ClientRefundAccountantTodoCreation implements MagnamediaJob {

    private ClientRefundTodoRepository clientRefundTodoRepository;
    private ClientRefundService clientRefundService;

    public ClientRefundAccountantTodoCreation() {
        clientRefundService = Setup.getApplicationContext().getBean(ClientRefundService.class);
        clientRefundTodoRepository = Setup.getRepository(ClientRefundTodoRepository.class);
    }

    private static final Logger logger =
            Logger.getLogger(ClientRefundAccountantTodoCreation.class.getName());

    @Override
    public void run(Map<String, ?> map) {
        this.runJob();
    }

    private void runJob() {
        Page<ClientRefundToDo> page;
        Integer pageIndex = 0;

        do {
            page = clientRefundTodoRepository.findPendingForCreationAccountantTodo(PageRequest.of(pageIndex++, 100),
                    Arrays.asList(ClientRefundTodoType.BANK_TRANSFER_CREATED.toString(),
                                ClientRefundTodoType.CREDIT_CARD_TRANSFER_CREATED.toString()),
                    ClientRefundStatus.PENDING);

            logger.log(Level.INFO, "ClientRefundAccountantTodoCreation page index: " + pageIndex);
            logger.log(Level.INFO, "ClientRefundAccountantTodoCreation page: " + page.getNumber());
            logger.log(Level.INFO, "ClientRefundAccountantTodoCreation page elements: " + page.getNumberOfElements());


            for (ClientRefundToDo clientRefundToDo : page.getContent()) {

                try {
                    double amount = 0D;

                    if (clientRefundToDo.getNumberOfMonthlyPayments() > 0) {

                        int numberOfAccountantTodos = clientRefundTodoRepository.findByParent(clientRefundToDo).size();

                        if (numberOfAccountantTodos < clientRefundToDo.getNumberOfMonthlyPayments()) {
                            amount = clientRefundToDo.getAmount() / clientRefundToDo.getNumberOfMonthlyPayments() * 1.0;
                            clientRefundService.createChildTodo(clientRefundToDo, amount);
                            //after create action in clientRefundTodo will create the accountantTodo
                        }
                    }

                } catch (Exception e) {

                    PrintWriter pw = new PrintWriter(System.out);
                    StringWriter sw = new StringWriter();
                    PrintWriter pw1 = new PrintWriter(sw);
                    e.printStackTrace(pw);
                    e.printStackTrace(pw1);

                    logger.log(Level.SEVERE, "ClientRefundAccountantTodoCreation" + e.getMessage());
                }
            }

        } while (page.hasNext());
    }
}
