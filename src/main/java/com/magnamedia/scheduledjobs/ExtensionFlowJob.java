package com.magnamedia.scheduledjobs;


import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.FlowProcessorEntity;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.repository.FlowProcessorEntityRepository;
import com.magnamedia.service.FlowProcessorService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.Map;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 * @created 03/04/2025 - 2:30 PM
 * ACC-8954
 */
public class ExtensionFlowJob implements MagnamediaJob {
    private static final Logger logger = Logger.getLogger(ExtensionFlowJob.class.getName());

    private final FlowProcessorEntityRepository flowProcessorEntityRepository;
    private final FlowProcessorService flowProcessorService;

    public ExtensionFlowJob() {
        flowProcessorEntityRepository = Setup.getRepository(FlowProcessorEntityRepository.class);
        flowProcessorService = Setup.getApplicationContext().getBean(FlowProcessorService.class);
    }

    @Override
    public void run(Map<String, ?> parameters) {
        logger.info("Started job");
        sendExtensionFlowMessages();

        logger.info("Ended job");
    }

    public void sendExtensionFlowMessages() {
        logger.info("Start");

        Page<FlowProcessorEntity> p;
        Long lastId = -1L;

        do {

            p = flowProcessorEntityRepository
                    .findByEventNameAndStoppedFalseAndCompletedFalse(
                            lastId, FlowEventConfig.FlowEventName.EXTENSION_FLOW, PageRequest.of(0, 200));

            p.getContent()
                    .forEach(flow -> {
                        try {
                            logger.info("processing flow id: " + flow.getId());
                            flowProcessorService.processFlowSubEventConfig(flow);
                        } catch (Exception e) {
                            e.printStackTrace();
                            logger.severe("Error processing flow " + flow.getId() + ": " + e.getMessage());
                        }
                    });

            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() - 1).getId();
            }

        } while (!p.getContent().isEmpty());
        logger.info("End");
    }
}
