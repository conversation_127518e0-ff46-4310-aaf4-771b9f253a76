package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.core.mail.MailService;
import com.magnamedia.core.mail.TemplateEmail;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.NonClientPDC;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.extra.ReportMail;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.report.TemplatedReport;
import com.magnamedia.repository.NonClientPDCRepository;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.joda.time.DateTime;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.text.NumberFormat;
import java.util.*;

/**
 * <AUTHOR>
 */
public class PdcDueDateEmailScheduledJob implements TemplatedReport<NonClientPDC>, MagnamediaJob {

    private MailService mailService;

    private TemplateEngine templateEngine;

    ReportMail reportMail;

    NonClientPDCRepository repository;

    public PdcDueDateEmailScheduledJob() {
        mailService = Setup.getMailService();
        templateEngine = Setup.getApplicationContext().getBean(TemplateEngine.class);
        reportMail = Setup.getApplicationContext().getBean(ReportMail.class);
        repository = Setup.getRepository(NonClientPDCRepository.class);
    }

    @Override
    public void run(Map<String, ?> map) {
        this.SendEmail();
    }

    @Override
    public String SendEmail() {
        String emails = Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_PDCS_WITHIN_WEEK_EMAILS);
        List<EmailRecipient> recipients = EmailHelper.getMailRecipients(emails);
        return SendEmail(recipients);
    }

    @Override
    public String SendEmail(List<EmailRecipient> recipients) {
        DateTime after = DateTime.now().withMillisOfDay(0);

        //Jirra ACC-464
        int days = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_PDCS_WITHIN_N_DAYS));
        DateTime before = after.plusDays(days);
        return SendEmail(recipients, after.toDate(), before.toDate(), days);
    }

    public String SendEmail(List<EmailRecipient> recipients, Date after, Date before, int days) {
        this.after = after;
        this.before = before;
        this.days = days;
        StringBuilder tableBuilder = new StringBuilder("");

        try {
            refreshData();
            tableBuilder.append(reportMail.toHtmlTable(getTable(), getHeaders(), getTitle(), 400));

            tableBuilder.append("<br/><hr/>");

        } catch (Throwable e) {
            tableBuilder.append("<p>").append(ExceptionUtils.getStackTrace(e)).append("</p>");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("title", getTitle());
        params.put("tableData", tableBuilder.toString());

        if (recipients.size() > 0 && !getData().isEmpty()) {
            mailService.sendEmail(recipients,
                    new TemplateEmail(getTitle(), "PDCsDueDateEmail", params), null);
        }
        return "";
    }

    Date after;
    Date before;
    //Jirra ACC-464
    private int days = 7;
    private List<NonClientPDC> data;

    @Override
    public List<NonClientPDC> getData() {
        if (data == null) {
            DateTime after = this.after == null ? DateTime.now().withMillisOfDay(0) : new DateTime(this.after);
            DateTime before = this.before == null ? after.plusDays(30) : new DateTime(this.before);
            this.after = after.toDate();
            this.before = before.toDate();
            data = repository.findByDueDateBetween(this.after, this.before);
        }
        return data;
    }

    @Override
    public void refreshData() {
        data = null;
    }

    @Override
    public String[] toStringArray(NonClientPDC obj) {

        String sb = NumberFormat.getNumberInstance(Locale.US).format(obj.getAmount());

        //Jirra ACC-1173
        return new String[]{
                obj.getPropertyDiscription(),
                obj.getLeaseCompanyName(),
                obj.getChequeIssuerName(),
                TemplatedReport.format(obj.getChequeDueDate()),
                sb,
                obj.getContractEndDate() == null ? "N\\A" : TemplatedReport.format(obj.getContractEndDate()),
                //Jirra ACC-970
                obj.getBankIssuerBucket() != null ? obj.getBankIssuerBucket().getName() : ""
        };
    }

    @Override
    public String getTitle() {
        //return String.format("ERP System - Accounting PDCs with Cheques Due between %s And %s", TemplatedReport.format(after), TemplatedReport.format(before));

        //Jirra ACC-464
        return "PDCs due in the next " + this.days + " days " + TemplatedReport.format(after);
    }

    @Override
    public String[] getHeaders() {
        //Jirra ACC-1173 remove Beneficiary
        return new String[]{
                "Property Description", "Company Name on Lease", "Name of Cheque Issuer", "Cheque Due Date", "Amount", "Contract End Date", "Bank Issuer"
        };
    }

}
