package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.repository.ClientRefundTodoRepository;
import com.magnamedia.service.MessagingService;
import com.magnamedia.workflow.type.ClientRefundTodoManagerAction;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Feb 29, 2020
 *         Jirra ACC-1435
 */

public class ClientRefundRequestorsReportSJ implements MagnamediaJob {

    ClientRefundTodoRepository clientRefundTodoRepository;

    public ClientRefundRequestorsReportSJ() {
        clientRefundTodoRepository = Setup.getRepository(ClientRefundTodoRepository.class);
    }

    @Override
    public void run(Map<String, ?> map) {
        
        //Date todayStartDate = new DateTime().withTimeAtStartOfDay().toDate();
        Date startDate = new DateTime().minusDays(1).toDate();
        
        HistorySelectQuery<ClientRefundToDo> historyQuery = new HistorySelectQuery<>(ClientRefundToDo.class);
        historyQuery.filterBy("lastModificationDate", ">=", startDate);
        historyQuery.filterByChanged("managerAction");
        List<ClientRefundToDo> todayToDos = historyQuery.execute();
        
        if (todayToDos != null && !todayToDos.isEmpty())
            todayToDos = clientRefundTodoRepository.findAll(todayToDos.stream().map(todo -> todo.getId()).collect(Collectors.toList()));
        
        Map<String, List<ClientRefundToDo>> toDosMap = new HashMap();
        for (ClientRefundToDo clientRefundToDo : todayToDos) {
            if (clientRefundToDo.getRequesterUser() == null) continue;

            if (!toDosMap.containsKey(clientRefundToDo.getRequesterUserName())) {
                toDosMap.put(clientRefundToDo.getRequesterUserName(), new ArrayList());
            }

            toDosMap.get(clientRefundToDo.getRequesterUserName()).add(clientRefundToDo);
        }

        for (String requesterName : toDosMap.keySet()) {
            sendRequesterReport(toDosMap.get(requesterName));
        }
    }

    private void sendRequesterReport(
            List<ClientRefundToDo> refundToDos) {

        User requester = refundToDos.get(0).getRequesterUser();

        List<ClientRefundToDo> approvedToDos = refundToDos.stream()
                .filter(todo -> todo.getManagerAction().equals(ClientRefundTodoManagerAction.APPROVE))
                .collect(Collectors.toList());

        List<ClientRefundToDo> rejectedToDos = refundToDos.stream()
                .filter(todo -> todo.getManagerAction().equals(ClientRefundTodoManagerAction.REJECT))
                .collect(Collectors.toList());

        Map<String, String> parameters = new HashMap();
        parameters.put("approved_requests", getRefundToDosAsHTML(approvedToDos, ClientRefundTodoManagerAction.APPROVE));
        parameters.put("rejected_requests", getRefundToDosAsHTML(rejectedToDos, ClientRefundTodoManagerAction.REJECT));

        if(requester.getEmail() == null) return;

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff("client_refund_requester_report",
                        parameters, requester.getEmail(),"Actions Done by Manager on Added Refunds");
    }

    private String getRefundToDosAsHTML(List<ClientRefundToDo> refundToDos, ClientRefundTodoManagerAction action) {
        String description = "";
        int index = 1;
        for (ClientRefundToDo toDo : refundToDos) {
            description += index++ + "- " + toDo.getClient().getName();

            if (action.equals(ClientRefundTodoManagerAction.REJECT) && !StringUtils.isEmpty(toDo.getManagerNotes())) {
                description += ", and they noted the following: " + toDo.getManagerNotes();
            }

            description += "<br/>";
        }

        return description;
    }
}