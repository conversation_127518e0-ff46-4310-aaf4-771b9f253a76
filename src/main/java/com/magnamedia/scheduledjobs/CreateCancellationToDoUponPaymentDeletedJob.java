package com.magnamedia.scheduledjobs;

import com.magnamedia.controller.DirectDebitCancelationToDoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.Payment;
import com.magnamedia.module.type.DirectDebitCancellationToDoReason;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.service.PaymentService;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

// ACC-5415
public class CreateCancellationToDoUponPaymentDeletedJob implements MagnamediaJob {

    private PaymentRepository paymentRepository;
    private DirectDebitCancelationToDoController directDebitCancelationToDoController;
    private PaymentService paymentService;

    private static final Logger logger =
            Logger.getLogger(CreateCancellationToDoUponPaymentDeletedJob.class.getName());

    @Override
    public void run(Map<String, ?> map) {

        paymentRepository = Setup.getRepository(PaymentRepository.class);
        directDebitCancelationToDoController = Setup.getApplicationContext().getBean(DirectDebitCancelationToDoController.class);
        paymentService = Setup.getApplicationContext().getBean(PaymentService.class);

        this.runJob();
    }

    public void runJob() {
        Long lastId = -1L;

        Page<Payment> page = paymentRepository.findByRequiredForBouncingAndStatusDeletedAndIdGreaterThan(
                lastId, PageRequest.of(0, 100));;
        logger.log(Level.INFO, "Start");

        while (!page.getContent().isEmpty()) {
            page.getContent().forEach(p -> {
                try {
                    process(p);
                } catch (Exception e) {
                    logger.log(Level.SEVERE, "exception while gcreate cancellation todo payment Id: {0}", p.getId());
                    logger.log(Level.SEVERE, "Exception: " + ExceptionUtils.getStackTrace(e));
                }
            });

            lastId = page.getContent().get(page.getContent().size() - 1).getId();
            logger.log(Level.INFO, "lastId {0}", lastId);

            page = paymentRepository.findByRequiredForBouncingAndStatusDeletedAndIdGreaterThan(
                    lastId, PageRequest.of(0, 100));
        }

        logger.log(Level.INFO, "End");
    }

    @Transactional
    private void process(Payment p) {
        logger.log(Level.INFO, "Start BK task create cancellation todo payment Id: {0}", p.getId());

        p.setRequiredForBouncing(false);
        paymentService.forceUpdatePayment(p);

        directDebitCancelationToDoController
                .createToDoIfValid(p.getContract(), Collections.singletonList(p.getId()),
                        DirectDebitCancellationToDoReason.PAYMENT_RECEIVAL_NO_NEED_MONEY);
    }
}
