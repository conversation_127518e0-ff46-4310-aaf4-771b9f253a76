package com.magnamedia.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.core.mail.Recipient;
import com.magnamedia.core.notification.AppAction;
import com.magnamedia.core.repository.PushNotificationRepository;
import com.magnamedia.core.repository.TemplateAllowedParameterRepository;
import com.magnamedia.core.type.*;
import com.magnamedia.core.type.template.ChannelSpecificSettingType;
import com.magnamedia.core.type.template.NotificationTarget;
import com.magnamedia.entity.*;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.extra.StringUtils;
import com.magnamedia.helper.UaePhoneNormlizer;
import com.magnamedia.repository.AccTemplateRepository;
import com.magnamedia.repository.DisablePushNotificationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by hp on 3/29/2021.
 */

@Service
public class MessagingService {
    private final Logger logger = Logger.getLogger(MessagingService.class.getName());

    @Autowired
    private DisablePushNotificationRepository disablePushNotificationRepository;
    @Autowired
    private TemplateAllowedParameterRepository templateAllowedParameterRepository;

    @Autowired
    private TemplateUtil templateUtil;

    public enum ButtonType {
        PRIMARY,
        SECONDARY
    }

    public void sendMessageToClient(
            Contract contract,
            Map<String, String> params,
            Map<String, AppAction> cta,
            Long ownerId,
            String ownerType,
            Template t) {

        sendMessageToClient(
                contract,
                params,
                cta,
                ownerId,
                ownerType,
                t,
                null,
                new HashMap<>());
    }

    public void sendMessageToClient(
            Contract contract,
            Map<String, String> params,
            Map<String, AppAction> cta,
            Long ownerId,
            String ownerType,
            Template t,
            DDMessagingContract ddMessagingContract) {

        sendMessageToClient(
                contract,
                params,
                cta,
                ownerId,
                ownerType,
                t,
                ddMessagingContract,
                new HashMap<>());
    }

    public void sendMessageToClient(
            Contract contract,
            Map<String, String> params,
            Map<String, AppAction> cta,
            Long ownerId,
            String ownerType,
            Template t,
            DDMessagingContract ddMessagingContract,
            Map<String, Object> m) {

        logger.info("Sending message for Client: " + contract.getClient().getId() +
                "; Message Template Name: " + (t == null ? "null" : t.getName()) +
                "; number: " + contract.getClient().getNormalizedMobileNumber());

        if (t == null) return;

        if (contract.getFreezingDate() != null) {
            logger.info("contract id : " + contract.getId() + " frozen");
            return;
        }

        if (cta != null && !cta.isEmpty()) {
            boolean isFirst = true;

            for (Map.Entry<String, AppAction> entry : cta.entrySet()) {

                if (!entry.getValue().getType().equals(AppActionType.BUTTON)) continue;

                if (isFirst) {
                    entry.getValue().getAppRouteArguments().put("buttonType", ButtonType.PRIMARY.toString());
                    isFirst = false;
                } else {
                    entry.getValue().getAppRouteArguments().put("buttonType", ButtonType.SECONDARY.toString());
                }
            }
        }

        Map<String, String> map = getClientNumbers(contract, t.getPrimaryChannel(), m);
        templateUtil.send(t,
                "en",
                Collections.singletonList(getClientTarget(
                        contract,
                        map.get("mobile"),
                        map.get("whatsapp"),
                        ownerId, ownerType, t, ddMessagingContract == null || ddMessagingContract.getDdMessaging() == null ?
                                null : ddMessagingContract.getDdMessaging().getEmailSubject(), params)),
                contract,
                cta,
                params);
        }

    public void sendClientSms(
            Contract contract,
            Template t,
            Map<String, String> params,
            Map<String, AppAction> cta,
            Long ownerId,
            String ownerType) {

        sendClientMessageByType(
                contract, t, params, cta,
                null, null, ownerId,
                ownerType, ChannelSpecificSettingType.SMS);

    }

    public void sendClientSms(
            Contract contract,
            Template t,
            Map<String, String> params,
            Map<String, AppAction> cta,
            String mobileNumber,
            String whatsappNumber,
            Long ownerId,
            String ownerType) {

        sendClientMessageByType(
                contract, t, params, cta,
                mobileNumber, whatsappNumber, ownerId,
                ownerType, ChannelSpecificSettingType.SMS);

    }

    public void sendClientMessageByType(
            Contract contract,
            Template t,
            Map<String, String> params,
            Map<String, AppAction> cta,
            String mobileNumber,
            String whatsappNumber,
            Long ownerId,
            String ownerType,
            ChannelSpecificSettingType channelType) {

        logger.info("Sending " + channelType + " for Client: " + contract.getClient().getId() +
                "; Message Template Name: " + (t == null ? "null" : t.getName()) +
                "; number: " + mobileNumber);

        if (t == null) return;

        if (contract.getFreezingDate() != null) {
            logger.info("contract id : " + contract.getId() + " frozen");
            return;
        }

        Map<String, String> m = new HashMap<>();
        if (mobileNumber == null && whatsappNumber == null) {
            m = getClientNumbers(contract, channelType, new HashMap<>());
        } else {
            List<String> whatsappNumbers = new ArrayList<>(Arrays.asList(
                    whatsappNumber, mobileNumber));
            List<String> smsNumbers = new ArrayList<>(Arrays.asList(mobileNumber, whatsappNumber));

            String mobile = smsNumbers.stream()
                    .filter(number -> !StringUtils.isEmpty(number)).collect(Collectors.joining(";"));
            String whatsapp = whatsappNumbers.stream()
                    .filter(number -> !StringUtils.isEmpty(number)).collect(Collectors.joining(";"));
            m.put("mobile", mobile);
            m.put("whatsapp", whatsapp);
        }

        templateUtil.send(t,
                "en",
                channelType,
                Collections.singletonList(getClientTarget(contract,
                        m.get("mobile"),
                        m.get("whatsapp"),
                        ownerId, ownerType, t, null, params)),
                contract,
                cta,
                params);
    }

    private NotificationTarget getClientTarget(
            Contract contract,
            String mobileNumber,
            String whatsappNumber,
            Long ownerId,
            String ownerType,
            Template t,
            String emailSubject,
            Map<String, String> params) {

        return new NotificationTarget() {
            @Override
            public Long getId() {
                return contract.getClient().getId();
            }

            @Override
            public String getEntityType() {
                return contract.getClient().getEntityType();
            }

            @Override
            public String getMobileNumber() { return mobileNumber; }

            @Override
            public String getReceiverName() { return contract.getClient().getName(); }

            @Override
            public SmsReceiverType getSmsReceiverType() {
                return SmsReceiverType.Client;
            }

            @Override
            public String getSmsReceiverName() { return  contract.getClient().getName(); };

            @Override
            public String getWhatsappNumber() { return whatsappNumber; }

            @Override
            public EmailReceiverType getEmailReceiverType() { return EmailReceiverType.Client; }

            @Override
            public String getEmail() { return contract.getClient().getEmail(); }

            @Override
            public boolean isAppendTrailingSentence() {
                return t.isChannelExist(ChannelSpecificSettingType.SMS) &&
                        t.getChannelSetting(ChannelSpecificSettingType.SMS)
                                .getTrailingSentence() != null;
            }

            @Override
            public boolean isIgnoreOnFailAction() { return false; }

            @Override
            public Long getOwnerId() { return ownerId; }

            @Override
            public String getOwnerEntityType() { return ownerType; }

            @Override
            public Long getRelatedEntityId1() {return contract.getId();}

            @Override
            public String getRelatedEntityType1() {return contract.getEntityType();}

            @Override
            public String getEmailSubject() {
                return emailSubject != null && !emailSubject.isEmpty() ? emailSubject : null;
            }

            @Override
            public Long getRelatedEntityId2() {return params.containsKey("relatedEntityId2") ?
                    Long.valueOf(params.get("relatedEntityId2")) : null;}

            @Override
            public String getRelatedEntityType2() {return params.containsKey("relatedEntityType2") ?
                    params.get("relatedEntityType2") : null;}

            @Override
            public boolean isOrderedDelivery() { return true; }

            @Override
            public Map<String, String> getAdditionalInfo() {
                if(contract.getHousemaid() == null) return null;
                Map<String, String> maidInfo = new HashMap<>();
                maidInfo.put("maidId", contract.getHousemaid().getId().toString());
                return maidInfo;
            }
        };
    }

    private Map<String, String> getClientNumbers(Contract contract, ChannelSpecificSettingType primaryChannel, Map<String, Object> m) {
        String spouseNumber = contract.getClient().getNormalizedSpouseMobileNumber();
        if (m.containsKey("ignoreSendingToSpouse")) {
            spouseNumber = "";
        }
        List<String> whatsappNumbers = new ArrayList<>(Arrays.asList(
                contract.getClient().getNormalizedWhatsappNumber(), contract.getClient().getNormalizedMobileNumber(),
                spouseNumber));
        List<String> smsNumbers = new ArrayList<>(Arrays.asList(
                contract.getClient().getNormalizedMobileNumber(), contract.getClient().getNormalizedWhatsappNumber(),
                spouseNumber));

        String mobile = smsNumbers.stream().limit(primaryChannel.equals(ChannelSpecificSettingType.SMS) ?
                        2 : smsNumbers.size())
                .filter(number -> !StringUtils.isEmpty(number)).collect(Collectors.joining(";"));
        String whatsapp = whatsappNumbers.stream().limit(primaryChannel.equals(ChannelSpecificSettingType.Whatsapp) ?
                        2 : whatsappNumbers.size())
                .filter(number -> !StringUtils.isEmpty(number)).collect(Collectors.joining(";"));

        return new HashMap<String, String>() {{
           put("mobile", mobile);
           put("whatsapp", whatsapp);
        }};
    }

    private Map<String, String> getMaidNumbers(Housemaid housemaid) {
        String mobileNumber = UaePhoneNormlizer.NormalizePhoneNumber(housemaid.getNormalizedPhoneNumber());

        List<String> whatsappNumbers = new ArrayList<>(Arrays.asList(
                housemaid.getNormalizedWhatsAppPhoneNumber(), mobileNumber));
        List<String> smsNumbers = new ArrayList<>(Arrays.asList(
                mobileNumber, housemaid.getNormalizedWhatsAppPhoneNumber()));

        String mobile = smsNumbers.stream().filter(number -> !StringUtils.isEmpty(number)).collect(Collectors.joining(";"));
        String whatsapp = whatsappNumbers.stream().filter(number -> !StringUtils.isEmpty(number)).collect(Collectors.joining(";"));

        return new HashMap<String, String>() {{
            put("mobile", mobile);
            put("whatsapp", whatsapp);
        }};
    }

    public void sendEmailToClient(
            Contract contract,
            String templateName,
            Map<String, String> params,
            String emails,
            String emailSubject) {

        if (contract.getFreezingDate() != null) {
            logger.info("contract id : " + contract.getId() + " frozen");
            return;
        }

        sendEmail(contract.getClient(), contract, templateName, params, contract.getClient().getNormalizedMobileNumber(),
                contract.getClient().getNormalizedWhatsappNumber(), emails, SmsReceiverType.Client,
                EmailReceiverType.Client, new ArrayList<>(), new ArrayList<>(),
                new ArrayList<>(), emailSubject);
    }

    public void sendEmailToOfficeStaff(
            String templateName,
            Map<String, String> params,
            String emails,
            String emailSubject) {

        sendEmailToOfficeStaffWithAttachments(templateName, params, emails, new ArrayList<>(), emailSubject);
    }

    public void sendEmailToOfficeStaffWithAttachments(
            String templateName,
            Map<String, String> params,
            String emails,
            List<Attachment> attachments,
            String emailSubject) {

        sendEmail(null, null, templateName, params, null,
                null, emails, SmsReceiverType.Office_Staff,
                EmailReceiverType.Office_Staff, attachments, new ArrayList<>(),
                new ArrayList<>(), emailSubject);
    }

    public void sendEmailToOfficeStaffWithCc(
            User recipient,
            String templateName,
            Map<String, String> params,
            String emails,
            List<String> ccEmails,
            String emailSubject) {

        List<EmailRecipient> cc = new ArrayList<>();
        if (!ccEmails.isEmpty()) {
            ccEmails.forEach(e -> cc.addAll(EmailHelper.getRecipients(e)));
        }

        sendEmail(recipient, null, templateName, params, recipient == null ? null : recipient.getMobileNumber(),
                recipient == null ? null : recipient.getWhatsappNumber(), emails, SmsReceiverType.Office_Staff,
                EmailReceiverType.Office_Staff, new ArrayList<>(), cc,
                new ArrayList<>(), emailSubject);
    }

    public void sendEmail(
            BaseEntity recipient,
            Contract contract,
            String templateName,
            Map<String, String> params,
            String mobileNumber,
            String whatsappNumber,
            String emails,
            SmsReceiverType smsReceiverType,
            EmailReceiverType emailReceiverType,
            List<Attachment> attachments,
            List<EmailRecipient> cc,
            List<EmailRecipient> bcc,
            String emailSubject) {

        logger.info("Sending Email email Template Name: " + templateName +
                "; emails: " + emails);

        if (Strings.isNullOrEmpty(emails)) return;

        Template t = TemplateUtil.getTemplate(templateName);
        if (t == null) return;

        List<EmailRecipient> recipients = EmailHelper.getEmails(emails)
                .stream()
                .map(e -> new Recipient(e, recipient != null && recipient.getLabel() != null ? recipient.getLabel() : e))
                .collect(Collectors.toList());

        List<NotificationTarget> targets = Collections.singletonList(getEmailTarget(recipient, mobileNumber, whatsappNumber, contract != null ? contract.getId() : null,
                contract != null ? contract.getEntityType() : null, smsReceiverType, emailReceiverType, recipients.get(0).getEmail(),
                recipients.get(0),
                recipients.size() > 1 ? recipients : new ArrayList<>(),
                attachments, cc, bcc, emailSubject, true, params));

        templateUtil.send(t,
                ChannelSpecificSettingType.Email,
                targets,
                contract,
                new HashMap<>(),
                params);
    }

    public NotificationTarget getEmailTarget(
            BaseEntity recipient,
            String mobileNumber,
            String whatsappNumber,
            Long ownerId,
            String ownerType,
            SmsReceiverType smsReceiverType,
            EmailReceiverType emailReceiverType,
            String email,
            EmailRecipient emailRecipient,
            List<EmailRecipient> emailRecipients,
            List<Attachment> attachments,
            List<EmailRecipient> cc,
            List<EmailRecipient> bcc,
            String emailSubject,
            boolean isEmailHml,
            Map<String,String> params) {

        return new NotificationTarget() {
            @Override
            public Long getId() { return recipient != null ? recipient.getId() : null; }

            @Override
            public String getEntityType() {
                return recipient != null ? recipient.getEntityType() : null;
            }

            @Override
            public String getReceiverName() {
                if(getEntityType() == null) return "";

                switch(getEntityType()) {
                    case "Client":
                        return ((Client)recipient).getName();
                    case "OfficeStaff":
                        return ((OfficeStaff)recipient).getName();
                }
                return "";
            }

            @Override
            public String getMobileNumber() { return mobileNumber; }

            @Override
            public SmsReceiverType getSmsReceiverType() {
                return smsReceiverType;
            }

            @Override
            public String getWhatsappNumber() { return whatsappNumber; }

            @Override
            public EmailReceiverType getEmailReceiverType() { return emailReceiverType; }

            @Override
            public String getEmail() { return email; }

            @Override
            public boolean isAppendTrailingSentence() {
                return NotificationTarget.super.isAppendTrailingSentence();
            }

            @Override
            public boolean isIgnoreOnFailAction() { return false; }

            @Override
            public Long getOwnerId() { return ownerId; }

            @Override
            public String getOwnerEntityType() { return ownerType; }

            @Override
            public EmailRecipient getEmailRecipient() { return emailRecipient; }

            @Override
            public List<Attachment> getEmailAttachments() {
                return attachments == null ? new ArrayList<>() : attachments;
            }

            @Override
            public List<EmailRecipient> getCc() {
                return cc;
            }

            @Override
            public List<EmailRecipient> getBcc() {
                return bcc;
            }

            @Override
            public String getEmailSubject() {
                return emailSubject;
            }

            @Override
            public boolean isEmailHml() {
                return isEmailHml;
            }

            @Override
            public Long getRelatedEntityId1() {return ownerId;}

            @Override
            public String getRelatedEntityType1() {return ownerType;}
            // ACC-7020
            @Override
            public List<EmailRecipient> getEmailRecipients() { return emailRecipients; }

            @Override
            public Long getRelatedEntityId2() {return params.containsKey("relatedEntityId2") ?
                    Long.valueOf(params.get("relatedEntityId2")) : null;}

            @Override
            public String getRelatedEntityType2() {return params.containsKey("relatedEntityType2") ?
                    params.get("relatedEntityType2") : null;}

            @Override
            public boolean isOrderedDelivery() { return false; }
        };
    }

    public void sendMessageToMaid(
            Contract contract,
            Housemaid housemaid,
            Template t,
            Map<String, String> params,
            Long ownerId,
            String ownerType) {

        logger.info("Sending message for Maid: " + housemaid.getId() +
                "; Message Template Name: " + (t == null ? "NULL" : t.getName()) +
                "; number: " + housemaid.getNormalizedPhoneNumber());

        if (t == null) return;

        Map<String, String> map = getMaidNumbers(contract.getHousemaid());
        templateUtil.send(t,
                housemaid.getYayaAppNotificationLang(),
                Collections.singletonList(getMaidTarget(contract,
                        housemaid,
                        map.get("mobile"),
                        map.get("whatsapp"),
                        ownerId, ownerType)),
                contract,
                new HashMap<>(),
                params);
    }

    /*public void sendMaidSms(
            Contract contract,
            Housemaid housemaid,
            Template t,
            Map<String, String> params,
            Long ownerId,
            String ownerType) {

        logger.info("Sending SMS for Maid: " + housemaid.getId() +
                "; Message Template Name: " + (t == null ? "NULL" : t.getName()) +
                "; number: " + housemaid.getNormalizedPhoneNumber());

        if(t == null) return;

        Map<String, String> map = getMaidNumbers(contract.getHousemaid());
        templateUtil.send(t,
                housemaid.getYayaAppNotificationLang(),
                ChannelSpecificSettingType.SMS,
                Collections.singletonList(getMaidTarget(contract,
                        housemaid,
                        map.get("mobile"),
                        map.get("whatsapp"),
                        ownerId, ownerType)),
                contract,
                new HashMap<>(),
                params);
    }*/

    private NotificationTarget getMaidTarget(
            Contract contract,
            Housemaid recipient,
            String mobileNumber,
            String whatsappNumber,
            Long ownerId,
            String ownerType) {

        return new NotificationTarget() {
            @Override
            public Long getId() { return recipient.getId(); }

            @Override
            public String getEntityType() { return recipient.getEntityType(); }

            @Override
            public String getMobileNumber() { return mobileNumber; }

            @Override
            public String getReceiverName() { return recipient.getName(); }

            @Override
            public SmsReceiverType getSmsReceiverType() { return SmsReceiverType.Housemaid; }

            @Override
            public String getWhatsappNumber() { return whatsappNumber; }

            @Override
            public EmailReceiverType getEmailReceiverType() { return EmailReceiverType.Housemaid; }

            @Override
            public String getEmail() {
                return NotificationTarget.super.getEmail();
            }

            @Override
            public boolean isAppendTrailingSentence() {
                return NotificationTarget.super.isAppendTrailingSentence();
            }

            @Override
            public boolean isIgnoreOnFailAction() { return false; }

            @Override
            public Long getOwnerId() { return ownerId; }

            @Override
            public String getOwnerEntityType() { return ownerType; }

            @Override
            public Long getRelatedEntityId1() {return contract.getId();}

            @Override
            public String getRelatedEntityType1() {return contract.getEntityType();}

            @Override
            public boolean isOrderedDelivery() { return true; }
        };
    }

    public void sendMessageToOfficeStaff(
            Contract contract,
            OfficeStaff recipient,
            Template t,
            Map<String, String> params,
            Long ownerId,
            String ownerType,
            String mobileNumber) {

        if (t == null) return;

        templateUtil.send(t,
                "en",
                ChannelSpecificSettingType.SMS,
                Collections.singletonList(getOfficeStaff(contract, recipient, ownerId, ownerType, mobileNumber)),
                null,
                new HashMap<>(),
                params);
    }

    private NotificationTarget getOfficeStaff(
            Contract contract,
            OfficeStaff recipient,
            Long ownerId,
            String ownerType,
            String mobileNumber) {

        return new NotificationTarget() {
            @Override
            public Long getId() { return recipient.getId(); }

            @Override
            public String getEntityType() { return recipient.getEntityType(); }

            @Override
            public String getMobileNumber() { return mobileNumber; }

            @Override
            public String getReceiverName() { return recipient.getName(); }

            @Override
            public SmsReceiverType getSmsReceiverType() { return SmsReceiverType.Office_Staff; }

            @Override
            public String getWhatsappNumber() { return null; }

            @Override
            public EmailReceiverType getEmailReceiverType() { return EmailReceiverType.Office_Staff; }

            @Override
            public String getEmail() { return recipient.getEmail(); }

            @Override
            public boolean isAppendTrailingSentence() {
                return NotificationTarget.super.isAppendTrailingSentence();
            }

            @Override
            public boolean isIgnoreOnFailAction() { return false; }

            @Override
            public Long getOwnerId() { return ownerId; }

            @Override
            public String getOwnerEntityType() { return ownerType; }

            @Override
            public Long getRelatedEntityId1() {return contract.getId();}

            @Override
            public String getRelatedEntityType1() {return contract.getEntityType();}

            @Override
            public boolean isOrderedDelivery() { return false; }
        };
    }

    public void createDisableNotificationBGT(List<Long> notificationIds, String reason) {
        if (notificationIds.isEmpty()) return;

        String notificationsIds = notificationIds.stream().map(id -> id.toString()).collect(Collectors.joining(";"));

        BackgroundTaskService backgroundTaskService =
                Setup.getApplicationContext().getBean(BackgroundTaskService.class);

        backgroundTaskService.addDirectCallBackgroundTaskForEntity(
                "disableNotifications", "messagingService", "accounting",
                "disableNotifications", null, null, true, false,
                new Class[]{ String.class, String.class },
                new Object[] { reason, notificationsIds });
    }

    public void disableNotifications(String reason, String notificationsIds) {
        List<Long> notificationIds =
                Arrays.stream(notificationsIds.split(";"))
                        .map(id -> Long.valueOf(id))
                        .collect(Collectors.toList());

        disableNotifications(reason, disablePushNotificationRepository.findAll(notificationIds));
    }

    public void disableNotifications(String reason, List<PushNotification> notifications) {
        if (notifications == null || notifications.isEmpty()) return;
        logger.log(Level.INFO, "Disabling notifications for reason: " + reason);

        notifications.stream()
                .filter(n -> !n.isDisabled())
                .forEach(n -> {
                    n.setDisabled(true);
                    n.setReceived(true);
                    n.setLocation(NotificationLocation.INBOX);
                    disablePushNotificationRepository.save(n);
                });
    }

    public void moveToInbox(String reason, String notificationsIds) {
        logger.log(Level.INFO, "Move to inbox notifications for reason: " + reason);
        logger.log(Level.INFO, "Move to inbox notifications ids: " + notificationsIds);

        List<Long> notificationIds =
            Arrays.stream(notificationsIds.split(";"))
                .map(Long::valueOf)
                .collect(Collectors.toList());

        disablePushNotificationRepository.findAll(notificationIds)
            .stream()
            .filter(n -> !n.getLocation().equals(NotificationLocation.INBOX))
            .forEach(n -> {
                n.setLocation(NotificationLocation.INBOX);
                n.setReceived(true);
                disablePushNotificationRepository.save(n);
            });
    }

    public void createMoveToInboxBgt(List<Long> notificationIds, String reason) {
        if (notificationIds.isEmpty()) return;

        String notificationsIds = notificationIds.stream()
            .map(Object::toString)
            .collect(Collectors.joining(";"));

        Setup.getApplicationContext().getBean(BackgroundTaskService.class)
            .create(new BackgroundTask.builder(
                "createMoveToInboxBgt" + new java.util.Date().getTime(),
                "accounting",
                "messagingService",
                "moveToInbox")
                .withRelatedEntity("PushNotification", null)
                .withParameters(
                    new Class[]{ String.class, String.class },
                    new Object[] { reason, notificationsIds })
                .withQueue(BackgroundTaskQueues.SequentialQueue)
                .build());
    }


    public int addMaidFirstNameAllowedParameter() {
        final int PAGE_SIZE = 100;
        long moduleId = Setup.getCurrentModule().getId();
        long lastSeenId     = 0L;
        int totalAdded = 0;

        AccTemplateRepository templateRepo =
                Setup.getRepository(AccTemplateRepository.class);

        Page<Template> page;

        do {
            // always start at offset 0 → rows that vanished after the last flush
            // are simply no longer returned; remaining rows shift up and are seen
            page = templateRepo.findTemplatesMissingMaidFirstName(
                    moduleId,
                    lastSeenId,
                    PageRequest.of(0, PAGE_SIZE)
            );

            for (Template t : page.getContent()) {

                try{
                    TemplateAllowedParameter p = new TemplateAllowedParameter();
                    p.setName("maid_first_name");
                    p.setExpression(false);
                    p.setTemplate(t);

                    templateAllowedParameterRepository.save(p);
                    ++totalAdded;
                }catch (BusinessException e){
                    logger.log(Level.WARNING, "Skipped template ID " + t.getId() +" due to restrictions: "  + " :" +e.getMessage() );
                }catch (Exception e) {
                    logger.log(Level.SEVERE, "Unexpected error on template ID " + t.getId() + ": " + e.getMessage(), e);
                }

                lastSeenId = t.getId();
            }
        } while (!page.isEmpty());   // loop until no rows remain

        return totalAdded;
    }


    public void addGenderAndWorkerTypeForTemplates(){
        Setup.getRepository(AccTemplateRepository.class)
                .getTemplateForGenerateGenderExpressions(Setup.getCurrentModule().getId())
                .forEach(t -> {
                    generateGenderExpressions(t,"her_him","her","him");
                    generateGenderExpressions(t,"her_his","her","his");
                    generateGenderExpressions(t,"she_he","she","he");
                    generateGenderExpressions(t,"She_He_Capitalized","She","He");
                    generateWorkerTypeExpression(t);
                });
    }

    public void generateGenderExpressions(
            Template template,
            String name,
            String v1,
            String v2){

        String exp = "contract == null || contract.getHousemaid() == null ? \"" + v1 + "\" : " +
                "contract.getHousemaid().getGender() != null && " +
                    "contract.getHousemaid().getGender().equals(T(com.magnamedia.extra.Gender).Female) ? " +
                "\"" + v1 + "\" : " +
                "\"" + v2 + "\"" ;

        TemplateAllowedParameter allowedParameter = new TemplateAllowedParameter();
        allowedParameter.setName(name);
        allowedParameter.setExpression(true);
        allowedParameter.setValue(exp);
        allowedParameter.setTemplate(template);

        templateAllowedParameterRepository.save(allowedParameter);
    }

    public void generateWorkerTypeExpression(Template template){
        String exp = "contract == null ? " + "\"maid\" : " +
                "contract.getWorkerType() != null && " +
                    "contract.getWorkerType().getCode().equals(\"private_driver\") ? " +
                "\"driver\" : " +
                "\"maid\" ";

        TemplateAllowedParameter allowedParameter = new TemplateAllowedParameter();
        allowedParameter.setName("worker_type");
        allowedParameter.setExpression(true);
        allowedParameter.setValue(exp);
        allowedParameter.setTemplate(template);

        templateAllowedParameterRepository.save(allowedParameter);
    }

    public void disablePushNotificationCtaByName(PushNotification pushNotification, String ctaName) {
        try {
            if (StringUtils.isEmpty(pushNotification.getContext())) return;

            ObjectMapper objectMapper = Setup
                    .getApplicationContext()
                    .getBean(ObjectMapper.class);
            Map<String, Object> context = objectMapper.readValue(pushNotification.getContext(), HashMap.class);

            if (!context.containsKey(ctaName) || context.get(ctaName) == null) return;

            Map<String, Object> cta = (Map) context.get(ctaName);
            if (!cta.containsKey("enabled") || cta.get("enabled") == null || !(Boolean)cta.get("enabled")) return;

            cta.put("visibility", AppActionVisibility.Disabled.toString());
            context.put(ctaName, cta);

            pushNotification.setContext(objectMapper.writeValueAsString(context));
            Setup.getRepository(PushNotificationRepository.class).save(pushNotification);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
    }
}