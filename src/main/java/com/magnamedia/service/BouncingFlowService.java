package com.magnamedia.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.entity.*;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static com.magnamedia.extra.ContractScheduleForTerminationUtils.isTerminationReasonDueBouncedPayment;

/**
 * <AUTHOR> Masod <<EMAIL>>
 *         Created on Jun 02, 2020
 *         Jirra ACC-1721
 */

@Service
public class BouncingFlowService {
    protected static final Logger logger = Logger.getLogger(BouncingFlowService.class.getName());

    @Autowired
    private DirectDebitService directDebitService;
    @Autowired
    private DirectDebitRepository ddRepository;
    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private DirectDebitFileRepository ddfRepository;
    @Autowired
    private InterModuleConnector moduleConnector;
    @Autowired
    private ContractRepository contractRepository;
    @Autowired
    private ContractPaymentTermRepository cptRepository;
    @Autowired
    private DirectDebitSignatureService directDebitSignatureService;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private AccountingEntityPropertyRepository accountingEntityPropertyRepository;
    @Autowired
    private ObjectMapper objectMapper;

    @PersistenceContext
    EntityManager entityManager;

    public Map paymentGetBouncedStartDirectDebitProcess(
            Payment entity,
            List<DirectDebitSignature> signatures,
            DirectDebit directDebit) {

        SwitchingNationalityService switchingNationalityService = Setup.getApplicationContext().getBean(SwitchingNationalityService.class);
        Map map = new HashMap();

        PicklistItem monthlyPayment = PicklistHelper.getItem("TypeOfPayment", "monthly_payment");
        if (entity.getDirectDebit() != null && entity.getDirectDebit().getId() != null &&
                switchingNationalityService.relatesToSwitching(entity.getDirectDebit().getId(), true) &&
                switchingNationalityService.doesPaymentCoverSwitchingPeriod(entity.getId()) &&
                entity.getTypeOfPayment() != null && entity.getTypeOfPayment().getId() != null &&
                entity.getTypeOfPayment().getId().equals(monthlyPayment.getId()) &&
                entity.getMethodOfPayment() != null && entity.getMethodOfPayment().equals(PaymentMethod.DIRECT_DEBIT)) {
            logger.log(Level.SEVERE, "relates to Switching Nationality -> do nothing");
            return map;
        }

        if (signatures != null) {
            logger.log(Level.SEVERE, "approvedSignatures is not null");
        }

        List<DirectDebit> dds = new ArrayList();

        boolean paymentAlreadyHasDD = directDebit != null;
        if (directDebit != null) {
            logger.log(Level.SEVERE, "Direct Debit is not null");
            dds = ddRepository.findByContractPaymentTermAndStatusAndCategoryAndManualDdfFileIsNullAndExpiryDateGreaterThanEqual(
                    directDebit.getContractPaymentTerm(), DirectDebitStatus.CONFIRMED, DirectDebitCategory.B, new Date());
        } else {
            logger.log(Level.SEVERE, "Direct Debit is  null");
            List<ContractPaymentTerm> cpts = cptRepository.findByContractAndIsActiveOrderByCreationDateDesc(entity.getContract(), true);
            ContractPaymentTerm cpt = !cpts.isEmpty() ? cpts.get(0) : null;
            Contract contract = contractRepository.findOne(entity.getContract().getId());

            if (cpt == null) {
                logger.log(Level.SEVERE, "CPT is  null; Contract " +
                        (contract == null ? "not " : "") + "found");
                
                ContractPaymentTerm oldCpt = cptRepository.findFirstByContractOrderByCreationDateDesc(entity.getContract());
                logger.log(Level.SEVERE, "oldCpt: " + (contract == null ? "not " : "") + "found: " + oldCpt.getId());
                
                PaymentTermConfig termConfig = Setup.getApplicationContext().getBean(ContractPaymentTermServiceNew.class)
                        .findSuitableConfig(contract.getHousemaid().getNationality(),
                                contract.getContractProspectType(),
                                oldCpt.getType(), oldCpt.getPackageType());
                logger.log(Level.SEVERE, "termConfig " + (termConfig == null ? "not " : "") + "found");
                
                cpt = new ContractPaymentTerm();
                cpt.setContract(contract);
                cpt.setProRated(entity.getIsProRated());
                cpt.setIsActive(true);
                cpt.setHousemaid(contract.getHousemaid());
                cpt.setFirstMonthPayment(0);
                cpt.setPaymentTermConfigWithValues(termConfig);

                logger.log(Level.SEVERE, "termConfig set");
                cptRepository.save(cpt);
            }

            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            Date ddStartDate = calendar.getTime();
            Date ddExpiryDate;
            Date dateOfLastPDCPayment = null;

            if (this.isFuturePDPPaymentsOnContractDDWithSameAmount(entity.getDateOfPayment(), entity.getAmountOfPayment(), contract.getId())) {
                dateOfLastPDCPayment = this.getLastPDCPaymentOnContractWithAmount(entity.getDateOfPayment(), contract.getId(), entity.getAmountOfPayment());
            }

            ddExpiryDate = dateOfLastPDCPayment != null ? new DateTime(dateOfLastPDCPayment).dayOfMonth().withMaximumValue().toDate() :
                    new DateTime(ddStartDate).dayOfMonth().withMaximumValue().toDate();

            long ddBankInfoGroup = new DateTime().getMillis();
            directDebit = directDebitService.createDirectDebit(signatures == null ||
                            !directDebitService.isRequiredBankInfoExist(cpt),
                    DirectDebitStatus.IN_COMPLETE, DirectDebitStatus.IN_COMPLETE, entity.getAmountOfPayment(),
                    cpt, ddStartDate, ddExpiryDate, DirectDebitType.DAILY, DirectDebitCategory.B, 0.0, false, null, null, true);

            directDebitService.copyBankInfo(cpt, directDebit, ddBankInfoGroup, false);
            if (directDebit.getNonCompletedInfo()) {
                directDebit.setStatus(DirectDebitStatus.IN_COMPLETE);
                directDebit.setMStatus(DirectDebitStatus.IN_COMPLETE);
            }

            //Jirra ACC-2107
            directDebit.setGenerateRelatedPayments(false);
            directDebit = ddRepository.save(directDebit);
            Long ddId = directDebit.getId();

            entity.setDirectDebitId(directDebit.getId());
            map.put("directDebitId", directDebit.getId());

            logger.log(Level.SEVERE, "completed; created DAILY DD: " + ddId);
            dds.add(directDebit);
        }

        for (DirectDebit dd : dds) {
            // if payment has DD, and we iterate over this DD, then we should check isFuturePDPPaymentsOnSameDDWithSameAmount, if it's not the Paymet DD then the expiry date
            // will be the expiry date of the DD.

            Date ddfExpiryDate = (paymentAlreadyHasDD && directDebit != null && ((dd.getId().equals(directDebit.getId()) && this.isFuturePDPPaymentsOnSameDDWithSameAmount(entity.getDateOfPayment(), entity.getAmountOfPayment(),
                    dd.getId())) || !dd.getId().equals(directDebit.getId())))
                    || !paymentAlreadyHasDD
                    ? dd.getExpiryDate() : new LocalDate().dayOfMonth().withMaximumValue().toDate();

            List<DirectDebitStatus> ddStatuses = Arrays.asList(
                    DirectDebitStatus.IN_COMPLETE,
                    DirectDebitStatus.PENDING_DATA_ENTRY,
                    DirectDebitStatus.PENDING);

            List<DirectDebitFile> manualFiles =
                    dd.getDirectDebitFiles() == null ? null : dd.getDirectDebitFiles().stream()
                            .filter(ddf -> ddf.getDdMethod().equals(DirectDebitMethod.MANUAL) &&
                                    ddStatuses.contains(ddf.getDdStatus()))
                            .collect(Collectors.toList());

            //Jirra ACC-2577
            if (manualFiles != null && !manualFiles.isEmpty()) {
                logger.log(Level.SEVERE, "DD already has PENDING DDFs -> mark them as for Bouncing");
                for (DirectDebitFile ddf : manualFiles) {
                    logger.log(Level.SEVERE, "mark ddf: #" + ddf.getId() + " as for bouncing");
                    ddf.setForBouncingPayment(true);
                    ddfRepository.save(ddf);
                }
            } else {
                logger.log(Level.SEVERE, "create manual ddf dd Id:" + dd.getId());
                logger.log(Level.SEVERE, "ddfExpiryDate:" + ddfExpiryDate);
                Map<String, Object> mapDd = new HashMap<>();
                mapDd.put("ddfExpiryDate", ddfExpiryDate);
                mapDd.put("signatures", signatures);
                mapDd.put("trials", entity.getTrials());
                this.createManualDDsForBouncedFlow(dd, mapDd);
            }
        }

        return map;
    }

    public void createManualDDsForBouncedFlow(DirectDebit directDebit, Map<String, Object> map) {
        List<Payment> bouncedPayments = paymentRepository
                .findByDirectDebitIdAndStatusAndReplaced(
                        directDebit.getId(), PaymentStatus.BOUNCED, Boolean.FALSE);

        Payment bouncedPayment = bouncedPayments.isEmpty() ? null :
                Collections.max(bouncedPayments, Comparator.comparing(Payment::getTrials));

        entityManager.refresh(directDebit);
        List<DirectDebitFile> ddfList = new ArrayList();
        List <DirectDebitSignature> signatures = (List <DirectDebitSignature>) map.get("signatures");
        Date ddfExpiryDate = (Date) map.get("ddfExpiryDate");
        DirectDebitConfiguration ddConfiguration = directDebit.getDdConfiguration();

        Integer numOfDDs = Math.min(ddConfiguration.getNumberOfGeneratedDDs(),
                signatures != null && !signatures.isEmpty() ? signatures.size() : Integer.MAX_VALUE);
        logger.info("numOfDDs: " + numOfDDs);

        logger.log(Level.SEVERE, "dd Id: " + directDebit.getId() +
                ", payments: " + (directDebit.getPayments() == null ? "NULL" : directDebit.getPayments().size()) +
                ", contractPayments: " + (directDebit.getContractPayments() == null ? "NULL" : directDebit.getContractPayments().size()) +
                ", signatures size:" + (signatures == null ? "NULL" : signatures.size()));

        Contract contract = directDebit.getContractPaymentTerm().getContract();
        boolean usePaperMode = false;

        if (!contract.isSigningPaperMode()) {
            Integer trials = map.containsKey("trials") ? (Integer)map.get("trials") :
                    (bouncedPayment != null ? bouncedPayment.getTrials() + 1 : null);

            int paperModeThreshold = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                    AccountingModule.PARAMETER_BOUNCING_REJECTION_SIGNING_PAPER_MODE_THRESHOLD_TRIAL));

            usePaperMode = trials != null && trials >= paperModeThreshold;

            if(!usePaperMode) {
                DirectDebitSignature rejectedSignature = directDebitSignatureService.getLastRejectedSignature(
                        directDebit.getContractPaymentTerm());
                usePaperMode = trials != null && trials >= 1 && (signatures == null || signatures.size() <= numOfDDs) &&
                        rejectedSignature != null && bouncedPayment != null &&
                        rejectedSignature.getCreationDate().before(bouncedPayment.getDateOfBouncing());
            }

            logger.log(Level.INFO, "usePaperMode: {0}; trials: {1}", new Object[] {usePaperMode, trials});
        }

        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date startDate = calendar.getTime();
        Date endDate = ddfExpiryDate != null ? ddfExpiryDate : directDebit.getExpiryDate();

        directDebit.setNonCompletedInfo(signatures == null || signatures.isEmpty());

        for (int i = 0; i < numOfDDs; i++) {
            logger.info("create new ddf, get signature");

            DirectDebitSignature signature = null;
            if (signatures != null) {
                signature = directDebitSignatureService.selectSignature(signatures, numOfDDs, 0, i);
            }

            logger.info( "Success" + (signature == null ? "NULL" : "NOT NULL"));

            DirectDebitFile ddf = directDebitService.createDDFile(directDebit, signature,
                    signatures != null && !signatures.isEmpty(),
                    false, DirectDebitMethod.MANUAL, DirectDebitType.DAILY);
            logger.info("ddf created successfully ddf id: " + ddf.getId());

            ddf.setStartDate(startDate.after(directDebit.getStartDate()) ? startDate : directDebit.getStartDate());
            ddf.setExpiryDate(endDate);
            ddf.setForBouncingPayment(true);

            if (signatures == null || signatures.isEmpty()) {
                ddf.setStatus(DirectDebitFileStatus.NOT_COMPLETED);
                ddf.setDdStatus(DirectDebitStatus.IN_COMPLETE);
                ddf.setConfirmedBankInfo(false);
            }

            if (!directDebitService.createDirectDebitActivationAttachmentIfNotExist(ddf)) ddfRepository.save(ddf);
            ddfList.add(ddf);
        }

        // ACC-6204
        directDebit.getDirectDebitFiles().addAll(ddfList);
        directDebit.setMStatus(signatures == null || signatures.isEmpty() ?
                DirectDebitStatus.IN_COMPLETE : DirectDebitStatus.PENDING);

        // ACC-3447
        if (usePaperMode)
            Setup.getApplicationContext()
                    .getBean(ContractService.class)
                    .updatePaperModeAsync(directDebit.getContractPaymentTerm(), true);

        logger.info("dd Id: " + directDebit.getId() +
                ", payments: " + (directDebit.getPayments() == null ? "NULL" : directDebit.getPayments().size()) +
                ", contractPayments: " + (directDebit.getContractPayments() == null ? "NULL" : directDebit.getContractPayments().size()));
        logger.info("save DD: " + (directDebit.getPayments() != null ?
                directDebit.getPayments().size() : "null payments"));

        ddRepository.save(directDebit);
    }

    public void addReplacementPayment(
            Payment payment,
            BankStatementTransaction bankTransaction) throws Exception {

        Payment replacementPayment = buildNewPaymentMapFromOldPayment(payment, bankTransaction);
        replacementPayment.setIsReplacement(Boolean.TRUE);
        replacementPayment.setReplacementFor(payment);
        replacementPayment.setBouncedPaymentId(payment.getId());
        paymentService.replacePayment(replacementPayment, false);
    }

    public void addNewPayment(
            Payment payment,
            BankStatementTransaction bankTransaction) {

        paymentService.createPayment(buildNewPaymentMapFromOldPayment(payment, bankTransaction));
    }

    public Payment buildNewPaymentMapFromOldPayment(
            Payment payment,
            BankStatementTransaction bankTransaction) {


        Payment paymentMap = new Payment();
        paymentMap.setAmountOfPayment(payment.getAmountOfPayment());
        paymentMap.setBankName(payment.getBankName());
        paymentMap.setDateOfPayment(payment.getDateOfPayment());
        paymentMap.setContract(payment.getContract());
        paymentMap.setIncludeWorkerSalary(payment.getIncludeWorkerSalary());
        paymentMap.setWorkerSalary(payment.getWorkerSalary());
        paymentMap.setTypeOfPayment(payment.getTypeOfPayment());
        paymentMap.setMethodOfPayment(payment.getMethodOfPayment());
        paymentMap.setStatus(PaymentStatus.RECEIVED);
        paymentMap.setVatPaidByClient(payment.getVatPaidByClient());
        paymentMap.setDiscount(payment.getDiscount());
        paymentMap.setContractPaymentId(payment.getContractPaymentId());

        if (payment.getSubType() != null) {
            paymentMap.setSubType(payment.getSubType());
        }
        // ACC-2354
        if (bankTransaction != null && bankTransaction.getId() != null) {
            paymentMap.setBankStatmentTransactionId(bankTransaction.getId());
            paymentMap.setUpdatedFromBankStatement(Boolean.TRUE);
        }

        // ACC-1811 #10 ACC-5048
        if (payment.getDirectDebitFile() != null && payment.getDirectDebitFile().getId() != null) {
            paymentMap.setDirectDebitFileId(payment.getDirectDebitFileId());
        }

        return paymentMap;
    }

//    public Boolean addClientRefundToDo(Payment payment) {
//        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
//        ClientRefundTodoController clientRefundController = Setup.getApplicationContext().getBean(ClientRefundTodoController.class);
//        PaymentRequestPurposeRepository paymentRequestPurposeRepository = Setup.getRepository(PaymentRequestPurposeRepository.class);
//
//        Contract contract = contractRepository.findOne(payment.getContract().getId());
//
//        PaymentRequestPurpose paymentRequestPurpose = paymentRequestPurposeRepository.findFirstByForClientAndNameEquals(true,
//                Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PAYMENT_REQUEST_PURPOSE_DUPLICATED_PAYMENT));
//
//        ClientRefundToDo clientRefundToDo = new ClientRefundToDo();
//        clientRefundToDo.setAutomaticRefund(true);
//        clientRefundToDo.setFlowTriggered("Payment received Event flow");
//        clientRefundToDo.setPurpose(paymentRequestPurpose);
//        clientRefundToDo.setRequestType(ClientRefundRequestType.DUPLICATED_PAYMENT);
//        clientRefundToDo.setContract(contract);
//
//        Client client = contract.getClient();
//        client.setContractPaymentTermInfo(contract);
//        clientRefundToDo.setClient(client);
//        clientRefundToDo.setMethodOfPayment(ClientRefundPaymentMethod.BANK_TRANSFER);
//        clientRefundToDo.setAmount(payment.getAmountOfPayment());
//        clientRefundToDo.setIban(client.getClientIBAN());
//        clientRefundToDo.setAccountName(client.getAccountName());
//
//        clientRefundToDo.setIgnoreRequestedByConstraint(true);
//
//        clientRefundController.createEntity(clientRefundToDo);
//
//        return true;
//    }

    public void addBouncedPaymentLog(Payment payment, DirectDebitFile ddf, String bouncingReason, Date bouncingDate) {
        BouncedPaymentLogRepository bouncedPaymentLogRepository = Setup.getRepository(BouncedPaymentLogRepository.class);

        BouncedPaymentLog bouncedPaymentLog = new BouncedPaymentLog();
        bouncedPaymentLog.setPayment(payment);
        bouncedPaymentLog.setDirectDebitFile(ddf);
        if (bouncingDate == null) {
            if (payment.getDateOfBouncing() != null)
                bouncedPaymentLog.setDateOfBouncing(payment.getDateOfBouncing());
            else
                bouncedPaymentLog.setDateOfBouncing(new java.sql.Date(System.currentTimeMillis()));
        } else {
            bouncedPaymentLog.setDateOfBouncing(new java.sql.Date(bouncingDate.getTime()));
        }

        if (bouncingReason != null) {
            bouncedPaymentLog.setBouncingReason(bouncingReason);
        }
        bouncedPaymentLogRepository.save(bouncedPaymentLog);
    }

    @Transactional
    public void processDDBouncedPayments(Long ddID) {
        SelectQuery<Payment> bouncedPaymentQuery = new SelectQuery(Payment.class);
        bouncedPaymentQuery.filterBy("directDebitId", "=", ddID);
        bouncedPaymentQuery.filterBy("status", "=", PaymentStatus.BOUNCED);
        bouncedPaymentQuery.filterBy("replaced", "=", Boolean.FALSE);
        bouncedPaymentQuery.filterBy("bouncedFlowPausedForReplacement", "=", Boolean.FALSE);
        List<Payment> bouncedPayments = bouncedPaymentQuery.execute();

        if (bouncedPayments == null || bouncedPayments.isEmpty()) {
            logger.log(Level.SEVERE, "BouncedPaymentsWithoutApprovedDDFSC bouncedPayments null or empty:");
            return;
        }

        Payment payment = Collections.max(bouncedPayments, Comparator.comparing(p -> p.getReminder()));
        Integer newReminder = payment.getReminder() + 1;

        logger.log(Level.SEVERE, "BouncedPaymentsWithoutApprovedDDFSC max payment id: " + payment.getId());
        logger.log(Level.SEVERE, "BouncedPaymentsWithoutApprovedDDFSC max payment reminder: " + payment.getReminder());
        logger.log(Level.SEVERE, "BouncedPaymentsWithoutApprovedDDFSC newReminder: " + newReminder);

        if (newReminder >= 4 && payment.getContract().getStatus().equals(ContractStatus.ACTIVE)) {
            payment.setContractScheduleDateOfTermination(
                    paymentService.setContractForTermination(payment.getContract(), "Due bounced payment", payment));
            payment.setContractCancellationReason("Due bounced payment");
        }

        bouncedPayments.forEach(p -> {
            logger.info("updating payment: " + p.getId());
            p.setReminder(newReminder);
            setPaymentAsHasNoESignature(p);
            paymentService.forceUpdatePayment(p);
        });
    }

    private void setPaymentAsHasNoESignature(Payment payment) {
        AccountingEntityPropertyRepository accountingEntityPropertyRepository = Setup.getRepository(AccountingEntityPropertyRepository.class);

        AccountingEntityProperty paymentHasNoESignature = new AccountingEntityProperty();
        paymentHasNoESignature.setOrigin(payment);
        paymentHasNoESignature.setKey(Payment.HAS_NO_E_SIGNATURE);
        paymentHasNoESignature.setValue(Boolean.TRUE.toString());
        accountingEntityPropertyRepository.save(paymentHasNoESignature);
    }

    public boolean isFuturePDPPaymentsOnSameDDWithSameAmount(java.sql.Date dateOfPayment, Double amountOfPayment, Long ddId) {
        PicklistItem monthlyPayment = PicklistHelper.getItem("TypeOfPayment", "monthly_payment");
        SelectQuery<Payment> futurePaymentsQuery = new SelectQuery(Payment.class);
        futurePaymentsQuery.filterBy("directDebitId", "=", ddId);
        futurePaymentsQuery.filterBy("status", "=", PaymentStatus.PDC);
        futurePaymentsQuery.filterBy("typeOfPayment.id", "=", monthlyPayment.getId());
        futurePaymentsQuery.filterBy("dateOfPayment", ">", dateOfPayment);

        List<Payment> futurePayments = futurePaymentsQuery.execute();

        if (futurePayments == null || futurePayments.isEmpty()) {
            logger.log(Level.SEVERE, "futurePayments null or empty");
            return false;
        }

        for (Payment futurePayment : futurePayments) {
            if (!futurePayment.getAmountOfPayment().equals(amountOfPayment)) {
                logger.log(Level.SEVERE, "payment#" + futurePayment.getId() + ", has different amount, " + futurePayment.getAmountOfPayment());
                return false;
            }
        }

        return true;
    }

    public boolean isFuturePDPPaymentsOnContractDDWithSameAmount(java.sql.Date dateOfPayment, Double amountOfPayment, Long contractId) {
        PicklistItem monthlyPayment = PicklistHelper.getItem("TypeOfPayment", "monthly_payment");
        SelectQuery<Payment> futurePaymentsQuery = new SelectQuery(Payment.class);
        futurePaymentsQuery.filterBy("contract.id", "=", contractId);
        futurePaymentsQuery.filterBy("status", "=", PaymentStatus.PDC);
        futurePaymentsQuery.filterBy("typeOfPayment.id", "=", monthlyPayment.getId());
        futurePaymentsQuery.filterBy("dateOfPayment", ">", dateOfPayment);

        List<Payment> futurePayments = futurePaymentsQuery.execute();

        if (futurePayments == null || futurePayments.isEmpty()) return false;

        for (Payment futurePayment : futurePayments) {
            if (!futurePayment.getAmountOfPayment().equals(amountOfPayment))
                return false;
        }

        return true;
    }

    private Date getLastPDCPaymentOnContractWithAmount(java.sql.Date dateOfPayment, Long contractId, Double amountOfPayment) {
        SelectQuery<Payment> futurePaymentsQuery = new SelectQuery(Payment.class);
        futurePaymentsQuery.filterBy("contract.id", "=", contractId);
        futurePaymentsQuery.filterBy("status", "=", PaymentStatus.PDC);
        futurePaymentsQuery.filterBy("dateOfPayment", ">", dateOfPayment);
        futurePaymentsQuery.filterBy("amountOfPayment", "=", amountOfPayment);
        futurePaymentsQuery.sortBy("dateOfPayment", false);
        futurePaymentsQuery.setLimit(1);

        List<Payment> futurePayments = futurePaymentsQuery.execute();

        if (futurePayments == null || futurePayments.isEmpty()) return null;

        return futurePayments.get(0).getDateOfPayment();
    }

    public boolean doWeWantMoneyFromClient(Contract contract, List<Long> excludedPaymentForBouncing) {
        if (contract.getContractProspectType().getCode().equals(PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE))) {
            // ACC-1633 ACC-2503

            boolean weNeedMoney = excludedPaymentForBouncing != null && !excludedPaymentForBouncing.isEmpty() ?
                    paymentRepository.existsByContractAndRequiredAndIdNotIn(contract, excludedPaymentForBouncing) :
                    paymentRepository.existsByContractAndRequiredForBouncing(contract, true);
            logger.info("weNeedMoney " + weNeedMoney);

            return weNeedMoney;
        }

        if (contract.getContractProspectType().getCode().equals(PicklistItem.getCode(AccountingModule.MAID_CC_PROSPECT_TYPE))) {
            String dateToBeSent = DateUtil.formatDateDashed(Arrays.asList(ContractStatus.CANCELLED, ContractStatus.EXPIRED).
                    contains(contract.getStatus()) ? contract.getDateOfTermination() :
                    contract.getScheduledDateOfTermination());

            Double correctedBalance = Setup.getApplicationContext().getBean(ContractService.class)
                    .getCorrectedBalance(contract.getId(), dateToBeSent);

            return correctedBalance != null && correctedBalance >= 1D;
        }

        return false;
    }

    public void increasePaymentsTrials(List<Payment> payments) {
        if (payments == null) return;

        for (Payment payment : payments) {
            payment.setTrials(payment.getTrials() + 1);
            logger.log(Level.SEVERE, "bounced payment increasing trials to id: " + payment.getId());
            logger.log(Level.SEVERE, "bounced payment new trials: " + payment.getTrials());

            paymentService.forceUpdatePayment(payment);
        }
    }

    public void increaseDDPaymentsTrials(DirectDebit directDebit) {
        if (directDebit == null || directDebit.getId() == null) return;

        List<Payment> payments = paymentRepository.findByDirectDebitIdAndStatusAndReplaced(directDebit.getId(), PaymentStatus.BOUNCED, Boolean.FALSE);

        increasePaymentsTrials(payments);
    }
        public boolean isBouncingFlowStopped(Payment payment) {
        logger.info("Check if Bouncing Flow is Stopped");
        Payment oldPayment = paymentRepository.findOne(payment.getId());
        if (oldPayment == null) {
            oldPayment = payment;
        }

        Contract contract = oldPayment != null && oldPayment.getContract() != null && oldPayment.getContract().getId() != null ?
                contractRepository.findOne(oldPayment.getContract().getId()) : null;
        if (contract == null) return false;

        logger.info("isCancelledWithinFirstXDays: " + contract.isCancelledWithinFirstXDays());
        if (contract.isCancelledWithinFirstXDays()) return true;

        return false;
    }

    /*public void createBouncedExpertTodo(Contract contract, DDMessaging ddMessaging) {
        logger.info("contract ID: " + (contract == null ? "" : contract.getId()));
        
        if (contract == null || contract.getId() == null || ddMessaging == null ||
                !ddMessaging.getEvent().equals(DDMessagingType.BouncedPayment)) {
            
            logger.info("exiting createExpertTodo");
            return;
        }
        
        String reasonToCall = ddMessaging.getBouncedPaymentStatus() != null && ddMessaging.getBouncedPaymentStatus().getCode().equals("has_no_e-signature") ? 
                "Last reminder to sign DD" :  "Client payment got bounced for the " + String.valueOf(Integer.parseInt(ddMessaging.getTrials()) + 1) + " time(s)";
        
        Map requestBody = new HashMap();
        requestBody.put("reasonToCall", reasonToCall);
        requestBody.put("initialNote", "");
        requestBody.put("type", VoiceResolverToDoReason.BOUNCED_PAYMENT.toString());

        moduleConnector.postJson(
                "/clientmgmt/voiceResolverToDo/createexperttodo/" + contract.getId(),
                requestBody, Map.class);
    }*/

    @Transactional
    public ContractPaymentConfirmationToDo createToDoIfNotExists(Long paymentId, ContractPaymentTerm cpt) {
            logger.log(Level.INFO,"payments id: {0}", paymentId);
            Payment p = paymentRepository.findOne(paymentId);
            if (p == null) return null;

            return paymentService.createToDoIfNotExists(p, cpt, ContractPaymentConfirmationToDo.Source.BOUNCED_PAYMENT_FLOW);
    }

    public static boolean shouldRetractBouncingFlowTermination(Payment entity) {

        if (entity.getStatus() == null) {
            logger.info("status null return false");
            return false;
        }

        if (entity.getContract() == null || entity.getContract().getId() == null) return false;

        Contract contract =  Setup.getRepository(ContractRepository.class).findOne(entity.getContract().getId());
        if (contract == null) {
            logger.info("contract not found return false");
            return false;
        }

        HistorySelectQuery<Payment> historyQuery = new HistorySelectQuery<>(Payment.class);
        historyQuery.filterBy("id", "=", entity.getId());
        historyQuery.filterByChanged("replaced");
        historyQuery.sortBy("lastModificationDate", false, true);
        historyQuery.setLimit(1);

        List<Payment> oldPayments = historyQuery.execute();
        Payment old;

        if (oldPayments.isEmpty()) {
            logger.info( "old payment null return false");
            return false;
        }

        old = oldPayments.get(0);

        PicklistItem reasonOfTermination = contract.getReasonOfTerminationList();
        logger.info("payment is replaced: " + entity.isReplaced() +
                "old payment is replaced: " + old.isReplaced() +
                "contract status: " + contract.getStatus() +
                "contract termination reason id: " + (reasonOfTermination != null ? reasonOfTermination.getId() : "NULL") +
                "contract termination reason code: " + (reasonOfTermination != null ? reasonOfTermination.getCode() : "NULL"));

        return entity.getStatus().equals(PaymentStatus.BOUNCED) &&
                entity.isReplaced() != null && entity.isReplaced() &&
                (old.isReplaced() == null || !old.isReplaced()) &&
                contract.getIsScheduledForTermination() != null && contract.getIsScheduledForTermination() &&
                contract.getScheduledDateOfTermination() != null &&
                contract.getScheduledDateOfTermination().after(new DateTime().minusDays(1).toDate()) &&
                contract.getStatus().equals(ContractStatus.ACTIVE) && reasonOfTermination != null &&
                isTerminationReasonDueBouncedPayment(reasonOfTermination.getCode());
    }
}
