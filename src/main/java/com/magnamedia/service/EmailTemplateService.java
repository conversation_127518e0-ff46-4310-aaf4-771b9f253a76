package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.entity.template.ChannelSpecificSetting;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.TemplateRepository;
import com.magnamedia.core.type.CoreParameter;
import com.magnamedia.core.type.template.ChannelSpecificSettingType;
import com.magnamedia.entity.ExpenseNotification;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.extra.DirectDebitGenerationPlanTemplate;
import com.magnamedia.extra.StringUtils;
import com.magnamedia.extra.Utils;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.extra.*;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on 22-7-2020
 *         Jirra ACC-2106
 */

@Service
public class EmailTemplateService {
    private static final Logger logger = Logger.getLogger(EmailTemplateService.class.getSimpleName());

    public List<Template> getEmailTemplates() {

        List<Template> templates = new ArrayList<>();

        templates.add(new Template("ddf_email", "Direct Debit File", "Dear @client_name@ ,<br/> <br/>"
                + "@greetings@"
                + ". Please find attached the actual Bank Direct Debit form, that we're going to submit it now to the Bank.\n"
                + "If you have any questions, please Whatsapp us at @PARAMETER_RENEWAL_PAGE_WHATSAPP_LINK@",
                "Direct Debit File email"));

        templates.add(new Template("EmailForNonDDPayment", "New non direct debit payment for confirmation",
                "There is a new non direct debit payment for @client_name@ with AED @amount@ amount and payment method @payment_method@ You can check it from here @payment_link@ .",
                "Email For Non DD Payment"));

        templates.add(new Template("emailForPaymentMatchingRecords", "Cheques Mass Update Import",
                "Payments records was confirmed successfully, you can find it here <a href='@frontEndUrl@#!/accounting/client-payments-mass-update/details/@file_id@'>Activation File</a>",
                "email For Payment Matching Records"));

        templates.add(new Template("BUCKET_BALANCE_ASSISTANT_MAIL", "",
                "This balance is the ending balance of @date@. <br/>" +
                        "If this doesn't match what you have, please contact @param_1@ on @param_2@ <br/>Thanks",
                "BUCKET BALANCE ASSISTANT MAIL"));

        templates.add(new Template("PAYMENT_REPORT_MAIL", "Payments Report",
                "",
                "PAYMENT REPORT MAIL"));

        /*templates.add(new Template("Bank_Direct_Debit_Activation_File_Mail", "Bank Direct Debit Activation File",
                "your file was parsed successfully, you can find it here <a href='@frontEndUrl@#!/accounting/payments-automation/importing-file/ddFile/@file_id@'>Activation File</a>",
                "Bank Direct Debit Activation File"));*/

        templates.add(new Template("Bank_Direct_Debit_Activation_File_Error_Mail", "Bank Direct Debit Activation File",
                "your file was not parsed successfully, an error occurred while parsing",
                "Error when parse Bank Direct Debit Activation File"));

        templates.add(new Template("contract_payment_term_updated_changed", "contract payment term updated",
                "The configs for contract @contract_id@ was changed, old full amount was : @old_monthly_payment@ , old discounted amount was : " +
                        " @old_discount_amount@ , new full amount is : @new_full_amount@ , new discounted amount is : @new_discount_amount@ ",
                "contract payment term updated and amount changed"));

        templates.add(new Template("contract_payment_term_updated_kept", "contract payment term updated",
                "The configs for contract @contract_id@ was kept and the DD Amount is @amount@ ",
                "contract payment term updated and cpt kept"));

        templates.add(new Template("new_data_entry_task_mail", "New data entry task.",
                "Hi,\n" +
                        "A new \"Data entry\" task has been added, please click " +
                        "<a href='@pageURL@'>here</a>" +
                        " and check it out.\n Thanks",
                "New data entry task."));

        templates.add(new Template("client_sign_error_email", "Client Sign Error",
                "Client @client_id@ tried to sign files but an error occurred at @date@",
                "Client Sign Error"));

        templates.add(new Template("direct_debit_file_report_email", "Direct Debit File Report",
                "",
                "Direct Debit File Report"));

        templates.add(new Template("manual_dd_batch_file", "Manual DD Batch File for",
                "",
                "Manual DD Batch File for"));

        templates.add(new Template("report_bounced_cheque_to_police", "Report bounced cheque to police",
                "The payment related to this cheque has been bounced and not replaced for @number_of_days@ days, please report it to police. \n" +
                        "Cheque info:\n \n" +
                        "Cheque #: @cheque_number@ \n" +
                        "Bank name: @bank_name@ \n" +
                        "Name on the cheque: @cheque_name@",
                "Report bounced cheque to police"));

        templates.add(new Template("MAID.cc_CANCELLATION_RETRACTION", "Maid.cc Cancellation Retraction",
                "@Client_Name@, ERP ID: @ERP_ID@ - Contract Id: @Contract_Id@ undid his cancellation from the app and the client's direct debit forms are sent to the bank for cancellation.",
                "Maid.cc, upon Cancellation Retraction"));
        // it's rendered as HTML
        templates.add(new Template("new_rejection_reason", "New Rejection Reason",
                "Greetings,<br/>" +
                        "Please be Informed that some clients' direct debit forms were rejected by the bank for a new rejection reason: @rejection_reason@.\n" +
                        "@html_table@",
                "New Rejection Reason came from the Bank."));
        // it's rendered as HTML
        templates.add(new Template("dd_activation_rpa", "New file has been uploaded and proceed by RPA",
                "Dear Accounting team, We would like to notify you that RPA has uploaded a new Activation file " +
                        "(<a href='@file_link@'>DD 300</a>) successfully, " +
                        "and here is the result:<br/><ls>" +
                        "<li> @matched_confirmed@ DD Matched and confirmed@matched_confirmed_needs_action@</li>" +
                        "<li> @unmatched_confirmed@ DD Unmatched and confirmed@unmatched_confirmed_needs_action@</li>" +
                        "<li> @matched_rejected@ DD Matched and rejected@matched_rejected_needs_action@</li>" +
                        "<li> @unmatched_rejected@ DD Unmatched and rejected@unmatched_rejected_needs_action@</li>" +
                        "</ls>"
                ,
                "New DD Activation File has been uploaded and processed by RPA."));
        // it's rendered as HTML
        templates.add(new Template("dd_cancellation_rpa", "New file has been uploaded and proceed by RPA",
                "Dear Accounting team, We would like to notify you that RPA has uploaded a new cancellation file " +
                        "(<a href='@file_link@'>DD 400</a>) successfully, " +
                        "and here is the result:<br/><ls>" +
                        "<li> @matched@ DD Matched and confirmed@matched_needs_action@</li>" +
                        "<li> @unmatched@ DD Unmatched and confirmed@unmatched_needs_action@</li>" +
                        "</ls>",
                "New DD Cancellation File has been uploaded and processed by RPA."));
        // it's rendered as HTML
        templates.add(new Template("coo_approval", "Required COO Approvals",
                "You have to review:<br/>" +
                        "1. @office_staff_num@ office staff approval requests.<br/>" +
                        "2. @client_refund_requests_num@ client refund requests.<br/>" +
                        "3. @bank_transfer_confirmation_num@ bank transfer confirmation.<br/>" +
                        "Please click [<a href='@link@'>here</a>] to proceed.",
                "Required COO Approvals"));
        // it's rendered as HTML
        templates.add(new Template("dd_cancellation_failed_due_rpa_process", "ERP Failed to cancel DD (@DD_ID@)",
                "ERP couldn't cancel the DD (@dd_id@) because it is under RPA approval process, please check and take the proper action.",
                "DD Cancellation Failed due to RPA process"));

        //Jirra CMA-996
        templates.add(new Template("switching_nationality_dd_forms_amending_confirmation", "Get WhatsApp approval: client_name to amend his Bank Direct debit",
                "Client Name: @client_name@ <br/>" +
                        "Details: @details@",
                "Client has confirmed to amend his DD Forms due to Switching Nationality."));
        // it's rendered as HTML, ACC-3155
        templates.add(new Template("client_refund_requester_report", "Actions Done by Manager on Added Refunds",
                "Please note that the managers approved the following refunds that you added:<br/>" +
                        "@approved_requests@" +
                        "While he rejected the following refunds:<br/>"
                        + "@rejected_requests@",
                "Actions Done by Manager on Added Refunds"));

        templates.add(new Template("client_refund_has_been_amended", "Client refund has been amended (@client_name@)",
                "Please note that the refund of client (@client_name@) has been amended: <br/>" +
                        "Contract ID: @contract_id@ <br/>" +
                        "Old refund amount: @old_refund_amount@ <br/>" +
                        "New refund amount:  @new_refund_amount@ <br/>" +
                        "Purpose: @purpose@ " +
                        "@manager_notes@",
                "When purpose or amount of refund modified"));

        //ACC-2988
        templates.add(new Template("bucket_replenishment_notification", "Bucket @bucket_name@ replenished @replenishment_count@ times this week",
                "Bucket @bucket_name@ replenished @replenishment_count@ times this week. Please consider changing the bucket replenishment level.",
                "email send to audit manager for replenishment notification"));

        templates.add(new Template("expense_request_approval", "Expense Approval Request for @expense_caption@",
                "Dear, <br/>" +
                "Please approve the following expense that is requested by @expense_requested_by@. Requested expense details: <br/>" +
                "Expense: @expense_caption@ <br/>" +
                "Amount: @currency@ @amount@ <br/>" +
                "@amount_of_loan_to_add_description@" +
                "Requester's notes: @requester_notes@" +
                "@file_description@" +
                "<a href=\"@approve_link@\">Approve</a>&nbsp;&nbsp;|&nbsp;&nbsp;" +
                        "<a href=\"@reject_link@\">Reject</a>",
                "email to manager to approve/reject expense request"));

        templates.add(new Template("expense_request_notification_amount_limit", "Expense Notification",
                "@expense_caption@  related to @related_to@ has amount @amount@ (Threshold = @threshold@).<br/>" +
                        "Expense details: @expense_description@",
                "Expense Notification"));

        templates.add(new Template("expense_request_notification_amount_percentage", "Expense Notification",
                "@expense_caption@  related to @related_to@ has amount @amount@ @percentageOperation@ than @percentage@% of the average of @percentageNOCompared@ @percentageComparedTo@.<br/>" +
                        "Expense details: @expense_description@",
                "Expense Notification"));

        templates.add(new Template("expense_request_notification_amount_repeated", "Expense Notification",
                "@expense_caption@  related to @related_to@ is requested more than @number_of_requests_threshold@ in this @numberOfRquestComparedTo@. <br/>" +
                        "Expense details: @expense_description@.",
                "Expense Notification"));
        // it's rendered as HTML
        templates.add(new Template("unsuccessful_dds_cancellation", "Unsuccessful DD Cancellation",
                "Greetings, <br/>" +
                        "Please be informed that the bank has rejected the cancelation requests of the below forms:<br/>" +
                        "@html_table@",
                "New DD Cancellation File has been uploaded and processed with Unsuccessful DD Cancellation."));

        templates.add(new Template("PURCHASING_STOCK_KEEPER_REMINDER", false,
                "Hello @stock_keeper_name@,\n\n" +
                        "The order of these item categories is scheduled for tomorrow.  " +
                        "Please update the inventory on SalesBinder TODAY for the following categories:\n\n " +
                        "@categories@",
                "mail be sent to stock keeper to update items in sales binder"));
        templates.add(new Template("PURCHASING_STOCK_KEEPER_MANAGER_REMINDER", false,
                "Hello @stock_keeper_manager_name@,\n\n" +
                        "@stock_keeper_name@, did not update the inventory.  " +
                        "Please update the inventory on SalesBinder NOW for the following categories:\n\n " +
                        "@categories@",
                "mail be sent to stock keeper to update items in sales binder"));
        templates.add(new Template("PURCHASE_ORDER_COST_EDITED", false,
                "Please note that the cost of purchase order for @supplier_name@ and @category@ is edited on @date@. " +
                        "Old value is @old_value@. New value is @new_value@.",
                "mail be sent controller admin when Purchase Order Cost Edited"));

        templates.add(new Template("dd_pending_for_cancellation", "DDs Pending for Cancellation for more than 5 days",
                "Greetings, <br/>" +
                        "Please be informed that the below forms have been pending for cancellation for more than @num_of_days@ days.<br/>" +
                        "@html_table@",
                "Pending for Cancellation DDs for more than n Days."));

        // it's rendered as HTML, Jirra ACC-3334
        templates.add(new Template("switching_bank_account_cc_app", "Switch Bank Account - CC APP",
                "Greetings<br/>" +
                        "Please be Informed that @client_name@ has switched his bank account from the cc app for contract @Contract_ID@",
                "Client Switched his Bank Account."));
        templates.add(new Template("coo_question_added", "Question from Mario",
                "Mario is questioning about @questioned_record@. please click <a href='@link@'>here</a> to view his question and further info",
                "Mario is asking about coo approval screen records."));

        templates.add(new Template("coo_question_answered", "@user_name@ replied to your question",
                        "<b>Your question was:</b> @COO_question@<br/>" +
                        "<b>@user_name@'s answer is:</b> @user_answer@<br/>" +
                        "To check the details of the record or to take any action, please click <a href='@link@'>here</a>",
                "A user has answered Jad about his question."));

        templates.add(new Template("coo_rejected_record", "@label@ Rejected",
                "@record_info@ <br />" +
                        "COO Rejection Notes: @rejection_notes@",
                "A user has answered Jad about his question."));

        templates.add(new Template("expense_request_rejected_approved", "@expense_name@ - @approved_or_reject@ - @date@",
                "@record_info@ <br/>Approver Name: @approver_name@<br/>Approver @rejection_or_approval@ notes: @notes@",
                "Send rejected-approved expense request from erp or the email"));
        // it's rendered as HTML
        templates.add(new Template("pending_coo_approval", "Pending Approvals for @today_date",
                "Click [<a href='@link@'>here</a>] to view the pending approvals.",
                "Pending Approval Email"));

        // ACC-5662
        templates.add(createNewModelTemplate("approve_expense_remind_manager",
                "There are expense requests that require your approval " +
                "<div> " +
                "<br/> " +
                "<div style=\"display: inline-flex;\">To view the expense requests&nbsp; " +
                "<a href='@link@'>click on this link.</a> " +
                "</div> " +
                "</div>"));

        // it's rendered as HTML
        templates.add(new Template("bank_confirmation_and_night_review", "Bank Confirmation and Night Review for @today_date",
                "Click [<a href='@link1@'>here</a>] to view the pending bank confirmation. <br/>" +
                        "Click [<a href='@link2@'>here</a>] to view the night review.",
                "Bank Confirmation and Night Review Email"));
        templates.add(new Template("alert_vip_has_collection_flow", "Alert : VIP client has a running collection flow",
                "Client name: @client_name@ with Contract: @contract_id@. Link to the client Details: @erp_profile_link@<br/>" +
                        "Event : @event@<br/>" +
                        "Trial : @number_of_trials@<br/>" +
                        "Reminder : @number_of_reminder@<br/>" +
                        "Message : @message_sent_to_client@",
                "Client is vip and dd messaging marked as alert for vip"));
        // it's rendered as HTML, Jirra ACC-3487
        templates.add(new Template("wire_transfer_amount_difference", "Missing transfer amount for client (@client_name@)",
                "Please follow up with the client to collect the remaining amount of AED @amount_difference@ and make sure to add this remaining amount as a Service Charge when the client transfers it:<br/>" +
                        "@payment_list@",
                "There is a difference between required amount and the actual paid amount."));

        templates.add(new Template(DirectDebitGenerationPlanTemplate.FAILED_DDS_GENERATION_REPORT.toString(),
                "Failed postpone dds generation report",
                "",
                "Failed postpone DDS generation"));

        templates.add(createNewModelTemplate(DirectDebitGenerationPlanTemplate.NON_MONTHLY_POSTPONED_DDS_GENERATION_REPORT.toString(),
                "Attached is the list of the non-monthly Postponed DDs that will be triggered next month.",
                "Scheduled Postponed DDs for the month of @next_month@"));

        // ACC-8752
        templates.add(createNewModelTemplate(
                "email_for_transaction_posting_rule",
                "<h4>Please create a transaction posting rule for:</h4>" +
                        "<ul>" +
                            "<li>Payment Type: @type_of_payment@</li>" +
                            "<li>Payment Method: @method@</li>" +
                            "<li>Contract Type: @contract_type@</li>" +
                            "<li>Is Initial: @is_initial@</li>" +
                        "</ul>" +
                "<h4>Please find the received payment details below:</h4>" +
                        "<ul>" +
                            "<li>Payment ID: @payment_id@</li>" +
                            "<li>Contract ID: @contract_id@</li>" +
                            "<li>Client Profile: @link_to_client_profile@</li>" +
                        "</ul>",
                "Create a Transaction Posting Rules for the payment @type_of_payment@"));


        templates.add(new Template("direct_debit_multiple_confirmed_file", "Alert - master DDs has multiple Automatic/Manual DDs confirmed",
                "The following contracts have a master DD with multiple Automatic/Manual DDs confirmed:<br/> @contract_ids@",
                "Direct debit multiple confirmed file"));

        templates.add(new Template("expense_todo_creation", "@todo_type@ TO-DO Notification",
                "Hi, @todo_type@ to-do is created. Please check the @related_screen_name@ screen: @link_of_the_screen@.",
                "A new expense todo has been created"));

        templates.add(new Template.TemplateBuilder()
                .Template("no_prorated_dd_for_one_month_agreement",
                        "", "the email that will be sent in case no dd cover prorated payment")
                        .subject("No prorated DD for one-month client paying via DD (@client_name@)")
                .text("Please note that this client (@client_name@), is a client with one-month agreement and paying via DD and has no prorated payment.<br/>" +
                        "Client profile link: @link@")
                .method("e-mail")
                .build());

        templates.add(new Template("cashier_reject_cash_collection", "Rejection of Cash Collection to do",
                "Cash collection to-do with amount @amount@ for @housemaid_name@ for absconding removal was rejected, " +
                        "and @user_name@ noted the following: @rejection_notes@",
                "Cashier reject cash collection"));

        // it's rendered as HTML // ACC-5588
        templates.add(createNewModelTemplate("bank_transfer_confirmation_coo",
                "Please go to your screen and check the following tab to take action: " +
                     "<br/>" +
                     "<br/>" +
                     "<div><span style=\"margin-left: 20px;\">&#8226;</span>" +
                         "<div style=\"margin-left: 5px;display: inline-flex;\">Bank transfer confirmation tab: </div>" +
                         "<br/>" +
                         "<div style=\"margin-left: 30px;\">" +
                            "To confirm that u authorised the transfers that are already done, <span>click&nbsp;</span><a href='@link@'>here</a>" +
                         "</div>" +
                     "</div>"));

        templates.add(createNewModelTemplate(
                "client_paying_via_credit_card_manual_switching_nationality",
                "<div>The Customer Support team has decided to switch the nationality for a client with a running collection flow.</div>" +
                        "</br></br>" +
                        "<div>Please review the case and submit the needed changes.</div>" +
                        "</br></br>" +
                        "<div>&bull; Old Maid’s nationality: @old_nationality@ </div></br>" +
                        "<div>&bull; New Maid’s nationality: @new_nationality@ </div></br>" +
                        "<div>&bull; Client ID: @client_id@ </div></br>" +
                        "<div>&bull; Old Maid ID: @old_maid_id@ </div></br>" +
                        "<div>&bull; New Maid ID: @new_maid_id@ </div></br>" +
                        "<div>&bull; Old CPT @old_cpt_name@</div></br>" +
                        "<div>&bull; New CPT @new_cpt_name@</div>"));

        templates.add(createNewModelTemplate(
                "client_paying_via_credit_card_downgrade_email",
                "Please refund the below client because the client is downgrading from @previous_nationality@ to @new_nationality@." +
                        "<br/>" +
                        "<br/>" +
                        "<div>" +
                                "<span>&#8226;</span>" +
                                "<div style=\"margin-left: 5px;display: inline-flex;\">Client Name: @client_name@" +
                                "</div>" +
                            "<br/>" +
                                "<span>&#8226;</span>" +
                                "<div style=\"margin-left: 5px;display: inline-flex;\">Client ID: @client_id@" +
                                "</div>" +
                            "<br/>" +
                                "<span>&#8226;</span>" +
                                "<div style=\"margin-left: 5px;display: inline-flex;\">Contract ID: @contract_id@" +
                                "</div>" +
                            "<br/>" +
                                "<span>&#8226;</span>" +
                                "<div style=\"margin-left: 5px;display: inline-flex;\">Refund amount: @refund_amount@" +
                                "</div>" +
                        "</div>"));

        templates.add(createNewModelTemplate("dd_requested_and_client_paying_via_credit_card",
                "Please check the payment details related to this client @client_id@ on this contract @contract_id@, " +
                        "it has been added after the trigger of the initial flow, which means that we don’t have a way to collect it." +
                        "<br/><br/>" +
                        "Please reach out to the client and send a Card Payment Link and ask him to pay it separately." +
                        "<br/><br/>" +
                        "Payment details:" +
                        "<br/>" +
                        "<ls>" +
                            "<li>Date: @date@</li>" +
                            "<li>Type: @type@</li>" +
                            "<li>Amount: @amount@</li>" +
                        "</ls>"));

        // ACC-6831 Email #1
        templates.add(createNewModelTemplate("cashier_schedule_collection_account_ceiling_exceeded",
                "Hello @bucket_holder_name@<br/>" +
                        "<br/>" +
                        "Please note that your current balance has reached the Transguard Ceiling Amount of @transguard_ceiling_amount@.<br/>" +
                        "<br/>" +
                        "Please schedule a convenient date for the Transguard to visit and collect the funds.",
                "Transguard Account Ceiling Exceeded - Immediate Action Required"));


        templates.add(createNewModelTemplate(
                "client_paying_via_credit_card_switched_nationalities_several_times",
                "The below client switched nationalities more than once in the current month. Please check his payments and fix anything if needed." +
                        "<br/>" +
                        "<br/>" +
                        "<div>" +
                            "<span>&#8226;</span>" +
                            "<div style=\"margin-left: 5px;display: inline-flex;\">Client Name: @client_name@" +
                            "</div>" +
                            "<br/>" +
                            "<span>&#8226;</span>" +
                            "<div style=\"margin-left: 5px;display: inline-flex;\">Client ID: @client_id@" +
                            "</div>" +
                            "<br/>" +
                            "<span>&#8226;</span>" +
                            "<div style=\"margin-left: 5px;display: inline-flex;\">Contract ID: @contract_id@" +
                            "</div>" +
                            "<br/>" +
                        "</div>" +
                        "<br/>" +
                        "<br/>" +
                        "Thanks",
                "Client paying via credit card switched nationalities several times"));

        templates.add(createNewModelTemplate(
                "alert_of_tenancy_contract_expiry_date_email",
                "<div>" +
                    "@tenancy_contract_name@ will expire on @tenancy_contract_expiry_date@" +
                    "<br>" +
                    "Take the necessary actions to renew or terminate it, or forward this email to the concerned person." +
                    "@link_of_the_page@" +
                "</div>"));

        // ---- related to ACC-7580 Risk Document Management Screen email templates for layers.
        templates.add(createNewModelTemplate("risk_document_layer_one_alert",
                "<div>@document_name@ expiry date is on @expiry_date@, " +
                        "we still have @layer_one_days_before_expiry@ to renew. " +
                        "Please start the renewal process to avoid any delays. " +
                        "If you started with the renewal process, please click on this button @on_it_action@" +
                        "</div>",
                "@document_name@ is expiring in @layer_one_days_before_expiry@ days"));

        templates.add(createNewModelTemplate("risk_document_layer_two_under_renewal_alert2",
                "<div>@document_name@ expiry date is @expiry_date@, " +
                        "we still have @layer2_days_before_expiry@ to renew. " +
                        "Please note that @assignee@ is working on it." +
                        "</div>",
                "@document_name@ is expiring in @layer2_days_before_expiry@ days"));

        templates.add(createNewModelTemplate("risk_document_layer_two_pending_renewal_alert",
                "<div>@document_name@ expiry date is @expiry_date@, " +
                        "we still have @layer2_days_before_expiry@ to renew. " +
                        "Please reach out to @assignee@ to identify the reason behind the delay." +
                        "</div>",
                "@document_name@ is expiring in @layer2_days_before_expiry@ days"));

        templates.add(createNewModelTemplate("risk_document_expiry_date_alert",
                "<div>@document_name@ expires today. " +
                        "The status of renewal is @status@.  " +
                        "Immediate action is needed to avoid repercussions." +
                        "</div>",
                "@document_name@ expires TODAY"));

        templates.add(createNewModelTemplate("risk_document_renewal_alert",
                "<div>@document_name@ has been renewed and its new expiry date is @new_expiry_date@. " +
                        "Click here to check the details @link_to_screen@." +
                        "</div>",
                "@document_name@ has been successfully Renewed!"));

        templates.add(createNewModelTemplate("requires_visit_alert",
                "<div>The visit for @service_report_name@ is scheduled for today. " +
                        "Please reach out to the company to confirm their visit" +
                        "</div>",
                "@service_report_name@ visit is scheduled for today!"));

        templates.add(createNewModelTemplate(
                "credit_card_refund_failed_email",
            "Dear," +
                    "<br/>" +
                    "Please note that @provider@ API rejected a refund with response “unauthorised” for the below client:" +
                    "<br/>" +
                    "<div>" +
                    "<div style='margin-left: 10px;display: inline-flex;'>Client ID: @client_id@</div>" +
                    "<br/>" +
                    "<div style='margin-left: 10px;display: inline-flex;'>Client Name: @client_name@</div>" +
                    "<br/>" +
                    "<div style='margin-left: 10px;display: inline-flex;'>Contract ID: @contract_id@</div>" +
                    "<br/>" +
                    "<div style='margin-left: 10px;display: inline-flex;'>Refund Amount: @refund_amount@</div>" +
                    "<br/>" +
                    "<div style='margin-left: 10px;display: inline-flex;'>Refund Purpose: @refund_purpose@</div>" +
                    "<br/>" +
                    "</div>" +
                    "<br/>" +
                    "Please take appropriate action accordingly.",
                "Refund Unauthorised from @provider@"));

        templates.add(createNewModelTemplate(
                "alert_of_tenancy_contract_expiry_date_email",
                "<div>" +
                    "@tenancy_contract_name@ will expire on @tenancy_contract_expiry_date@" +
                    "<br>" +
                    "Take the necessary actions to renew or terminate it, or forward this email to the concerned person." +
                    "@link_of_the_page@" +
                "</div>"));

        templates.add(new Template.TemplateBuilder()
                .Template("bank_transfer_manager_rejected",
                        "Bank Transfer Rejected for @Type@ - @expense_name@",
                        "the email that will be sent to the requester upon bank transfer todo get rejected by the manager")
                .text("Beneficiary: @beneficiary@ <br/>" +
                        "Type: @type@ <br/>" +
                        "Description: @description@ <br/>" +
                        "Amount (AED): @amount_aed@ <br/>" +
                        "Amount: @amount@ <br/>" +
                        "Requester: @requester@ <br/>" +
                        "Approvals: @approvals@ <br/>" +
                        "Due Since: @due_since@ <br/>" +
                        "Manager Rejection notes: @rejection_notes@")
                .method("e-mail")
                .build());

        templates.add(createNewModelTemplate(
                "dd_300_activation_report",
                "<ul>" +
                    "<li>Report name: @report_name@</li>" +
                    "<li>Date and time of uploading the file: @upload_date@</li>" +
                    "<li>Number of the matched and accepted: @matched_accepted_count@</li>" +
                    "<li>Number of matched and rejected: @matched_rejected_count@</li>" +
                    "<li>Number of unmatched: @unmatched_count@</li>" +
                    "<li>Details Link: <a href='@details_link@'>click here</a></li>" +
                 "</ul>",
                "DD 300 Activation Report"));

        templates.add(createNewModelTemplate(
                "credit_card_unknown_error_codes",
                "Hi," +
                "<br/>" +
                " Please note that we have the following new CC payments issues codes" +
                "<div>" +
                    "@html_table@"+
                "</div>" +
                "<br/>" +
                "Please click on the following link and link the new codes with their related flows:" +
                "<br/>" +
                "@card_payments_Issues_management_screen_link@",
                "New Recurring Card Payments Issues"));

        templates.add(createNewModelTemplate(
                "switch_nationality_fees_mapped_replacement_failed_email_recipients",
                "Client @client_name@ is paying via credit card and he had a failed upgrading replacement " +
                "after having paid the upgrading fee. Please add a refund for him. Thanks.",
                "Upgrading replacement failed after the client paying upgrading fee"));

        templates.add(createNewModelTemplate(
                "payment_collected_after_termination",
                "Please note that a client has paid a payment after the termination of his contract, " +
                        "please check and decide if you need to reactivate his contract or not. Here are the details:<br/>" +
                        "<ls>" +
                        "<li> Client ID: @client_id@</li>" +
                        "<li>Client Name: @client_name@</li>" +
                        "<li>Contract ID: @contract_id@</li>" +
                        "<li>Payment ID: @payment_id@</li>" +
                        "<li>Payment Collection Date: @date@</li>" +
                        "</ls>",
                "Payment Collected After Termination - @contract_id@"));

        templates.add(createNewModelTemplate(
                "alert_of_tenancy_contract_expiry_date_email",
                "<div>" +
                    "@tenancy_contract_name@ will expire on @tenancy_contract_expiry_date@" +
                    "<br>" +
                    "Take the necessary actions to renew or terminate it, or forward this email to the concerned person." +
                    "@link_of_the_page@" +
                "</div>"));

        templates.add(createNewModelTemplate(
                "contract_reactivated_while_switch_nationality_flow_running",
                "Contract @contract_id@ was reactivated while having a switch nationality flow running. Kindly do the below:<br/>" +
                        "<ls>" +
                            "<li>Trigger the replacement again for the contract.</li>" +
                         "<li>Review the contract DDs.</li>" +
                        "</ls><br/>" +
                        "Thank you.",
                "Contract Reactivated with Switching Nationality Flow"));

        templates.add(createNewModelTemplate(
                "alert_of_tenancy_contract_expiry_date_email",
                "<div>" +
                    "@tenancy_contract_name@ will expire on @tenancy_contract_expiry_date@" +
                    "<br>" +
                    "Take the necessary actions to renew or terminate it, or forward this email to the concerned person." +
                    "@link_of_the_page@" +
                "</div>"));

        templates.add(createNewModelTemplate(
                "expense_requested_deleted_email",
                "Please note that @user_name@ deleted the requested expense @expense_name@<br/>" +
                        "Expense Description: @expense_Description@<br/><br/>" +
                        "Expense Amount: @expense_amount@<br/><br/>" +
                        "Date of Request: @date_Of_request@<br/><br/>" +
                        "Requested by: @requested_by@<br/><br/><br/>" +
                        "for this reason: @notes@",
                "The requested @expense_name@ was deleted by @user_name@"));

        templates.add(createNewModelTemplate(
                "rejected_refund_requests_email",
                "Please note that Refund Request @refund_id@ of the client @client_name@ has been rejected: " +
                        "<br/>" +
                        "<div>" +
                            "<ls>" +
                                "<li style='margin-left: 10px;'> <div style='display: inline-flex;'>Client ID: @client_id@</div></li>" +
                                "<li style='margin-left: 10px;'> <div style='display: inline-flex;'>Contract ID: @contract_id@</div></li>" +
                                "<li style='margin-left: 10px;'> <div style='display: inline-flex;'>Refund ID: @refund_id@</div></li>" +
                                "<li style='margin-left: 10px;'> <div style='display: inline-flex;'>The user who rejected the refund: @user_name@, @user_email@</div></li>" +
                                "<li style='margin-left: 10px;'> <div style='display: inline-flex;'>Rejected at: @rejected_at_time@</div></li>" +
                                "<li style='margin-left: 10px;'> <div style='display: inline-flex;'>Reason of Rejection: @reason_of_rejection@</div></li>" +
                            "</ls>" +
                        "</div>",
                "Refund Request @refund_id@ for Client @client_name@ was rejected."));

        templates.add(createNewModelTemplate(
                "alert_of_tenancy_contract_expiry_date_email",
                "<div>" +
                    "@tenancy_contract_name@ will expire on @tenancy_contract_expiry_date@" +
                    "<br>" +
                    "Take the necessary actions to renew or terminate it, or forward this email to the concerned person." +
                    "@link_of_the_page@" +
                "</div>"));

        // ---- related to ACC-7580 Risk Document Management Screen email templates for layers.
        templates.add(createNewModelTemplate("risk_document_layer_one_alert",
                "<div>@document_name@ expiry date is on @expiry_date@, " +
                        "we still have @layer_one_days_before_expiry@ to renew. " +
                        "Please start the renewal process to avoid any delays. " +
                        "If you started with the renewal process, please click on this button @on_it_action@" +
                        "</div>",
                "@document_name@ is expiring in @layer_one_days_before_expiry@ days"));

        templates.add(createNewModelTemplate("risk_document_layer_two_under_renewal_alert2",
                "<div>@document_name@ expiry date is @expiry_date@, " +
                        "we still have @layer2_days_before_expiry@ to renew. " +
                        "Please note that @assignee@ is working on it." +
                     "</div>",
                "@document_name@ is expiring in @layer2_days_before_expiry@ days"));

        templates.add(createNewModelTemplate("risk_document_layer_two_pending_renewal_alert",
                "<div>@document_name@ expiry date is @expiry_date@, " +
                        "we still have @layer2_days_before_expiry@ to renew. " +
                        "Please reach out to @assignee@ to identify the reason behind the delay." +
                     "</div>",
                "@document_name@ is expiring in @layer2_days_before_expiry@ days"));

        templates.add(createNewModelTemplate("risk_document_expiry_date_alert",
                "<div>@document_name@ expires today. " +
                        "The status of renewal is @status@.  " +
                        "Immediate action is needed to avoid repercussions." +
                     "</div>",
                "@document_name@ expires TODAY"));

        templates.add(createNewModelTemplate("risk_document_renewal_alert",
                "<div>@document_name@ has been renewed and its new expiry date is @new_expiry_date@. " +
                        "Click here to check the details @link_to_screen@." +
                     "</div>",
                "@document_name@ has been successfully Renewed!"));

        templates.add(createNewModelTemplate("requires_visit_alert",
                "<div>The visit for @service_report_name@ is scheduled for today. " +
                        "Please reach out to the company to confirm their visit" +
                    "</div>",
                "@service_report_name@ visit is scheduled for today!"));


        templates.add(createNewModelTemplate(
                "expense_requested_deleted_email",
                "Please note that @user_name@ deleted the requested expense @expense_name@<br/>" +
                        "Expense Description: @expense_Description@<br/><br/>" +
                        "Expense Amount: @expense_amount@<br/><br/>" +
                        "Date of Request: @date_Of_request@<br/><br/>" +
                        "Requested by: @requested_by@<br/><br/><br/>" +
                        "for this reason: @notes@",
                "The requested @expense_name@ was deleted by @user_name@"));

        templates.add(createNewModelTemplate(
                "mv_maid_failed_medical_auto_refund",
                "The following client has a miscalculated transaction. Kindly look into the failed medical case and find the discrepancy." +
                     "<br/> " +
                     "Client: @link@ " +
                     "<br/> " +
                     "Contract ID: @contract_id@" + ", " + "Payment Method (@contract_type@) " +
                     "<br /> " +
                     "Amount needed from the client = Received payments - Refunds - Housemaid Transactions - Ticket fee = @remaining_amount@ " +
                     "<br/> " +
                     "Received payments = @received_payments@ " +
                     "<br/> " +
                     "Refunds = @refundedAmount@ " +
                     "<br/> " +
                     "Housemaid Transactions = @hm_transactions@" +
                     "<br/> " +
                     "Ticket fee = @mv_maid_failed_ticket_and_administration_fees@",
                "Payment Miscalculation - Failed Medical - @maid name@"));

        templates.add(createNewModelTemplate(
                "switch_nationality_fees_mapped_replacement_failed_email_recipients",
                "Client @client_name@ is paying via credit card and he had a failed upgrading replacement " +
                "after having paid the upgrading fee. Please add a refund for him. Thanks.",
                "Upgrading replacement failed after the client paying upgrading fee"));

        // ACC-9005
        templates.add(createNewModelTemplate(
                "unmatched_dd_cancellation_report",
                "@reports@",
                "Unmatched Cases in DD 400 Cancellation Report"));

        templates.add(createNewModelTemplate("contract_termination_max_trial_alert_incomplete_rejection_flow",
                "Client profile link: @client_profile_link@<br/>" +
                        "Contract ID: @contract_id@<br/>" +
                        "Contract Type: @contract_type@<br/>" +
                        "Schedule for termination: @termination_date@",
                "Reschedule cancellation of contract due to accounting flows (incomplete/rejection flow)"));

        templates.add(createNewModelTemplate(
                "dd_400_cancellation_report",
                "<ul>" +
                         "<li>Report name: @report_name@</li>" +
                         "<li>Date and time of uploading the file: @upload_date@</li>" +
                         "<li>Number of the matched and accepted: @matched_accepted_count@</li>" +
                         "<li>Number of matched and rejected: @matched_rejected_count@</li>" +
                         "<li>Number of unmatched: @unmatched_count@</li>" +
                         "<li>Details Link: <a href='@details_link@'>click here</a></li>" +
                       "</ul>",
                "DD 400 Cancellation Report"));

        templates.add(createNewModelTemplate(
                "expense_payment_reassignment",
                "Hi, kindly note that the following cash expense request has been re-assigned to" +
                        " @new_cashier_name@ by @current_user_name@:\n" +
                        "Expense: @expense_name@\n" +
                        "Beneficiary: @beneficiary_name@\n" +
                        "Amount: @amount@\n" +
                        "Expense Notes: @expense_notes@\n" +
                        "@current_user_name@'s Notes: @reassignment_notes@",
                "Cash Expense Request Reassignment"));

        templates.add(createNewModelTemplate("expense_payment_dismissal",
                "Hi, kindly note that the following cash expense request has been dismissed by @current_user_name@:\n" +
                        "Expense: @expense_name@\n" +
                        "Beneficiary: @beneficiary_name@\n" +
                        "Amount: @amount@\n" +
                        "Expense Notes: @expense_notes@\n" +
                        "@current_user_name@'s Notes: @dismissal_notes@",
                "Cash Expense Request Dismissal"));

        templates.add(createNewModelTemplate(
            "missing_visa_expense_alert",
            "<h2>ACTION NEEDED: A Transaction is Missing in Visa Expenses</h2>" +
            "<p>There is a missing transaction that you need to add manually in the visa expense page.</p>" +
            "<h3>Details of the missing transactions:</h3>" +
            "<ul>" +
            "<li><b>Date:</b> @date@</li>" +
            "<li><b>Reference Number:</b> @referenceNumber@</li>" +
            "<li><b>Amount:</b> @amount@</li>" +
            "<li>Additional notes: In the uploaded file on @today@ there are @matchedCount@ matched expenses " +
                    "with the same amount and @missingCount@ missing from ERP expenses with the same amount. <br/>" +
                    "@sentence@" +
            "</li>" +
            "</ul>",
            "ACTION NEEDED: A Transaction is Missing in Visa Expenses"
        ));

        templates.add(createNewModelTemplate(
                "same_reference_number_but_different_amount_visa_expense_alert",
                "<h2>ACTION NEEDED: A Transaction is Same Reference Number but Different Amount in Visa Expenses</h2>" +
                        "<p>There are same reference number but different amount transactions that you need to add manually in the visa expense page.</p>" +
                        "<h3>Details of the  same reference number but different amount transactions:</h3>" +
                        "<ul>" +
                        "<li><b>Creation Dates:</b> @creationDates@</li>" +
                        "<li><b>Names:</b> @names@</li>" +
                        "<li><b>Erp Amounts:</b> @erpAmounts@</li>" +
                        "<li><b>File Amounts:</b> @fileAmounts@</li>" +
                        "<li><b>Reference Numbers:</b> @referenceNumbers@</li>" +
                        "<li><b>Descriptions:</b> @descriptions@</li>" +
                        "<li><b>Buckets From:</b> @bucketFroms@</li>" +
                        "<li><b>Expenses Name:</b> @expenseNames@</li>" +
                        "<li><b>Contract Types:</b> @contractTypes@</li>" +
                        "<li><b>Employee Types:</b> @employeeTypes@</li>" +
                        "</ul>",
                "ACTION NEEDED: A Transaction is Missing in Visa Expenses"
        ));

        return templates;
    }

    private Template createNewModelTemplate(String name, String text) {

        return createNewModelTemplate(name, text, null);
    }

    private Template createNewModelTemplate(String name, String text, String subject) {

        ChannelSpecificSetting eChannel = new ChannelSpecificSetting();
        eChannel.setType(ChannelSpecificSettingType.Email);
        eChannel.setActive(true);
        eChannel.setText(text);
        eChannel.setOnFailAction(ChannelSpecificSettingType.SMS);

        Template.TemplateBuilder templateBuilder = new Template.TemplateBuilder().Template(name,"", "")
                .showInReceiverLog(true)
                .unicode(true)
                .channelSetting(eChannel)
                .smsChannelSetting(text, new HashMap<>())
                .target(PicklistHelper.getItem("template_target", "office_staff"))
                .method("e-mail")
                .subject(subject)
                .excludeFromWindow(true) // SD-55626
                .newModel();

        List<String> parameters = TemplateUtil.getParameters(text);
        parameters.forEach(templateBuilder::allowedParameter);

        return templateBuilder.build();
    }

    public HashMap<String, String> getParameters(ExpenseRequestTodo expenseRequestTodo) {
        HashMap<String, String> parameters = new HashMap<>();
        parameters.put("expense_caption", expenseRequestTodo.getExpense() != null ? expenseRequestTodo.getExpense().getCaption() : "");
        parameters.put("todo_id", expenseRequestTodo.getId().toString());
        parameters.put("related_to", !StringUtils.isEmpty(expenseRequestTodo.getRelatedToInfo()) ? "related to " + expenseRequestTodo.getRelatedToInfo() + " " : "");
        parameters.put("amount", expenseRequestTodo.getAmount().toString());
        parameters.put("currency", expenseRequestTodo.getCurrency() != null ? Setup.getRepository(PicklistItemRepository.class).findOne(expenseRequestTodo.getCurrency().getId()).getName() : "");
        parameters.put("expense_requested_by", expenseRequestTodo.getRequestedBy() != null ? expenseRequestTodo.getRequestedBy().getFullName() : "");
        parameters.put("requester_notes", !StringUtils.isEmpty(expenseRequestTodo.getNotes()) ? expenseRequestTodo.getNotes() + "<br/>" : "");
        parameters.put("requester_attachments", expenseRequestTodo.getAttachments() != null && !expenseRequestTodo.getAttachments().isEmpty() ? "attachments: <br/>" : "");
        parameters.put("notes", expenseRequestTodo.getNotes());
        parameters.put("expense_description", expenseRequestTodo.getDescription());
        parameters.put("approve_link", Setup.getCoreParameter(CoreParameter.PUBLIC_LINK_BASE) +
                Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_EXPENSE_REJECTED_FROM_EMAIL_PAGE) +
                        expenseRequestTodo.getToken() + "/" + expenseRequestTodo.getId() + "?approved=true");
        parameters.put("reject_link",  Setup.getCoreParameter(CoreParameter.PUBLIC_LINK_BASE) +
                Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_EXPENSE_REJECTED_FROM_EMAIL_PAGE) +
                        expenseRequestTodo.getToken() + "/" + expenseRequestTodo.getId() + "?approved=false");
        if (expenseRequestTodo.getExpense() != null && expenseRequestTodo.getExpense().getNotification() != null) {
            ExpenseNotification notificationInfo = expenseRequestTodo.getExpense().getNotification();
            parameters.put("threshold", notificationInfo.getAmount() != null ? notificationInfo.getAmount().toString() : "");
            parameters.put("percentageOperation", notificationInfo.getPercentageOperation() != null ? notificationInfo.getPercentageOperation().getLabel() : "");
            parameters.put("percentage", notificationInfo.getPercentage() != null ? notificationInfo.getPercentage().toString() : "");
            parameters.put("percentageNOCompared", notificationInfo.getPercentageNOCompared() != null ? notificationInfo.getPercentageNOCompared().toString() : "");
            parameters.put("percentageComparedTo", notificationInfo.getPercentageComparedTo() != null ? notificationInfo.getPercentageComparedTo().toString() : "");
            int threshold = Integer.valueOf(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_EXPENSE_NOTIFICATION_NUMBER_OF_REQUESTS_THRESHOLD));
            parameters.put("number_of_requests_threshold", threshold == 1 ? "once" : threshold + " times");
            parameters.put("numberOfRquestComparedTo", notificationInfo.getNumberOfRquestComparedTo() != null ? notificationInfo.getNumberOfRquestComparedTo().toString() : "");
        } else {
            parameters.put("threshold", "");
            parameters.put("percentageOperation", "");
            parameters.put("percentage", "");
            parameters.put("percentageNOCompared", "");
            parameters.put("percentageComparedTo", "");
            parameters.put("number_of_requests_threshold", "");
            parameters.put("numberOfRquestComparedTo", "");
        }

        if (expenseRequestTodo.getLoanAmount() != null) {
            parameters.put("amount_of_loan_to_add", "Amount of loan to add: " + expenseRequestTodo.getLoanAmount().toString() + " <br/>");
            parameters.put("amount_of_loan_to_add_description", "Amount to add as a loan: " + parameters.get("currency") + " " + expenseRequestTodo.getLoanAmount().toString() + " <br/>");
        } else {
            parameters.put("amount_of_loan_to_add", "");
            parameters.put("amount_of_loan_to_add_description", "");
        }


        return parameters;
    }

    public String fillTemplate(String templateName, ExpenseRequestTodo expenseRequestTodo) {
        return TemplateUtil.compileTemplate(getTemplateByName(templateName), null, getParameters(expenseRequestTodo));
    }

    public void sendExpenseRequestTodoEmail(
            List<User> users, String subject,
            String templateName, ExpenseRequestTodo expenseRequestTodo) {

        sendExpenseRequestTodoEmail(String.join(";", users.stream().map(User::getEmail).collect(Collectors.toList())),
                subject, templateName, expenseRequestTodo);
    }

    public void sendExpenseRequestTodoEmail(
            String commaSeparatedEmails, String subject,
            String templateName, ExpenseRequestTodo expenseRequestTodo) {

        sendEmail(commaSeparatedEmails, subject, templateName, expenseRequestTodo.getAttachments(), getParameters(expenseRequestTodo));
    }

    public void sendExpenseRequestTodoEmail(
            String commaSeparatedEmails, String subject,
            String templateName, Map<String, String> paramValues) {

        sendEmail(commaSeparatedEmails, subject, templateName, null, paramValues);
    }

    public void sendEmail(
            String emails, String subject,
            String templateName, List<Attachment> attachments, Map<String, String> paramValues) {

        Template template = getTemplateByName(templateName);
        if (template == null
                || template.getStatus() == null
                || !template.getStatus().getCode().equals("active"))
            return;

        if (templateName.equalsIgnoreCase("expense_request_approval") &&
                attachments != null && !attachments.isEmpty()) {
            logger.info("Adding Attachments to the Email");
            paramValues.put("file_description", "Files added by requester are attached to the email." + "<br/>");

        } else paramValues.put("file_description", "");

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaffWithAttachments(templateName,
                        paramValues, emails, attachments,
                        subject != null && !subject.isEmpty() ? subject : template.getSubject());
    }

    Template getTemplateByName(String name) {
        return Setup.getRepository(TemplateRepository.class)
                .findByNameIgnoreCase(name);
    }

    // ACC-9005 ACC-9004
    public void sendEmailForBankDDReport(HashMap<String, String> payload) {
        Long fileId = Long.parseLong(payload.get("fileId"));
        // ACC-9366
        Long matchedAndAcceptedCount = payload.containsKey("matchedAndAcceptedCount")
                ? Utils.parseValue(payload.get("matchedAndAcceptedCount"), Long.class)
                : 0L;
        Long matchedAndRejectedCount = payload.containsKey("matchedAndRejectedCount")
                ? Utils.parseValue(payload.get("matchedAndRejectedCount"), Long.class)
                : 0L;
        Long unMatchedCount = payload.containsKey("unMatchedCount")
                ? Utils.parseValue(payload.get("unMatchedCount"), Long.class)
                : 0L;

        switch (payload.get("templateName")) {
            case "dd_300_activation_report":
                Setup.getApplicationContext()
                        .getBean(BankDirectDebitActivationRecordService.class)
                        .sendReportInEmail(fileId, matchedAndAcceptedCount, matchedAndRejectedCount, unMatchedCount);
                break;
            case "dd_400_cancellation_report":
                Setup.getApplicationContext()
                        .getBean(BankDirectDebitCancellationRecordService.class)
                        .sendReportInEmail(fileId, matchedAndAcceptedCount, matchedAndRejectedCount, unMatchedCount);
                break;
        }
    }
}