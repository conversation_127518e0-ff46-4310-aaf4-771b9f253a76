package com.magnamedia.service;


import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.core.mail.Recipient;
import com.magnamedia.core.mail.TemplateEmail;
import com.magnamedia.entity.PaymentOrder;
import com.magnamedia.module.AccountingModule;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;


@Service
public class PaymentOrderService {
    protected static final Logger logger = Logger.getLogger(PaymentOrderService.class.getName());
    //ACC-4507
    public void sendEmailUponRejection(PaymentOrder paymentOrder) {
        logger.log(Level.INFO, "paymentOrder id : {0}", paymentOrder.getId());
        HashMap<String, String> parameters = new HashMap<>();
        parameters.put("amount", String.valueOf(paymentOrder.getAmount().intValue()));
        parameters.put("housemaid_name", paymentOrder.getHousemaid().getName());
        parameters.put("user_name", CurrentRequest.getUser().getName());
        parameters.put("rejection_notes", paymentOrder.getRejectionNotes());

        Setup.getApplicationContext()
                .getBean(MessagingService.class).
                sendEmailToOfficeStaff(
                        "cashier_reject_cash_collection", parameters,
                        Setup.getParameter(Setup.getCurrentModule(),
                                AccountingModule.PARAMETER_CASHIER_REJECT_CASH_COLLECTION_RECIPIENT),
                        "Rejection of Cash Collection to do");
    }
}
