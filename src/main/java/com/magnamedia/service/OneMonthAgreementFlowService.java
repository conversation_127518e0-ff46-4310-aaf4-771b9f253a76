package com.magnamedia.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.api.client.util.ArrayMap;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.entity.PushNotification;
import com.magnamedia.core.helper.*;
import com.magnamedia.core.notification.AppAction;
import com.magnamedia.core.type.AppActionType;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.core.type.CoreParameter;
import com.magnamedia.entity.*;
import com.magnamedia.extra.*;
import com.magnamedia.helper.ContractPaymentTermHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PushNotificationHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.joda.time.Months;
import org.joda.time.format.DateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

//ACC-4905
@Service
public class OneMonthAgreementFlowService {
    private static final Logger logger = Logger.getLogger(OneMonthAgreementFlowService.class.getName());

    @Autowired
    private ContractPaymentConfirmationToDoService paymentConfirmationService;
    @Autowired
    private MessagingService messagingService;
    @Autowired
    private ContractPaymentConfirmationToDoRepository contractPaymentConfirmationToDoRepository;
    @Autowired
    private FlowProcessorEntityRepository flowProcessorEntityRepository;
    @Autowired
    private FlowProcessorMessagingService flowProcessorMessagingService;
    @Autowired
    private DisablePushNotificationRepository disablePushNotificationRepository;
    @Autowired
    private PushNotificationHelper pushNotificationHelper;
    @Autowired
    private FlowProcessorService flowProcessorService;
    @Autowired
    private FlowSubEventConfigRepository flowSubEventConfigRepository;
    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private ContractPaymentRepository contractPaymentRepository;
    @Autowired
    private ContractRepository contractRepository;
    @Autowired
    private ContractPaymentTermRepository contractPaymentTermRepository;
    @Autowired
    private SwitchingNationalityService switchingNationalityService;
    @Autowired
    private ContractPaymentTermServiceNew contractPaymentTermServiceNew;
    @Autowired
    private DirectDebitRepository directDebitRepository;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private Shortener shortener;
    @Autowired
    private ContractPaymentConfirmationToDoService contractPaymentConfirmationToDoService;
    @Autowired
    private DirectDebitSignatureService directDebitSignatureService;
    @Autowired
    private ContractPaymentService contractPaymentService;
    @Autowired
    private CalculateDiscountsWithVatService calculateDiscountsWithVatService;


    public ContractPaymentConfirmationToDo createTodoIfNotExists(ContractPaymentTerm cpt) {
        DateTime lastPayment = Setup.getApplicationContext()
                .getBean(PaymentService.class)
                .getLastReceivedMonthlyPaymentDate(cpt.getContract());
        Date paymentDate = lastPayment == null ?
                cpt.getContract().getStartOfContract() :
                new LocalDate(lastPayment)
                        .plusMonths(1)
                        .withDayOfMonth(getDayOfPaymentDate(cpt.getContract(), lastPayment.plusMonths(1))) // ACC-6007
                        .toDate();

        return createTodoIfNotExists(cpt, paymentDate, null);
    }

    public ContractPaymentConfirmationToDo createTodoIfNotExists(
            ContractPaymentTerm cpt, Date paymentDate, ContractPaymentWrapper additionPayment) {
        try {
            logger.log(Level.INFO,"cpt id: {0}", cpt.getId());
            ContractPaymentConfirmationToDo toDo = contractPaymentConfirmationToDoRepository
                    .findFirstByContractPaymentTerm_ContractAndSourceAndShowOnERPFalseAndDisabledFalseOrderByCreationDateDesc(
                            cpt.getContract(), ContractPaymentConfirmationToDo.Source.ONE_MONTH_AGREEMENT);
            if (toDo != null) return toDo;

            ContractPaymentType monthlyPaymentType = cpt.getContractPaymentTypes()
                    .stream()
                    .filter(c -> c.getType().getCode().equals("monthly_payment")).findFirst().orElse(null);
            if (monthlyPaymentType == null) return null;

            ContractPaymentConfirmationToDo todo = new ContractPaymentConfirmationToDo();
            todo.setContractPaymentTerm(cpt);
            todo.setSource(ContractPaymentConfirmationToDo.Source.ONE_MONTH_AGREEMENT);
            todo.setPaymentType(monthlyPaymentType.getType());
            todo.setPaymentMethod(PaymentMethod.CARD);
            todo.setPayingOnline(true);
            todo.setDescription(monthlyPaymentType.getDescription());

            ContractPaymentWrapper wrapper = new ContractPaymentWrapper();
            wrapper.setContractPaymentConfirmationToDo(todo);
            wrapper.setPaymentDate(paymentDate);

            wrapper.setProrated(false);
            wrapper.setInitial(false);
            wrapper.setDescription(monthlyPaymentType.getDescription());
            wrapper.setPaymentType(monthlyPaymentType.getType());
            wrapper.setSubType(monthlyPaymentType.getSubType());
            // Amount
            Map<String, Object> m = calculateDiscountsWithVatService.getAmountOfMonthlyPaymentAtTimeBeforeProratedForOMA(cpt, wrapper.getPaymentDate());
            wrapper.setAmount((Double) m.get("amount"));
            wrapper.setActualReceivedAmount((Double) m.get("amount"));
            wrapper.setAffectedByAdditionalDiscount((Boolean) m.get("affectedByAdditionalDiscount"));
            wrapper.setMoreAdditionalDiscount((Double) m.getOrDefault("moreAdditionalDiscount", 0.0));

            // TODO Before Prorated not Including Worker Salary
            if (additionPayment != null) {
                additionPayment.setContractPaymentConfirmationToDo(todo);
                todo.getContractPaymentList().add(additionPayment);
            }

            todo.getContractPaymentList().add(wrapper);
            paymentConfirmationService.createConfirmationToDo(todo);

            return contractPaymentConfirmationToDoRepository.findOne(todo.getId());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
    
    @Transactional
    public void createPaymentAfterPaidSuccess(ContractPaymentConfirmationToDo toDo) throws Exception {
        boolean changedRecurringPaymentToReceived = paymentService.changeRecurringPaymentToReceived(toDo.getContractPaymentList().get(0));

        if (!changedRecurringPaymentToReceived) {
            logger.log(Level.INFO, "After cash flow create new payment, todo id: {0}", toDo.getId());

            paymentConfirmationService.createPayment(
                    toDo.getContractPaymentList().get(0),
                    toDo.getAttachments(), PaymentStatus.RECEIVED, true);
        }

        disableOldNotification(toDo.getContractPaymentTerm().getContract());
    }
    
    public void disableOldNotification(Contract c) {
        List<PushNotification> clientNotifications = disablePushNotificationRepository
                .findActiveNotificationsByDDMessagingType(c.getClient().getId().toString(),
                        Arrays.asList(DDMessagingType.ClientsPayingViaCreditCard,
                                DDMessagingType.OneMonthAgreement),
                        c.getId());
        pushNotificationHelper.stopDisplaying(clientNotifications);
    }
    
    public void flowStoppedResetFlag(Contract contract) {

        logger.info("contract id: " + contract.getId());
        // ACC-6647
        Setup.getApplicationContext()
                .getBean(ContractService.class)
                .updatePayingViaCreditCardFlag(contract, false, true);
    }

    // ACC-5791
    // Whenever the “Paying via CC / Initial flow” flow runs on a contract flagged as “One-month”
    // then remove the “one-month” flag directly and once the CC payment gets received flag the contract as prorated
    public void validateSwitchToProrated(Contract c) {
        if (c.getIsProRated() || !c.isOneMonthAgreement()) return;


        logger.log(Level.INFO, "Remove OMA flag contract id: {0} as prorated", c.getId());
        Map<String, Object> map = new HashMap<>();
        map.put("id", c.getId());
        map.put("isOneMonthAgreement", false);

        Setup.getApplicationContext()
                .getBean(ContractService.class)
                .updateContractFromClientMgtAsync(map);
    }

    public boolean isPayingViaCreditCard(Contract contract) {
        return contract.isOneMonthAgreement() && contract.isPayingViaCreditCard();
    }

    public boolean isPayingViaDirectDebit(Contract contract) {
        return !isPayingViaCreditCard(contract);
    }

    public DateTime getCurrentPaymentDate(Contract contract) {
        return getPaymentDate(contract, new DateTime());
    }

    public DateTime getPaymentDate(Contract contract, DateTime date) {
        date = date.dayOfMonth().get() >= new DateTime(contract.getStartOfContract()).dayOfMonth().get() ?
                date : date.minusMonths(1);

       return date.withDayOfMonth(getDayOfPaymentDate(contract, date)).withTimeAtStartOfDay();
    }

    // ACC-8913
    public int getDayOfPaymentDate(Contract contract, DateTime date) {
        int day = Math.min(
                new LocalDate(contract.getStartOfContract()).getDayOfMonth(),
                date.dayOfMonth().withMaximumValue().getDayOfMonth());
        logger.info("date:"  + date.toString("yyyy-MM-dd HH:mm:ss") + "; day: " + day);
        return day;
    }

    public DateTime getLastDayInCurrentPayment(Contract contract) {

        return getCurrentPaymentDate(contract)
                .plusMonths(1)
                .minusDays(1)
                .withHourOfDay(23)
                .withMinuteOfHour(59)
                .withSecondOfMinute(59);
    }

    // ACC-5902
    public void handleSwitchNationality(Long cptId, Long newHousemaidId, Long replacementId, Boolean isUpgradingNationality) throws Exception {

        handleSwitchNationality(
                contractPaymentTermRepository.findOne(cptId),
                Setup.getRepository(HousemaidRepository.class).findOne(newHousemaidId),
                Setup.getRepository(ReplacementRepository.class).findOne(replacementId),
                isUpgradingNationality, true, DirectDebitCancellationToDoReason.SWITCHING_NATIONALITY);
    }

    // ACC-5902
    public void handleSwitchNationality(
        ContractPaymentTerm cpt, Housemaid newHousemaid,
        Replacement replacement, Boolean isUpgradingNationality,
        boolean sendMsg, DirectDebitCancellationToDoReason reason) throws Exception {

        if (isUpgradingNationality)
            handleUpgradingNationality(cpt, newHousemaid, replacement, sendMsg, reason);
        else handleDowngradingNationality(cpt, newHousemaid, replacement, sendMsg, reason);
    }

    // ACC-5902
    private void handleUpgradingNationality(
            ContractPaymentTerm cpt, Housemaid newHousemaid,
            Replacement replacement, boolean sendMsg,
            DirectDebitCancellationToDoReason reason) throws Exception {

        if (isPayingViaDirectDebit(cpt.getContract()))
            handleUpgradingNationalityViaDirectDebit(cpt, newHousemaid, replacement, sendMsg, reason);
        else handleUpgradingNationalityRequestViaCreditCard(cpt, newHousemaid, replacement, sendMsg);

    }

    // ACC-5902
    public void handleUpgradingNationalityViaDirectDebit(
            ContractPaymentTerm cpt, Housemaid newHousemaid,
            Replacement replacement, boolean sendMsg,
            DirectDebitCancellationToDoReason reason) throws Exception {

        logger.info("cpt id: " + cpt.getId());
        DateTime replacementDate = new DateTime();
        ContractPaymentTerm newCpt = switchingNationalityService.createNewCptAfterSwitchingAndMoveOldFlow(
                cpt, newHousemaid, replacementDate, SwitchingNationalityService.SwitchingNationalityType.UPGRADING, replacement);

        Setup.getApplicationContext()
                .getBean(DirectDebitService.class)
                .moveNotReceivedDdaAndNotMonthlyToNewCpt(cpt, newCpt);

        cancelOldDds(cpt, reason);

        createNewDdsAfterSwitchingNationality(cpt, newCpt, replacementDate);

        if (sendMsg) { switchingNationalityService.sendReplacementSuccessMessage(newCpt, newHousemaid); }
    }

    // ACC-5902
    private void handleDowngradingNationality(
            ContractPaymentTerm cpt, Housemaid newHousemaid,
            Replacement replacement, boolean sendMsg,
            DirectDebitCancellationToDoReason reason) throws Exception {

        if (isPayingViaDirectDebit(cpt.getContract()))
            handleDowngradingNationalityViaDirectDebit(cpt, newHousemaid, replacement, sendMsg, reason);
        else handleDowngradeNationalityRequestViaCreditCard(cpt, newHousemaid, replacement, sendMsg);
    }

    // ACC-5902
    public void handleDowngradingNationalityViaDirectDebit(
            ContractPaymentTerm cpt, Housemaid newHousemaid,
            Replacement replacement, boolean sendMsg,
            DirectDebitCancellationToDoReason reason) throws Exception {

        logger.info("cpt id: " + cpt.getId());
        DateTime replacementDate = new DateTime();
        ContractPaymentTerm newCpt = switchingNationalityService.createNewCptAfterSwitchingAndMoveOldFlow(
                cpt, newHousemaid, replacementDate, SwitchingNationalityService.SwitchingNationalityType.DOWNGRADING, replacement);

        Setup.getApplicationContext()
                .getBean(DirectDebitService.class)
                .moveNotReceivedDdaAndNotMonthlyToNewCpt(cpt, newCpt);

        cancelOldDds(cpt, reason);

        createNewDdsAfterSwitchingNationality(cpt, newCpt, replacementDate);

        if (sendMsg) { switchingNationalityService.sendReplacementSuccessMessage(newCpt, newHousemaid); }

        double amount = getDirectDebitRefundAmount(cpt, newCpt, replacementDate);
        if (amount <= 0) return;

        logger.info("Add Client Refund, Amount: " + amount);
        Setup.getApplicationContext()
                .getBean(ClientMessagingAndRefundService.class)
                .refundAfterReplacement(cpt.getContract(), amount,
                        AccountingModule.PAYMENT_REQUEST_PURPOSE_SWITCHING_TO_A_CHEAPER_NATIONALITY_REFUND,
                        "Downgrading switching nationality flow");
    }

    private void createNewDdsAfterSwitchingNationality(ContractPaymentTerm oldCpt, ContractPaymentTerm newCpt, DateTime replacementDate) throws Exception {

        logger.info("new cpt id: " + newCpt.getId());
        newCpt.setForceGenerateDds(true);
        List<ContractPayment> payments = (List<ContractPayment>) contractPaymentTermServiceNew.
                getDefaultDirectDebitPayments(newCpt, new HashMap<>()).get("payments");

        Collections.reverse(payments);
        List<ContractPayment> l = contractPaymentService.getUniqueAndSortedPayments(
                oldCpt.getContract(), payments, null);

        l = l.stream().filter(p -> p.getDate().after(replacementDate.toDate())).collect(Collectors.toList());
        boolean existsSignatures = directDebitSignatureService.hasSignature(oldCpt);

        contractPaymentTermServiceNew.updateContractPaymentTermWithPayments(newCpt, true,
                existsSignatures, l, null, false,
                false, true, false, false, true, false, new HashMap<>());

    }

    public Double getDirectDebitRefundAmount(ContractPaymentTerm oldCpt, ContractPaymentTerm newCpt, DateTime replacementDate) {

        LocalDate d = replacementDate.getDayOfMonth() ==
                getLastDayInCurrentPayment(oldCpt.getContract()).getDayOfMonth() ?
                getCurrentPaymentDate(oldCpt.getContract()).plusMonths(1).toLocalDate() :
                getCurrentPaymentDate(oldCpt.getContract()).toLocalDate();

        logger.info("date: " + d +
                "contract id: " + oldCpt.getContract().getId());
        AtomicReference<Double> amount = new AtomicReference<>(0D);
        contractPaymentService.getUniqueAndSortedMonthlyPayments(
                paymentRepository.getMonthlyPaymentForRefundAfterDownGradeNationality(oldCpt.getContract(), d.toDate()))
                .forEach(o -> {
                    if (new LocalDate(o[2]).toString("yyyy-MM")
                            .equals(getCurrentPaymentDate(oldCpt.getContract()).toString("yyyy-MM"))){
                        amount.updateAndGet(v -> v + downgradeNationalityCalculateDifferenceFeeForCurrentMonth(newCpt, replacementDate, (Double) o[1]));

                    } else if (new LocalDate(o[0]).toString("yyyy-MM")
                            .equals(new LocalDate(newCpt.getContract().getStartOfContract()).plusMonths(1).toString("yyyy-MM"))) {
                        Map<String, Object> map = new ArrayMap<>();
                        map.put("monthlyPaymentAmount", oldCpt.getMonthlyPayment());
                        map.put("proRatedDate", new LocalDate(oldCpt.getContract().getStartOfContract()).plusMonths(1));
                        map.put("isOneMonthAgreement", oldCpt.getContract().isOneMonthAgreement());
                        Double oldProratedAmount = calculateDiscountsWithVatService.getProRatedAmount(map);
                        map.put("monthlyPaymentAmount", newCpt.getMonthlyPayment());
                        Double newProratedAmount = calculateDiscountsWithVatService.getProRatedAmount(map);
                        logger.log(Level.INFO, "oldProratedAmount: {0}, newProratedAmount: {1}",
                                new Object[]{oldProratedAmount, newProratedAmount});
                        amount.updateAndGet(v -> v + (newProratedAmount - oldProratedAmount));

                    } else if (new LocalDate(o[0]).isAfter(new LocalDate(replacementDate))) {
                        amount.updateAndGet(v -> {
                            Double newPrice = v + ((Double) o[1] - calculateDiscountsWithVatService.getCPTAmountAtTime(newCpt, (Date) o[0]));
                            logger.log(Level.INFO, "After replacement old amount: {0}, new amount: {1}",
                                    new Object[]{(Double) o[1], newPrice});
                            return v + ((Double) o[1] - newPrice);
                        });
                    }
                });

        return amount.get();
    }

    private Double downgradeNationalityCalculateDifferenceFeeForCurrentMonth(ContractPaymentTerm newCpt, DateTime replacementDate, Double oldAmount) {

        Map<String, Object>  newPrice = calculateDiscountsWithVatService.getAmountOfMonthlyPaymentAtTimeBeforeProratedForOMA(
                newCpt, getCurrentPaymentDate(newCpt.getContract()).toDate());
        int numOfDaysToEOF = Days.daysBetween(
                replacementDate, getLastDayInCurrentPayment(newCpt.getContract())).getDays() + 1;
        int currentMonthDays = Days.daysBetween(
                getCurrentPaymentDate(newCpt.getContract()), getLastDayInCurrentPayment(newCpt.getContract())).getDays() + 1;

        logger.log(Level.INFO, "contract id: {0}, oldPrice: {1}, newPrice: {2}, numOfDaysToEOF: {3}",
                new Object[]{newCpt.getContract().getId(), oldAmount, newPrice, numOfDaysToEOF});
        return (((Double) oldAmount / currentMonthDays * numOfDaysToEOF)) - (((Double) newPrice.get("amount")) / currentMonthDays * numOfDaysToEOF);
    }

    public Double getCreditCardRefundAmount(ContractPaymentTerm oldCpt, ContractPaymentTerm newCpt, DateTime replacementDate) {

        LocalDate d = replacementDate.getDayOfMonth() ==
                getLastDayInCurrentPayment(oldCpt.getContract()).getDayOfMonth() ?
                getCurrentPaymentDate(oldCpt.getContract()).plusMonths(1).toLocalDate() :
                getCurrentPaymentDate(oldCpt.getContract()).toLocalDate();
        logger.info("date: " + d +
                "contract id: " + oldCpt.getContract().getId());

        AtomicReference<Double> amount = new AtomicReference<>(0.0);
        contractPaymentService.getUniqueAndSortedMonthlyPayments(
                paymentRepository.getMonthlyPaymentForRefundAfterDownGradeNationality(oldCpt.getContract(), d.toDate()))
                .forEach(o -> {

                    if (new LocalDate(o[2]).toString("yyyy-MM")
                            .equals(getCurrentPaymentDate(oldCpt.getContract()).toString("yyyy-MM"))){
                        amount.updateAndGet(v -> v + downgradeNationalityCalculateDifferenceFeeForCurrentMonth(newCpt, replacementDate, (Double) o[1]));
                        return;
                    }

                    Double newPrice = (Double) calculateDiscountsWithVatService.getAmountOfMonthlyPaymentAtTimeBeforeProratedForOMA(newCpt, (Date) o[2]).get("amount");
                    logger.log(Level.INFO, "oldPrice: {0}, newPrice: {1}",  new Object[]{o[1], newPrice});
                    amount.updateAndGet(v -> Double.valueOf((double) (v + ((Double) o[1] - newPrice))));
                });

        logger.info("amount: " + amount.get());
        return amount.get();
    }

    private void cancelOldDds(ContractPaymentTerm cpt, DirectDebitCancellationToDoReason reason) {

        Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "cancelOldDds_" + cpt.getContract().getId(),
                        "accounting",
                        "oneMonthAgreementFlowService",
                        "cancelOldDds")
                        .withRelatedEntity("ContractPaymentTerm", cpt.getId())
                        .withParameters(
                                new Class[] { Long.class, String.class },
                                new Object[] { cpt.getId(), reason.toString() })
                        .withQueue(BackgroundTaskQueues.SequentialQueue)
                        .withDelay(60L * 1000L)
                        .build());
    }

    public void cancelOldDds(Long cptId, String reason) {

        logger.info("cpt id: " + cptId);
        DirectDebitCancellationService directDebitCancellationService =
                Setup.getApplicationContext().getBean(DirectDebitCancellationService.class);
        ContractPaymentTerm cpt = Setup.getRepository(ContractPaymentTermRepository.class).findOne(cptId);
        Setup.getRepository(DirectDebitRepository.class)
                .findByContractPaymentTermAndStatusNotIn(cpt, Arrays.asList(
                        DirectDebitStatus.CANCELED,
                        DirectDebitStatus.PENDING_FOR_CANCELLATION,
                        DirectDebitStatus.EXPIRED))
                .forEach(d -> {
                    logger.info("dd id: " + d.getId());
                    directDebitCancellationService.cancelWholeDD(d, false, DirectDebitCancellationToDoReason.valueOf(reason));
                });
    }

    public Map<String, Object> getDowngradeNationalityPaymentInfoCreditCard(
            ContractPaymentTerm cpt, Housemaid newHousemaid, boolean payingViaDd, DateTime replacementDate) {

        logger.log(Level.INFO, "cpt id: {0}", cpt.getId());
        Map<String, Object> r = new HashMap<>();
        r.put("payments", new ArrayList<>());

        Map<String, Object> cond = getSwitchNationalitySpecialCasesConditions(cpt.getContract(), replacementDate);
        if ((boolean) cond.get("clientPaidCurrentMonth") && !(boolean) cond.get("downgradeOnLastDayOfMonth")) return r;

        ContractPaymentTerm newCpt = switchingNationalityService.createNewTermForSwitchingNationalities(
                cpt.getContract(), newHousemaid, cpt, false, replacementDate,
                SwitchingNationalityService.SwitchingNationalityType.DOWNGRADING, true);

        List<Map<String, Object>> l = new ArrayList<>();
        if (!(boolean) cond.get("clientPaidCurrentMonth")) {
            l.add(switchNationalityGetCurrentMonthPayment(replacementDate, cpt, newCpt, false));
        }

        if ((boolean) cond.get("downgradeOnLastDayOfMonth")) {
            l.add(switchNationalityGetNextMonthPayment(newCpt, payingViaDd, false));
        }

        Map<String, String> parameters = new HashMap<>();
        parameters.put("date", DateTimeFormat.forPattern("yyyy-MM-dd 00:00:00")
                .parseDateTime((String) l.get(l.size() - 1).get("paymentDate"))
                .dayOfMonth()
                .withMaximumValue()
                .toString("EEEE, MMM dd, yyyy"));
        parameters.put("total_amount", String.valueOf(Double.valueOf(l.stream().mapToDouble(p -> (Double) p.get("amount")).sum()).intValue()));
        parameters.put("payments_info", l.size() < 2 ? "" :
                switchingNationalityService.getPayingViaCreditCardPaymentsInfo(l));

        r.put("parameters", parameters);
        r.put("payments", l);
        return r;
    }

    public Map<String, Object> getSwitchNationalitySpecialCasesConditions(Contract contract, DateTime replacementDate) {

        DateTime lastPaymentReceived = paymentService.getLastReceivedMonthlyPaymentDate(contract);

        boolean clientPaidCurrentMonth = lastPaymentReceived != null &&
                lastPaymentReceived.plusHours(1)
                        .isAfter(getCurrentPaymentDate(contract));
        boolean downgradeOnLastDayOfMonth = replacementDate.getDayOfMonth() == getLastDayInCurrentPayment(contract).getDayOfMonth() &&
                (lastPaymentReceived == null || lastPaymentReceived.plusHours(1).isBefore(replacementDate));

        logger.info("contract id: " + contract.getId() +
                "; clientPaidCurrentMonth: " + clientPaidCurrentMonth +
                "; downgradeOnLastDayOfMonth: " + downgradeOnLastDayOfMonth);

        return new HashMap<String, Object>() {{
            put("clientPaidCurrentMonth", clientPaidCurrentMonth);
            put("downgradeOnLastDayOfMonth", downgradeOnLastDayOfMonth);
        }};
    }

    private Map<String, Object> switchNationalityGetCurrentMonthPayment(DateTime replacementDate, ContractPaymentTerm oldCpt, ContractPaymentTerm newCpt, boolean isUpgrade) {

        int numOfDaysToEOF = Days.daysBetween(replacementDate, getLastDayInCurrentPayment(oldCpt.getContract())).getDays() + 1;
        int passedDays = Days.daysBetween(getCurrentPaymentDate(oldCpt.getContract()), replacementDate).getDays();

        double oldPriceRate = (Double) calculateDiscountsWithVatService.getAmountOfMonthlyPaymentAtTimeBeforeProratedForOMA(
                oldCpt, getCurrentPaymentDate(oldCpt.getContract()).toDate()).get("amount") /
                (passedDays + numOfDaysToEOF);
        Map<String, Object> newPrice = calculateDiscountsWithVatService.getAmountOfMonthlyPaymentAtTimeBeforeProratedForOMA(
                newCpt, getCurrentPaymentDate(newCpt.getContract()).toDate());
        double newPriceRate = (Double) newPrice.get("amount") /
                (passedDays + numOfDaysToEOF);

        Double amount = (oldPriceRate * passedDays) + (newPriceRate * numOfDaysToEOF);
        logger.info("contract id: " + oldCpt.getContract().getId() +
                "; oldPriceRate: " + oldPriceRate +
                "; passedDays: " + passedDays +
                "; newPriceRate: " + newPriceRate +
                "; numOfDaysToEOF: " + numOfDaysToEOF);

        Map<String, Object> m = new HashMap<>();
        m.put("paymentType", new HashMap<String, Object>(){{
            put("id", Setup.getItem("TypeOfPayment", "monthly_payment").getId());
        }});
        m.put("paymentDate", getCurrentPaymentDate(oldCpt.getContract()).toString("yyyy-MM-dd 00:00:00"));
        m.put("amount", Math.floor(amount));
        m.put("affectedByAdditionalDiscount", newPrice.get("affectedByAdditionalDiscount"));
        m.put("moreAdditionalDiscount", newPrice.getOrDefault("moreAdditionalDiscount", 0.0));
        m.put("includeUpgradingFee", isUpgrade);
        return m;
    }

    private Map<String, Object> switchNationalityGetNextMonthPayment(ContractPaymentTerm newCpt, boolean payingViaDd, boolean isUpgrade) {

        Map<String, Object> m = new HashMap<>();
        m.put("paymentType", new HashMap<String, Object>(){{
            put("id", Setup.getItem("TypeOfPayment", "monthly_payment").getId());
        }});
        m.put("paymentDate", getLastDayInCurrentPayment(newCpt.getContract()).plusDays(1).toString("yyyy-MM-dd 00:00:00"));
        m.put("includeUpgradingFee", isUpgrade);

        if (payingViaDd) {
            Map<String, Object> map = new ArrayMap<>();
            map.put("monthlyPaymentAmount", newCpt.getMonthlyPayment());
            map.put("proRatedDate", new LocalDate(newCpt.getContract().getStartOfContract()).plusMonths(1));
            map.put("isOneMonthAgreement", newCpt.getContract().isOneMonthAgreement());
            m.put("amount", calculateDiscountsWithVatService.getProRatedAmount(map));

        } else {
            Map<String, Object>  newPrice = calculateDiscountsWithVatService.getAmountOfMonthlyPaymentAtTimeBeforeProratedForOMA(
                    newCpt, getLastDayInCurrentPayment(newCpt.getContract()).withTimeAtStartOfDay().plusDays(1).toDate());
            m.put("amount", newPrice.get("amount"));
            m.put("affectedByAdditionalDiscount", newPrice.get("affectedByAdditionalDiscount"));
            m.put("includeWorkerSalary", newPrice.get("includeWorkerSalary"));
            m.put("moreAdditionalDiscount", newPrice.getOrDefault("moreAdditionalDiscount", 0.0));
        }

        logger.info("contract id: " + newCpt.getContract().getId() +
                "payment: " + m.entrySet());
        return m;
    }

    public Map<String, String> getDowngradeNationalityPaymentInfoDirectDebit(ContractPaymentTerm cpt, Housemaid newHousemaid, DateTime replacementDate) {

        logger.log(Level.INFO, "cpt id: {0}", cpt.getId());
        ContractPaymentTerm newCpt = switchingNationalityService.simulateNewCPTCreation(
                cpt.getContract(), cpt, newHousemaid.getNationality(), newHousemaid.getLiveOut(), cpt.getContract().getClientPaidVat(),
                SwitchingNationalityService.SwitchingNationalityType.DOWNGRADING, replacementDate);

        Map<String, String> r = new HashMap<>();
        Map<String, Object>  newPrice = calculateDiscountsWithVatService.getAmountOfMonthlyPaymentAtTimeWithDiscounts(
                newCpt, new LocalDate(newCpt.getContract().getStartOfContract()).plusMonths(2).dayOfMonth().withMinimumValue());
        r.put("amount", String.valueOf(DiscountsWithVatHelper.getAmountWithoutVat((Double) newPrice.get("amount")).longValue()));

        return r;
    }

    public Map<String, Object> getUpgradeNationalityPaymentInfoCreditCard(
            ContractPaymentTerm cpt, Housemaid newHousemaid,
            boolean payingViaDd, DateTime replacementDate) {

        logger.log(Level.INFO, "cpt id: {0}", cpt.getId());
        ContractPaymentTerm newCpt = switchingNationalityService.createNewTermForSwitchingNationalities(
                cpt.getContract(), newHousemaid, cpt, false, replacementDate,
                SwitchingNationalityService.SwitchingNationalityType.UPGRADING, true);
        Map<String, Object> r = new HashMap<>();
        Map<String, String> parameters = new HashMap<>();
        parameters.put("payments_info", "");
        List<Map<String, Object>> l = new ArrayList<>();
        Long paymentTypeId = Setup.getItem("TypeOfPayment", "upgrading_nationality").getId();
        Map<String, Object> m;
        Map<String, Object> firstPayment = new HashMap<>();
        DateTime currentPaymentDate = getCurrentPaymentDate(cpt.getContract());

        List<Object[]> receivedPayments = contractPaymentService.getUniqueAndSortedMonthlyPayments(
                paymentRepository.findMonthlyPaymentByStatusAndDateOfPaymentGreaterThanEqual(
                        cpt.getContract(), PaymentStatus.RECEIVED, currentPaymentDate.toDate()));

        Map<String, Map<String, Object>> groupByAmount = new LinkedHashMap<>();
        double lastAmount = -1D;
        String key = null;
        for (Object[] p : receivedPayments) {
            Double oldPrice = (Double) p[1];
            if (new LocalDate(p[2]).toString("yyyy-MM").equals(currentPaymentDate.toString("yyyy-MM"))) {
                Map<String, Object> newPrice = calculateDiscountsWithVatService.getAmountOfMonthlyPaymentAtTimeBeforeProratedForOMA(newCpt, currentPaymentDate.toDate());

                int replacementMonthDays = Days.daysBetween(
                        currentPaymentDate, getLastDayInCurrentPayment(cpt.getContract())).getDays() + 1;
                int numOfDaysToEOF = Days.daysBetween(
                        replacementDate, getLastDayInCurrentPayment(cpt.getContract())).getDays() + 1;

                logger.log(Level.INFO, "firstPayment: oldPrice: {0}, newPrice: {1}, replacementMonthDays {2}, numOfDaysToEOF {3}",
                        new Object[]{oldPrice, newPrice.get("amount"), replacementMonthDays, numOfDaysToEOF});

                double amount = Math.floor(((Double)newPrice.get("amount") - oldPrice) / replacementMonthDays * numOfDaysToEOF);

                if (amount > 0){
                    firstPayment.put("paymentType", new HashMap<String, Object>(){{
                        put("id", paymentTypeId);
                    }});
                    firstPayment.put("paymentDate", currentPaymentDate.toString("yyyy-MM-dd 00:00:00"));
                    firstPayment.put("amount", amount);
                    l.add(firstPayment);
                }
                continue;
            }

            double newPrice = (double) (payingViaDd ?
                    calculateDiscountsWithVatService.getAmountOfMonthlyPaymentAtTimeWithDiscounts(newCpt, new LocalDate(p[2])).get("amount") :
                    calculateDiscountsWithVatService.getAmountOfMonthlyPaymentAtTimeBeforeProratedForOMA(newCpt, (Date) p[2]).get("amount"));

            logger.log(Level.INFO, "oldPrice: {0}, newPrice: {1}",  new Object[]{ oldPrice, newPrice });

            m = new HashMap<>();
            LocalDate d = new LocalDate(p[2]);
            m.put("paymentType", new HashMap<String, Object>(){{
                put("id", paymentTypeId);
            }});
            m.put("paymentDate", d.toString("yyyy-MM-dd 00:00:00"));
            double amount = Math.floor(newPrice - oldPrice);
            m.put("amount", amount);
            l.add(m);
            if (amount == lastAmount && key != null) {
                Map<String, Object> a = groupByAmount.get(key);
                a.put("lastMonth", d.toString("MMM"));
                a.put("count", (Integer) a.get("count") + 1);
                groupByAmount.put(key, a);
            } else {
                key = d.toString("yyyy-MM");
                Map<String, Object> a = new HashMap<>();
                a.put("firstMonth", d.toString("MMM"));
                a.put("lastMonth", d.toString("MMM"));
                a.put("amount", amount);
                a.put("count", 1);
                groupByAmount.put(key, a);
            }
            lastAmount = amount;
        }

        LocalDate date = null;
        Map<String, Object> cond = getSwitchNationalitySpecialCasesConditions(cpt.getContract(), replacementDate);
        if (!(boolean) cond.get("clientPaidCurrentMonth")) {
            firstPayment = switchNationalityGetCurrentMonthPayment(replacementDate, cpt, newCpt, true);
            l.add(firstPayment);
            date = DateTimeFormat.forPattern("yyyy-MM-dd 00:00:00")
                    .parseLocalDate((String) firstPayment.get("paymentDate"))
                    .plusMonths(1)
                    .minusDays(1);
        }

        String nextMonthInfo = "";
        if ((boolean) cond.get("downgradeOnLastDayOfMonth")) {
            Map<String, Object> nextMonthPayment = switchNationalityGetNextMonthPayment(newCpt, payingViaDd, true);
            l.add(nextMonthPayment);
            nextMonthInfo = switchingNationalityService.getPayingViaCreditCardPaymentsInfo(Collections.singletonList(nextMonthPayment));
            date = DateTimeFormat.forPattern("yyyy-MM-dd 00:00:00")
                    .parseLocalDate((String) nextMonthPayment.get("paymentDate"))
                    .plusMonths(1)
                    .minusDays(1);
        }

        if (l.size() > 1) {
            if (receivedPayments.isEmpty()) {
                parameters.put("payments_info", switchingNationalityService.getPayingViaCreditCardPaymentsInfo(l));
            } else {
                logger.info("contract id: " + cpt.getContract().getId() +
                        "; groupByAmount: " + groupByAmount.entrySet());
                parameters.put("payments_info",
                        switchingNationalityService.getPayingViaCreditCardPaymentsInfo(firstPayment, groupByAmount));
                if (!nextMonthInfo.isEmpty())
                    parameters.put("payments_info", parameters.get("payments_info") + " + " + nextMonthInfo);
            }
        }

        parameters.put("total_amount", String.valueOf(Double.valueOf(l.stream().mapToDouble(p -> (Double) p.get("amount")).sum()).intValue()));
        parameters.put("date", (date != null ? date : new LocalDate(cpt.getContract().getPaidEndDate()))
                .toString("EEEE, MMM dd, yyyy"));
        r.put("payments", l);
        r.put("parameters", parameters);
        return r;
    }

    public HashMap<String, Object> getAmendDdPaymentTerms(ContractPaymentTerm cpt, Housemaid newHousemaid, DateTime replacementDate) {
        logger.log(Level.INFO, "cpt id: {0}", cpt.getId());
        HashMap<String, Object> result = new HashMap<>();
        result.put("one_time_terms", "");
        result.put("monthly_terms", "");

        ContractPaymentTerm newCpt = switchingNationalityService.simulateNewCPTCreation(
                cpt.getContract(), cpt, newHousemaid.getNationality(), newHousemaid.getLiveOut(), cpt.getContract().getClientPaidVat(),
                SwitchingNationalityService.SwitchingNationalityType.UPGRADING, replacementDate);

        DateTime lastPayment = paymentService.getLastReceivedMonthlyPaymentDate(cpt.getContract());
        String label = "";

        // prorated
        if (lastPayment == null || lastPayment.isBefore(new DateTime(newCpt.getContract().getStartOfContract()).plusMonths(1))) {
            label = "<div class=\"row-content\">" +
                    "<div class=\"label-container\">" +
                    "<p class=\"content-paragraph-label\">" ;

            label += "On " + DateUtil.formatMonthDayOfMonthWithSuffix(
                    new LocalDate(newCpt.getContract().getStartOfContract()).plusMonths(1).toDate());
            label += ":</p> </div>" +
                    "<div class=\"value-container\">" +
                    "<div class=\"value-content\">" +
                    "<p  class=\"currency-paragraph\">" ;

            Map<String, Object> map = new ArrayMap<>();
            map.put("monthlyPaymentAmount", newCpt.getMonthlyPayment());
            map.put("proRatedDate", new LocalDate(newCpt.getContract().getStartOfContract()).plusMonths(1));
            map.put("isOneMonthAgreement", newCpt.getContract().isOneMonthAgreement());


            label +="<span class=\"currency-span last\" >AED</span> <span>" +
                    PaymentHelper.df.format(DiscountsWithVatHelper.getAmountWithoutVat(calculateDiscountsWithVatService.getProRatedAmount(map)).intValue()) +
                    "/month + VAT</span></p>" +
                    "<p class=\"value-paragraph\">" +
                    "This will cover your required payments till " +
                    DDUtils.getAmendDdFormattedDate(
                            new DateTime(newCpt.getContract().getStartOfContract()).plusMonths(1).dayOfMonth().withMaximumValue());
            label += "</p></div></div></div>";
            result.put("one_time_terms", label);
        }

        // full month
        LocalDate fullMonthDate = lastPayment == null || !result.toString().isEmpty() ?
                new LocalDate(newCpt.getContract().getStartOfContract()).plusMonths(2).dayOfMonth().withMinimumValue() :
                lastPayment.plusMonths(1).toLocalDate().dayOfMonth().withMinimumValue();
        label = "<div class=\"row-content\">" +
                "<div class=\"label-container\">" +
                "<p class=\"content-paragraph-label\">" ;

        label += "On " + DateUtil.formatMonthDayOfMonthWithSuffix(fullMonthDate.toDate());
        label += " Onwards:";

        label += "</p> </div>" +
                "<div class=\"value-container\">" +
                "<div class=\"value-content\">" +
                "<p  class=\"currency-paragraph\">" ;

        label += "<span class=\"currency-span last\" >AED</span> <span>" +
                PaymentHelper.df.format(DiscountsWithVatHelper.getAmountWithoutVat(newCpt.getMonthlyPayment()).intValue()) +
                "/month + VAT</span></p>" +
                "<p class=\"value-paragraph\">" +
                "This is the monthly rate of hiring " +
                StringUtils.getHousemaidNationality(newHousemaid.getNationality().getName())  +
                (newHousemaid.getLiveOut() ? " live-out" : "") + " maid.";
        label += "</p></div></div></div>";

        result.put("monthly_terms", label);

        result.forEach((key, value1) -> logger.info(key + " " + value1));

        return result;
    }

    private void handleDowngradeNationalityRequestViaCreditCard(
            ContractPaymentTerm cpt, Housemaid newHousemaid,
            Replacement replacement, boolean sendMsg) {

        logger.log(Level.INFO, "cpt id: {0}", cpt.getId());
        DateTime replacementDate = new DateTime();
        // move to new cpt
        ContractPaymentTerm newCpt = switchingNationalityService.createNewCptAfterSwitchingAndMoveOldFlow(cpt, newHousemaid,
                replacementDate, SwitchingNationalityService.SwitchingNationalityType.DOWNGRADING, replacement);
        // disable old paytabs link
        contractPaymentConfirmationToDoService.disablePayTabLinksBySource(
                cpt.getContract(), ContractPaymentConfirmationToDo.Source.ONE_MONTH_AGREEMENT);

        // send email to the user and notification to the client
        if (sendMsg) { switchingNationalityService.sendReplacementSuccessMessage(newCpt, newHousemaid); }

        double amount = getCreditCardRefundAmount(cpt, newCpt, replacementDate);
        if (amount <= 0) return;

        switchingNationalityService.sendRefundEmailUponDowngradingNationality(cpt, newHousemaid, amount);
    }

    private void handleUpgradingNationalityRequestViaCreditCard(
            ContractPaymentTerm cpt, Housemaid newHousemaid,
            Replacement replacement, boolean sendMsg) {

        logger.log(Level.INFO, "cpt id: {0}", cpt.getId());
        ContractPaymentTerm newCpt = switchingNationalityService.createNewCptAfterSwitchingAndMoveOldFlow(cpt, newHousemaid,
                new DateTime(), SwitchingNationalityService.SwitchingNationalityType.UPGRADING, replacement);
        logger.log(Level.INFO, "newCpt id: {0}", newCpt.getId());

        contractPaymentConfirmationToDoService.disablePayTabLinksBySource(
                cpt.getContract(), ContractPaymentConfirmationToDo.Source.ONE_MONTH_AGREEMENT);

        if (sendMsg) { switchingNationalityService.sendReplacementSuccessMessage(newCpt, newHousemaid); }
    }

    public void setReplacementSuccessMessageParameters(
            ContractPaymentTerm cpt,
            Housemaid newHousemaid,
            DateTime lastPayment,
            Map<String, String> p,
            Map<String, AppAction> c) {

        logger.info("cpt id: " + cpt.getId());
        ContractPaymentConfirmationToDo toDo = null;

        if (lastPayment == null || lastPayment.isBefore(getLastDayInCurrentPayment(cpt.getContract())) &&
                isPayingViaCreditCard(cpt.getContract())) {
            toDo = createTodoIfNotExists(cpt);
        }

        if (toDo != null) {
            String link  = shortener.shorten(Setup.getCoreParameter(CoreParameter.PUBLIC_LINK_BASE)
                    + "/modules/accounting/paytabs/#!/home?uid=" + cpt.getContract().getUuid());
            int amount = toDo.getTotalAmount().intValue();
            p.put("amount", String.valueOf(amount));
            p.put("todoUuid", toDo.getUuid());
            p.put("paying_via_credit_card_sms", link);

            String cmsText = TemplateUtil.compileTemplate(
                    TemplateUtil.getTemplate(CcAppCmsTemplate.CC_PAYMENT_IN_ADVANCE_AFTER_REPLACEMENT.toString()),
                    null, new HashMap<String, String>() {{

                        put("date", getLastDayInCurrentPayment(cpt.getContract()).plusMonths(1).toString("yyyy-MM-dd"));
                        put("amount", String.valueOf(amount));
                    }});

            c.put("link", Setup.getApplicationContext()
                    .getBean(FlowProcessorMessagingService.class)
                    .getPaytabsAction(p, cpt.getContract(), AppActionType.LINK,
                            "link", cmsText, "/replace_switch_nationality_pay_notification"));
            p.put("paid_end_date", getLastDayInCurrentPayment(cpt.getContract()).plusMonths(1).toString("yyyy-MM-dd"));
            p.put("todoId", String.valueOf(toDo.getId()));
            p.put("link", "@link@");

        } else {
            p.put("amount", String.valueOf(((Double) calculateDiscountsWithVatService.getAmountOfMonthlyPaymentAtTimeWithDiscounts(cpt, lastPayment.plusMonths(1)
                            .withDayOfMonth(1)
                            .withTimeAtStartOfDay()
                            .toLocalDate())
                    .get("amount"))
                    .intValue()));
            p.put("paid_end_date", new LocalDate(cpt.getContract().getPaidEndDate()).toString("yyyy-MM-dd"));
        }

        p.put("new_nationality", StringUtils.getHousemaidNationality(newHousemaid.getNationality().getName()));
    }
}
