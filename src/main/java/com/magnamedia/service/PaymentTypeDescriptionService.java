package com.magnamedia.service;


import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.ContractPaymentWrapper;
import com.magnamedia.entity.PaymentTypeDescription;
import com.magnamedia.extra.Gender;
import com.magnamedia.extra.PaymentHelper;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.repository.PaymentTypeDescriptionRepository;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;


@Service
public class PaymentTypeDescriptionService {

    protected static final Logger logger = Logger.getLogger(PaymentTypeDescriptionService.class.getName());

    @Autowired
    private PaymentTypeDescriptionRepository paymentTypeDescriptionRepository;

    public void initializePaymentTypeDescription(MultipartFile file) throws IOException {

        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        XSSFSheet sheet = workbook.getSheetAt(0);

        if (sheet == null) {
            logger.log(Level.WARNING, "Sheet not found");
            return;
        }

        PicklistItem cc = PicklistHelper.getItem("ProspectType", "maids.cc_prospect");
        PicklistItem mv = PicklistHelper.getItem("ProspectType", "maidvisa.ae_prospect");

        for (Row row : sheet) {

            if (row.getRowNum() == 0) continue;
            try {
                logger.info("Row Num: " + row.getRowNum());

                int trial = (int) row.getCell(0).getNumericCellValue();
                if (trial == -1) break;

                String ccDescription = row.getCell(2).getStringCellValue().trim();
                String mvDescription = row.getCell(3).getStringCellValue().trim();
                PicklistItem paymentType = PicklistHelper.getItem("TypeOfPayment", row.getCell(1).getStringCellValue().trim());
                Map<String, Object> m = new HashMap<>();
                m.put("paymentType", paymentType);

                String additionalCondition = row.getCell(4).getStringCellValue().trim();
                if (!additionalCondition.equals("-")) {
                    m.put("additionalCondition", PaymentTypeDescription.AdditionalCondition.valueOf(additionalCondition));
                }

                if (!ccDescription.equals("-")) {
                    m.put("contractProspectType", cc);
                    m.put("description", ccDescription);
                    createOrUpdatePaymentDescription(m);
                }

                if (!mvDescription.equals("-")) {
                    m.put("contractProspectType", mv);
                    m.put("description", mvDescription);
                    createOrUpdatePaymentDescription(m);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    public void createOrUpdatePaymentDescription(Map<String, Object> m) {

        PaymentTypeDescription paymentTypeDescription = getPaymentTypeDescription(m);
        if (paymentTypeDescription == null) {
            paymentTypeDescription = new PaymentTypeDescription();
            paymentTypeDescription.setPaymentType((PicklistItem) m.get("paymentType"));
            paymentTypeDescription.setContractProspectType((PicklistItem) m.get("contractProspectType"));

            if (m.containsKey("paymentSubType")) {
                paymentTypeDescription.setPaymentSubType((PicklistItem) m.get("paymentSubType"));
            }

            if (m.containsKey("additionalCondition")) {
                paymentTypeDescription.setAdditionalCondition((PaymentTypeDescription.AdditionalCondition) m.get("additionalCondition"));
            }
        }
        paymentTypeDescription.setDescription((String) m.get("description"));
        paymentTypeDescriptionRepository.save(paymentTypeDescription);
    }

    private PaymentTypeDescription getPaymentTypeDescription(Map<String, Object> m) {
        SelectQuery<PaymentTypeDescription> q = new SelectQuery<>(PaymentTypeDescription.class);
        q.filterBy("paymentType", "=", m.get("paymentType"));
        q.filterBy("contractProspectType", "=", m.get("contractProspectType"));

        if (m.containsKey("paymentSubType")) {
            q.filterBy("paymentSubType", "=", m.get("paymentSubType"));
        }

        if (m.containsKey("additionalCondition")) {
            q.filterBy("additionalCondition", "=", m.get("additionalCondition"));
        }

        List<PaymentTypeDescription> l = q.execute();

        return l.isEmpty() ? null : l.get(0);
    }

    public String getPaymentTypeDescriptionText(Map<String, Object> m) {

        PaymentTypeDescription paymentTypeDescription = getPaymentTypeDescription(m);

        if (paymentTypeDescription == null) return ((PicklistItem) m.get("paymentType")).getName();

        String description = paymentTypeDescription.getDescription();

        if (description.contains("@month@")) {
            description = description.replace("@month@", new LocalDate(m.get("dateOfPayment")).toString("MMMM"));
        }

        if (description.contains("@x_days_prorated@")) {
            LocalDate proRatedDate = new LocalDate(m.get("dateOfPayment"));
            int remainingDays =
                    Days.daysBetween(proRatedDate, proRatedDate.dayOfMonth().withMaximumValue()).getDays() + 1;
            description = description.replace("@x_days_prorated@", String.valueOf(remainingDays));
        }

        if (description.contains("@worker_type@")) {
            description = description.replace("@worker_type@", (String) m.get("workerType"));
        }

        if (description.contains("@her_his@")) {
            description = description.replace("@her_his@", (String) m.get("her_his"));
        }

        return description;
    }

    public String getPaymentTypeDescriptionTextFromWrapper(ContractPaymentWrapper w) {

        ContractPaymentTerm cpt = w.getContractPaymentConfirmationToDo().getContractPaymentTerm();
        Map<String, Object> m = new HashMap<String, Object>() {{
            put("paymentType", w.getPaymentType());
            put("contractProspectType", w.getContractPaymentConfirmationToDo().getContractPaymentTerm()
                    .getContract().getContractProspectType());
            put("dateOfPayment", w.getPaymentDate());
            put("workerType",
                    w.getContractPaymentConfirmationToDo().getContractPaymentTerm().getContract()
                            .getWorkerType() == null ? "maid" :
                    w.getContractPaymentConfirmationToDo().getContractPaymentTerm().getContract()
                            .getWorkerType().getCode().equals("private_driver") ? "driver" : "maid");
            put("her_his", cpt.getContract().getHousemaid() != null && cpt.getContract().getHousemaid().getGender() != null &&
                    cpt.getContract().getHousemaid().getGender().equals(Gender.Male) ? "his" : "her");

            if (PaymentHelper.isMonthlyPayment(w.getPaymentType())) {
                if (w.isProrated()) {
                    put("additionalCondition", PaymentTypeDescription.AdditionalCondition.PRO_RATED);
                }

                if (w.isIncludeWorkerSalary()) {
                    put("additionalCondition", PaymentTypeDescription.AdditionalCondition.WITH_WORKER_SALARY);
                }
            }
        }};

        return getPaymentTypeDescriptionText(m);
    }
}
