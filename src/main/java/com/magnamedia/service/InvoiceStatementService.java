package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.entity.*;
import com.magnamedia.entity.dto.*;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.extra.InvoiceStatementStatus;
import com.magnamedia.extra.InvoiceStatementTransactionType;
import com.magnamedia.module.type.BucketType;
import com.magnamedia.module.type.ExpenseBeneficiaryType;
import com.magnamedia.module.type.ExpensePaymentMethod;
import com.magnamedia.repository.*;
import com.magnamedia.workflow.type.ExpenseRequestStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;


@Service
public class InvoiceStatementService {

    private static final Logger logger = Logger.getLogger(InvoiceStatementService.class.getName());
    private static final String MEDICAL_SUPPLIER_NAME = "Capital Medical Centre for Health";

    @Autowired
    private InvoiceStatementRepository invoiceStatementRepository;

    @Autowired
    private InvoiceStatementTransactionRepository invoiceStatementTransactionRepository;

    @Autowired
    private InvoiceStatementRecordRepository invoiceStatementRecordRepository;

    @Autowired
    private ExpenseRequestTodoRepository expenseRequestTodoRepository;

    @Autowired
    private SupplierRepository supplierRepository;

    @Autowired
    private BucketRepository bucketRepository;

    @Autowired
    private HousemaidRepository housemaidRepository;

    @Autowired
    private BackgroundTaskService backgroundTaskService;

    private Page<InvoiceStatementTransaction> getPendingMatchedTransactions(
            Long statementId,
            InvoiceStatementTransactionSearchDto searchDto,
            Pageable pageable) {

        InvoiceStatement statement = invoiceStatementRepository.findOne(statementId);
        Supplier supplier = findMedicalSupplier();

        List<InvoiceStatementTransactionType> usedTypes = Arrays.asList(
                InvoiceStatementTransactionType.Matched,
                InvoiceStatementTransactionType.MISMATCHED
        );

        Page<ExpenseRequestTodo> filteredExpenseRequests = expenseRequestTodoRepository.findPendingMatchedTransactions(
                statementId, usedTypes, supplier.getId(),
                searchDto.getMaidName(), searchDto.getPassportNumber(),
                pageable
        );

        List<InvoiceStatementTransaction> transactions = filteredExpenseRequests.getContent().stream()
                .map(expenseRequest -> {
                    InvoiceStatementTransaction transaction = new InvoiceStatementTransaction();
                    transaction.setExpenseRequestTodo(expenseRequest);
                    transaction.setHousemaid(housemaidRepository.findOne(expenseRequest.getRelatedToId()));
                    transaction.setStatement(statement);
                    transaction.setAmount(expenseRequest.getAmount());
                    transaction.setType(InvoiceStatementTransactionType.PENDING_MATCHED);
                    transaction.setVisitDate(expenseRequest.getCreationDate());
                    return transaction;
                })
                .collect(Collectors.toList());

        return new PageImpl<>(transactions, pageable, filteredExpenseRequests.getTotalElements());
    }

    public Page<InvoiceStatementTransactionDto> searchTransactions(
            Long statementId,
            InvoiceStatementTransactionSearchDto searchDto,
            Pageable pageable) {

        InvoiceStatement statement = invoiceStatementRepository.findOne(statementId);
        if (searchDto.getType() == null) {
            throw new BusinessException("InvoiceStatementTransactionType is required");
        }

        Page<InvoiceStatementTransaction> transactionPage;

        if (searchDto.getType() == InvoiceStatementTransactionType.PENDING_MATCHED) {
            transactionPage = getPendingMatchedTransactions(statementId, searchDto, pageable);
        } else {
            // Get specific type Matched or MISMATCHED (exclude DELETED transactions)
            transactionPage = invoiceStatementTransactionRepository.searchTransactions(
                    statementId,
                    searchDto.getType(),
                    searchDto.getMaidName(),
                    searchDto.getPassportNumber(),
                    searchDto.getFromDate(),
                    searchDto.getToDate(),
                    pageable
            );
        }

        List<InvoiceStatementTransactionDto> transactionDtos = transactionPage.getContent().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());

        return new PageImpl<>(transactionDtos, pageable, transactionPage.getTotalElements());
    }

    @Transactional
    public void processInvoiceStatementTransactionPayment(InvoiceStatementTransactionPayDto payDto) {
        for (InvoiceStatementTransactionPayItemDto item : payDto.getTransactionItems()) {
            logger.info("processing item: " + item.getId());
            InvoiceStatementTransaction transaction = invoiceStatementTransactionRepository.findOne(item.getId());
            if(transaction.getConfirmed()) throw new BusinessException("This transaction has been processed or is under processing");
            transaction.setConfirmed(true);
            invoiceStatementTransactionRepository.save(transaction);
        }

        ExpensePaymentPayInvoiceDto payInvoiceDto = convertToPayInvoiceDto(payDto);

        logger.info("querying bucket");
        List<Bucket> buckets = bucketRepository.findByBucketType(BucketType.BANK_ACCOUNT);
        if (buckets.isEmpty()) {
            throw new RuntimeException("No bank account bucket found");
        }

        ExpensePayment savedPayment = Setup.getApplicationContext().getBean(ExpensePaymentService.class)
                .generateInvoicePayment(payInvoiceDto, buckets.get(0));

        logger.info("Created invoice statement transaction payment with ID: " + savedPayment.getId() +
                   " for " + payDto.getTransactionItems().size() + " transactions");

        checkAndCloseStatementsAfterPayment(payDto.getTransactionItems());
    }

    @Transactional
    public void processAllMatchedTransactionsPayment(Long statementId, InvoiceStatementTransactionPayDto payDto) {
        Page<InvoiceStatementTransaction> matchedTransactions = invoiceStatementTransactionRepository.searchTransactions(
                statementId,
                InvoiceStatementTransactionType.Matched,
                payDto.getMaidName(),
                payDto.getPassportNumber(),
                payDto.getFromDate(),
                payDto.getToDate(),
                Pageable.unpaged()
        );
        if (matchedTransactions.getContent().isEmpty()) {
            throw new RuntimeException("No matched transactions found with the specified criteria");
        }

        List<InvoiceStatementTransactionPayItemDto> paymentItems = matchedTransactions.getContent().stream()
                .map(transaction -> {
                    InvoiceStatementTransactionPayItemDto item = new InvoiceStatementTransactionPayItemDto();
                    item.setId(transaction.getId());
                    return item;
                })
                .collect(Collectors.toList());

        InvoiceStatementTransactionPayDto filteredPayDto = new InvoiceStatementTransactionPayDto();
        filteredPayDto.setTransactionItems(paymentItems);
        filteredPayDto.setTaxable(payDto.getTaxable());
        filteredPayDto.setAttachments(payDto.getAttachments());

        processInvoiceStatementTransactionPayment(filteredPayDto);
    }

    private ExpensePaymentPayInvoiceDto convertToPayInvoiceDto(InvoiceStatementTransactionPayDto payDto) {
        logger.info("convertToPayInvoiceDto");

        ExpensePaymentPayInvoiceDto payInvoiceDto = new ExpensePaymentPayInvoiceDto();
        payInvoiceDto.setTaxable(payDto.getTaxable());
        payInvoiceDto.setAttachments(payDto.getAttachments());
        List<ExpenseRequestPayInvoiceDto> expenseRequestDtos = new ArrayList<>();

        for (InvoiceStatementTransactionPayItemDto item : payDto.getTransactionItems()) {
            logger.info("processing item: " + item.getId());

            InvoiceStatementTransaction transaction = invoiceStatementTransactionRepository.findOne(item.getId());

            if (transaction != null && transaction.getExpenseRequestTodo() != null) {
                logger.info("setting  expense request");
                ExpenseRequestPayInvoiceDto expenseDto = new ExpenseRequestPayInvoiceDto(transaction.getExpenseRequestTodo());
                expenseRequestDtos.add(expenseDto);
            }
        }

        payInvoiceDto.setExpenseRequestPayInvoiceDtos(expenseRequestDtos);
        return payInvoiceDto;
    }

    @Transactional
    public void checkAndCloseStatementsAfterPayment(List<InvoiceStatementTransactionPayItemDto> paymentItems) {
        if (paymentItems == null || paymentItems.isEmpty()) {
            return;
        }

        InvoiceStatementTransactionPayItemDto firstItem = paymentItems.get(0);
        InvoiceStatementTransaction firstTransaction = invoiceStatementTransactionRepository.findOne(firstItem.getId());
        InvoiceStatement statement = firstTransaction.getStatement();
        if (InvoiceStatementStatus.CLOSED.equals(statement.getStatus())) {
            logger.info("Statement " + statement.getId() + " is already closed");
            return;
        }

        Double balance = calculateBalance(statement.getId());
        logger.info("Statement " + statement.getId() + " current balance: " + balance);

        if (Math.abs(balance) < 0.01) {
            statement.setStatus(InvoiceStatementStatus.CLOSED);
            invoiceStatementRepository.save(statement);
            logger.info("Closed invoice statement " + statement.getId() + " - balance reached 0");
        }
    }

    private InvoiceStatementTransactionDto convertToDto(InvoiceStatementTransaction transaction) {
        String expenseName = transaction.getExpenseRequestTodo() != null && transaction.getExpenseRequestTodo().getExpense() != null
                ? transaction.getExpenseRequestTodo().getExpense().getName() : "";
        String relatedTo = transaction.getHousemaid() != null ? transaction.getHousemaid().getName() :
                          (transaction.getRelatedMaidName() != null ? transaction.getRelatedMaidName() : "");
        String passportNo = transaction.getHousemaid() != null ? transaction.getHousemaid().getPassportNumber() : "";

        // Convert HousemaidType enum to String
        String maidType = "";
        if (transaction.getHousemaid() != null && transaction.getHousemaid().getHousemaidType() != null) {
            maidType = transaction.getHousemaid().getHousemaidType().toString().equals("MAID_VISA") ? "MV" : "CC";
        }

        return new InvoiceStatementTransactionDto(
            transaction.getId(),
            transaction.getVisitDate(),
            expenseName,
            relatedTo,
            passportNo,
            maidType,
            transaction.getAmount(),
            transaction.getReason(),
            transaction.getType(),
            transaction.getExpenseRequestTodo() != null
                    ? transaction.getExpenseRequestTodo().getId()
                    : null
        );
    }

    public Double calculateBalance(Long statementId) {
        List<InvoiceStatementTransactionType> balanceTypes = Arrays.asList(
                InvoiceStatementTransactionType.Matched,
                InvoiceStatementTransactionType.MISMATCHED
        );
        return invoiceStatementTransactionRepository.sumAmountByStatementIdAndTypes(statementId, balanceTypes);
    }

    public Double calculateTotalAmountToPay(Long statementId) {
        return invoiceStatementTransactionRepository.sumAmountByStatementIdAndType(
                statementId, InvoiceStatementTransactionType.Matched);
    }

    public Attachment getAnnexureFileUuid(Long statementId) {
        InvoiceStatement statement = invoiceStatementRepository.findOne(statementId);
        if (statement == null) {
            throw new BusinessException("Invoice statement not found with ID: " + statementId);
        }
        return statement.getAttachment("annexure_file");
    }

    public InvoiceStatementCalculationsDto getInvoiceStatementCalculations(Long statementId) {
        Double balance = calculateBalance(statementId);
        Double totalAmountToPay = calculateTotalAmountToPay(statementId);
        Attachment annexureFileUuid = getAnnexureFileUuid(statementId);

        Supplier medicalSupplier = findMedicalSupplier();
        Long supplierId = medicalSupplier != null ? medicalSupplier.getId() : null;
        String supplierName = medicalSupplier != null ? medicalSupplier.getName() : MEDICAL_SUPPLIER_NAME;

        return new InvoiceStatementCalculationsDto(balance, totalAmountToPay, annexureFileUuid, supplierId, supplierName);
    }

    public void createInvoiceStatementTransactions(InvoiceStatement statement) {
        Supplier supplier = findMedicalSupplier();

        List<ExpenseRequestTodo> expenseRequests = expenseRequestTodoRepository.findPendingInvoicedExpenseRequests(
                supplier.getId(), statement.getId(), null);

        Map<Long, List<ExpenseRequestTodo>> m = expenseRequests.stream()
                .collect(Collectors.groupingBy(t -> t.getHousemaid().getId()));

        List<InvoiceStatementRecord> records = invoiceStatementRecordRepository.findByStatementAndDeletedFalse(statement);

        records.stream().forEach(record -> {
            String maidName = record.getHousemaid() != null ? record.getHousemaid().getName() : record.getName();

            InvoiceStatementTransaction transaction = new InvoiceStatementTransaction();
            transaction.setHousemaid(record.getHousemaid());
            transaction.setVisitDate(record.getVisitDate());
            transaction.setStatement(record.getStatement());
            transaction.setRelatedMaidName(record.getName());
            transaction.setPassportNo(record.getPassportNo());

            // Case 1: No expense requests found for this housemaid
            if(record.getHousemaid() == null || !m.containsKey(record.getHousemaid().getId())) {
                transaction.setAmount(record.getAmount());
                transaction.setType(InvoiceStatementTransactionType.MISMATCHED);
                transaction.setReason("Not Existing in ERP");

                saveTransactionAndUpdateRecord(transaction, record);

                logger.info("Created Not Existing in ERP for: " + maidName);
            } else {
                ExpenseRequestTodo matchedRequest = m.get(record.getHousemaid().getId())
                        .stream()
                        .filter(t -> Math.abs(t.getAmount() - record.getAmount()) < 0.01)
                        .findFirst().orElse(null);

                if (matchedRequest != null) {
                    // Perfect match found - move to Matched Cases grid
                    transaction.setAmount(record.getAmount()); // Use invoice amount for matched cases
                    transaction.setType(InvoiceStatementTransactionType.Matched);
                    transaction.setExpenseRequestTodo(matchedRequest);
                    saveTransactionAndUpdateRecord(transaction, record);

                    logger.info("Created MATCHED transaction for: " + maidName);
                } else {
                    // No exact match - handle amount mismatch
                    // match only one record to match the first eligible record
                    ExpenseRequestTodo firstRequest = m.get(record.getHousemaid().getId()).get(0);

                    // Create mismatched transaction with ERP amount
                    transaction.setAmount(firstRequest.getAmount()); // Use ERP amount for mismatched cases
                    transaction.setType(InvoiceStatementTransactionType.MISMATCHED);
                    transaction.setExpenseRequestTodo(firstRequest);
                    transaction.setReason("Amount in the invoice: " + record.getAmount() + " AED");
                    saveTransactionAndUpdateRecord(transaction, record);

                    logger.info("Created Amount Mismatch transaction for: " + maidName +
                            " - ERP: " + firstRequest.getAmount() + ", Invoice: " + record.getAmount());
                }
            }
        });
    }

    private void saveTransactionAndUpdateRecord(InvoiceStatementTransaction transaction, InvoiceStatementRecord record) {
        invoiceStatementTransactionRepository.save(transaction);

        record.setTransaction(transaction);
        invoiceStatementRecordRepository.save(record);
    }

    private Supplier findMedicalSupplier() {
        Supplier s = supplierRepository.findFirstByName(MEDICAL_SUPPLIER_NAME);

        if(s == null) throw new RuntimeException("Supplier '" + MEDICAL_SUPPLIER_NAME + "' not found");
        return s;
    }

    public List<InvoiceStatementTransaction> getPendingMatchingTransactions(
            Long statementId, Long housemaidId) {

        InvoiceStatement statement = invoiceStatementRepository.findOne(statementId);
        Housemaid housemaid = housemaidRepository.findOne(housemaidId);

        Supplier supplier = findMedicalSupplier();

        // Find expense requests for this specific housemaid
        List<ExpenseRequestTodo> expenseRequests = expenseRequestTodoRepository.findPendingInvoicedExpenseRequestsForMaid(
                housemaidId, supplier.getId(), statementId);

        // Filter out already used expense requests and create virtual transactions
        return expenseRequests.stream()
                .map(expenseRequest -> {
                    InvoiceStatementTransaction transaction = new InvoiceStatementTransaction();
                    transaction.setExpenseRequestTodo(expenseRequest);
                    transaction.setHousemaid(housemaid);
                    transaction.setStatement(statement);
                    transaction.setAmount(expenseRequest.getAmount());
                    transaction.setType(InvoiceStatementTransactionType.PENDING_MATCHED);
                    transaction.setVisitDate(expenseRequest.getCreationDate());
                    return transaction;
                })
                .collect(Collectors.toList());
    }


    // rematch methods
    @org.springframework.transaction.annotation.Transactional
    public RematchResult performRematch(Long statementId) {
        logger.info("Starting rematch process for statement ID: " + statementId);
        InvoiceStatement statement = invoiceStatementRepository.findOne(statementId);

        RematchResult result = new RematchResult();
        List<InvoiceStatementTransaction> mismatchedTransactions = invoiceStatementTransactionRepository
                .findTransactionsForRematch(statementId);

        logger.info("Found " + mismatchedTransactions.size() + " eligible mismatched transactions to process");
        for (InvoiceStatementTransaction mismatchedTransaction : mismatchedTransactions) {
            try {
                processRematchForTransaction(mismatchedTransaction, statement, result);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Error processing rematch for transaction " + mismatchedTransaction.getId(), e);
            }
        }
        logger.info("Rematch completed. Processed: " + result.getProcessedCount() +
                ", Matched: " + result.getMatchedCount() +
                ", Updated: " + result.getUpdatedCount());
        return result;
    }

    private void processRematchForTransaction(
            InvoiceStatementTransaction mismatchedTransaction,
            InvoiceStatement statement,
            RematchResult result) {

        logger.info("transaction ID: " + mismatchedTransaction.getId());

        result.incrementProcessed();
        Housemaid housemaid = mismatchedTransaction.getHousemaid();

        if (housemaid == null) {
            logger.info("Housemaid is null for transaction " + mismatchedTransaction.getId() +
                    " trying to fetch by passport");
            housemaid = tryFetchHousemaidByPassport(mismatchedTransaction);

            if (housemaid != null) {
                logger.info("Found housemaid by passport: " + housemaid.getName());

                mismatchedTransaction.setHousemaid(housemaid);
                invoiceStatementTransactionRepository.save(mismatchedTransaction);
                logger.info("Found and updated housemaid for transaction " + mismatchedTransaction.getId() +
                        " using passport: " + housemaid.getPassportNumber());
            } else {
                logger.info("No housemaid found by passport for transaction " + mismatchedTransaction.getId());
                return;
            }
        }

        if(mismatchedTransaction.getExpenseRequestTodo() != null &&
           Math.abs(mismatchedTransaction.getAmount() - mismatchedTransaction.getExpenseRequestTodo().getAmount()) < 0.01){
            mismatchedTransaction.setType(InvoiceStatementTransactionType.Matched);
            mismatchedTransaction.setReason(null);
            invoiceStatementTransactionRepository.save(mismatchedTransaction);

            result.incrementMatched();
            return;
        }

        List<InvoiceStatementTransaction> pendingTransactions = getPendingMatchingTransactions(
                statement.getId(), housemaid.getId());
        logger.info("pendingTransactions size: " + pendingTransactions.size());

        for (InvoiceStatementTransaction pending : pendingTransactions) {
            logger.info("pending amount: " + pending.getAmount());

            if (Math.abs(mismatchedTransaction.getAmount() - pending.getAmount()) < 0.01) {
                mismatchedTransaction.setType(InvoiceStatementTransactionType.Matched);
                mismatchedTransaction.setExpenseRequestTodo(pending.getExpenseRequestTodo());
                mismatchedTransaction.setReason(null);
                invoiceStatementTransactionRepository.save(mismatchedTransaction);
                result.incrementMatched();
            } else {
                mismatchedTransaction.setReason("Amount in the invoice: " + mismatchedTransaction.getAmount() + " AED");
                mismatchedTransaction.setAmount(pending.getAmount());
                mismatchedTransaction.setExpenseRequestTodo(pending.getExpenseRequestTodo());
                invoiceStatementTransactionRepository.save(mismatchedTransaction);
                result.incrementUpdated();
            }
        }
    }

    private Housemaid tryFetchHousemaidByPassport(InvoiceStatementTransaction mismatchedTransaction) {
        if (mismatchedTransaction.getPassportNo() != null) {
            logger.info("Trying to fetch housemaid by passport: " + mismatchedTransaction.getPassportNo());

            List<Housemaid> housemaids = housemaidRepository.findByPassportNumber(mismatchedTransaction.getPassportNo());
            if (!housemaids.isEmpty()) {
                logger.info("Found housemaid by passport: " + housemaids.get(0).getName());
                return housemaids.get(0);
            }
        }
        logger.info("No passport found for transaction: " + mismatchedTransaction.getId());
        return null;
    }

    public void handleCapitalMedicalCentreInvoicedPayments(ExpenseRequestTodo requestTodo) {
        if (!shouldProcessCapitalMedicalCentreRequest(requestTodo)) return;

        try {
            List<InvoiceStatement> openInvoiceStatements = invoiceStatementRepository.findByStatus(InvoiceStatementStatus.PENDING);

            for (InvoiceStatement statement : openInvoiceStatements) {
                boolean e = invoiceStatementTransactionRepository.existsByStatementAndHousemaid_IdAndType(
                        statement, requestTodo.getRelatedToId(), InvoiceStatementTransactionType.MISMATCHED);

                if (e) createPerformRematchBackgroundTask(statement.getId());
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error processing Capital Medical Centre invoiced payment" , e);
        }
    }

    private boolean shouldProcessCapitalMedicalCentreRequest(ExpenseRequestTodo requestTodo) {
        return requestTodo.getRelatedToType() != null &&
                requestTodo.getRelatedToType().equals(ExpenseRelatedTo.ExpenseRelatedToType.MAID) &&
                requestTodo.getPaymentMethod() != null &&
                requestTodo.getPaymentMethod().equals(ExpensePaymentMethod.INVOICED) &&
                requestTodo.getStatus() != null &&
                requestTodo.getStatus().equals(ExpenseRequestStatus.PENDING_PAYMENT) &&
                requestTodo.getBeneficiaryType() != null &&
                requestTodo.getBeneficiaryType().equals(ExpenseBeneficiaryType.SUPPLIER) &&
                requestTodo.getBeneficiaryId() != null &&
                isCapitalMedicalCentreSupplier(requestTodo.getBeneficiaryId());
    }

    private boolean isCapitalMedicalCentreSupplier(Long supplierId) {
        try {
            Supplier supplier = supplierRepository.findOne(supplierId);
            return supplier != null && "Capital Medical Centre for Health".equals(supplier.getName());
        } catch (Exception e) {
            logger.warning("Error checking supplier: " + e.getMessage());
            return false;
        }
    }

    private void createPerformRematchBackgroundTask(Long statementId) {
        Long time = new Date().getTime();
        backgroundTaskService.create(new BackgroundTask.builder(
                "performRematchCapitalMedical_" + statementId + "_" + time,
                "accounting",
                "invoiceStatementService",
                "performRematch")
                .withRelatedEntity("InvoiceStatement", statementId)
                .withParameters(
                        new Class[]{Long.class},
                        new Object[]{statementId})
                .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                .build());

        logger.info("Created background task for rematch on statement ID: " + statementId);
    }

    public static class RematchResult {
        private int processedCount = 0;
        private int matchedCount = 0;
        private int updatedCount = 0;

        public void incrementProcessed() { processedCount++; }
        public void incrementMatched() { matchedCount++; }
        public void incrementUpdated() { updatedCount++; }

        public int getProcessedCount() { return processedCount; }
        public int getMatchedCount() { return matchedCount; }
        public int getUpdatedCount() { return updatedCount; }
    }

}
