package com.magnamedia.service;

import com.magnamedia.controller.ContractController;
import com.magnamedia.controller.TransactionsController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.Picklist;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.dto.BankTransactionInfo;
import com.magnamedia.entity.maidsatv2.actions.employeragreement.SendRequestForApprovalAction;
import com.magnamedia.entity.payroll.logging.OfficeStaffPayrollLog;
import com.magnamedia.entity.workflow.BucketReplenishmentTodo;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.entity.workflow.PayrollAccountantTodo;
import com.magnamedia.extra.PaymentHelper;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.opencsv.CSVReader;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigInteger;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.magnamedia.module.AccountingModule.*;

@Service
public class BankStatementFileService {

    private final Logger logger = Logger.getLogger(BankStatementFileService.class.getName());

    @Autowired
    private BankStatementTransactionRepository bankStatementTransactionRepository;
    @Autowired
    private BankStatementRecordRepository bankStatementRecordRepository;
    @Autowired
    private BucketRepository bucketRepository;
    @Autowired
    private ExpenseRepository expenseRepository;
    @Autowired
    private RevenueRepository revenueRepository;
    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private TransactionRepository transactionRepository;
    @Autowired
    private TransactionsController transactionsController;
    @Autowired
    private ContractController contractController;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private BouncingFlowService bouncingFlowService;
    @Autowired
    private SwitchingBankAccountService switchingBankAccountService;
    @Autowired
    private OfficeStaffPayrollLogRepository officeStaffPayrollLogRepository;
    @Autowired
    private BankStatementFileRepository bankStatementFileRepository;

    @Transactional
    public Boolean confirmBucketRefillTransaction(
            BankStatementTransaction bankTransaction,
            BankStatementRecord bankRecord) {

        logger.info("Enter confirmBucketRefillTransaction for transaction: " + bankTransaction.getId());

        String bucketCode = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_STATEMENTS_TRANSACTIONS_FROM_BUCKET);

        Transaction transaction = new Transaction();
        transaction.setDate(new java.sql.Date(bankRecord.getDate().getTime()));
        transaction.setPnlValueDate(new java.sql.Date(bankRecord.getDate().getTime()));
        transaction.setAmount(bankRecord.getDebitAmount());
        transaction.setFromBucket(bucketRepository.findByCode(bucketCode));//Mustaqeem ADCB
        transaction.setToBucket(bucketRepository.findByCode(bankTransaction.getToBucket()));
        transaction.setVatAmount(bankTransaction.getVatAmount());
        transaction.setVatType(bankTransaction.getVatType());
        transaction.setLicense(bankTransaction.getLicense());
        transaction.setPaymentType(PaymentMethod.WIRE_TRANSFER);
        transaction.setDescription(bankTransaction.getDescription());
        transaction.setTransactionType(TransactionEntityType.UNKNOWN);
        transaction.setAutomatic(true);
        transaction = (Transaction) transactionsController.createEntity(transaction).getBody();

        logger.info("new Transaction created with Id: " + transaction.getId());

        bankTransaction.setTransaction(transaction);
        bankTransaction.setResolved(true);
        bankStatementTransactionRepository.save(bankTransaction);

        logger.info("bankTransaction " + bankTransaction.getId() + " is saved and confirmed now.");
        return true;
    }

    @Transactional
    public Boolean confirmExpensesTransaction(
            BankStatementTransaction bankTransaction,
            BankStatementRecord bankRecord) {

        logger.info("Enter confirmExpensesTransaction for transaction: " + bankTransaction.getId() +
                " with status " + bankTransaction.getBankTransactionStatus());

        String bucketCode = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_STATEMENTS_TRANSACTIONS_FROM_BUCKET);

        switch (bankTransaction.getBankTransactionStatus()) {
            case MATCHED: //Matched Client Refund
                logger.info("getting the matched payment in order to make it RECEIVED.");
                Payment matchedPayment = bankTransaction.getPayment();
                if (matchedPayment == null) {
                    List<Payment> matchedPayments = bankTransaction.getContract().getPayments()
                            .stream().filter(x -> x.getAmountOfPayment().equals(bankTransaction.getTransactionAmount()) &&
                                    (x.getDateOfPayment().compareTo(bankRecord.getDate()) == 0))
                            .collect(Collectors.toList());

                    if (matchedPayments != null && !matchedPayments.isEmpty()) {
                        matchedPayment = matchedPayments.get(0);
                    }
                }

                if (matchedPayment != null) {
                    logger.info("#confirmExpensesTransaction, we found " + matchedPayment.getId() + " matches.");
                    if (matchedPayment.getStatus() != PaymentStatus.RECEIVED && matchedPayment.getStatus() != PaymentStatus.BOUNCED) {
                        logger.info("set the payment " + matchedPayment.getId() + " status as RECEIVED");

                        matchedPayment.setStatus(PaymentStatus.RECEIVED);
                        matchedPayment.setUpdatedFromBankStatement(true);
                        matchedPayment.setBankStatmentTransactionId(bankTransaction.getId());

                        paymentService.forceUpdatePayment(matchedPayment);
                        logger.info("finish calling updatePaymentFromCmDirectCall for Payment " + matchedPayment.getId());
                    }

                    bankTransaction.setResolved(true);
                    bankStatementTransactionRepository.save(bankTransaction);
                    logger.info("bankTransaction " + bankTransaction.getId() + " is saved and confirmed now.");
                    return true;
                }
                break;

            case OTHER_EXPENSES:
                logger.info("create a new transaction now.");
                Transaction transaction = new Transaction();
                transaction.setDate(new java.sql.Date(bankRecord.getDate().getTime()));
                transaction.setPnlValueDate(new java.sql.Date(bankRecord.getDate().getTime()));
                transaction.setAmount(bankTransaction.getTransactionAmount());
                transaction.setFromBucket(bucketRepository.findByCode(bucketCode));//Mustaqeem ADCB
                transaction.setExpense(expenseRepository.findByCodeAndDeletedFalse(bankTransaction.getExpenseCode()));
                transaction.setVatAmount(bankTransaction.getVatAmount());
                transaction.setVatType(VatType.IN);
                transaction.setLicense(PicklistHelper.getItemNoException(
                        PICKLIST_TRANSACTION_LICENSE, PICKLIST_TRANSACTION_LICENSE_MUSTAQEEM_ITEM));
                transaction.setPaymentType(PaymentMethod.WIRE_TRANSFER);
                transaction.setDescription(bankTransaction.getDescription());
                transaction.setTransactionType(TransactionEntityType.UNKNOWN);
                transaction.setAutomatic(true);
                transaction = (Transaction) transactionsController.createEntity(transaction).getBody();

                logger.info("new Transaction created with Id: " + transaction.getId());

                bankTransaction.setTransaction(transaction);
                bankTransaction.setResolved(true);
                bankStatementTransactionRepository.save(bankTransaction);
                logger.info("bankTransaction " + bankTransaction.getId() + " is saved and confirmed now.");
                return true;
        }

        logger.info("finished but didn't confirmed");
        return false;
    }

    @Transactional
    public Boolean confirmExpensesPaymentTransaction(
            BankStatementTransaction bankTransaction,
            HashMap parameters) {

        logger.info("Enter confirmExpensesPaymentRefundTransaction for transaction: " + bankTransaction.getId() +
                " with status " + bankTransaction.getBankTransactionStatus());

        switch (bankTransaction.getBankTransactionStatus()) {
            case MATCHED:
                logger.info("Matched -> confirm related Expense Payment");

                if (parameters != null && parameters.containsKey("description")) {
                    bankTransaction.setDescription(parameters.get("description").toString());
                }

                Boolean result = Setup.getApplicationContext().getBean(ExpensePaymentService.class)
                        .confirmRelatedExpensePayment(bankTransaction);

                bankTransaction.setResolved(true);
                bankStatementTransactionRepository.save(bankTransaction);
                return result;
        }

        logger.info("finished but didn't confirmed");
        return false;
    }

    @Transactional
    public Boolean confirmPDCTransaction(
            BankStatementTransaction bankTransaction,
            BankStatementRecord bankRecord) {

        logger.info("Enter confirmPDCTransaction for transaction: " + bankTransaction.getId());
        String bucketCode = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_STATEMENTS_TRANSACTIONS_FROM_BUCKET);
        Transaction transaction = new Transaction();
        transaction.setDate(new java.sql.Date(bankRecord.getDate().getTime()));
        transaction.setPnlValueDate(new java.sql.Date(bankRecord.getDate().getTime()));
        transaction.setAmount(bankRecord.getDebitAmount());
        transaction.setFromBucket(bucketRepository.findByCode(bucketCode)); //Mustaqeem ADCB
        transaction.setExpense(bankTransaction.getExpense());
        transaction.setVatAmount(bankTransaction.getVatAmount());
        transaction.setVatType(VatType.OUT);
        transaction.setLicense(bankTransaction.getLicense());
        transaction.setPaymentType(PaymentMethod.CHEQUE);
        transaction.setTransactionType(TransactionEntityType.UNKNOWN);
        transaction.setAutomatic(true);
        transaction = (Transaction) transactionsController.createEntity(transaction).getBody();

        logger.info("new Transaction created with Id: " + transaction.getId());

        bankTransaction.setTransaction(transaction);
        bankTransaction.setResolved(true);
        bankStatementTransactionRepository.save(bankTransaction);

        logger.info("bankTransaction " + bankTransaction.getId() + " is saved and confirmed now.");
        return true;
    }

    @Transactional
    public Boolean confirmCashDepositOrWireTransferTransaction(
            BankStatementTransaction bankTransaction,
            BankStatementRecord bankRecord,
            PaymentMethod paymentType) {

        logger.info("Enter confirmCashDepositOrWireTransferTransaction for transaction: " + bankTransaction.getId());
        String bucketCode = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_STATEMENTS_TRANSACTIONS_FROM_BUCKET);
        Transaction transaction = new Transaction();
        transaction.setDate(new java.sql.Date(bankRecord.getDate().getTime()));
        transaction.setPnlValueDate(new java.sql.Date(bankRecord.getDate().getTime()));
        transaction.setAmount(bankRecord.getCreditAmount());
        transaction.setRevenue(revenueRepository.findByCode("UWR 01"));
        transaction.setToBucket(bucketRepository.findByCode(bucketCode)); //Mustaqeem ADCB
        transaction.setVatAmount(0.0);
        transaction.setVatType(null);
        transaction.setLicense(PicklistHelper.getItemNoException(
                PICKLIST_TRANSACTION_LICENSE, PICKLIST_TRANSACTION_LICENSE_MUSTAQEEM_ITEM));
        transaction.setPaymentType(paymentType);
        transaction.setDescription(bankRecord.getCustomerReferenceNo() + " - " + bankRecord.getDescription());
        transaction.setTransactionType(TransactionEntityType.UNKNOWN);
        transaction.setAutomatic(true);
        transaction = (Transaction) transactionsController.createEntity(transaction).getBody();

        logger.info("new Transaction created with Id: " + transaction.getId());

        bankTransaction.setTransaction(transaction);
        bankTransaction.setResolved(true);
        bankStatementTransactionRepository.save(bankTransaction);

        logger.info("bankTransaction " + bankTransaction.getId() + " is saved and confirmed  now.");
        return true;
    }

    @Transactional
    public Boolean confirmExEmployerTransaction(
            BankStatementTransaction bankTransaction,
            BankStatementRecord bankRecord) {

        logger.info("Enter confirmExEmployerTransaction for transaction : " + bankTransaction.getId());
        String bucketCode = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_STATEMENTS_TRANSACTIONS_FROM_BUCKET);
        Transaction transaction = new Transaction();
        transaction.setDate(new java.sql.Date(bankRecord.getDate().getTime()));
        transaction.setPnlValueDate(new java.sql.Date(bankRecord.getDate().getTime()));
        transaction.setAmount(bankRecord.getCreditAmount());
        transaction.setVatType(null);
        transaction.setPaymentType(PaymentMethod.CARD);
        transaction.setExpense(expenseRepository.findOneByCode("SP 03"));
        transaction.setTransactionType(TransactionEntityType.HOUSEMAID);
        transaction.setDescription(bankTransaction.getDescription());
        transaction.setFromBucket(bucketRepository.findByCode(bucketCode));
        transaction.setLicense(PicklistHelper.getItemNoException(
                PICKLIST_TRANSACTION_LICENSE, PICKLIST_TRANSACTION_LICENSE_NO_VAT_ITEM));
        transaction.setAutomatic(true);
        transaction = (Transaction) transactionsController.createEntity(transaction).getBody();

        logger.info("new Transaction created with Id: " + transaction.getId());

        bankTransaction.setTransaction(transaction);
        bankTransaction.setResolved(true);
        bankStatementTransactionRepository.save(bankTransaction);

        logger.info("bankTransaction is saved and confirmed now " + bankTransaction.getId());
        return true;
    }

    @Transactional
    public Boolean confirmPayrollTransferTransaction(BankStatementTransaction bankTransaction, BankStatementRecord bankRecord) {
        logger.info("transaction id : " + bankTransaction.getId());

        Transaction transaction = new Transaction();
        String bucketCode = Setup.getParameter(Setup.getCurrentModule(), PARAMETER_STATEMENTS_TRANSACTIONS_FROM_BUCKET);

        transaction.setDate(new java.sql.Date(bankRecord.getDate().getTime()));
        transaction.setPnlValueDate(new java.sql.Date(bankRecord.getDate().getTime()));
        transaction.setAmount(bankRecord.getDebitAmount());
        transaction.setVatType(null);
        transaction.setPaymentType(PaymentMethod.BANK_TRANSFER);
        transaction.setExpense(bankTransaction.getExpense());
        transaction.setTransactionType(TransactionEntityType.OFFICE_STAFF);
        transaction.setDescription(bankTransaction.getDescription());
        transaction.setPayrollUniqueCode(bankTransaction.getPaymentDetail());
        transaction.setFromBucket(bucketRepository.findByCode(bucketCode));
        transaction.setLicense(PicklistHelper.getItemNoException(
                PICKLIST_TRANSACTION_LICENSE, PICKLIST_TRANSACTION_LICENSE_MUSTAQEEM_ITEM));
        transaction.setAccrual(false);
        transaction = (Transaction) transactionsController.createEntity(transaction).getBody();

        logger.info("new Transaction created with Id: " + transaction.getId());

        bankTransaction.setTransaction(transaction);
        bankTransaction.setResolved(true);
        bankTransaction.setBankTransactionMatchType(BankTransactionMatchType.Automatic);
        bankStatementTransactionRepository.save(bankTransaction);

        logger.info("bankTransaction is saved and confirmed now " + bankTransaction.getId());
        return true;
    }

    @Transactional
    public Boolean confirmDirectDebitTransaction(
            BankStatementTransaction bankTransaction) throws Exception {

        logger.info("Enter confirmDirectDebitTransaction for transaction: " + bankTransaction.getId());
        Payment payment = bankTransaction.getPayment();

        if (payment == null) {
            logger.info("payment null");
            return false;
        }

        logger.info("DD ID: " + (payment.getDirectDebit() != null ? payment.getDirectDebit().getId() : "NULL"));

        logger.info("we found the matched payment.");
        switch(bankTransaction.getBankTransactionStatus()) {
            case RECEIVED:
                switch(payment.getStatus()) {
                    case BOUNCED:
                        if (!payment.isReplaced()) { // ACC-1721
                            logger.info("RECEIVED After BOUNCED, add a Replacement Payment for payment " + payment.getId());
                            bouncingFlowService.addReplacementPayment(payment, bankTransaction);
                        } else {
                            logger.info("RECEIVED Already Replaced BOUNCED Payment: " + payment.getId() + ", create new payment");
                            bouncingFlowService.addNewPayment(payment, bankTransaction);
                        }
                        break;

                    default:
                        if (payment.getStatus() == PaymentStatus.RECEIVED) break;

                        //set the related payment as received and then the posting engine will create the transactions as per the rules.
                        logger.info("set the payment " + payment.getId() + " status as RECEIVED");

                        payment.setStatus(PaymentStatus.RECEIVED);
                        payment.setUpdatedFromBankStatement(true);
                        payment.setBankStatmentTransactionId(bankTransaction.getId());

                        paymentService.forceUpdatePayment(payment);

                        logger.info("finish calling updatePaymentFromCmDirectCall for Payment " + payment.getId());
                        break;
                }

                bankTransaction.setResolved(true);
                bankStatementTransactionRepository.save(bankTransaction);

                logger.info("bankTransaction " + bankTransaction.getId() + " is saved and confirmed now.");
                return true;

            case BOUNCED:
                bouncingFlowService.addBouncedPaymentLog(payment, bankTransaction.getDirectDebitFile(),
                        "Insufficient Funds", bankTransaction.getDate());
                return processDirectDebitBouncedPayment(payment, bankTransaction);
        }

        logger.info("finished but didn't confirmed");
        return false;
    }

    public boolean processDirectDebitBouncedPayment(
            Payment payment,
            BankStatementTransaction bankTransaction) {

        if (payment.getBouncedFlowPausedForReplacement()) { // CMA-622
            payment.setBouncedAgainDuringPausingPeriod(true);
            payment.setSentToBankByMDD(false);
            payment.setUpdatedFromBankStatement(true);
            payment.setBankStatmentTransactionId(bankTransaction.getId());
            paymentService.forceUpdatePayment(payment);

        } else {
            if (!checkSwitchingBankAccount(payment, bankTransaction)) { // ACC-2418
                if (payment.getStatus() != PaymentStatus.BOUNCED) {
                    //set the related payment as Bounced and then the posting engine will create the transactions as per the rules.
                    logger.info("set the payment " + payment.getId() + " status as BOUNCED");

                    payment.setStatus(PaymentStatus.BOUNCED);
                    payment.setTrials(0);
                    payment.setSentToBankByMDD(false);
                    payment.setDateOfBouncing(new java.sql.Date(bankTransaction.getDate().getTime()));
                    payment.setUpdatedFromBankStatement(true);
                    payment.setBankStatmentTransactionId(bankTransaction.getId());
                    payment.setBouncedLogAdded(true);

                    payment.setReasonOfBouncingCheque(Setup.getItem("ReasonOfBouncedCheck", "insufficient_funds"));

                    payment = paymentService.forceUpdatePayment(payment);

                    logger.info("finish calling updatePaymentFromCmDirectCall for Payment " + payment.getId());
                } else if (!payment.isReplaced()) { // bounced but not replaced yet

                    payment = processDirectDebitBouncedAgainPayment(payment);

                    payment.setSentToBankByMDD(false);
                    payment.setUpdatedFromBankStatement(true);
                    payment.setBankStatmentTransactionId(bankTransaction.getId());
                    payment = paymentService.forceUpdatePayment(payment);

                    logger.info("payment " + payment.getId() + " already BOUNCED, start Bouncing flow");
                }
            }
        }

        bankTransaction.setResolved(true);
        bankStatementTransactionRepository.save(bankTransaction);

        logger.info("bankTransaction " + bankTransaction.getId() + " is saved and confirmed now.");
        return true;
    }

    public Payment processDirectDebitBouncedAgainPayment(Payment payment) {
        payment.setTrials(payment.getTrials() + 1);
        int bouncedMaxTrials = payment.getContract().isMaidVisa() ?
                Integer.parseInt(Setup.getParameter(
                        Setup.getCurrentModule(), AccountingModule.PARAMETER_BOUNCED_MAX_TRIALS_FOR_MV)) :
                Integer.parseInt(Setup.getParameter(
                        Setup.getCurrentModule(), AccountingModule.PARAMETER_BOUNCED_MAX_TRIALS_FOR_CC)); // acc-4603

        if (payment.getTrials() < bouncedMaxTrials) return payment;

        if (!payment.getContract().getStatus().equals(ContractStatus.ACTIVE)) return payment;
        payment.setContractScheduleDateOfTermination(
                paymentService.setContractForTermination(payment.getContract(), "Due bounced payment", payment));
        payment.setContractCancellationReason("Due bounced payment");
        return payment;
    }

    // ACC-2418
    public boolean checkSwitchingBankAccount(
            Payment payment,
            BankStatementTransaction bankTransaction) {

        int paymentTrials = payment.getTrials() + 1;
        Boolean isMV = payment.getContract().getContractProspectType().getCode().equals("maidvisa.ae_prospect");

        int bouncedMaxTrials = isMV ?
                Integer.parseInt(Setup.getParameter(
                        Setup.getCurrentModule(), AccountingModule.PARAMETER_BOUNCED_MAX_TRIALS_FOR_MV)) :
                Integer.parseInt(Setup.getParameter(
                        Setup.getCurrentModule(), AccountingModule.PARAMETER_BOUNCED_MAX_TRIALS_FOR_CC)); // acc-4603

        if (paymentTrials >= bouncedMaxTrials) {
            if (!switchingBankAccountService.isClientSwitchingBankAccount(payment) ||
                    !switchingBankAccountService.isSwitchingBankAccountDDsCoverPayment(payment)) {

                logger.info("Not Switching Bank Account");
                return false;
            }
        } else {
            if (!switchingBankAccountService.isClientSwitchingBankAccount(payment) ||
                    !switchingBankAccountService.isSwitchingBankAccountDDsCoverPaymentAndApproved(payment)) {

                logger.info("Not Switching Bank Account");
                return false;
            }
        }

        DirectDebit oldDD = payment.getDirectDebit();
        DirectDebit ddImage = switchingBankAccountService.getPaymentDDImage(payment);
        Payment paymentImage = switchingBankAccountService.getPaymentImage(payment);

        if (payment.getStatus() != PaymentStatus.BOUNCED) {
            //set the related payment as Bounced and then the posting engine will create the transactions as per the rules.
            logger.info("set the payment " + payment.getId() + " status as DELETED");

            payment.setStatus(PaymentStatus.DELETED);
            payment.setTrials(0);
            payment.setSentToBankByMDD(false);
            payment.setDateOfBouncing((new java.sql.Date(bankTransaction.getDate().getTime())));
            payment.setUpdatedFromBankStatement(true);
            payment.setBankStatmentTransactionId(bankTransaction.getId());

            payment.setReasonOfBouncingCheque(Setup.getItem("ReasonOfBouncedCheck", "insufficient_funds"));

            paymentService.forceUpdatePayment(payment);

            logger.info("make sure that the Payment Image#" + paymentImage.getId() + " isn't sent to bank so we can send it for collection");
            paymentImage.setSentToBankByMDD(false);
            paymentService.forceUpdatePayment(paymentImage);

            logger.log(Level.SEVERE, "#confirmDirectDebitTransaction, finish calling updatePaymentFromCmDirectCall for Payment " + payment.getId());
        } else {
            logger.log(Level.SEVERE, "Switching Bank Account");

            if (!payment.isReplaced()) { // bounced but not replaced yet
                payment.setTrials(paymentTrials);
                payment.setSentToBankByMDD(false);
                payment.setUpdatedFromBankStatement(true);
                payment.setBankStatmentTransactionId(bankTransaction.getId());

                if (payment.getDirectDebit().getCategory().equals(DirectDebitCategory.B)) {
                    payment.setDirectDebitId(ddImage.getId()); // link old payment with new DD
                }

                // link new payment with old DD
                if (paymentImage != null) {
                    if (payment.getDirectDebit().getCategory().equals(DirectDebitCategory.B)) {
                        paymentImage.setDirectDebitId(oldDD.getId());
                    } else {
                        paymentImage.copyRejectionAndBouncingPropsFrom(payment);
                        payment.setSentToBankByMDD(true);
                    }

                    paymentService.forceUpdatePayment(paymentImage);
                }

                payment = paymentService.forceUpdatePayment(payment);
            }
        }

        if (switchingBankAccountService.isSwitchingBankAccountDDsCoverPaymentAndApproved(payment)) {
            switchingBankAccountService.cancelDDOrShowHiddenCancellationToDos(oldDD);
        } else if (paymentTrials >= 3) {
            switchingBankAccountService.markNewDDFsAsForBouncingPayment(Arrays.asList(oldDD));
        }

        return true;
    }

    @Transactional
    public Boolean confirmChequeDepositTransaction(
            BankStatementTransaction bankTransaction,
            BankStatementRecord bankRecord) {

        logger.info("Enter confirmChequeDepositTransaction for transaction: " + bankTransaction.getId());

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(bankRecord.getDate());
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.DATE, -1);
        Date lastDayOfMonth = calendar.getTime();

        calendar.setTime(bankRecord.getDate());
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date firstDayOfMonth = calendar.getTime();

        SelectQuery<Payment> query = new SelectQuery<>(Payment.class);
        query.filterBy("dateOfPayment", ">=", firstDayOfMonth);
        query.filterBy("dateOfPayment", "<=", lastDayOfMonth);
        query.filterBy("amountOfPayment", "=", bankRecord.getCreditAmount());
        query.filterBy("chequeNumber", "=", bankRecord.getBankReferenceNo());
        query.filterBy("methodOfPayment", "=", PaymentMethod.CHEQUE);

        SelectFilter statusSelectFilter = new SelectFilter();
        statusSelectFilter.and("status", "=", PaymentStatus.PDC).
                or("status", "=", PaymentStatus.BOUNCED);

        query.filterBy(statusSelectFilter);

        List<Payment> payments = query.execute();
        if (payments.isEmpty()) {
            logger.info("no payments");
            return false;
        }

        logger.info("found " + payments.size() + " matches");

        switch(bankTransaction.getBankTransactionStatus()) {
            case RECEIVED:
                if (payments.get(0).getStatus() != PaymentStatus.RECEIVED && payments.get(0).getStatus() != PaymentStatus.BOUNCED) {
                    logger.log(Level.INFO, "#confirmChequeDepositTransaction, we will set the payment " + payments.get(0).getId() + " status as RECEIVED. ");

                    //set the related payment as received and then the posting engine will create the transactions as per the rules.
                    Payment p = payments.get(0);
                    p.setStatus(PaymentStatus.RECEIVED);
                    p.setUpdatedFromBankStatement(true);
                    p.setBankStatmentTransactionId(bankTransaction.getId());
                    paymentService.forceUpdatePayment(p);

                    logger.log(Level.INFO, "finish calling updatePaymentFromCmDirectCall for Payment " + payments.get(0).getId());
                }
                //set BankTransaction as resolved
                bankTransaction.setResolved(true);
                bankStatementTransactionRepository.save(bankTransaction);
                logger.log(Level.INFO, "bankTransaction " + bankTransaction.getId() + " is saved and confirmed  now.");
                return true;

            case BOUNCED:
                if (payments.get(0).getStatus() != PaymentStatus.BOUNCED) {
                    logger.log(Level.INFO, "set the payment " + payments.get(0).getId() + " status as BOUNCED. ");

                    //and set the related payment as Bounced and then the posting engine will create the transactions as per the rules.
                    Payment p =  payments.get(0);
                    p.setStatus(PaymentStatus.BOUNCED);
                    p.setDateOfBouncing(new java.sql.Date(bankTransaction.getDate().getTime()));
                    p.setUpdatedFromBankStatement(true);
                    p.setBankStatmentTransactionId(bankTransaction.getId());

                    p.setReasonOfBouncingCheque(bankTransaction.getBouncingReason());

                    paymentService.forceUpdatePayment(p);
                    logger.log(Level.INFO, "finish calling updatePaymentFromCmDirectCall for Payment " + payments.get(0).getId());
                }

                //set BankTransaction as resolved
                bankTransaction.setResolved(true);
                bankStatementTransactionRepository.save(bankTransaction);
                logger.log(Level.INFO, "bankTransaction " + bankTransaction.getId() + " is saved and confirmed  now.");
                return true;
        }

        logger.log(Level.INFO, "finished but didn't confirmed");
        return false;
    }

    @Transactional
    public Boolean confirmChequeDepositTransactionForLinkToPaymentAction(
            BankStatementTransaction bankTransaction) {

        Payment payment = bankTransaction.getPayment();

        if (payment == null) {
            logger.info("no payment");
            return false;
        }

        switch (bankTransaction.getBankTransactionStatus()) {
            case RECEIVED:
                if (payment.getStatus() != PaymentStatus.RECEIVED && payment.getStatus() != PaymentStatus.BOUNCED) {
                    logger.info("set the payment " + payment.getId() + " status as RECEIVED");
                    //set the related payment as received and then the posting engine will create the transactions as per the rules.

                    payment.setStatus(PaymentStatus.RECEIVED);
                    payment.setUpdatedFromBankStatement(true);
                    payment.setBankStatmentTransactionId(bankTransaction.getId());

                    paymentService.forceUpdatePayment(payment);
                    logger.info("finish calling updatePaymentFromCmDirectCall for Payment " + payment.getId());
                }

                bankTransaction.setResolved(true);
                bankStatementTransactionRepository.save(bankTransaction);
                logger.info("bankTransaction " + bankTransaction.getId() + " is saved and confirmed now.");
                return true;

            case BOUNCED:
                if (payment.getStatus() != PaymentStatus.BOUNCED) {
                    logger.info("set the payment " + payment.getId() + " status as BOUNCED");
                    //and set the related payment as Bounced and then the posting engine will create the transactions as per the rules.

                    payment.setStatus(PaymentStatus.BOUNCED);
                    payment.setDateOfBouncing(new java.sql.Date(bankTransaction.getDate().getTime()));
                    payment.setUpdatedFromBankStatement(true);
                    payment.setBankStatmentTransactionId(bankTransaction.getId());

                    payment.setReasonOfBouncingCheque(bankTransaction.getBouncingReason());

                    payment = paymentService.forceUpdatePayment(payment);
                    logger.info("finish calling updatePaymentFromCmDirectCall for Payment " + payment.getId());
                }

                bankTransaction.setResolved(true);
                bankStatementTransactionRepository.save(bankTransaction);

                logger.info("bankTransaction " + bankTransaction.getId() + " is saved and confirmed  now.");
                return true;

        }

        logger.info("finished but didn't confirmed");
        return false;
    }

    public void parseFile(Map<String, Object> payload) {
        BankStatementFile entity = bankStatementFileRepository.findOne(Long.parseLong(payload.get("entityId").toString()));
        logger.info("file id : " + entity.getId());

        processRecords(entity);
        entity.setResolved(true);

        bankStatementFileRepository.save(entity);
    }

    public void processRecords(BankStatementFile file) {

        processPayrollTransfersRecords(file);
        processNonDirectDebitTypeOfRecords(file);
        processDirectDebitRecords(file);

        logger.info("the processing of file finished!");
    }

    public void extractRecords(BankStatementFile file) {

        if (file.getAttachments() == null || file.getAttachments().isEmpty())
            throw new RuntimeException("Attachment file is required.");

        Attachment bankStatementAttachment = null;
        Attachment creditCardStatementAttachment = null;
        for (Attachment att : file.getAttachments()) {
            switch (att.getTag()) {
                case ("bankStatement"): {
                    bankStatementAttachment = att;
                    break;
                }
                case ("creditCardStatement"): {
                    creditCardStatementAttachment = att;
                    break;
                }
            }
        }
        //extractBankStatementRecords
        try {
            DataFormatter formatter = new DataFormatter();
            Workbook workbook = null;
            if (bankStatementAttachment != null && bankStatementAttachment.getExtension().equals("xlsx"))
                workbook = new XSSFWorkbook(Storage.getStream(bankStatementAttachment));
            else if (bankStatementAttachment != null && bankStatementAttachment.getExtension().equals("xls"))
                workbook = new HSSFWorkbook(Storage.getStream(bankStatementAttachment));

            List<BankStatementRecord> bankStatementRecords = new ArrayList<>();
            if (workbook != null) {
                NumberFormat nf_in = NumberFormat.getNumberInstance(Locale.ENGLISH);
                Sheet sheet = workbook.getSheetAt(0);
                Iterator<Row> rowIterator = sheet.iterator();
                while (rowIterator.hasNext()) {
                    try {
                        Row row = rowIterator.next();
                        String srNoVal = formatter.formatCellValue(row.getCell(0)).trim();
                        if (!StringUtils.isNumeric(srNoVal))
                            continue;

                        BankStatementRecord bankStatementRecord = new BankStatementRecord();
                        bankStatementRecord.setBankStatementFile(file);
                        bankStatementRecord.setSrNo(Integer.parseInt(srNoVal));
                        logger.log(Level.SEVERE, "BankStatementFile: " + file.getId() + " bankStatementRecord.setDate: " + row.getCell(1).getStringCellValue().trim());
                        bankStatementRecord.setDate(new Date(DateUtil.parseDateDashedV2(row.getCell(1).getStringCellValue().trim()).getTime()));
                        bankStatementRecord.setValueDate(new Date(DateUtil.parseDateDashedV2(row.getCell(2).getStringCellValue().trim()).getTime()));
                        bankStatementRecord.setBankReferenceNo(formatter.formatCellValue(row.getCell(3)).trim());
                        bankStatementRecord.setCustomerReferenceNo(formatter.formatCellValue(row.getCell(4)).trim());
                        bankStatementRecord.setDescription(formatter.formatCellValue(row.getCell(5)).trim());

                        bankStatementRecord.setDebitAmount(!formatter.formatCellValue(row.getCell(6)).trim().equals("-") ?
                                nf_in.parse(formatter.formatCellValue(row.getCell(6)).trim()).doubleValue() : 0.0);
                        bankStatementRecord.setCreditAmount(!formatter.formatCellValue(row.getCell(7)).trim().equals("-") ?
                                nf_in.parse(formatter.formatCellValue(row.getCell(7)).trim()).doubleValue() : 0.0);
                        bankStatementRecord.setRunningBalance(!formatter.formatCellValue(row.getCell(8)).trim().equals("-") ?
                                nf_in.parse(formatter.formatCellValue(row.getCell(8)).trim()).doubleValue() : 0.0);
                        bankStatementRecord.setSwiftRefNo(formatter.formatCellValue(row.getCell(9)).trim());
                        bankStatementRecord.setBeneficiaryInfo1(formatter.formatCellValue(row.getCell(10)).trim());
                        bankStatementRecord.setBeneficiaryInfo2(formatter.formatCellValue(row.getCell(11)).trim());
                        bankStatementRecord.setBeneficiaryInfo3(formatter.formatCellValue(row.getCell(12)).trim());
                        bankStatementRecord.setRemitterInfo1(formatter.formatCellValue(row.getCell(13)).trim());
                        bankStatementRecord.setRemitterInfo2(formatter.formatCellValue(row.getCell(14)).trim());
                        bankStatementRecord.setRemitterInfo3(formatter.formatCellValue(row.getCell(15)).trim());
                        bankStatementRecord.setPaymentDetail(formatter.formatCellValue(row.getCell(16)).trim());
                        bankStatementRecord.setUploadDate(!formatter.formatCellValue(row.getCell(17)).trim().equals("") ? new Date(DateUtil.parseDateDashedV2(row.getCell(17).getStringCellValue().trim()).getTime()) : null);
                        bankStatementRecord.setUploadFileName(formatter.formatCellValue(row.getCell(18)).trim());
                        bankStatementRecords.add(bankStatementRecord);
                    } catch (ParseException ex) {
                        logger.log(Level.SEVERE, "Parsing Exception: " + ex.getMessage());
                        ex.printStackTrace();
                    } catch (NumberFormatException ex) {
                        logger.log(Level.SEVERE, "Number Parsing Exception: " + ex.getMessage());
                        ex.printStackTrace();
                    } catch (Exception ex) {
                        logger.log(Level.SEVERE, "Exception: " + ex.getMessage());
                        ex.printStackTrace();
                    }
                }
            }
            if (bankStatementRecords != null && !bankStatementRecords.isEmpty()) {
                bankStatementRecordRepository.save(bankStatementRecords);
            }
        } catch (IOException ex) {
            logger.log(Level.SEVERE, "Exception: " + ex.getMessage());
            ex.printStackTrace();
        }


        //extractCreditCardRecords
        InputStream attachmentInputStream = null;
        try {
            if (creditCardStatementAttachment != null && creditCardStatementAttachment.getExtension().equals("csv")) {
                List<CreditCardStatementRecord> creditCardRecords = new ArrayList<>();
                NumberFormat nf_in = NumberFormat.getNumberInstance(Locale.ENGLISH);

                attachmentInputStream = Storage.getStream(creditCardStatementAttachment);
                CSVReader reader = new CSVReader(new InputStreamReader(attachmentInputStream));
                String[] nextLine;

                //skip the header of the file
                reader.readNext();

                while ((nextLine = reader.readNext()) != null) {
                    try {
                        CreditCardStatementRecord creditCardStatementRecord = new CreditCardStatementRecord();
                        creditCardStatementRecord.setBankStatementFile(file);
                        creditCardStatementRecord.setSrlNbr(nextLine[0].trim());
                        creditCardStatementRecord.setMerchantId(nextLine[1].trim());
                        creditCardStatementRecord.setChainId(nextLine[2].trim());
                        creditCardStatementRecord.setMerchantName(nextLine[3].trim());
                        creditCardStatementRecord.setLocation(nextLine[4].trim());
                        creditCardStatementRecord.setTelephon(nextLine[5].trim());
                        creditCardStatementRecord.setTerminalId(nextLine[6].trim());
                        creditCardStatementRecord.setSequenceNumber(nextLine[7].trim());
                        creditCardStatementRecord.setTransactionCurr(nextLine[8] != null ? nf_in.parse(nextLine[8].trim()).doubleValue() : 0.0);
                        creditCardStatementRecord.setTransactionDate(nextLine[9] != null && !nextLine[9].trim().equals("") ?
                                new SimpleDateFormat("MM/dd/yyyy").parse(nextLine[9].trim()) : null);
                        creditCardStatementRecord.setCardType(nextLine[10].trim());
                        creditCardStatementRecord.setCardNumber(nextLine[11].trim());
                        creditCardStatementRecord.setAuthCode(nextLine[12].trim());
                        creditCardStatementRecord.setTransactionType(nextLine[13].trim());
                        creditCardStatementRecord.setSalesAmount(nextLine[14] != null ? nf_in.parse(nextLine[14].trim()).doubleValue() : 0.0);
                        creditCardStatementRecord.setCommission(nextLine[15] != null ? nf_in.parse(nextLine[15].trim()).doubleValue() : 0.0);
                        creditCardStatementRecord.setNetAmount(nextLine[16] != null ? nf_in.parse(nextLine[16].trim()).doubleValue() : 0.0);
                        creditCardStatementRecord.setAcquirerData(nextLine[17].trim());
                        creditCardStatementRecord.setReferenceNbr(nextLine[18].trim());
                        creditCardStatementRecord.setTy(nextLine[19].trim());
                        creditCardStatementRecord.setBankAccount(nextLine[20].trim());
                        creditCardStatementRecord.setTransactionTime(nextLine[21].trim());
                        creditCardStatementRecord.setIbanAcctNbr(nextLine[22].trim());
                        creditCardStatementRecord.setDiscType(nextLine[23].trim());
                        creditCardStatementRecord.setMasterChainId1(nextLine[24].trim());
                        creditCardStatementRecord.setMasterChainId2(nextLine[25].trim());
                        creditCardStatementRecord.setTipAmount(nextLine[26] != null ? nf_in.parse(nextLine[26].trim()).doubleValue() : 0.0);
                        creditCardStatementRecord.setTagId(nextLine[27].trim());
                        creditCardStatementRecord.setDriverId(nextLine[28].trim());
                        creditCardStatementRecord.setPwcbCashBack(nextLine[29].trim());
                        creditCardStatementRecord.setTxnInd(nextLine[30].trim());
                        creditCardStatementRecord.setCommInd(nextLine[31].trim());
                        creditCardStatementRecord.setSource(nextLine[32].trim());
                        creditCardStatementRecord.setTransactionFee(nextLine[33] != null ? nf_in.parse(nextLine[33].trim()).doubleValue() : 0.0);
                        creditCardStatementRecord.setVatOnFee(nextLine[34] != null ? nf_in.parse(nextLine[34].trim()).doubleValue() : 0.0);
                        creditCardStatementRecord.setVatOnCommission(nextLine[35] != null ? nf_in.parse(nextLine[35].trim()).doubleValue() : 0.0);

                        creditCardRecords.add(creditCardStatementRecord);
                    } catch (ParseException ex) {
                        logger.log(Level.SEVERE, "Parsing Exception: " + ex.getMessage());
                        ex.printStackTrace();
                    } catch (Exception ex) {
                        logger.log(Level.SEVERE, "Exception: " + ex.getMessage());
                        ex.printStackTrace();
                    }
                }
                CreditCardStatementRecordRepository creditCardStatementRecordRepository = Setup.getRepository(CreditCardStatementRecordRepository.class);
                creditCardStatementRecordRepository.save(creditCardRecords);
            }
        } catch (IOException ex) {
            logger.log(Level.SEVERE, "Exception: " + ex.getMessage());
            ex.printStackTrace();
        } finally {
            StreamsUtil.closeStream(attachmentInputStream);
        }
    }

    public void processPayrollTransfersRecords(BankStatementFile file) {
        List<BankStatementRecord> bankRecords = bankStatementRecordRepository
                .findByBankStatementFileIdOrderByDateAscForPayroll(file.getId());
        if (bankRecords.isEmpty()) return;

        String uniqueId = "";
        for(BankStatementRecord bankRecord : bankRecords) {
            try {
                //make the uniqueId
                uniqueId = bankRecord.getSrNo() + bankRecord.getDate().toString() + bankRecord.getBankReferenceNo() + bankRecord.getCustomerReferenceNo() + bankRecord.getDebitAmount() + bankRecord.getCreditAmount() + bankRecord.getDescription() + bankRecord.getPaymentDetail();
                uniqueId = uniqueId.trim();

                //check if exists before.. skip this record (NO NEED TO DO ANYTHING)
                if (bankStatementTransactionRepository.existsByUniqueId(uniqueId)) {
                    logger.info("record id: " + bankRecord.getId() + "; uniqueId: " + uniqueId);
                    continue;
                }
                processPayrollTransferRecord(bankRecord, uniqueId, file);
                bankRecord.setProcessed(true);
            } catch (Exception ex) {
                logger.info("exception while processing the record: " + bankRecord.getId()
                        + ", Error is: " + ex.getMessage());
                ex.printStackTrace();
                bankRecord.setErrorDesc("exception while processing the record : " + ex.getMessage());
            }
            bankStatementRecordRepository.save(bankRecord);
        }
    }

    public void processNonDirectDebitTypeOfRecords(BankStatementFile file) {
        List<Long> expensesMatchedPaymentsIds = new ArrayList<>();

        List<BankStatementRecord> bankRecords = bankStatementRecordRepository
                .findByBankStatementFileIdOrderByDateAsc(file.getId());

        logger.info("bankRecords size: " + bankRecords.size());

        Set<Long> processedNoqodi = new HashSet<>();

        BankStatementTransaction bankStatementTransaction;
        String uniqueId = "";
        if (bankRecords != null && !bankRecords.isEmpty()) {
            for (BankStatementRecord bankRecord : bankRecords) {
                try {

                    bankRecord = bankStatementRecordRepository.findOne(bankRecord.getId());
                    if (bankRecord.getProcessed()) {
                        logger.info("record already processed id: " + bankRecord.getId());
                        continue;
                    }

                    //make the uniqueId
                    uniqueId = bankRecord.getSrNo() + bankRecord.getDate().toString() + bankRecord.getBankReferenceNo() + bankRecord.getCustomerReferenceNo() + bankRecord.getDebitAmount() + bankRecord.getCreditAmount() + bankRecord.getDescription() + bankRecord.getPaymentDetail();
                    uniqueId = uniqueId.trim();

                    //check if exists before.. skip this record (NO NEED TO DO ANYTHING)
                    if (bankStatementTransactionRepository.existsByUniqueId(uniqueId)) {
                        logger.info("record id: " + bankRecord.getId() + "; uniqueId: " + uniqueId);
                        continue;
                    }

                    bankStatementTransaction = new BankStatementTransaction();
                    bankStatementTransaction.setUniqueId(uniqueId);
                    bankStatementTransaction.setBankStatementRecord(bankRecord);
                    bankStatementTransaction.setDate(bankRecord.getDate());
                    bankStatementTransaction.setFile(file);

                    bankStatementTransaction.setPaymentDetail(bankRecord.getPaymentDetail());

                    //Noqodi
                    if(bankRecord.getPaymentDetail() != null && bankRecord.getPaymentDetail().toLowerCase().contains("noqodi")){
                        if(processNoqodi(bankRecord, bankStatementTransaction, processedNoqodi)) continue; //Done
                    }

                    //Cheque Deposit
                    if (bankRecord.getPaymentDetail() != null && bankRecord.getPaymentDetail().toLowerCase().contains("cheque deposit")) {
                        processChequeDeposit(bankRecord, bankStatementTransaction);
                        continue;//Done
                    }
                    //Cash Deposit
                    if (bankRecord.getCreditAmount() != null && bankRecord.getCreditAmount() > 0
                            && bankRecord.getDescription() != null && bankRecord.getDescription().toLowerCase().contains("cash deposit")) {
                        bankStatementTransaction.setBankTransactionType(BankTransactionType.CASH);
                        bankStatementTransaction.setTransactionAmount(bankRecord.getCreditAmount());
                        bankStatementTransaction.setDate(bankRecord.getDate());
                        bankStatementTransaction.setDescription(bankRecord.getDescription());
                        RevenueRepository revenueRepository = Setup.getRepository(RevenueRepository.class);
                        bankStatementTransaction.setRevenue(revenueRepository.findByCode("UWR 01"));//TODO: is the Revenue static here or not
                        bankStatementTransactionRepository.save(bankStatementTransaction);
                        continue;//Done
                    }
                    //Credit Card
                    if (bankRecord.getCreditAmount() != null && bankRecord.getCreditAmount() > 0
                            && bankRecord.getDescription() != null && bankRecord.getDescription().toLowerCase().contains("pos settlement")) {
                        continue;//no action should be done here
                    }
                    //PDC
                    if (bankRecord.getDescription() != null && bankRecord.getDescription().toLowerCase().startsWith("i/w")
                            && bankRecord.getDebitAmount() != null && bankRecord.getDebitAmount() > 0) {
                        processPDCPayment(bankRecord, bankStatementTransaction);
                        continue;//Done
                    }
                    //Client Refund
                    if (bankRecord.getDebitAmount() != null && bankRecord.getDebitAmount() > 0
                            && bankRecord.getPaymentDetail() != null && bankRecord.getPaymentDetail().toLowerCase().contains("refund")) {
                        //process the expenses record to extract the needed data
                        processClientRefundRecord(bankRecord, bankStatementTransaction, expensesMatchedPaymentsIds);
                        continue;//Done
                    }
                    //Expense Payment
                    if (bankRecord.getDebitAmount() != null && bankRecord.getDebitAmount() > 0
                            && bankRecord.getPaymentDetail() != null && bankRecord.getPaymentDetail().toLowerCase().matches("(.|\n|\r)*ep\\d+(.|\n|\r)*")
                            //process the expenses record to extract the needed data
                            && processExpensePaymentRecord(bankRecord, bankStatementTransaction)) {
                        continue;//Done
                    }
                    //Replenishment
                    if (bankRecord.getDebitAmount() != null && bankRecord.getDebitAmount() > 0
                            && bankRecord.getPaymentDetail() != null && bankRecord.getPaymentDetail().toLowerCase().matches("(.|\n|\r)*rpx\\d+(.|\n|\r)*")
                            //process the expenses record to extract the needed data
                            && processReplenishmentRecord(bankRecord, bankStatementTransaction)) {
                        continue;//Done
                    }
                    //Expenses
                    if (bankRecord.getDebitAmount() != null && bankRecord.getDebitAmount() > 0
                            && bankRecord.getPaymentDetail() != null && bankRecord.getPaymentDetail().toLowerCase().contains("expense")) {
                        //process the expenses record to extract the needed data
                        processExpensesRecord(bankRecord, bankStatementTransaction, expensesMatchedPaymentsIds);
                        continue;//Done
                    }
                    //Bucket Refill
                    if (bankRecord.getDebitAmount() != null && bankRecord.getDebitAmount() > 0
                            && bankRecord.getPaymentDetail() != null && bankRecord.getPaymentDetail().toLowerCase().startsWith("b2b")) {
                        //process the bucket refill record to extract the needed data
                        processBucketRefillRecord(bankRecord, bankStatementTransaction);
                        continue;//Done
                    }
                    //Unmatched Expenses
                    if (bankRecord.getDebitAmount() != null && bankRecord.getDebitAmount() > 0
                            && bankRecord.getPaymentDetail() != null && bankRecord.getPaymentDetail().toLowerCase().startsWith("none")) {
                        bankStatementTransaction.setBankTransactionType(BankTransactionType.UNMATCHED_EXPENSES);
                        bankStatementTransaction.setTransactionAmount(bankRecord.getDebitAmount());
                        bankStatementTransaction.setPaymentDetail(bankRecord.getPaymentDetail());
                        bankStatementTransactionRepository.save(bankStatementTransaction);
                        continue;//Done
                    }
                    //Ex Employer
                    if (bankRecord.getDebitAmount() != null && bankRecord.getDebitAmount() > 0
                            && bankRecord.getPaymentDetail() != null && bankRecord.getPaymentDetail().toLowerCase().contains("mt")) {
                        processExEmployerRecord(bankRecord, bankStatementTransaction);
                        continue;//Done
                    }
                    //Wire Transfer
                    if (bankRecord.getCreditAmount() != null && bankRecord.getCreditAmount() > 0) {
                        bankStatementTransaction.setBankTransactionType(BankTransactionType.WIRE_TRANSFER);
                        bankStatementTransaction.setTransactionAmount(bankRecord.getCreditAmount());
                        bankStatementTransaction.setDate(bankRecord.getDate());
                        bankStatementTransaction.setDescription(bankRecord.getDescription());
                        RevenueRepository revenueRepository = Setup.getRepository(RevenueRepository.class);
                        bankStatementTransaction.setRevenue(revenueRepository.findByCode("UWR 01"));//TODO: is the Revenue static here or not
                        bankStatementTransactionRepository.save(bankStatementTransaction);
                        continue;//Done
                    } else
                    //Undefined Transaction
                    {
                        bankStatementTransaction.setBankTransactionType(BankTransactionType.UNDEFINED_TRANSACTION);
                        bankStatementTransaction.setTransactionAmount(bankRecord.getCreditAmount() != 0 ? bankRecord.getCreditAmount() : bankRecord.getDebitAmount());
                        bankStatementTransaction.setPaymentDetail(bankRecord.getPaymentDetail());
                        bankStatementTransactionRepository.save(bankStatementTransaction);
                    }
                } catch (Exception ex) {
                    logger.log(Level.INFO, "exception while processing the record: " + bankRecord.getId()
                            + ", Error is: " + ex.getMessage());
                    ex.printStackTrace();
                    bankRecord.setErrorDesc("exception while processing the record");
                    bankStatementRecordRepository.save(bankRecord);
                }
            }
        }
        logger.info("the processing of Records Non Direct Debit Type finished!");
    }

    public void processDirectDebitRecords(BankStatementFile file) {

        List<BankStatementRecord> similarsSave = new ArrayList<>();
        List<Map<String, Object>> bankRecordsDirectDebit = processSimilarDirectDebitRecords(bankStatementRecordRepository
                .findAllBankRecordsOfDdByBankStatementFileId(file.getId()), similarsSave);

        logger.info("similarsSave size: " + similarsSave.size());
        if (!similarsSave.isEmpty()) {
            bankStatementRecordRepository.save(similarsSave);
        }
        logger.info("bankRecordsDirectDebit: " + bankRecordsDirectDebit);
        List<String> currentUniqueIds = new ArrayList<>();

        List<BankStatementRecord> bankStatementRecords = new ArrayList<>();
        Map<Long, List<Map<String, Object>>> bankRecordsDdWithDdf  = bankStatementRecordRepository
                .findAllBankRecordsOfDirectDebitByBankStatementFileId(bankRecordsDirectDebit
                        .stream()
                        .map(m -> {
                            BankStatementRecord r = ((BankStatementRecord) m.get("bankRecord"));
                            currentUniqueIds.add(getUniqueIdByRecord(r));
                            return r.getId();
                        })
                        .collect(Collectors.toList()))
                .stream()
                .collect(Collectors.groupingBy(m -> (Long) m.get("rId")));

        logger.info("bankRecordsDdWithDdf: " + bankRecordsDdWithDdf);

        // add ddf to bankStatementRecord
        for (Map<String, Object> map : bankRecordsDirectDebit) {
            BankStatementRecord bankRecord = (BankStatementRecord) map.get("bankRecord");
            DirectDebitFile ddf = null;
            if(!bankRecordsDdWithDdf.get(bankRecord.getId()).isEmpty()) {
                ddf = (DirectDebitFile) bankRecordsDdWithDdf.get(bankRecord.getId()).get(0).get("directDebitFile");
            }

            if (ddf!= null) {
                bankRecord.setDirectDebitFile(ddf);
                bankStatementRecords.add(bankRecord);
            }
        }

        // get payments and grouping by ddf id
        Map<Long, List<Payment>> paymentsByDdf = new HashMap<>();
        List<TransactionPostingRule> rules = new ArrayList<>();
        if (!bankStatementRecords.isEmpty()) {
            bankStatementRecordRepository.saveAllAndFlush(bankStatementRecords);
            logger.info("BankStatementRecord Ids: " + bankStatementRecords.stream().map(BankStatementRecord::getId).collect(Collectors.toList()));

            List<BigInteger> ids = paymentRepository.findMatchedPaymentForProcessDirectDebit(bankStatementRecords
                    .stream()
                    .map(BankStatementRecord::getId)
                    .collect(Collectors.toList()));

            if (!ids.isEmpty()) {
                List<Long> paymentIds = ids.stream()
                        .map(BigInteger::longValue)
                        .collect(Collectors.toList());
                logger.info("payment Ids: " + paymentIds);

                List<Payment> l = paymentRepository.findAll(paymentIds);

                paymentsByDdf = l.stream().collect(Collectors.groupingBy(Payment::getDirectDebitFileId));
                logger.info("paymentsByDdf: " + paymentsByDdf);

                // get All Usage TransactionPostingRules
                Set<String> types = l.stream().map(p -> p.getTypeOfPayment().getCode()).collect(Collectors.toSet());
                logger.info("Type Of Payments: " + types);

                rules = Setup.getRepository(TransactionPostingRuleRepository.class)
                        .findPostingRulesOfPaymentsDirectDebitAndReceivedByType(types);

                logger.info("Transaction Posting Rules Size: " + rules.size());
            }
        }

        List<String> uniqueIdsInDb = bankStatementTransactionRepository.findUniqueIdByUniqueIdIn(currentUniqueIds);
        List<String> uniqueIds = new ArrayList<>();
        List<BankStatementTransaction> transactions = new ArrayList<>();
        for (Map<String, Object> map : bankRecordsDirectDebit) {
            BankStatementRecord bankRecord = (BankStatementRecord) map.get("bankRecord");
            try {
                //check if exists before... skip this record (NO NEED TO DO ANYTHING)
                String uniqueId = getUniqueIdByRecord(bankRecord);
                if (uniqueIdsInDb.contains(uniqueId) || uniqueIds.contains(uniqueId)) {
                    logger.info("record id: " + bankRecord.getId() + "; uniqueId: " + uniqueId);
                    continue;
                }

                BankStatementTransaction bankStatementTransaction = new BankStatementTransaction();
                bankStatementTransaction.setUniqueId(uniqueId);
                bankStatementTransaction.setBankStatementRecord(bankRecord);
                bankStatementTransaction.setDate(bankRecord.getDate());
                bankStatementTransaction.setFile(file);
                bankStatementTransaction.setPaymentDetail(bankRecord.getPaymentDetail());

                //Direct Debit
                logger.info("record id: " + bankRecord.getId() +
                        "; uniqueNo " + (bankRecord.getUniqueNo() != null ? bankRecord.getUniqueNo() : "NULL") +
                        "; ddRefNo " + (bankRecord.getDDRefNo() != null ? bankRecord.getDDRefNo() : "NULL"));
                DirectDebitFile ddf = null;
                if(!bankRecordsDdWithDdf.get(bankRecord.getId()).isEmpty()) {
                    ddf = (DirectDebitFile) bankRecordsDdWithDdf.get(bankRecord.getId()).get(0).get("directDebitFile");
                }
                transactions.addAll(processDirectDebit(bankRecord, bankStatementTransaction, uniqueIds,
                        ddf, (ddf != null && paymentsByDdf.containsKey(ddf.getId()) ? paymentsByDdf.get(ddf.getId()) : null),
                        (boolean) map.get("isReceived"), rules));
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Exception when Direct Debit processing: " + e.getMessage());
                e.printStackTrace();
                bankRecord.setErrorDesc("Error while parsing this record.");
                bankStatementRecordRepository.save(bankRecord);
            }
        }

        logger.info("transactions size: " + transactions.size());
        if (!transactions.isEmpty()) {
            bankStatementTransactionRepository.save(transactions);
        }
        logger.info( "the processing of Records Direct Debit finished!");
    }

    private String getUniqueIdByRecord(BankStatementRecord r) {
        return (r.getSrNo() + r.getDate().toString() + r.getBankReferenceNo() +
                r.getCustomerReferenceNo() + r.getDebitAmount() + r.getCreditAmount() + r.getDescription() + r.getPaymentDetail()).trim();
    }

    public List<Map<String, Object>> processSimilarDirectDebitRecords(List<BankStatementRecord> bankRecordsDirectDebit, List<BankStatementRecord> similarsSave) {

        List<Long> similarsId = new ArrayList<>();
        List<Map<String, Object>> result = new ArrayList<>();

        logger.info("bankRecordsDirectDebit size: " + bankRecordsDirectDebit.size());

        for(BankStatementRecord bankRecord : bankRecordsDirectDebit) {
            try {
                if(similarsId.contains(bankRecord.getId())) continue;

                //check if this is the second appear then continue
                if (repeatedDirectDebit(bankRecord)) {
                    logger.info("record id: " + bankRecord.getId() + "; repeatedDirectDebit true");
                    continue;//skip
                }

                Map<String, Object> m = new HashMap<>();
                m.put("bankRecord", bankRecord);

                String uniqueNo = bankRecord.getUniqueNo();
                String dDRefNo = bankRecord.getDDRefNo();
                logger.info("record id: " + bankRecord.getId() + "; date: " + bankRecord.getDate() +
                        "; UniqueNo: " + uniqueNo + "; DDRefNo: " + dDRefNo);

                List<BankStatementRecord> similars = bankRecordsDirectDebit.stream()
                        .filter(r ->
                                !r.getId().equals(bankRecord.getId()) && !r.getProcessed() && r.getDate().equals(bankRecord.getDate()) &&
                                        r.getPaymentDetail().startsWith(uniqueNo) &&
                                        repeatedDirectDebit(r) &&                    // description ex: (****************_P_AM0001-1020987) => check this case '_P_'
                                        r.getDescription().startsWith(uniqueNo) &&   // => check this case '****************'
                                        r.getDescription().endsWith(dDRefNo))        // => check this case 'AM0001-1020987'
                        .collect(Collectors.toList());

                logger.info("similars size: " + similars.size());

                if (similars.isEmpty()) {
                    m.put("isReceived", true);
                } else {
                    m.put("isReceived", false);
                    for (BankStatementRecord similar : similars) {
                        logger.info("similar id: " + similar.getId() +
                                "; PaymentDetail: " + similar.getPaymentDetail() +
                                "; Description: " + similar.getDescription());
                        similar.setProcessed(true);
                    }
                    similarsSave.addAll(similars);
                    similarsId.addAll(similars.stream().map(BankStatementRecord::getId).collect(Collectors.toList()));
                }

                result.add(m);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    private boolean repeatedDirectDebit(BankStatementRecord record) {
        String paymentDetail = record.getPaymentDetail();
        return (paymentDetail.indexOf("_") == 16 &&
                Character.isLetter(paymentDetail.charAt(17)) &&
                paymentDetail.lastIndexOf("_") == 18) &&
                paymentDetail.substring(0, 16).chars().allMatch(Character::isDigit);

    }

    public List<BankStatementTransaction> processDirectDebit(
            BankStatementRecord bankRecord, BankStatementTransaction bankStatementTransaction,
            List<String> uniqueIds, DirectDebitFile debitFile,
            List<Payment> paymentsByDdf, boolean isReceived,
            List<TransactionPostingRule> rules) {
        logger.info("record id: " + bankRecord.getId() + "; isReceived: " + isReceived + "; debitFile id: " + (debitFile != null ? debitFile.getId() : null));
        List<BankStatementTransaction> transactions = new ArrayList<>();
        bankStatementTransaction.setBankTransactionType(BankTransactionType.DIRECT_DEBIT);

        if (debitFile != null) {
            bankStatementTransaction.setDirectDebitFile(debitFile);
            bankStatementTransaction.setContract(debitFile.getDirectDebit().getContractPaymentTerm().getContract());

            List<Payment> payments = getMatchedPayment(debitFile.getDirectDebit().getType(), paymentsByDdf, bankRecord.getCreditAmount(), bankRecord.getDate());
            logger.info("Matched payments size : " + (payments != null ? payments.size() : null));

            if (payments != null) {
                Payment payment = payments.get(0);
                payments.remove(payment);
                bankStatementTransaction.setPayment(payment);
                bankStatementTransaction.setBankTransactionMatchType(BankTransactionMatchType.Automatic);
                bankStatementTransaction.setPaymentStatus(payment.getStatus());
                bankStatementTransaction.setContract(payment.getContract());
                bankStatementTransaction.setClient(payment.getContract().getClient().getName());

                if (isReceived) {
                    bankStatementTransaction.setTransactionAmount(payment.getAmountOfPayment());
                    bankStatementTransaction.setDate(bankRecord.getDate());
                    bankStatementTransaction.setVatAmount(payment.getVat());
                    bankStatementTransaction.setVatType(VatType.OUT);

                    bankStatementTransaction.setRevenue(getMatchedRevenueByRulesAndPayment(payment, rules));
                    bankStatementTransaction.setBankTransactionStatus(BankTransactionStatus.RECEIVED);

                    bankStatementTransaction.setDescription("Client-" + payment.getContract().getClient().getName() + " / Payment-" + payment.getId()
                            + " / Contract-" + payment.getContract().getId() + " / Date of payment-" + payment.getDateOfPayment());

                } else {
                    bankStatementTransaction.setBouncedPaymentId(payment.getId());
                    bankStatementTransaction.setPaymentDetail(bankRecord.getPaymentDetail());
                    bankStatementTransaction.setTransactionAmount(payment.getAmountOfPayment());
                    bankStatementTransaction.setDate(bankRecord.getDate());
                    bankStatementTransaction.setBankTransactionStatus(BankTransactionStatus.BOUNCED);
                }

                for (Payment p: payments) {
                    logger.log(Level.SEVERE, "clone transaction for payment id : {0}", payment.getId());
                    BankStatementTransaction cloneBankStatementTransaction = cloneBankStatementTransaction(bankStatementTransaction, p, isReceived, rules);
                    transactions.add(cloneBankStatementTransaction);
                }
            } else {
                bankStatementTransaction.setReason("Payment Not Found");
                bankStatementTransaction.setDate(bankRecord.getDate());
                bankStatementTransaction.setTransactionAmount(bankRecord.getCreditAmount());
                bankStatementTransaction.setPaymentDetail(bankRecord.getPaymentDetail());

                if (isReceived) {
                    bankStatementTransaction.setBankTransactionStatus(BankTransactionStatus.UNMATCHED_RECEIVED_TRANSACTION_NOT_FOUND);
                    bankStatementTransaction.setDescription("Client-" + debitFile.getDirectDebit().getContractPaymentTerm().getContract().getClient().getName() + " / Contract-" + debitFile.getDirectDebit().getContractPaymentTerm().getContract().getId());
                } else {
                    bankStatementTransaction.setBankTransactionStatus(BankTransactionStatus.UNMATCHED_BOUNCED_TRANSACTION_NOT_FOUND);
                }
            }
        } else {
            bankStatementTransaction.setReason("Contract Not Found");
            bankStatementTransaction.setDate(bankRecord.getDate());
            bankStatementTransaction.setTransactionAmount(bankRecord.getCreditAmount());
            bankStatementTransaction.setPaymentDetail(bankRecord.getPaymentDetail());
            if (isReceived) {
                bankStatementTransaction.setBankTransactionStatus(BankTransactionStatus.UNMATCHED_RECEIVED_TRANSACTION_NOT_FOUND);
            } else {
                bankStatementTransaction.setBankTransactionStatus(BankTransactionStatus.UNMATCHED_BOUNCED_TRANSACTION_NOT_FOUND);
            }

        }
        uniqueIds.add(bankStatementTransaction.getUniqueId());
        transactions.add(bankStatementTransaction);
        return transactions;
    }

    private Revenue getMatchedRevenueByRulesAndPayment(Payment p, List<TransactionPostingRule> rules) {

        if(rules == null || rules.isEmpty() || p == null) return null;

        List<TransactionPostingRule> transactionPostingRules = rules.stream()
                .filter(r -> r.getCompany().equals(p.getContract().getContractProspectType()) &&
                                r.getTypeOfPayment().equals(p.getTypeOfPayment()) &&
                                p.getIsInitial().equals(r.isInitialPayment()))
                .collect(Collectors.toList());

        return transactionPostingRules.size() == 1 ? transactionPostingRules.get(0).getRevenue() : null;
    }

    private List<Payment> getMatchedPayment
            (DirectDebitType directDebitType, List<Payment> payments,
             Double creditAmount, Date date) {

        if (payments == null || payments.isEmpty()) return null;

        LocalDate bankRecordDate = new LocalDate(date);
        LocalDate bankRecordDateMinusOneMonth = new LocalDate(date).minusMonths(1);

        List<Payment> matchedPayments = payments.stream().filter(
                p -> {
                    LocalDate paymentDate = new LocalDate(p.getDateOfPayment());
                    // ACC-1989
                    // if one time dd then monthly
                    return (!directDebitType.equals(DirectDebitType.ONE_TIME) && PaymentHelper.isMonthlyPayment(p.getTypeOfPayment())) &&
                            p.getAmountOfPayment().equals(creditAmount) &&
                            ((paymentDate.getYear() == bankRecordDateMinusOneMonth.getYear() && paymentDate.getMonthOfYear() == bankRecordDateMinusOneMonth.getMonthOfYear())
                                    || (paymentDate.getYear() == bankRecordDate.getYear() && paymentDate.getMonthOfYear() == bankRecordDate.getMonthOfYear()));
                }).collect(Collectors.toList());

        if (matchedPayments.isEmpty()) {
            matchedPayments = payments.stream().filter(
                    p -> {
                        LocalDate paymentDate = new LocalDate(p.getDateOfPayment());
                        return (directDebitType.equals(DirectDebitType.ONE_TIME) && !p.isReplaced() &&
                                ((paymentDate.getYear() == bankRecordDateMinusOneMonth.getYear() && paymentDate.getMonthOfYear() == bankRecordDateMinusOneMonth.getMonthOfYear())
                                        || (paymentDate.getYear() == bankRecordDate.getYear() && paymentDate.getMonthOfYear() == bankRecordDate.getMonthOfYear())));
                    }).collect(Collectors.toList());
            if (!matchedPayments.isEmpty() && matchedPayments.stream().mapToDouble(Payment::getAmountOfPayment).sum() == creditAmount) {
                logger.info("bank statement file, matchedPayments size: " + matchedPayments.size());
                for (Payment payment: matchedPayments) {
                    logger.info("bank statement file, dda paymentToReturn id : " + payment.getId());
                }
                return matchedPayments;
            } else return null;
        }

        Payment paymentToReturn = matchedPayments.size() == 1 ? matchedPayments.get(0) : null;

        if (matchedPayments.size() > 1) {

            Contract c = matchedPayments.get(0).getContract();

            paymentToReturn = (c != null && c.getStatus().equals(ContractStatus.ACTIVE) &&
                    matchedPayments.get(0).getStatus() == PaymentStatus.BOUNCED &&
                    matchedPayments.get(1).getStatus() == PaymentStatus.PDC) ? matchedPayments.get(1)
                    : ((matchedPayments.get(0).getStatus() == PaymentStatus.BOUNCED && (matchedPayments.get(0).getReplaced() != null && matchedPayments.get(0).getReplaced()))
                    ? matchedPayments.get(1) : matchedPayments.get(0));

        }

        logger.log(Level.SEVERE, "bank statement file, matchedPayments size: " + matchedPayments.size());

        if (paymentToReturn != null) {
            logger.info( "bank statement file, paymentToReturn: " + paymentToReturn.getId());
            return new ArrayList<>(Collections.singletonList(paymentToReturn));
        }
        return null;
    }

    public BankStatementTransaction cloneBankStatementTransaction(BankStatementTransaction old, Payment p, boolean similar, List<TransactionPostingRule> rules) {
        BankStatementTransaction bankStatementTransaction = new BankStatementTransaction();
        bankStatementTransaction.setUniqueId(old.getUniqueId());
        bankStatementTransaction.setBankStatementRecord(old.getBankStatementRecord());
        bankStatementTransaction.setDate(old.getDate());
        bankStatementTransaction.setFile(old.getFile());
        bankStatementTransaction.setPaymentDetail(old.getPaymentDetail());
        bankStatementTransaction.setDirectDebitFile(old.getDirectDebitFile());
        bankStatementTransaction.setContract(old.getContract());
        bankStatementTransaction.setBankTransactionType(old.getBankTransactionType());
        bankStatementTransaction.setPayment(p);
        bankStatementTransaction.setBankTransactionMatchType(old.getBankTransactionMatchType());
        bankStatementTransaction.setPaymentStatus(p.getStatus());
        bankStatementTransaction.setContract(p.getContract());
        bankStatementTransaction.setClient(p.getContract().getClient().getName());
        bankStatementTransaction.setTransactionAmount(p.getAmountOfPayment());

        if (similar) {
            bankStatementTransaction.setDate(old.getDate());
            bankStatementTransaction.setVatAmount(p.getVat());
            bankStatementTransaction.setVatType(VatType.OUT);
            bankStatementTransaction.setRevenue(getMatchedRevenueByRulesAndPayment(p, rules));
            bankStatementTransaction.setBankTransactionStatus(BankTransactionStatus.RECEIVED);

            bankStatementTransaction.setDescription("Client-" + p.getContract().getClient().getName() + " / Payment-" + p.getId()
                    + " / Contract-" + p.getContract().getId() + " / Date of payment-" + p.getDateOfPayment());
        } else {
            bankStatementTransaction.setBouncedPaymentId(p.getId());
            bankStatementTransaction.setPaymentDetail(old.getPaymentDetail());
            bankStatementTransaction.setDate(old.getDate());
            bankStatementTransaction.setBankTransactionStatus(BankTransactionStatus.BOUNCED);
        }

        return bankStatementTransaction;
    }

    // ACC-4624
    private Boolean processNoqodi(
            BankStatementRecord bankRecord,
            BankStatementTransaction bankStatementTransaction,
            Set<Long> processedNoqodi){

        Integer parameterOffsetForValidExpenseRequest = Integer.parseInt(Setup.getParameter(
                Setup.getCurrentModule(), AccountingModule.PARAMETER_OFFSET_FOR_VALID_EXPENSE_REQUEST));

        try{
            Date bankRecordDate = bankRecord.getDate();
            Date beforeDate = DateUtil.getPreviousDays(bankRecordDate, parameterOffsetForValidExpenseRequest);
            Date afterDate = DateUtil.addDays(bankRecordDate, parameterOffsetForValidExpenseRequest);

            SelectQuery<ExpensePayment> query = new SelectQuery<>(ExpensePayment.class);
            query.filterBy("amount", "=" , bankRecord.getDebitAmount());
            query.filterBy("paymentDate", "<=", afterDate);
            query.filterBy("paymentDate", ">=", beforeDate);
            query.filterBy("toBucket", "is not null", null);
            query.filterBy("confirmed", "=", Boolean.FALSE);

            if(!processedNoqodi.isEmpty()) {
                query.filterBy("id", "not in ", processedNoqodi);
            }

            List<ExpensePayment> expensePaymentsList = query.execute();
            logger.log(Level.INFO, "Noqodi Replenishment size: " + expensePaymentsList.size());

            if (expensePaymentsList.isEmpty()) return false;

            ExpensePayment expensePayment = null;
            try {
                expensePayment = expensePaymentsList.stream().filter(
                        e -> e.getToBucket().getIsWallet()
                ).findFirst().get();
            }catch (NoSuchElementException ex){
                logger.info("cannot find an expensePayment with bucket as a wallet!");
                ex.printStackTrace();
                return false;
            }

            logger.log(Level.INFO, "Found Noqodi: " + expensePayment.getId());

            bankStatementTransaction.setExpense(expensePayment.getExpenseToPost());
            bankStatementTransaction.setExpensePayment(expensePayment);
            bankStatementTransaction.setTransactionAmount(bankRecord.getDebitAmount());
            bankStatementTransaction.setDate(bankRecord.getDate());
            bankStatementTransaction.setBankTransactionStatus(BankTransactionStatus.MATCHED);
            bankStatementTransaction.setBankTransactionType(BankTransactionType.REPLENISHMENT_REQUEST);
            bankStatementTransaction.setToBucket(expensePayment.getToBucket().getCode());
            bankStatementTransaction.setFromBucket(expensePayment.getFromBucket().getCode());
            bankStatementTransaction.setDescription(expensePayment.getDescription());
            bankStatementTransactionRepository.save(bankStatementTransaction);

            bankRecord.setProcessed(true);
            bankStatementRecordRepository.save(bankRecord);

            processedNoqodi.add(expensePayment.getId());
            return true;
        } catch (Exception ex){
            logger.log(Level.SEVERE, "Exception when Noqodi processing: " + ex.getMessage());
            ex.printStackTrace();

            bankRecord.setErrorDesc("Error while parsing this record.");
            bankStatementRecordRepository.save(bankRecord);
            return false;
        }
    }

    // ACC-3011
    private void processClientRefundRecord(BankStatementRecord bankRecord, BankStatementTransaction bankStatementTransaction, List<Long> expensesMatchedPaymentsIds) {
        List<String> lines = Arrays.asList(bankRecord.getPaymentDetail().split("[\\r\\n]+"));

        if (lines == null || lines.size() != 3) {
            bankRecord.setErrorDesc("Error while parsing this record: " +
                    "we must have 3 lines here");
            bankStatementRecordRepository.save(bankRecord);
            return;
        }

        ClientRefundTodoRepository clientRefundTodoRepository = Setup.getRepository(ClientRefundTodoRepository.class);
        bankStatementTransaction.setBankTransactionType(BankTransactionType.EXPENSES);

        try {
            String description = lines.get(1);
            bankStatementTransaction.setDescription(description);
            bankStatementTransaction.setTransactionAmount(bankRecord.getDebitAmount());

            Long clientRefundId = Long.parseLong(description.toLowerCase().split("rx")[1].trim());
            ClientRefundToDo clientRefundToDo = clientRefundTodoRepository.findOne(clientRefundId);

            if (clientRefundToDo == null) {
                bankStatementTransaction.setBankTransactionStatus(BankTransactionStatus.UNMATCHED_REFUND_REQUEST_NOT_FOUND);
                bankStatementTransaction.setReason("Refund Request Not Found");
            } else {
                Contract contract = clientRefundToDo.getContract();
                bankStatementTransaction.setContract(contract);
                bankStatementTransaction.setClientRefundToDo(clientRefundToDo);

                expenseRecordsSetMatchedPayment(contract, bankStatementTransaction, null, expensesMatchedPaymentsIds);
            }

            bankStatementTransactionRepository.save(bankStatementTransaction);
        } catch (Exception ex) {
            bankRecord.setErrorDesc("Error while processing this record.");
            ex.printStackTrace();
            bankStatementRecordRepository.save(bankRecord);

            logger.severe("Exception while processing Client Refund Record '" + bankRecord.getId() + "'  error: " + ExceptionUtils.getStackTrace(ex));
        }
    }

    //ACC-3915
    private boolean processExEmployerRecord(BankStatementRecord bankRecord, BankStatementTransaction bankStatementTransaction) {
        logger.log(Level.INFO, "bankRecord id : {0}; bankStatementTransaction id : {1}", new Object[] {bankRecord.getId(), bankStatementTransaction.getId()});
        bankStatementTransaction.setTransactionAmount(bankRecord.getDebitAmount());
        bankStatementTransaction.setBankTransactionType(BankTransactionType.MAIDS_AT_EXPENSE);
        bankStatementTransaction.setPaymentDetail(bankRecord.getPaymentDetail());
        bankStatementTransaction.setDescription(bankRecord.getDescription());

        // ACC-5421
        Expense e = Setup.getRepository(ExpenseRepository.class).findOneByCode("SP 03");
        bankStatementTransaction.setExpense(e);
        bankStatementTransaction.setExpenseCode(e.getName());

        Pattern pattern = Pattern.compile("[m][t][0-9]+");
        Matcher matcher = pattern.matcher(bankRecord.getPaymentDetail().toLowerCase());
        if (!matcher.find()) return  false;
        logger.log(Level.INFO, "matched id : {0}", matcher.group());

        SendRequestForApprovalAction sendRequestForApprovalAction = Setup.getRepository(
                        SendRequestForApprovalActionRepository.class)
                .findOne(Long.valueOf(matcher.group().replace("mt", "")));
        if (sendRequestForApprovalAction == null) {
            bankStatementTransaction.setBankTransactionStatus(BankTransactionStatus.UNMATCHED_MAIDS_AT_EXPENSE);
            bankStatementTransaction.setReason("Send Request For Approval Action Not Found");
            bankStatementTransactionRepository.save(bankStatementTransaction);
            return  false;
        }
        logger.log(Level.INFO, "sendRequestForApprovalAction id : {0}", sendRequestForApprovalAction.getId());

        PayrollAccountantTodo payrollAccountantTodo = Setup.getRepository(PayrollAccountantTodoRepository.class)
                .findFirstBySendRequestForApprovalAction(sendRequestForApprovalAction);
        if (payrollAccountantTodo == null) {
            bankStatementTransaction.setBankTransactionStatus(BankTransactionStatus.UNMATCHED_MAIDS_AT_EXPENSE);
            bankStatementTransaction.setReason("Payroll Accountant Todo Not Found");
            bankStatementTransactionRepository.save(bankStatementTransaction);
            return  false;
        }

        logger.log(Level.INFO, "payrollAccountantTodo id : {0}", payrollAccountantTodo.getId());
        bankStatementTransaction.setDescription(payrollAccountantTodo.getDescription());
        bankStatementTransaction.setBankTransactionStatus(BankTransactionStatus.MATCHED);
        bankStatementTransaction.setSendRequestForApprovalAction(sendRequestForApprovalAction);
        bankStatementTransactionRepository.save(bankStatementTransaction);

        return true;
    }

    // ACC-9056
    private void processPayrollTransferRecord(BankStatementRecord bankRecord, String uniqueId, BankStatementFile file) {
        logger.info("bankRecord id : " + bankRecord.getId() + "; file id : " + file.getId() +
                ", payment details : " + bankRecord.getPaymentDetail());
        BankStatementTransaction bankStatementTransaction = new BankStatementTransaction();
        bankStatementTransaction.setUniqueId(uniqueId);
        bankStatementTransaction.setFile(file);
        bankStatementTransaction.setBankStatementRecord(bankRecord);
        bankStatementTransaction.setDate(new Date());
        bankStatementTransaction.setPaymentDetail(bankRecord.getPaymentDetail());
        bankStatementTransaction.setTransactionAmount(bankRecord.getDebitAmount());
        bankStatementTransaction.setBankTransactionType(BankTransactionType.PAYROLL_TRANSFER);
        // Example of paymentDetail : SAL00000631-@PnlCode@
        String[] splitArray = new String[0];
        String expenseCode = null;
        if (bankStatementTransaction.getPaymentDetail() != null && !bankStatementTransaction.getPaymentDetail().isEmpty()) {

            splitArray = bankStatementTransaction.getPaymentDetail().split("-");
            if (splitArray.length >= 2) {
                expenseCode = splitArray[splitArray.length - 1];
            }
        }

        // Match SAL(X) to log
        List<OfficeStaffPayrollLog> logs = splitArray.length >=1 ?
                officeStaffPayrollLogRepository.findByBankTransferUniqueCodeStartingWith(splitArray[0]) : null;
        PayrollAccountantTodo todo = logs != null && !logs.isEmpty() ? logs.get(0).getPayrollAccountantTodo() : null;

        // Match SAL(X) to Transaction
        Transaction existTransaction = splitArray.length >=1 ?
                transactionRepository.findFirstByPayrollUniqueCodeStartingWith(splitArray[0]) : null;

        Expense e = expenseCode != null ? expenseRepository.findByCodeAndDeletedFalse(expenseCode): null;

        String bucketCode = Setup.getParameter(Setup.getCurrentModule(), PARAMETER_STATEMENTS_TRANSACTIONS_FROM_BUCKET);

        //ALREADY_MATCHED
        if(existTransaction != null) {
            bankStatementTransaction.setTransaction(existTransaction);
            bankStatementTransaction.setBankTransactionStatus(BankTransactionStatus.MATCHED);
            bankStatementTransaction.setResolved(true);
            bankStatementTransaction.setBankTransactionMatchType(BankTransactionMatchType.Automatic);
            if (e != null) {
                bankStatementTransaction.setExpense(e);
                bankStatementTransaction.setExpenseCode(e.getName());
            }
            bankStatementTransaction.setFromBucket(bucketRepository.findByCode(bucketCode).getName());
            fillInfoOnTransactionFromPayrollLogs(bankStatementTransaction, logs);
        }
        //MATCHED
        else if (todo != null && (todo.isStopped() || todo.isCompleted())) {
            // if pnl code is missing then it should not be matched, should be in unmatched grid
            if (e == null) {
                fillUnMatchedPayrollRecordInfo(bankStatementTransaction, PayrollTransferUnmatchedReason.PNL_CODE_IS_MISSING.getLabel(),
                        bankRecord.getBeneficiaryInfo1());
            } else {
                bankStatementTransaction.setBankTransactionStatus(BankTransactionStatus.MATCHED);
                bankStatementTransaction.setExpense(e);
                bankStatementTransaction.setExpenseCode(e.getName());
                bankStatementTransaction.setFromBucket(bucketRepository.findByCode(bucketCode).getName());

                fillInfoOnTransactionFromPayrollLogs(bankStatementTransaction, logs);
            }
        }
        //UNMATCHED_PAYROLL_EXPENSE
        else {
            // Priority for To-Do check
            // On close the To-Do and the pnl code is missing ==> the reason should be updated to "PNL Code is Missing"

            fillUnMatchedPayrollRecordInfo(bankStatementTransaction, todo != null && !todo.isStopped() && !todo.isCompleted() ?
                            PayrollTransferUnmatchedReason.ACCOUNTANT_TODO_IS_OPEN.getLabel() :
                            PayrollTransferUnmatchedReason.NO_SALARY_PAYMENT_RECORD.getLabel(),
                    bankRecord.getBeneficiaryInfo1());
        }

        bankStatementTransactionRepository.save(bankStatementTransaction);
    }

    //Jirra ACC-3049
    private boolean processExpensePaymentRecord(BankStatementRecord bankRecord, BankStatementTransaction bankStatementTransaction) {
        ExpensePaymentRepository expensePaymentRepo = Setup.getRepository(ExpensePaymentRepository.class);
        PayrollAccountantTodoRepository payrollAccountantTodoRepository = Setup.getRepository(PayrollAccountantTodoRepository.class);
        try {
            Long expensePaymentId = Long.parseLong(bankRecord.getPaymentDetail().toLowerCase().split("ep")[1].split("(\n|\r|\\s)")[0].trim());
            bankStatementTransaction.setBankTransactionType(BankTransactionType.EXPENSE_REQUEST);

            ExpensePayment expensePayment = expensePaymentRepo.findOne(expensePaymentId);

            if (!validateExpensePayment(expensePayment, bankRecord) ||
                    BooleanUtils.toBoolean(expensePayment.getConfirmed())) {
                logger.info("processExpensePaymentRecord Not Matched");
                return false;
            }

            PayrollAccountantTodo payrollAccountantTodo = payrollAccountantTodoRepository.findFirstByExpensePayment(expensePayment);
            if (payrollAccountantTodo == null) {
                logger.info("processExpensePaymentRecord Payroll Accountant Todo Not Found -> Not Matched");
                return false;
            }

            logger.info("processExpensePaymentRecord Matched");
            bankStatementTransaction.setBankTransactionStatus(BankTransactionStatus.MATCHED);
            bankStatementTransaction.setExpensePayment(expensePayment);

            CurrencyExchangeSevice currencyExchangeSevice = Setup.getApplicationContext().getBean(CurrencyExchangeSevice.class);

            if (!expensePayment.getCurrency().getId().equals(currencyExchangeSevice.getLocalCurrency().getId())) {
                bankStatementTransaction.setTransactionAmount(bankRecord.getDebitAmount());
            } else {
                bankStatementTransaction.setTransactionAmount(payrollAccountantTodo.getTotal());
            }

            bankStatementTransaction.setDescription(expensePayment.getDescription());
            //Jirra ACC-3372#8
            if (expensePayment.getMethod().equals(ExpensePaymentMethod.MONEY_TRANSFER)) {
                Bucket bankBucket = Setup.getRepository(BucketRepository.class).findFirstByBucketType(BucketType.BANK_ACCOUNT);
                if (bankBucket == null) throw new RuntimeException("No Bank Bucket Found");
                bankStatementTransaction.setFromBucket(bankBucket.getCode());
            } else {
                bankStatementTransaction.setFromBucket(expensePayment.getFromBucket().getCode());
            }

            bankStatementTransaction.setToBucket(null);

            Expense expense = expensePayment.getExpenseToPost() != null ? expensePayment.getExpenseToPost() :
                    (expensePayment.getExpenseRequestTodos() != null && !expensePayment.getExpenseRequestTodos().isEmpty() ?
                            expensePayment.getExpenseRequestTodos().get(0).getExpense() : null);

            bankStatementTransaction.setExpense(expense);
            bankStatementTransaction.setExpenseCode(expense != null ? expense.getCode() : null);

            bankStatementTransaction.setLicense(PicklistHelper.getItemNoException(PICKLIST_TRANSACTION_LICENSE,
                    (expensePayment.getVatAmount() != null && !expensePayment.getVatAmount().equals(0D)) ? PICKLIST_TRANSACTION_LICENSE_MUSTAQEEM_ITEM : PICKLIST_TRANSACTION_LICENSE_NO_VAT_ITEM));

            bankStatementTransaction.setVatType((expensePayment.getVatAmount() != null && !expensePayment.getVatAmount().equals(0D)) ? VatType.IN : null);
            bankStatementTransaction.setVatAmount(expensePayment.getVatAmount());
            bankStatementTransaction.setDate(new Date());

            bankStatementTransactionRepository.save(bankStatementTransaction);
            return true;
        } catch (Exception ex) {
            bankRecord.setErrorDesc("Error while processing this record.");
            bankStatementRecordRepository.save(bankRecord);

            logger.severe("Exception while processing Expense Payment Record'" + bankRecord.getId() + "'  error: " + ExceptionUtils.getStackTrace(ex));
        }

        return false;
    }

    private boolean validateExpensePayment(
            ExpensePayment expensePayment,
            BankStatementRecord bankStatementRecord) {

        if (expensePayment == null) return false;
        if (expensePayment.getMethod().equals(ExpensePaymentMethod.BANK_TRANSFER)) return true;

        BucketRepository bucketRepo = Setup.getRepository(BucketRepository.class);
        Bucket moneyTransfer = bucketRepo.findFirstByBucketType(BucketType.MONEY_TRANSFER);

        if (moneyTransfer == null) throw new RuntimeException("There is no Money Transfer Bucket");

        return expensePayment.getMethod().equals(ExpensePaymentMethod.MONEY_TRANSFER) &&
                moneyTransfer.getNameInStatement() != null &&
                moneyTransfer.getNameInStatement().equalsIgnoreCase(bankStatementRecord.getBeneficiaryInfo1());
    }

    //Jirra ACC-3049
    private boolean processReplenishmentRecord(BankStatementRecord bankRecord, BankStatementTransaction bankStatementTransaction) {
        BucketReplenishmentTodoRepository bucketReplenishmentTodoRepo = Setup.getRepository(BucketReplenishmentTodoRepository.class);
        PayrollAccountantTodoRepository payrollAccountantTodoRepository = Setup.getRepository(PayrollAccountantTodoRepository.class);
        try {
            Long replenishmentToDoId = Long.parseLong(bankRecord.getPaymentDetail().toLowerCase().split("rpx")[1].split("(\n|\r|\\s)")[0].trim());
            bankStatementTransaction.setBankTransactionType(BankTransactionType.REPLENISHMENT_REQUEST);

            BucketReplenishmentTodo replenishmentTodo = bucketReplenishmentTodoRepo.findOne(replenishmentToDoId);
            List<ExpensePayment> notCashExpensePayments = replenishmentTodo != null ?
                    replenishmentTodo.getExpensePayments()
                            .stream().filter(p -> !p.getMethod().equals(ExpensePaymentMethod.CASH))
                            .collect(Collectors.toList()) :
                    null;

            ExpensePayment expensePayment = notCashExpensePayments != null && notCashExpensePayments.size() > 0 ?
                    notCashExpensePayments.get(0) : null;

            if (!validateExpensePayment(expensePayment, bankRecord)) return false;

            PayrollAccountantTodo payrollAccountantTodo = payrollAccountantTodoRepository.findFirstByExpensePayment(expensePayment);
            if (payrollAccountantTodo == null) {
                logger.info("processReplenishmentRecord Payroll Accountant Todo Not Found -> Not Matched");
                return false;
            }
            logger.info("processReplenishmentRecord Matched");

            bankStatementTransaction.setBankTransactionStatus(BankTransactionStatus.MATCHED);
            bankStatementTransaction.setExpensePayment(expensePayment);

            if (expensePayment.getCurrency() == null) {
                throw new RuntimeException("Payment has no Currency");
            }

            CurrencyExchangeSevice currencyExchangeSevice = Setup.getApplicationContext().getBean(CurrencyExchangeSevice.class);

            if (!expensePayment.getCurrency().getId().equals(currencyExchangeSevice.getLocalCurrency().getId())) {
                bankStatementTransaction.setTransactionAmount(bankRecord.getDebitAmount());
            } else {
                bankStatementTransaction.setTransactionAmount(payrollAccountantTodo.getTotal());
            }

            bankStatementTransaction.setDescription(expensePayment.getDescription());
            bankStatementTransaction.setFromBucket(expensePayment.getReplenishmentTodo().getFillFrom().getCode());
            bankStatementTransaction.setToBucket(expensePayment.getReplenishmentTodo().getBucket().getCode());
            bankStatementTransaction.setLicense(PicklistHelper.getItemNoException(PICKLIST_TRANSACTION_LICENSE, PICKLIST_TRANSACTION_LICENSE_NO_VAT_ITEM));

            if (BooleanUtils.toBoolean(expensePayment.getConfirmed())) {
                bankStatementTransaction.setResolved(true);
            }

            bankStatementTransactionRepository.save(bankStatementTransaction);
            return true;
        } catch (Exception ex) {
            bankRecord.setErrorDesc("Error while processing this record.");
            bankStatementRecordRepository.save(bankRecord);

            logger.severe("Exception while processing Replenishment Record'" + bankRecord.getId() + "'  error: " + ExceptionUtils.getStackTrace(ex));
        }

        return false;
    }

    private void processExpensesRecord(BankStatementRecord bankRecord, BankStatementTransaction bankStatementTransaction, List<Long> expensesMatchedPaymentsIds) {
        bankStatementTransaction.setBankTransactionType(BankTransactionType.EXPENSES);
        List<String> lines = Arrays.asList(bankRecord.getPaymentDetail().split("[\\r\\n]+"));
        Contract contract = null;
        try {
            //Jirra ACC-2249, expense on the same line
            //must be at least 3 lines
            if (lines == null || lines.size() < 3) {
                bankRecord.setErrorDesc("Error while parsing this record: " +
                        "we have less than 3 lines here");
                bankStatementRecordRepository.save(bankRecord);
                return;
            } else {

                //handle the shared lines before
                // <editor-fold defaultstate="collapsed" desc="###LINE 2">
                List<String> lineParts = Arrays.asList(lines.get(1).trim().split("/"));
                bankStatementTransaction.setDate(bankRecord.getDate());
                bankStatementTransaction.setForEntity(lineParts.get(0) != null ?
                        ("c".equals(lineParts.get(0).trim().toLowerCase()) ? "Client" :
                                ("m".equals(lineParts.get(0).trim().toLowerCase()) ? "Maid" :
                                        ("f".equals(lineParts.get(0).trim().toLowerCase()) ? "Freedom Operator" : ""))) : "");
                bankStatementTransaction.setForName(lineParts.get(1) != null ? lineParts.get(1).trim() : "");

                //this is other expenses
                if (!(lineParts.get(0) != null && lineParts.get(0).trim().toLowerCase().equals("c"))) {
                    bankStatementTransaction.setBankTransactionStatus(BankTransactionStatus.OTHER_EXPENSES);
                    bankStatementTransaction.setFromBucket("Mustaqeem ADCB");//static value //TODO: ask if From Bucket is always static
                }
                //else this is client refund, we will handle it from the last line

                // </editor-fold>

                // <editor-fold defaultstate="collapsed" desc="###LAST LINE">
                lineParts = Arrays.asList(lines.get(lines.size() - 1).trim().split("/"));
                bankStatementTransaction.setDescription(lines.get(lines.size() - 1));
                bankStatementTransaction.setErpObjectId(lineParts.get(0) != null ? lineParts.get(0).split(":")[1].trim() : "");
                bankStatementTransaction.setExpensifyId(lineParts.get(1) != null ? lineParts.get(1).split(":")[1].trim() : "");
                if (bankStatementTransaction.getBankTransactionStatus() != BankTransactionStatus.OTHER_EXPENSES) {
                    ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
                    contract = contractRepository.findOne(Long.valueOf(bankStatementTransaction.getErpObjectId().substring(bankStatementTransaction.getErpObjectId().indexOf(":") + 1)));
                    if (contract == null) {
                        bankStatementTransaction.setBankTransactionStatus(BankTransactionStatus.UNMATCHED_CLIENT_NOT_FOUND);
                        bankStatementTransaction.setReason("Client Not Found");
                    } else
                        bankStatementTransaction.setContract(contract);
                }
                // </editor-fold>

                //if 3 lines then one transaction only and the transaction amount is same as debit amount
                if (lines.size() == 3) {
                    // <editor-fold defaultstate="collapsed" desc="#LINE 1">
                    lineParts = Arrays.asList(lines.get(0).toLowerCase().split("expense")[1].trim().split("/"));
                    //if there is no 4 parts then the record has wrong  data//
                    if (lineParts == null || lineParts.size() != 4) {
                        bankRecord.setErrorDesc("Error while parsing this record: " +
                                "wrong parsing for line 1");
                        bankStatementRecordRepository.save(bankRecord);
                        return;
                    }
                    bankStatementTransaction.setExpenseCode(lineParts.get(0) != null ? lineParts.get(0).trim() : "");
                    bankStatementTransaction.setVatAmount(lineParts.get(1) != null ? Double.valueOf(lineParts.get(1).trim()) : 0.0);
                    if (bankStatementTransaction.getVatAmount().equals(0.0))
                        bankStatementTransaction.setVatType(null);
                    else
                        bankStatementTransaction.setVatType(lineParts.get(2) != null ? (lineParts.get(2).trim().toLowerCase().equals("i") ? VatType.IN : VatType.OUT) : null);

                    bankStatementTransaction.setLicense(lineParts.get(3) != null ? PicklistHelper.getItemNoException(
                            PICKLIST_TRANSACTION_LICENSE,
                            lineParts.get(3).trim().equalsIgnoreCase("m") ? "Mustaqeem" : "Storage") : null);
                    bankStatementTransaction.setTransactionAmount(bankRecord.getDebitAmount());

                    //trying to get the matching payments from the contract
                    if (contract != null) {
                        expenseRecordsSetMatchedPayment(contract, bankStatementTransaction, null, expensesMatchedPaymentsIds);
                    }
                    // </editor-fold>
                } else//more than 4 lines then multiple transactions and the transaction amount
                {
                    Double sumVatAmount = 0.0;
                    Double sumTransactionAmount = 0.0;

                    //start from second to the last line and create another transaction for it until we reach the line 2 (i=1, it's related to the first transaction
                    for (int i = lines.size() - 2; i > 1; i--) {
                        //first get the shared fields
                        BankStatementTransaction anotherBankTransaction = new BankStatementTransaction();
                        anotherBankTransaction.setUniqueId(bankStatementTransaction.getUniqueId() + " -" + i);
                        anotherBankTransaction.setBankTransactionType(bankStatementTransaction.getBankTransactionType());
                        anotherBankTransaction.setBankTransactionStatus(bankStatementTransaction.getBankTransactionStatus());
                        anotherBankTransaction.setBankStatementRecord(bankStatementTransaction.getBankStatementRecord());
                        anotherBankTransaction.setFile(bankStatementTransaction.getFile());
                        anotherBankTransaction.setForEntity(bankStatementTransaction.getForEntity());
                        anotherBankTransaction.setForName(bankStatementTransaction.getForName());
                        anotherBankTransaction.setFromBucket(bankStatementTransaction.getFromBucket());
                        anotherBankTransaction.setErpObjectId(bankStatementTransaction.getErpObjectId());
                        anotherBankTransaction.setExpensifyId(bankStatementTransaction.getExpensifyId());
                        anotherBankTransaction.setContract(bankStatementTransaction.getContract());
                        anotherBankTransaction.setDescription(bankStatementTransaction.getDescription());
                        anotherBankTransaction.setDate(bankStatementTransaction.getDate());

                        //now the new transaction fields
                        lineParts = Arrays.asList(lines.get(i).trim().split("/"));
                        //if there is no 5 parts then the record has wrong  data
                        if (lineParts == null || lineParts.size() != 5) {
                            bankRecord.setErrorDesc("Error while parsing this record: " +
                                    "wrong parsing the line " + i);
                            bankStatementRecordRepository.save(bankRecord);
                            return;
                        }
                        anotherBankTransaction.setExpenseCode(lineParts.get(0) != null ? lineParts.get(0).trim() : "");
                        anotherBankTransaction.setVatAmount(lineParts.get(1) != null ? Double.valueOf(lineParts.get(1).trim()) : 0.0);
                        if (anotherBankTransaction.getVatAmount().equals(0.0))
                            anotherBankTransaction.setVatType(null);
                        else
                            anotherBankTransaction.setVatType(lineParts.get(2) != null ? (lineParts.get(2).trim().toLowerCase().equals("i") ? VatType.IN : VatType.OUT) : null);
                        anotherBankTransaction.setLicense(lineParts.get(3) != null ? PicklistHelper.getItemNoException(
                                PICKLIST_TRANSACTION_LICENSE, lineParts.get(3).trim().equalsIgnoreCase("m") ? "Mustaqeem" : "Storage") : null);
                        anotherBankTransaction.setTransactionAmount(lineParts.get(4) != null ? Double.valueOf(lineParts.get(4).trim()) : 0.0);

                        //trying to get the matching payments from the contract
                        if (contract != null) {
                            expenseRecordsSetMatchedPayment(contract, anotherBankTransaction, BankTransactionMatchType.Automatic, expensesMatchedPaymentsIds);
                        }
                        bankStatementTransactionRepository.save(anotherBankTransaction);

                        //sum vatAmount & transactionAmount
                        sumVatAmount += anotherBankTransaction.getVatAmount();
                        sumTransactionAmount += anotherBankTransaction.getTransactionAmount();

                    }
                    //last step to put the correct data of the main transaction that we were creating (LINE 1)
                    lineParts = Arrays.asList(lines.get(0).toLowerCase().split("expense")[1].trim().split("/"));
                    //if there is no 4 parts then the record has wrong  data
                    if (lineParts == null || lineParts.size() != 4) {
                        bankRecord.setErrorDesc("Error while parsing this record: " +
                                "wrong parsing the line 1");
                        bankStatementRecordRepository.save(bankRecord);
                        return;
                    }
                    bankStatementTransaction.setExpenseCode(lineParts.get(0) != null ? lineParts.get(0).trim() : "");
                    bankStatementTransaction.setVatAmount((lineParts.get(1) != null ? Double.valueOf(lineParts.get(1).trim()) : 0.0) - sumVatAmount);
                    bankStatementTransaction.setVatType(lineParts.get(2) != null ? (lineParts.get(2).trim().toLowerCase().equals("i") ? VatType.IN : VatType.OUT) : null);
                    bankStatementTransaction.setLicense(lineParts.get(3) != null ? PicklistHelper.getItemNoException(
                            PICKLIST_TRANSACTION_LICENSE, lineParts.get(3).trim().equalsIgnoreCase("m") ? "Mustaqeem" : "Storage") : null);
                    bankStatementTransaction.setTransactionAmount(bankRecord.getDebitAmount() - sumTransactionAmount);

                    //trying to get the matching payments from the contract
                    if (contract != null) {
                        expenseRecordsSetMatchedPayment(contract, bankStatementTransaction, null, expensesMatchedPaymentsIds);
                    }
                }
                bankStatementTransactionRepository.save(bankStatementTransaction);
            }
        } catch (Exception ex) {
            bankRecord.setErrorDesc("Error while processing this record.");
            ex.printStackTrace();
            bankStatementRecordRepository.save(bankRecord);

            logger.severe("Exception while processing Expenses Record '" + bankRecord.getId() + "'  error: " + ExceptionUtils.getStackTrace(ex));
        }
    }

    private void expenseRecordsSetMatchedPayment(Contract contract, BankStatementTransaction bankStatementTransaction, BankTransactionMatchType matchType, List<Long> expensesMatchedPaymentsIds) {
        if (contract == null) return;

        Payment matchedPayment = null;

        DateTime now = new DateTime();
        DateTime statementDate = new DateTime(bankStatementTransaction.getDate());
        Date fromDate = now.isAfter(statementDate.dayOfMonth().withMaximumValue()) ? statementDate.minusMonths(1).dayOfMonth().withMinimumValue().toDate()
                : now.minusMonths(1).dayOfMonth().withMinimumValue().toDate();

        Date toDate = now.isAfter(statementDate.dayOfMonth().withMaximumValue()) ? now.toDate() : now.dayOfMonth().withMaximumValue().toDate();

        //Jirra ACC-2768, New Matching Algorithm
        List<Payment> matchedPayments = contract.getPayments().stream().filter(p -> p.getTypeOfPayment() != null && p.getTypeOfPayment().hasTag("refund") &&
                        p.getStatus().equals(PaymentStatus.PDC) &&
                        p.getAmountOfPayment().equals(bankStatementTransaction.getTransactionAmount())
                        && !expensesMatchedPaymentsIds.contains(p.getId())
                        && ((p.getDateOfPayment().after(fromDate) || p.getDateOfPayment().equals(fromDate)) && (p.getDateOfPayment().before(toDate) || p.getDateOfPayment().equals(toDate))))
                .sorted(Comparator.comparing(Payment::getDateOfPayment)).
                collect(Collectors.toList());

        if (matchedPayments != null && !matchedPayments.isEmpty()) {
            matchedPayment = matchedPayments.get(0);
        }

        if (matchedPayment != null) {
            bankStatementTransaction.setPayment(matchedPayment);
            bankStatementTransaction.setBankTransactionStatus(BankTransactionStatus.MATCHED);
            if (matchType != null) {
                bankStatementTransaction.setBankTransactionMatchType(matchType);
            }
            expensesMatchedPaymentsIds.add(matchedPayment.getId());
        } else {
            bankStatementTransaction.setBankTransactionStatus(BankTransactionStatus.UNMATCHED_PAYMENT_NOT_FOUND);
            bankStatementTransaction.setReason("Payment Not Found");
        }
    }

    private void processBucketRefillRecord(BankStatementRecord bankRecord, BankStatementTransaction bankStatementTransaction) {
        bankStatementTransaction.setBankTransactionType(BankTransactionType.BUCKET_REFILL);
        bankStatementTransaction.setDate(bankRecord.getDate());
        List<String> lines = Arrays.asList(bankRecord.getPaymentDetail().split("[\\r\\n]+"));
        if (lines != null && lines.get(1) != null) {
            List<String> lineParts = Arrays.asList(lines.get(1).trim().split("/"));
            //if there is no 4 parts then the record has wrong  data
            if (lineParts == null || lineParts.size() != 4) {
                bankRecord.setErrorDesc("Error while parsing this record: " +
                        "wrong parsing for line 2");
                bankStatementRecordRepository.save(bankRecord);
                return;
            }

            bankStatementTransaction.setToBucket(lineParts.get(0).trim());
            bankStatementTransaction.setFromBucket("Mustaqeem ADCB");//static value
            bankStatementTransaction.setVatAmount(lineParts.get(1) != null ? Double.valueOf(lineParts.get(1).trim()) : 0.0);
            if (bankStatementTransaction.getVatAmount().equals(0.0))
                bankStatementTransaction.setVatType(null);
            else
                bankStatementTransaction.setVatType(lineParts.get(2) != null ? (lineParts.get(2).trim().toLowerCase().equals("i") ? VatType.IN : VatType.OUT) : null);

            bankStatementTransaction.setLicense(lineParts.get(3) != null ? PicklistHelper.getItemNoException(
                    PICKLIST_TRANSACTION_LICENSE,
                    lineParts.get(3).trim().equalsIgnoreCase("m") ? "Mustaqeem" : "Storage") : null);
            bankStatementTransaction.setTransactionAmount(bankRecord.getDebitAmount());
            String newDesc = "";
            for (int i = 2; i < lines.size(); i++)
                newDesc += lines.get(i);
            bankStatementTransaction.setDescription(newDesc);

            bankStatementTransactionRepository.save(bankStatementTransaction);
        } else {
            logger.log(Level.SEVERE, "Exception when Bucket Refill processing");
            bankRecord.setErrorDesc("Error while parsing this record: " +
                    "wrong parsing the lines");
            bankStatementRecordRepository.save(bankRecord);
            return;
        }
    }

    public void processPDCPayment(BankStatementRecord bankRecord, BankStatementTransaction bankStatementTransaction) {
        Double debitAmount = bankRecord.getDebitAmount();
        try {
            bankStatementTransaction.setBankTransactionType(BankTransactionType.PDC);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Calendar c = Calendar.getInstance();
            Date date = bankRecord.getDate();
            c.setTime(sdf.parse(date.toString()));
            c.add(Calendar.DATE, -3);  // number of days to add
            String paymentDate2Str = sdf.format(c.getTime());
            Date paymentDate2 = new SimpleDateFormat("yyyy-MM-dd").parse(paymentDate2Str);

            SelectQuery<NonClientPDC> query = new SelectQuery<>(NonClientPDC.class);
            query.filterBy("amount", "=", debitAmount.intValue());
            query.filterBy("chequeDueDate", "<=", bankRecord.getDate());
            query.filterBy("chequeDueDate", ">=", paymentDate2);
            List<NonClientPDC> nonClientPDCS = query.execute();

            if (!nonClientPDCS.isEmpty()) {
                bankStatementTransaction.setBankTransactionType(BankTransactionType.PDC);
                bankStatementTransaction.setDate(bankRecord.getDate());
                bankStatementTransaction.setTransactionAmount(bankRecord.getDebitAmount());
                bankStatementTransaction.setExpense(nonClientPDCS.get(0).getExpense());
                bankStatementTransaction.setVatAmount(nonClientPDCS.get(0).getVatAmount() != null ? Double.valueOf(nonClientPDCS.get(0).getVatAmount().toString()) : null);
                bankStatementTransaction.setLicense(nonClientPDCS.get(0).getLicense() != null ? PicklistHelper.getItemNoException(
                        PICKLIST_TRANSACTION_LICENSE, nonClientPDCS.get(0).getLicense()) : null);
                bankStatementTransaction.setDescription("PDC");
                bankStatementTransactionRepository.save(bankStatementTransaction);
            } else {
                bankRecord.setErrorDesc("Error while parsing this record: " +
                        "there is no matching NonClientPDC");
                bankStatementRecordRepository.save(bankRecord);
                return;
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Exception when PDC Payment processing: " + e.getMessage());
            e.printStackTrace();
            bankRecord.setErrorDesc("Error while parsing this record.");
            bankStatementRecordRepository.save(bankRecord);
        }
    }

    public void processChequeDeposit(BankStatementRecord bankRecord, BankStatementTransaction bankStatementTransaction) {
        try {
            bankStatementTransaction.setBankTransactionType(BankTransactionType.CHEQUE);
            SelectQuery<Payment> query = new SelectQuery<>(Payment.class);

            // filter for "date" column
            Calendar dateCalendar = Calendar.getInstance();
            dateCalendar.setTime(bankRecord.getDate());
            dateCalendar.add(Calendar.MONTH, 1);
            dateCalendar.set(Calendar.DAY_OF_MONTH, 1);
            dateCalendar.add(Calendar.DATE, -1);
            Date dateEOM = dateCalendar.getTime();

            dateCalendar.setTime(bankRecord.getDate());
            dateCalendar.set(Calendar.DAY_OF_MONTH, 1);
            Date dateFOM = dateCalendar.getTime();

            SelectFilter dateSelectFilter = new SelectFilter();
            dateSelectFilter.and("dateOfPayment", ">=", dateFOM).
                    and("dateOfPayment", "<=", dateEOM);

            // ACC-2076
            // filter by "value date" column in case public holidays & fridays
            Calendar valueDateCalendar = Calendar.getInstance();
            valueDateCalendar.setTime(bankRecord.getValueDate());
            valueDateCalendar.add(Calendar.MONTH, 1);
            valueDateCalendar.set(Calendar.DAY_OF_MONTH, 1);
            valueDateCalendar.add(Calendar.DATE, -1);
            Date valueDateEOM = valueDateCalendar.getTime();

            valueDateCalendar.setTime(bankRecord.getValueDate());
            valueDateCalendar.set(Calendar.DAY_OF_MONTH, 1);
            Date valueDateFOM = valueDateCalendar.getTime();

            SelectFilter valueDateSelectFilter = new SelectFilter();
            valueDateSelectFilter.and("dateOfPayment", ">=", valueDateFOM).
                    and("dateOfPayment", "<=", valueDateEOM);

            SelectFilter dateOfPaymentSelectFilter = new SelectFilter(dateSelectFilter);
            dateOfPaymentSelectFilter.or(valueDateSelectFilter);

            query.filterBy(dateOfPaymentSelectFilter);
            query.filterBy("amountOfPayment", "=", bankRecord.getCreditAmount());
            query.filterBy("chequeNumber", "=", bankRecord.getBankReferenceNo());
            query.filterBy("methodOfPayment", "=", PaymentMethod.CHEQUE);

            SelectFilter statusSelectFilter = new SelectFilter();
            statusSelectFilter.and("status", "=", PaymentStatus.PDC).
                    or("status", "=", PaymentStatus.BOUNCED);

            query.filterBy(statusSelectFilter);

            bankStatementTransaction.setBankTransactionType(BankTransactionType.CHEQUE);

            List<Payment> payments = query.execute();

            SelectQuery<BankStatementRecord> recordSelectQuery = new SelectQuery<>(BankStatementRecord.class);
            recordSelectQuery.filterBy("description", "like", "%RETRN%");
            recordSelectQuery.filterBy("customerReferenceNo", "=", bankRecord.getCustomerReferenceNo());
            recordSelectQuery.filterBy("date", "=", bankRecord.getDate());
            recordSelectQuery.filterBy("debitAmount", "=", bankRecord.getCreditAmount());
            recordSelectQuery.filterBy("id", "<>", bankRecord.getId());
            recordSelectQuery.filterBy("bankStatementFile", "=", bankRecord.getBankStatementFile());


            List<BankStatementRecord> records = recordSelectQuery.execute();

            if (!records.isEmpty()) { // we don't have to process this records again
                for (BankStatementRecord similar : records) {
                    similar.setProcessed(true);
                    bankStatementRecordRepository.save(similar);
                }
            }

            List<String> stringList = Arrays.asList(bankRecord.getPaymentDetail().trim().toLowerCase().split("cheque deposit"));
            List<String> Parts = Arrays.asList(stringList.get(1).split("chq no"));
            String clientName = Parts.get(0).trim();
            logger.log(Level.SEVERE, "STATEMENT FILE payments matched size for cheques is: " + payments.size());
            if (!payments.isEmpty() && payments.size() == 1) {

                Long PaymentId = payments.get(0).getId();
                Payment payment = payments.get(0);
                bankStatementTransaction.setPayment(payment);
                if (payment.getContract() != null) {
                    bankStatementTransaction.setContract(payment.getContract());
                }

                if (records != null && !records.isEmpty()) {
                    bankStatementTransaction.setBankTransactionStatus(BankTransactionStatus.BOUNCED);
                    bankStatementTransaction.setBankTransactionMatchType(BankTransactionMatchType.Automatic);
                    bankStatementTransaction.setDate(bankRecord.getDate());
                    PicklistItem bouncingReason = getBouncingReason(records.get(0).getPaymentDetail());
                    bankStatementTransaction.setBouncingReason(bouncingReason);
                    bankStatementTransaction.setTransactionAmount(bankRecord.getCreditAmount());
                    bankStatementTransaction.setPaymentDetail(bankRecord.getPaymentDetail());
                    bankStatementTransaction.setBouncedPaymentId(PaymentId);
                    bankStatementTransaction.setClient(clientName);

                } else {
                    bankStatementTransaction.setDate(bankRecord.getDate());
                    bankStatementTransaction.setTransactionAmount(bankRecord.getCreditAmount());
                    bankStatementTransaction.setDescription("Client-" + clientName + " / Payment-" + PaymentId + " / Contract-" + payment.getContract().getId() + " / Date of payment-" + payment.getDateOfPayment());
                    bankStatementTransaction.setVatAmount(payment.getVat());
                    bankStatementTransaction.setVatType(VatType.OUT);
                    TransactionPostingRuleRepository repository = Setup.getRepository(TransactionPostingRuleRepository.class);
                    List<TransactionPostingRule> transactionPostingRules = repository.findByCompanyAndTypeOfPaymentAndMethodOfPaymentAndInitialPaymentAndPaymentStatusAndActiveTrue(
                            payment.getContract().getContractProspectType(), payment.getTypeOfPayment(), payment.getMethodOfPayment(), payment.getIsInitial(), PaymentStatus.RECEIVED);
                    bankStatementTransaction.setRevenue(transactionPostingRules != null && transactionPostingRules.size() == 1 ? transactionPostingRules.get(0).getRevenue() : null);
                    bankStatementTransaction.setBankTransactionStatus(BankTransactionStatus.RECEIVED);
                    bankStatementTransaction.setBankTransactionMatchType(BankTransactionMatchType.Automatic);
                }
            } else {
                bankStatementTransaction.setDate(bankRecord.getDate());
                bankStatementTransaction.setTransactionAmount(bankRecord.getCreditAmount());
                bankStatementTransaction.setPaymentDetail(bankRecord.getPaymentDetail());
                bankStatementTransaction.setReason("Payment Not Found");
                if (records != null && !records.isEmpty()) {
                    bankStatementTransaction.setBankTransactionStatus(BankTransactionStatus.UNMATCHED_BOUNCED_TRANSACTION_NOT_FOUND);
                } else {
                    bankStatementTransaction.setBankTransactionStatus(BankTransactionStatus.UNMATCHED_RECEIVED_TRANSACTION_NOT_FOUND);
                    bankStatementTransaction.setDescription("Client-" + clientName);
                }
            }
            bankStatementTransactionRepository.save(bankStatementTransaction);
        } catch (Exception ex) {
            logger.log(Level.SEVERE, "Exception when Cheque Deposit processing: " + ex.getMessage());
            ex.printStackTrace();
            bankRecord.setErrorDesc("Error while parsing this record.");
            bankStatementRecordRepository.save(bankRecord);
        }
    }

    private PicklistItem getBouncingReason(String paymentDetail) {

        if (paymentDetail == null || paymentDetail.trim().equals(""))
            return null;

        PicklistRepository picklistRepository = Setup.getRepository(PicklistRepository.class);
        PicklistItemRepository picklistItemRepository = Setup.getRepository(PicklistItemRepository.class);

        // get bouncing Reason Pick List Items
        Picklist bouncingReasonPickList = picklistRepository.findByCode("ReasonOfBouncedCheck");
        List<PicklistItem> bouncingReasonPickListItems = picklistItemRepository.findByListOrderByNameAsc(bouncingReasonPickList);

        // get cheque bouncing Reason Pick List Items
        List<PicklistItem> chequeBouncingReasonPickListItems = bouncingReasonPickListItems.stream().filter(item -> item.hasTag("Bank Value")).collect(Collectors.toList());

        // get mapped reason entered in payment detail
        if (chequeBouncingReasonPickListItems != null && !chequeBouncingReasonPickListItems.isEmpty())

            // get "Bank Value" tag (it's key value tag)
            for (PicklistItem item : chequeBouncingReasonPickListItems) {
                String value = item.getTagValue(("Bank Value")).getValue();
                if (value != null
                        && !value.isEmpty()
                        && paymentDetail.contains(value))
                    return item;
            }

        return null;
    }

    public void preHandleRecords(Long fileId, String source) {
        BankStatementFile file = bankStatementFileRepository.findOne(fileId);
        if (file == null) throw new BusinessException("file is null");
        logger.info("file id : " + fileId + ", source : " + source);
        String bucketCode = Setup.getParameter(Setup.getCurrentModule(), PARAMETER_STATEMENTS_TRANSACTIONS_FROM_BUCKET);
        // 1. Process matched and UnMatched records to make it Already Matched
        preHandleRecordsShouldBeAlreadyMatched(file);

        // 2. Process unmatched records with reason "A Matching is Found, but Accountant To-Do is still open"
        preHandleUnMatchedRecordsOpenedTodo(file, bucketCode);

        // 3. Process unmatched records with reason "No salary payment record"
        preHandleUnMatchedRecordsNoSalary(file, bucketCode);
    }

    // done
    private void preHandleUnMatchedRecordsNoSalary(BankStatementFile file, String bucketCode) {
        List<Object[]> noSalaryRecords = bankStatementTransactionRepository.findUnMatchedRecordsByReason(file.getId(),
                PayrollTransferUnmatchedReason.NO_SALARY_PAYMENT_RECORD.getLabel(),
                java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

        // Group data to handle the case that each record may have more than one OfficeStaffPayrollLog
        Map<BankStatementTransaction, BankTransactionInfo> grouped = new HashMap<>();
        for (Object[] row : noSalaryRecords) {
            BankStatementTransaction bst = (BankStatementTransaction) row[0];
            OfficeStaffPayrollLog log = (OfficeStaffPayrollLog) row[1];
            Expense e = (Expense) row[2];

            grouped.computeIfAbsent(bst, id -> new BankTransactionInfo(bst, e))
                    .addLog(log);
        }

        for (Map.Entry<BankStatementTransaction, BankTransactionInfo> entry : grouped.entrySet()) {
            BankTransactionInfo info = entry.getValue();
            BankStatementTransaction record = info.getTransaction();
            try {
                Expense e = info.getExpense();
                List<OfficeStaffPayrollLog> logs = info.getLogs();

                PayrollAccountantTodo todo = logs.get(0).getPayrollAccountantTodo();

                if (!todo.isCompleted() && !todo.isStopped()) {
                    record.setReason(PayrollTransferUnmatchedReason.ACCOUNTANT_TODO_IS_OPEN.getLabel());
                    bankStatementTransactionRepository.save(record);
                    logger.info("Updated record " + record.getId());
                } else {
                    // Move to matched transactions
                    record.setBankTransactionStatus(BankTransactionStatus.MATCHED);
                    record.setReason(null);
                    fillInfoOnTransactionFromPayrollLogs(record, logs);
                    if (e != null) {
                        record.setExpense(e);
                        record.setExpenseCode(e.getName());
                    }
                    record.setFromBucket(bucketRepository.findByCode(bucketCode).getName());
                    bankStatementTransactionRepository.save(record);
                    logger.info("Moved record " + record.getId());
                }
            } catch (Exception ex) {
                logger.severe("Error while processing record " + record.getId() + " error: " + ex.getMessage());
                ex.printStackTrace();
            }
        }
    }

    private void preHandleUnMatchedRecordsOpenedTodo(BankStatementFile file, String bucketCode) {
        List<Object[]> unmatchedRecords = bankStatementTransactionRepository.findUnMatchedRecordsByReason(file.getId(),
                PayrollTransferUnmatchedReason.ACCOUNTANT_TODO_IS_OPEN.getLabel(),
                java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

        // Group data to handle the case that each record may have more than one OfficeStaffPayrollLog
        Map<BankStatementTransaction, BankTransactionInfo> grouped = new HashMap<>();
        for (Object[] row : unmatchedRecords) {
            BankStatementTransaction bst = (BankStatementTransaction) row[0];
            OfficeStaffPayrollLog log = (OfficeStaffPayrollLog) row[1];
            Expense e = (Expense) row[2];

            grouped.computeIfAbsent(bst, id -> new BankTransactionInfo(bst, e))
                    .addLog(log);
        }

        for (Map.Entry<BankStatementTransaction, BankTransactionInfo> entry : grouped.entrySet()) {
            BankTransactionInfo info = entry.getValue();
            BankStatementTransaction record = info.getTransaction();
            try {
                List<OfficeStaffPayrollLog> logs = info.getLogs();

                PayrollAccountantTodo todo = logs.get(0).getPayrollAccountantTodo();

                if (todo == null || (!todo.isCompleted() && !todo.isStopped())) continue;

                Expense e = info.getExpense();
                if (e == null) {
                    record.setReason(PayrollTransferUnmatchedReason.PNL_CODE_IS_MISSING.getLabel());
                    logger.info("reason is changed for record : " + record.getId());
                } else {
                    // Move to matched transactions
                    record.setBankTransactionStatus(BankTransactionStatus.MATCHED);
                    record.setReason(null);
                    record.setExpense(e);
                    record.setExpenseCode(e.getName());
                    record.setFromBucket(bucketRepository.findByCode(bucketCode).getName());
                    fillInfoOnTransactionFromPayrollLogs(record, logs);
                    logger.info("Moved record " + record.getId());
                }
                bankStatementTransactionRepository.save(record);
            } catch (Exception ex) {
                logger.severe("Error while processing record " + record.getId() + " error: " + ex.getMessage());
                ex.printStackTrace();
            }
        }
    }

    private void preHandleRecordsShouldBeAlreadyMatched(BankStatementFile file) {
        List<Object[]> records = bankStatementTransactionRepository.findRecordsShouldBeAlreadyMatched(file.getId(),
                java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

        for (Object[] r : records) {
            BankStatementTransaction record = (BankStatementTransaction) r[0];
            try {
                Transaction transaction = (Transaction) r[1];
                // Same PaymentDetail exists in another file for a record Matched and resolved
                record.setResolved(true);
                record.setDescription(transaction.getDescription());
                record.setTransaction(transaction);
                if (record.getBankTransactionStatus().equals(BankTransactionStatus.UNMATCHED_PAYROLL_EXPENSE)) {
                    record.setBankTransactionStatus(BankTransactionStatus.MATCHED);
                }
                bankStatementTransactionRepository.save(record);
                logger.info("Marked record " + record.getId());
            } catch (Exception ex) {
                logger.severe("Error while processing record " + record.getId() + " error: " + ex.getMessage());
                ex.printStackTrace();
            }
        }
    }

    private void fillInfoOnTransactionFromPayrollLogs(BankStatementTransaction transaction, List<OfficeStaffPayrollLog> logs) {
        if (logs == null || logs.isEmpty()) return;

        OfficeStaffPayrollLog firstLog = logs.get(0);

        String payrollMonthsConcat = logs.stream()
                .map(x -> new LocalDate(x.getPayrollMonth()).toString("yyyy-MM-dd"))
                .collect(Collectors.joining(", "));

        double totalAmount = logs.stream().mapToDouble(OfficeStaffPayrollLog::getTotalSalary).sum();

        transaction.setBeneficiaryName(firstLog.getEmployeeName());
        if (firstLog.getForEmployeeLoan()) {
            transaction.setDescription(firstLog.getLoanDescription());
        } else {
            transaction.setDescription("Salary / " + payrollMonthsConcat + " / " + firstLog.getEmployeeName() + "/" + totalAmount);
        }
    }

    private void fillUnMatchedPayrollRecordInfo(BankStatementTransaction bankStatementTransaction, String reason, String beneficiaryName) {

        bankStatementTransaction.setBankTransactionStatus(BankTransactionStatus.UNMATCHED_PAYROLL_EXPENSE);
        bankStatementTransaction.setReason(reason);
        bankStatementTransaction.setBeneficiaryName(beneficiaryName);
        bankStatementTransaction.setDescription("Salary / " + bankStatementTransaction.getTransactionAmount());
    }
}