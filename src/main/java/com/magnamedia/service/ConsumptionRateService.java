package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.Tag;
import com.magnamedia.entity.BedAssignmentHistory;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.MaidInAccommodationStatistics;
import com.magnamedia.entity.PurchaseItem;
import com.magnamedia.entity.dto.RoomDateKey;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ItemMeasureOfConsumption;
import com.magnamedia.module.type.OrderCycle;
import com.magnamedia.repository.BedAssignmentHistoryRepository;
import com.magnamedia.repository.HousemaidRepository;
import com.magnamedia.repository.MaidInAccommodationStatisticsRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Mohammad Nosairat (Mar 10, 2021)
 */
@Service
public class ConsumptionRateService {


    @Autowired
    BedAssignmentHistoryRepository bedAssignmentHistoryRepository;

    public BigDecimal calculateInitialCycleInventory(PicklistItem measureOfConsumption, BigDecimal consumptionRate, BigDecimal orderCycleDays) {
        if (measureOfConsumption == null) return new BigDecimal(0);
        if (consumptionRate == null) return new BigDecimal(0);

        Tag worseCase = measureOfConsumption.getTags()
                .stream().filter(t -> t.getKey().equals(Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_WORSE_CASE_SCENARIO_TAG)))
                .findAny()
                .orElse(null);
        if (worseCase == null) return new BigDecimal(0);

        return new BigDecimal(worseCase.getValue())
                .multiply(consumptionRate)
                .multiply(orderCycleDays);
    }

    public BigDecimal calculateTheoreticalConsumption(PurchaseItem purchaseItem) {

        Date end = purchaseItem.getCreationDate() != null ? purchaseItem.getCreationDate() : new Date();
        end = DateUtil.getDayEnd(DateUtil.addDays(end, -1));

        Date begin = getBeginDate(purchaseItem, end);

        BigDecimal wight = new BigDecimal(1);
        String measureOfConsumption = purchaseItem.getItem().getMeasureOfConsumption().getName();
        if (measureOfConsumption.equals(ItemMeasureOfConsumption.NONE.toString())) {
            wight = new BigDecimal(1);
        } else if (measureOfConsumption.equals(ItemMeasureOfConsumption.PER_NUMBER_OF_ASSIGNED_ROOMS.toString())) {
            wight = getNumberOfRooms(begin, end);
        } else if (measureOfConsumption.equals(ItemMeasureOfConsumption.PER_NUMBER_OF_MAIDS_IN_ACCOMMODATION.toString())) {
            wight = getNumberOfMaids(begin, end);
        } else if (measureOfConsumption.equals(ItemMeasureOfConsumption.PER_NUMBER_OF_MAIDS_WHO_RETURNED_FROM_CLIENT_TO_ACCOMMODATION.toString())) {
            wight = getNumberOfMaidsReturnedFromClient(begin, end);
        } else if (measureOfConsumption.equals(ItemMeasureOfConsumption.PER_NUMBER_OF_MAIDS_WHO_WENT_WITH_CLIENT_FROM_ACCOMMODATION.toString())) {
            wight = getNumberOfMaidsWhentWithClient(begin, end);
        } else if (measureOfConsumption.equals(ItemMeasureOfConsumption.PER_NUMBER_OF_NEWLY_JOINED_MAIDS.toString())) {
            wight = getNumberOfJoinedMaids(begin, end);
        } else if (measureOfConsumption.equals(ItemMeasureOfConsumption.PER_NUMBER_OF_SICK_MAIDS.toString())) {
            wight = getNumberOfSickMaids(begin, end);
        }

        Logger.getLogger(PurchaseItem.class.getName()).info("calculateTheoreticalConsumption");

        return wight.multiply(purchaseItem.getItem().getConsumptionRate()).setScale(2, RoundingMode.HALF_EVEN);
    }

    private BigDecimal getNumberOfMaidsWhentWithClient(Date begin, Date end) {
        List<MaidInAccommodationStatistics> maidsStatistics = Setup.getRepository(MaidInAccommodationStatisticsRepository.class)
                .findByCreationDateGreaterThanEqualAndCreationDateLessThanEqual(begin, end);
        Integer total = maidsStatistics.stream().map(MaidInAccommodationStatistics::getNumberOfMaidsWhoWentWithClient).reduce(0, Integer::sum);
        if (total == null) total = 0;
        return new BigDecimal(total);
    }

    private BigDecimal getNumberOfMaidsReturnedFromClient(Date begin, Date end) {
        List<MaidInAccommodationStatistics> maidsStatistics = Setup.getRepository(MaidInAccommodationStatisticsRepository.class)
                .findByCreationDateGreaterThanEqualAndCreationDateLessThanEqual(begin, end);
        Integer total = maidsStatistics.stream().map(MaidInAccommodationStatistics::getNumberOfMaidsWhoComeFromClient).reduce(0, Integer::sum);
        if (total == null) total = 0;
        return new BigDecimal(total);
    }

    private BigDecimal getNumberOfSickMaids(Date begin, Date end) {
        List<BedAssignmentHistory> beds = bedAssignmentHistoryRepository
                .findByAssignmentDateGreaterThanAndLessThanAndMaidStatus(begin,
                        end, "SICK_WITHOUT_CLIENT");

        return new BigDecimal(beds.size());
    }

    @Autowired
    HousemaidRepository housemaidRepository;

    private BigDecimal getNumberOfJoinedMaids(Date begin, Date end) {
        List<Housemaid> housemaids = housemaidRepository.findByLandedInDubaiDateBetweenDates(begin, end);
        return new BigDecimal(housemaids.size());
    }

    private BigDecimal getNumberOfMaids(Date begin, Date end) {
        List<BedAssignmentHistory> beds = bedAssignmentHistoryRepository.findByAssignmentDateGreaterThanAndLessThan(begin, end);
        return new BigDecimal(beds.size());
    }

    private BigDecimal getNumberOfRooms(Date begin, Date end) {
        List<BedAssignmentHistory> beds = bedAssignmentHistoryRepository.findByAssignmentDateGreaterThanAndLessThan(begin, end);
        Map<RoomDateKey, List<BedAssignmentHistory>> groupingResult = beds.stream()
                .collect(Collectors.groupingBy(b -> new RoomDateKey(b.getRoomNumber(), b.getAssignmentDate())));

        return new BigDecimal(groupingResult.size());
    }

    private Date getBeginDate(PurchaseItem item, Date end) {
        if (item.getItem().getCategory().getOrderCycle().getCode().equals(OrderCycle.MONTHLY.getName())) {
            int daysNumBeforeStart = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.NBR_DAYS_BEFORE_START_ORDER_CYCLE_MONTHLY));
            Instant s = end.toInstant().atZone(ZoneId.systemDefault()).withDayOfMonth(1).plus(-daysNumBeforeStart, ChronoUnit.DAYS).toInstant();
            return DateUtil.getDayStart(Date.from(s));
        } else if (item.getItem().getCategory().getOrderCycle().getCode().equals(OrderCycle.WEEKLY.getName())) {
            return DateUtil.getDayStart(DateUtil.addDays(end, -6));
        }
        return null;
    }


}