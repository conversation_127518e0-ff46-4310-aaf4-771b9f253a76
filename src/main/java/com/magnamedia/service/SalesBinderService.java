package com.magnamedia.service;

import com.magnamedia.entity.Category;
import com.magnamedia.entity.Item;
import com.magnamedia.entity.Supplier;
import com.magnamedia.entity.UnitOfMeasure;
import com.magnamedia.entity.dto.salesbinder.CategoryDto;
import com.magnamedia.entity.dto.salesbinder.ItemDto;
import com.magnamedia.entity.dto.salesbinder.SupplierDto;
import com.magnamedia.entity.dto.salesbinder.UnitOfMeasureDto;
import com.magnamedia.extra.SalesBinderApiClient;
import com.magnamedia.repository.CategoryRepository;
import com.magnamedia.repository.ItemRepository;
import com.magnamedia.repository.SupplierRepository;
import com.magnamedia.repository.UnitOfMeasureRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <PERSON> (Jan 25, 2021)
 */
@Service
public class SalesBinderService {
    @Autowired
    SalesBinderApiClient apiClient;
    @Autowired
    SupplierRepository supplierRepository;
    @Autowired
    CategoryRepository categoryRepository;
    @Autowired
    ItemRepository itemRepository;
    @Autowired
    UnitOfMeasureRepository unitOfMeasureRepository;

    public void updateSuppliers() {
        List<SupplierDto> dtos = apiClient.getSalesBinderSuppliers();
        for (SupplierDto dto : dtos) {
            try {
                Supplier dbSupplier = supplierRepository.findBySupplierId(dto.getSupplierId());
                if (dbSupplier == null) {
                    Supplier supplier = new Supplier(dto);
                    supplierRepository.save(supplier);
                    continue;
                }
                boolean updated = false;
                if (isUpdated(dbSupplier.getEmail(), dto.getEmail())) {
                    dbSupplier.setEmail(dto.getEmail());
                    updated = true;
                }
                if (isUpdated(dbSupplier.getPhoneNumber(), dto.getPhoneNumber())) {
                    dbSupplier.setPhoneNumber(dto.getPhoneNumber());
                    updated = true;
                }
                if (isUpdated(dbSupplier.getName(), dto.getName())) {
                    dbSupplier.setName(dto.getName());
                    updated = true;
                }
                if (isUpdated(dbSupplier.getWebSite(), dto.getWebSite())) {
                    dbSupplier.setWebSite(dto.getWebSite());
                    updated = true;
                }
                if (updated)
                    supplierRepository.save(dbSupplier);

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void updateCategories() {
        List<CategoryDto> dtos = apiClient.getSalesBinderCategories();
        for (CategoryDto dto : dtos) {
            try {
                Category dbCategory = categoryRepository.findByCategoryId(dto.getCategoryId());
                if (dbCategory == null) {
                    Category category = new Category(dto);
                    categoryRepository.save(category);
                    continue;
                }
                boolean updated = false;
                if (!Objects.equals(dbCategory.getDescription(), dto.getDescription())) {
                    dbCategory.setDescription(dto.getDescription());
                    updated = true;
                }
                if (!Objects.equals(dbCategory.getName(), dto.getName())) {
                    dbCategory.setName(dto.getName());
                    updated = true;
                }
                if (updated)
                    categoryRepository.save(dbCategory);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private <T> boolean isUpdated(T oldValue, T newValue) {
        if (oldValue == null && newValue == null)
            return false;
        if (oldValue == null)
            return true;
//        if (newValue == null)
//            return true;
        if (oldValue != null)
            return false;

        return true;
    }
    
    private static final Logger logger =
            Logger.getLogger(SalesBinderService.class.getName());
    
    public void updateItems() {
        List<ItemDto> dtos = apiClient.getSalesBinderItems();
        UnitOfMeasure unitOfMeasure = null;
        for (ItemDto dto : dtos) {
            try {
                Item dbItem = itemRepository.findByItemId(dto.getItemId());
                unitOfMeasure = getUnitOfMeasureForItem(unitOfMeasure, dto);
                if (dbItem == null) {
                    logger.log(Level.SEVERE, "SalesBinderService item not found ");
                    Item item = new Item(dto, unitOfMeasure);
                    itemRepository.save(item);
                    continue;
                }
                else
                    logger.log(Level.SEVERE, "SalesBinderService item id: " + dbItem.getId());
                boolean updated = false;
                if (!Objects.equals(dbItem.getDescription(), dto.getDescription())) {
                    dbItem.setDescription(dto.getDescription());
                    updated = true;
                }
                if (!Objects.equals(dbItem.getName(), dto.getName())) {
                    dbItem.setName(dto.getName());
                    updated = true;
                }
                if (isUpdated((dbItem.getUnitOfMeasure() == null ? null : dbItem.getUnitOfMeasure().getUnitOfMeasureId()),
                        unitOfMeasure == null ? null : unitOfMeasure.getUnitOfMeasureId())) {
                    dbItem.setUnitOfMeasure(unitOfMeasure);
                    updated = true;
                }
                if (dbItem.getQuantity() == null
                        || (dbItem.getQuantity().compareTo(BigDecimal.ZERO) == 0 && (dto.getQuantity() != null && dto.getQuantity().compareTo(BigDecimal.ZERO) != 0))
                        || dbItem.getQuantity().compareTo(dto.getQuantity()) != 0) {
                    dbItem.setQuantity(dto.getQuantity());
                    updated = true;
                }
                if (!Objects.equals(dbItem.getBarcode(), dto.getBarcode())) {
                    dbItem.setBarcode(dto.getBarcode());
                    updated = true;
                }
                logger.log(Level.SEVERE, "SalesBinderService dbItem.getCategoryIdStr(): " + dbItem.getCategoryIdStr());
                logger.log(Level.SEVERE, "SalesBinderService dto.getCategoryId(): " + dto.getCategoryId());
                if (!Objects.equals(dbItem.getCategoryIdStr(), dto.getCategoryId())) {
                    dbItem.setCategoryIdStr(dto.getCategoryId());
                    if (dto.getCategoryId() != null && ! dto.getCategoryId().isEmpty()){
                        Category dbCategory = categoryRepository.findByCategoryId(dto.getCategoryId());
                        dbItem.setCategory(dbCategory);
                    }
                    updated = true;
                }
                if (!Objects.equals(dbItem.getItemNumber(), dto.getItemNumber())) {
                    dbItem.setItemNumber(dto.getItemNumber());
                    updated = true;
                }
                if (!Objects.equals(dbItem.getSerialNumber(), dto.getSerialNumber())) {
                    dbItem.setSerialNumber(dto.getSerialNumber());
                    updated = true;
                }
                if (!Objects.equals(dbItem.getSku(), dto.getSku())) {
                    dbItem.setSku(dto.getSku());
                    updated = true;
                }

                if (!Objects.equals(dbItem.getThreshold(), dto.getThreshold())) {
                    dbItem.setThreshold(dto.getThreshold());
                    updated = true;
                }
                if (!Objects.equals(dbItem.getCost(), dto.getCost())) {
                    dbItem.setCost(dto.getCost());
                    updated = true;
                }
                if (!Objects.equals(dbItem.getPrice(), dto.getPrice())) {
                    dbItem.setPrice(dto.getPrice());
                    updated = true;
                }

                if (updated)
                    itemRepository.save(dbItem);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    private UnitOfMeasure getUnitOfMeasureForItem(UnitOfMeasure unitOfMeasure, ItemDto dto) {
        UnitOfMeasureDto unitDto = dto.getUnitOfMeasure();
        if (unitDto != null && unitDto.getUnitOfMeasureId() != null) {
            if (unitOfMeasure == null || !unitOfMeasure.getUnitOfMeasureId().equals(unitDto.getUnitOfMeasureId())) {
                unitOfMeasure = unitOfMeasureRepository.findByUnitOfMeasureId(unitDto.getUnitOfMeasureId());
                if (unitOfMeasure == null) {
                    UnitOfMeasure newUnit = new UnitOfMeasure();
                    newUnit.setUnitOfMeasureId(unitDto.getUnitOfMeasureId());
                    newUnit.setFullName(unitDto.getFullName());
                    newUnit.setShortName(unitDto.getShortName());
                    unitOfMeasure = unitOfMeasureRepository.save(newUnit);
                }
            }
        } else {
            unitOfMeasure = null;
        }
        return unitOfMeasure;
    }


    public void test() {
        updateCategories();
        updateItems();
        updateSuppliers();
    }
}
