package com.magnamedia.service;

import com.magnamedia.controller.InsuranceAgreementController;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.entity.InsuranceAuditingRecord;
import com.magnamedia.entity.InsuranceAuditingSetup;
import com.magnamedia.entity.InsuranceAuditingStatement;
import com.magnamedia.extra.InsuranceNote;
import com.magnamedia.helper.NumberFormatter;
import com.magnamedia.module.type.InsuranceAuditingStatementStatus;
import com.magnamedia.repository.InsuranceAuditingRecordRepository;
import com.magnamedia.repository.InsuranceAuditingSetupRepository;
import com.magnamedia.repository.InsuranceAuditingStatementRepository;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 * @date 2/11/2021
 */
@SuppressWarnings("Duplicates")
@Service
public class InsuranceStatementAuditingService {
    protected static final Logger logger = Logger.getLogger(InsuranceStatementAuditingService.class.getName());
    @Autowired
    InsuranceAgreementController insuranceAgreementController;

    @Autowired
    InsuranceAuditingSetupRepository insuranceAuditingSetupRepository;

    @Autowired
    InsuranceAuditingStatementRepository insuranceAuditingStatementRepository;

    @Autowired
    InsuranceAuditingRecordRepository insuranceAuditingRecordRepository;


    public InsuranceAuditingStatement audit(Long attachmentId, InsuranceAuditingStatement statement) {

        InsuranceAuditingSetup insuranceAuditingSetup = insuranceAuditingSetupRepository.findAll().get(0);
        List<String> dismissedPassports = insuranceAuditingSetup.getDismissedPassports();

        final DataFormatter dataFormatter = new DataFormatter();//to get any cell value as string
        //credit meaning additions -- reversed 27-2-2021 credit -> deletion
        List<InsuranceNote> creditNoteList = (List<InsuranceNote>) insuranceAgreementController.creditNoteList(statement.getFromDate(), statement.getToDate(), null).getBody();
        //debit meaning deletions -- reversed 27-2-2021 debit -> addition
        List<InsuranceNote> debitNoteList = (List<InsuranceNote>) insuranceAgreementController.debitNoteList(statement.getFromDate(), statement.getToDate(), null).getBody();


        Double dismissedDeletionSum = 0D;
        Double dismissedAdditionSum = 0D;

        Double invoiceDeletionSum = 0D;
        Double invoiceAdditionSum = 0D;

        boolean isMatched = false;

        Attachment attachment = Storage.getAttchment(attachmentId);
        if (attachment == null) {
            throw new RuntimeException("attachment with id=" + attachmentId + " is null");
        }
        if (!attachment.getExtension().equals("xlsx") && !attachment.getExtension().equals("xls")) {
            throw new RuntimeException("attachment should be excel file");
        }
        statement.addAttachment(attachment);
        List<InsuranceAuditingRecord> records = new ArrayList<>();
        //SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
        Workbook workbook = null;
        try {
            if (attachment.getExtension().equals("xlsx"))
                workbook = new XSSFWorkbook(Storage.getStream(attachment));
            else if (attachment != null && attachment.getExtension().equals("xls"))
                workbook = new HSSFWorkbook(Storage.getStream(attachment));

            //get second sheet for addition
            Sheet sheet = workbook.getSheetAt(1);
            Iterator<Row> rowIterator = sheet.iterator();
            //reach first data row
            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                String stringCellValue = dataFormatter.formatCellValue(row.getCell(0));
                if (stringCellValue.equalsIgnoreCase("EFFFECTIVEDATE")) break;
            }

            //read rows
            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                if (isRowEmpty(row)) break;

                Date effectiveDate = resolveEffectiveDate(row);
                String name = dataFormatter.formatCellValue(row.getCell(1));
                String passport = dataFormatter.formatCellValue(row.getCell(4));
                Double amount = row.getCell(9).getNumericCellValue();
                amount = NumberFormatter.twoDecimalPoints(amount);
                //create record
                InsuranceAuditingRecord record = new InsuranceAuditingRecord();
                record.setStatement(statement);
                record.setAmount(amount);
                record.setEffectiveDate(effectiveDate == null ? null : new java.sql.Date(effectiveDate.getTime()));
                record.setPassportNum(passport);
                record.setName(name);
                record.setType(InsuranceAuditingRecord.recordType.ADDITION);

                invoiceAdditionSum += amount;

                if (dismissedPassports != null && dismissedPassports.contains(passport)){
                    dismissedAdditionSum += amount;
                    logger.log(Level.INFO, "setPassportNum: " + passport + "dismissedAdditionSum: " + amount);
                }

                if(record.getEffectiveDate() == null) {
                    if (dismissedPassports == null || !dismissedPassports.contains(passport)){
                        record.setMatchType(InsuranceAuditingRecord.matchType.UNMATCHED);
                    }
                } else if(records.stream().filter(x -> x.getEffectiveDate() != null)
                        .anyMatch(x-> x.getType() == record.getType() &&
                                x.getPassportNum().equalsIgnoreCase(record.getPassportNum()) &&
                                x.getAmount().equals(record.getAmount()) &&
                                x.getEffectiveDate().equals(record.getEffectiveDate()))) {

                    //duplicated
                    record.setMatchType(InsuranceAuditingRecord.matchType.DUPLICATED);
                } else {
                    Optional<InsuranceNote> first = debitNoteList.stream()
                            .filter(n -> new DateTime(n.getInsuranceStartDate()).withTimeAtStartOfDay()
                                    .equals(new DateTime(record.getEffectiveDate()).withTimeAtStartOfDay()) &&
                                    n.getPassportNumber().equalsIgnoreCase(record.getPassportNum()))
                            .findFirst();
                    if (first.isPresent()) {
                        InsuranceNote insuranceNote = first.get();
                        record.setMatchType(InsuranceAuditingRecord.matchType.MATCHED);
                        record.setErpAmount(insuranceNote.getPremium() + insuranceNote.getBasmaCharge());
                        isMatched = true;
                    } else if (dismissedPassports == null || !dismissedPassports.contains(passport)){
                        record.setMatchType(InsuranceAuditingRecord.matchType.UNMATCHED);
                    }
                }

                insuranceAuditingRecordRepository.save(record);
                records.add(record);
            }

            logger.log(Level.INFO, "read addition sheet is done");

            //get third sheet for addition
            sheet = workbook.getSheetAt(2);
            rowIterator = sheet.iterator();

            //reach first data row
            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                String stringCellValue = dataFormatter.formatCellValue(row.getCell(0));
                if (stringCellValue.equalsIgnoreCase("EFFFECTIVEDATE")) break;
            }

            //read rows
            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                if (isRowEmpty(row)) break;

                Date effectiveDate = resolveEffectiveDate(row);
                String name = dataFormatter.formatCellValue(row.getCell(1));
                String passport = dataFormatter.formatCellValue(row.getCell(4));
                Double amount = row.getCell(9).getNumericCellValue();
                amount = NumberFormatter.twoDecimalPoints(amount);
                //create record
                InsuranceAuditingRecord record = new InsuranceAuditingRecord();
                record.setStatement(statement);
                record.setAmount(amount);
                record.setEffectiveDate(new java.sql.Date(effectiveDate.getTime()));
                record.setPassportNum(passport);
                record.setName(name);
                record.setType(InsuranceAuditingRecord.recordType.DELETION);

                invoiceDeletionSum += amount;

                if (dismissedPassports != null && dismissedPassports.contains(passport)){
                    dismissedDeletionSum += amount;
                    logger.log(Level.INFO, "setPassportNum: " + passport + "dismissedDeletionSum: " + amount);
                }

                if(record.getEffectiveDate() == null) {
                    if (dismissedPassports == null || !dismissedPassports.contains(passport)){
                        record.setMatchType(InsuranceAuditingRecord.matchType.UNMATCHED);
                    }
                } else if(records.stream().anyMatch(x->
                        x.getType() == record.getType()
                                && x.getPassportNum().equalsIgnoreCase(record.getPassportNum())
                                && x.getAmount().equals(record.getAmount())
                                && x.getEffectiveDate().equals(record.getEffectiveDate()))) {

                    //duplicated
                    record.setMatchType(InsuranceAuditingRecord.matchType.DUPLICATED);
                } else {
                    Optional<InsuranceNote> first = creditNoteList.stream().filter(n ->
                            new DateTime(n.getInsuranceEndDate()).withTimeAtStartOfDay().equals(new DateTime(record.getEffectiveDate()).withTimeAtStartOfDay())
                                    && n.getPassportNumber().equalsIgnoreCase(record.getPassportNum())
                    ).findFirst();
                    if (first.isPresent()) {
                        InsuranceNote insuranceNote = first.get();
                        record.setMatchType(InsuranceAuditingRecord.matchType.MATCHED);
                        record.setErpAmount(insuranceNote.getPremium() + insuranceNote.getBasmaCharge());
                        isMatched = true;
                    } else if (dismissedPassports == null || !dismissedPassports.contains(passport)){
                        record.setMatchType(InsuranceAuditingRecord.matchType.UNMATCHED);
                    }
                }
                insuranceAuditingRecordRepository.save(record);
                records.add(record);
            }
            logger.log(Level.INFO, "read deletion sheet is done");
        } catch (IOException e) {
            e.printStackTrace();
        }

        //calculate summary

        //ERP Total Amount is the total amount calculated by our insurance invoicing page (additions - deletions)
        Double erpDeletionSum = creditNoteList.stream().mapToDouble(InsuranceNote::getPremium).sum();
        Double erpAdditionSum = debitNoteList.stream().mapToDouble(InsuranceNote::getErpAmount).sum();
        logger.log(Level.INFO, "erp total deletion sum= " + erpDeletionSum + "\nerp total addition sum= " + erpAdditionSum);
        statement.setErpTotalAmount(NumberFormatter.twoDecimalPoints(erpAdditionSum - Math.abs(erpDeletionSum)));

        //Invoice Total Amount is the sum of amounts of all additions in the first sheet - sum of amounts of all deletions in the deletion sheets.
        logger.log(Level.INFO, "erp total deletion sum= " + invoiceAdditionSum + "\nerp total addition sum= " + invoiceDeletionSum);
        statement.setInvoiceTotalAmount(NumberFormatter.twoDecimalPoints(invoiceAdditionSum - Math.abs(invoiceDeletionSum)));

        //Amounts Dismissed is the total sum of dismissed passports in additions - total sum of all dismissed passports in deletions.
        logger.log(Level.SEVERE, "dismissedAdditionSum: " + dismissedAdditionSum);
        logger.log(Level.SEVERE, "dismissedDeletionSum: " + dismissedDeletionSum);
        statement.setDismissedAmount(NumberFormatter.twoDecimalPoints(dismissedAdditionSum - dismissedDeletionSum));

        // ACC-4655
        if(isMatched)
            statement.setStatus(InsuranceAuditingStatementStatus.MATCHED);

        statement.getRecords().addAll(records);
        return insuranceAuditingStatementRepository.save(statement);
    }

    public Date resolveEffectiveDate(Row row) {
        Date effectiveDate = null;
        if(row.getCell(0).getCellType() == 0) {
            effectiveDate = row.getCell(0).getDateCellValue();
        } else if(row.getCell(0).getCellType() == 1) {
            String d = row.getCell(0).getRichStringCellValue().getString();
            try {
                effectiveDate = new SimpleDateFormat("dd/MM/yyyy").parse(d);
            } catch(Exception e) {
                try {
                    effectiveDate = new SimpleDateFormat("dd/M/yyyy").parse(d);
                } catch (Exception e2) {
                    try {
                        effectiveDate = new SimpleDateFormat("d/MM/yyyy").parse(d);
                    } catch (Exception e3) {
                        try {
                            effectiveDate = new SimpleDateFormat("d/M/yyyy").parse(d);
                        } catch (Exception e4) {
                        }
                    }
                }
            }
        }
        return effectiveDate;
    }

    private boolean isRowEmpty(Row row) {
        if (row == null) {
            return true;
        }
        if (row.getLastCellNum() <= 0) {
            return true;
        }
        for (int cellNum = row.getFirstCellNum(); cellNum < row.getLastCellNum(); cellNum++) {
            Cell cell = row.getCell(cellNum);
            if (cell != null && cell.getCellType() != Cell.CELL_TYPE_BLANK && StringUtils.isNotBlank(cell.toString())) {
                return false;
            }
        }
        return true;
    }
}