package com.magnamedia.service.paymentstate.handlers.received;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.notification.AppAction;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.type.AppActionType;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.core.type.FunctionType;
import com.magnamedia.core.type.NavigationType;
import com.magnamedia.entity.*;
import com.magnamedia.extra.CcNotificationTemplateCode;
import com.magnamedia.extra.MvNotificationTemplateCode;
import com.magnamedia.extra.PaymentHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.AccountingEntityPropertyRepository;
import com.magnamedia.repository.ContractPaymentConfirmationToDoRepository;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.scheduledjobs.AccountingModuleMainJob;
import com.magnamedia.service.ContractService;
import com.magnamedia.service.DisableAccountingNotificationService;
import com.magnamedia.service.MessagingService;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static com.magnamedia.module.AccountingModule.*;

/**
 * <AUTHOR> Mahfoud
 *         Created on Dec 05, 2023
 *         ACC-6587
 */
@Service
public class PaymentReceivedNotificationHandler  {

    protected static final Logger logger = Logger.getLogger(PaymentReceivedNotificationHandler.class.getName());

    @Autowired
    private AccountingEntityPropertyRepository accountingEntityPropertyRepository;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private MessagingService messagingService;
    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private ContractRepository contractRepository;
    @Autowired
    private ContractService contractService;

    public void handlePayment(Payment payment) {
        if (validate(payment)) {
            try {
                execute(payment);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        }
    }

    private boolean validate(Payment entity) {
        logger.info("payment id: " + entity.getId() +
                "; status: " + entity.getStatus() );

        return !Setup.getRepository(PicklistItemRepository.class)
                .findOne(entity.getTypeOfPayment().getId()).hasTag("refund") && entity.getAmountOfPayment() > 0.0;
    }

    private void execute(Payment entity) throws JsonProcessingException {
        logger.info("payment id: " + entity.getId());
        Contract contract = contractRepository.findOne(entity.getContract().getId());
        if (contract.getReceivePaymentNotifications() != null &&
                contract.getReceivePaymentNotifications()
                        .equals(Contract.ReceivePaymentNotificationsStatus.DISABLED)) return;
        int first_X_Payments = ContractService.isPreCollectedSalary(contract) ?
                Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PARAMETER_SEND_NOTIFICATION_FOR_PRE_COLLECTED_FIRST_X_RECEIVED_PAYMENTS)) :
                Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PARAMETER_SEND_NOTIFICATION_FOR_FIRST_X_RECEIVED_PAYMENTS)) ;

        int countOfPayments = getMonthlyPaymentCount(contract) + 1;
        logger.info("payments count: " + countOfPayments);
        if (contract.getReceivePaymentNotifications() == null) {

            Map<String, Object> m = new HashMap<>();
            m.put("id", contract.getId());
            m.put("receivePaymentNotifications", countOfPayments >= first_X_Payments ?
                    Contract.ReceivePaymentNotificationsStatus.DISABLED :
                    Contract.ReceivePaymentNotificationsStatus.AUTOMATICALLY_ENABLED);
            contractService.updateContractFromClientMgtAsync(m);

            logger.info("Payment is after first_X_Payments: " +
                    m.get("receivePaymentNotifications").equals(Contract.ReceivePaymentNotificationsStatus.DISABLED));

            if (m.get("receivePaymentNotifications").equals(Contract.ReceivePaymentNotificationsStatus.DISABLED)) return;
        } else if(countOfPayments >= first_X_Payments  &&
                Contract.ReceivePaymentNotificationsStatus.AUTOMATICALLY_ENABLED.equals(contract.getReceivePaymentNotifications())) {

            logger.info("Payment is equals x month");
            Map<String, Object> m = new HashMap<>();
            m.put("id", contract.getId());
            m.put("receivePaymentNotifications", Contract.ReceivePaymentNotificationsStatus.DISABLED);
            contractService.updateContractFromClientMgtAsync(m);

            Setup.getApplicationContext()
                    .getBean(DisableAccountingNotificationService.class)
                    .moveReceivePaymentNotificationsToInbox(contract);
        }

        AccountingEntityProperty a = accountingEntityPropertyRepository
                .findByKeyAndOriginAndDeletedFalseAndPurposeEquals(AccountingModuleMainJob.SEND_MESSAGE, contract,
                        "PAYMENT_RECEIVED_NOTIFICATION");
        if (a == null) {
            //ACC-6731 case #2
            if (!entity.getTypeOfPayment().getCode().equals("monthly_payment")) return;

            int waitingMinutes = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PARAMETER_SEND_NOTIFICATION_FOR_RECEIVED_PAYMENTS_WAITING_MINUTES));
            List<String> payments = paymentRepository.getNonMonthlyPaymentReceivedDateBetween(contract,
                    new DateTime().minusMinutes(waitingMinutes).toDate() ,
                    new DateTime().toDate()).stream()
                    .filter(p -> !p.getTypeOfPayment().hasTag("refund"))
                    .map(payment -> payment.getId().toString())
                    .collect(Collectors.toList());
            payments.add(entity.getId().toString());

            DateTime sendDate = new DateTime();
            if (new DateTime().isAfter(new DateTime().withTimeAtStartOfDay().withHourOfDay(23))) {
                sendDate = new DateTime().plusDays(1).withTimeAtStartOfDay().withHourOfDay(8);
            } else if (new DateTime().isBefore(new DateTime().withTimeAtStartOfDay().withHourOfDay(7))) {
                sendDate = new DateTime().withTimeAtStartOfDay().withHourOfDay(8);
            }

            a = new AccountingEntityProperty();
            a.setKey(AccountingModuleMainJob.SEND_MESSAGE);
            a.setOrigin(contract);
            a.setMessageSendDate(sendDate.toDate());
            a.setPurpose("PAYMENT_RECEIVED_NOTIFICATION");
            a.setValue(objectMapper.writeValueAsString(new HashMap<String, String>() {{
                put("templateBaseName", "PAYMENT_RECEIVED_NOTIFICATION");
                put("payments", objectMapper.writeValueAsString(payments));
            }}));
        } else {
            Map<String, String> m = objectMapper.readValue(a.getValue(), Map.class);
            List<String> l = (List<String>) objectMapper.readValue(m.get("payments"), List.class);
            l.add(entity.getId().toString());
            m.put("payments", objectMapper.writeValueAsString(l));
            a.setValue(objectMapper.writeValueAsString(m));
        }

        accountingEntityPropertyRepository.save(a);
    }
    public void sendPaymentReceivedNotification(Long contractId) throws JsonProcessingException {
        Contract contract = contractRepository.findOne(contractId);

        AccountingEntityProperty a = accountingEntityPropertyRepository.findByKeyAndOriginAndDeletedFalse(
                Contract.PAYMENT_RECEIVED_NOTIFICATION, contract);
        if (a == null) return;

        List<String> ids = ((List<String>)objectMapper.readValue(a.getValue(), List.class));
        sendPaymentReceivedNotification(contract, ids);

        accountingEntityPropertyRepository.deleteByKeyAndOrigin(Contract.PAYMENT_RECEIVED_NOTIFICATION, contract);
    }

    public void sendPaymentReceivedNotification(Contract contract, List<String> ids) {

        List<Payment> l = paymentRepository.findAll(ids.stream().map(Long::valueOf).collect(Collectors.toList()))
                .stream()
                .filter(p -> !Setup.getRepository(ContractPaymentConfirmationToDoRepository.class)
                        .existsByGeneratedPaymentIdAndIgnoreSendNotificationTrue(p.getId()))
                .collect(Collectors.toList()); // ACC-7573

        if (l.isEmpty()) return;

        l = l.stream().sorted(Comparator.comparing(Payment::getDateOfPayment))
                .sorted(Comparator.comparing(p -> p.getTypeOfPayment().getId()))
                .collect(Collectors.toList());

        Setup.getApplicationContext()
                .getBean(DisableAccountingNotificationService.class)
                .moveReceivePaymentNotificationsToInbox(contract);

        Template t = getTemplate(l, contract);
        if (t == null) return;

            if (t.getName().equals(MvNotificationTemplateCode.MV_PAYMENT_RECEIVED_ON_X_MONTH_NOTIFICATION.toString())) {
                triggerSendingTheMessageFromPayroll(contract, l);
                return;
            }

            Map<String, String> parameters = sendPaymentReceivedNotificationFillParameters(l, contract);
            Map<String, AppAction> cta = sendPaymentReceivedNotificationFillContext(t, contract);

        messagingService.sendMessageToClient(contract,
                parameters,
                cta,
                contract.getId(),
                contract.getEntityType(),
                t);
    }

    private void triggerSendingTheMessageFromPayroll(Contract c, List<Payment> payments) {
        double totalAmount = payments.stream().mapToDouble(Payment::getAmountOfPayment).sum();
        double monthlyFee = payments.stream().filter(p -> PaymentHelper.isMonthlyPayment(p.getTypeOfPayment()))
                .mapToDouble(Payment::getVisaFees).sum();
        double totalWorkerSalary = payments.stream().filter(p -> PaymentHelper.isMonthlyPayment(p.getTypeOfPayment()))
                .mapToDouble(p -> p.getWorkerSalaryWithoutVAT() != null ?
                        p.getWorkerSalaryWithoutVAT() :
                                p.getWorkerSalary() != null ?
                        p.getWorkerSalary() : 0D).sum();

        Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "triggerSendingTheMessageFromPayroll_" + c.getId(),
                        "payroll",
                        "messagingService",
                        "sendPaymentWhatsappMessageBGT")
                        .withRelatedEntity("Contract", c.getId())
                        .withParameters(
                                new Class[] {Long.class, Boolean.class, Double.class, Double.class, Double.class},
                                new Object[] {c.getId(), ContractService.isPreCollectedSalary(c), totalAmount, monthlyFee, totalWorkerSalary})
                        .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                        .build());
    }

    private Map<String, String> sendPaymentReceivedNotificationFillParameters(List<Payment> l, Contract contract) {
        Map<String, String> parameters = new HashMap<>();

        parameters.put("cc_app_download_url",
                Setup.getParameter(Setup.getModule(AccountingModule.CLIENT_MGMT_MODULE_CODE),
                        AccountingModule.PARAMETER_CLIENT_MGMT_CC_APP_DOWNLOAD_URL));
        parameters.put("total_amount", PaymentHelper.df.format(((Double) l.stream().mapToDouble(Payment::getAmountOfPayment)
                .sum())
                .intValue()));

        List<Date> futurePayments = paymentRepository.getMonthlyPaymentAfterPaidEndDate(contract, PaymentStatus.PDC, contract.getPaidEndDate(), PageRequest.of(0,1));
        if (futurePayments.isEmpty()){
            futurePayments = paymentRepository.getMonthlyPaymentAfterPaidEndDate(contract, PaymentStatus.PRE_PDP, contract.getPaidEndDate(), PageRequest.of(0,1));
        }
        parameters.put("monthly_payment_scheduled_date", futurePayments.isEmpty() ?
                "" :
                "\nYour next monthly payment is scheduled on " +
                        new LocalDate(futurePayments.get(0)).toString("dd-MMM-yyyy") + ".");

        List<String> paymentsDescriptionArray = new ArrayList<>();
        for (int i = 1; i<= l.size(); i++) {
            String month = new DateTime(l.get(i - 1).getDateOfPayment()).toString("MMMM");
            String worker_type = contract.getWorkerType() != null && contract.getWorkerType().getCode().equals("private_driver") ?
                    "driver" : "maid";
            String defaultDescription = "a payment of AED " + l.get(i - 1).getAmountOfPayment().intValue();
            switch (l.get(i - 1).getTypeOfPayment().getCode()) {
                case "same_day_recruitment_fee":
                    if (l.get(i -1).getSubType() != null) {
                        switch (l.get(i -1).getSubType().getCode()) {
                            case "2_year_visa_fee":
                                paymentsDescriptionArray.add("the cost of the 2-year visa, Emirates ID, medical, WPS program " +
                                        "registration and 1-year health insurance + VAT");
                                break;
                            case "agency_fee":
                                paymentsDescriptionArray.add("the agency fee + VAT");
                                break;
                            default:
                                paymentsDescriptionArray.add(defaultDescription);
                                break;
                        }
                    } else {
                        paymentsDescriptionArray.add("the cost of the 2-year visa, Emirates ID, medical, WPS program " +
                                "registration and 1-year health insurance + VAT");
                    }
                    break;
                case "monthly_payment":
                    if (contract.isMaidVisa()) {
                        if (l.get(i -1).getIncludeWorkerSalary()) {
                            paymentsDescriptionArray.add("your " + worker_type + "'s salary");
                        }
                        paymentsDescriptionArray.add("the cost of the monthly fee for your " + worker_type + "'s visa benefits for " + month);
                    } else if (contract.isMaidCc()) {
                        if (l.get(i - 1).getIsProRated()) {
                            int x_days = Days.daysBetween(new LocalDate(l.get(i - 1).getDateOfPayment()),
                                    new LocalDate(l.get(i - 1).getDateOfPayment()).dayOfMonth().withMaximumValue()).getDays() + 1;
                            paymentsDescriptionArray.add("your "+ worker_type + " service fee for the last " + x_days + " days of " + month + " + VAT");
                        } else {
                            paymentsDescriptionArray.add("your " + worker_type + " service fee for " + month + " + VAT");
                        }
                    }
                    break;
                case "transfer_fee":
                case "matching_fee":
                    paymentsDescriptionArray.add("the agency fee + VAT");
                    break;
                case "insurance":
                    paymentsDescriptionArray.add("the cost of the second year health insurance for your "+ worker_type + " + VAT");
                    break;
                default:
                    paymentsDescriptionArray.add(defaultDescription);
                    break;
            }
        }

        StringBuilder paymentsDescription = new StringBuilder();
        for (int i = 1; i <= paymentsDescriptionArray.size(); i++) {
            paymentsDescription.append(paymentsDescriptionArray.get(i - 1));
            if ((paymentsDescriptionArray.size() >= 2 && i == (paymentsDescriptionArray.size() - 1))) {
                paymentsDescription.append(" and ");
            }
            if (paymentsDescriptionArray.size() > 2 && i < (paymentsDescriptionArray.size() -1)) {
                paymentsDescription.append(", ");
            }
        }
        parameters.put("payments_description", paymentsDescription + ".");

        return parameters;
    }

    private Map<String, AppAction> sendPaymentReceivedNotificationFillContext(Template t, Contract contract) {
        Map<String, AppAction> cta = new HashMap<>();

        AppAction a = new AppAction();
        a.setType(AppActionType.BUTTON);
        a.setFunctionType(FunctionType.NAVIGATE);
        a.setNavigationType(NavigationType.INAPP);
        a.setAppRouteArguments(new HashMap<String, String>() {{
            put("contractId", contract.getId().toString());
            put("contractUuid", contract.getUuid());
        }});
        switch (t.getName()) {
            case "CC_PAYMENT_RECEIVED_FROM_ONE_TO_X_MINUS_ONE_MONTHS_NOTIFICATION":
            case "MV_PAYMENT_RECEIVED_FROM_ONE_TO_X_MINUS_ONE_MONTHS_NOTIFICATION":
                a.setText("My Payments to maids.cc");
                a.setAppRouteName("/monthlyPaymentNewHomePage");
                cta.put("my_payments_to_maids_cc", a);
                break;
            case "CC_PAYMENT_RECEIVED_ON_X_MONTH_NOTIFICATION":
            case "MV_PAYMENT_RECEIVED_ON_X_MONTH_NOTIFICATION":
                a.setText("Enable Payment Notification");
                a.setAppRouteName("/successPaymentNotificationsUpdate");
                cta.put("enable_payment_notification", a);
                break;
        }

        return cta;
    }

    private Template getTemplate(List<Payment> l, Contract contract) {
        String tName;

        int countOfPayment = getMonthlyPaymentCount(contract);
        int first_X_Payments = ContractService.isPreCollectedSalary(contract) ?
                Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PARAMETER_SEND_NOTIFICATION_FOR_PRE_COLLECTED_FIRST_X_RECEIVED_PAYMENTS)) :
                Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PARAMETER_SEND_NOTIFICATION_FOR_FIRST_X_RECEIVED_PAYMENTS));

        if (countOfPayment < first_X_Payments) {
            tName = contract.isMaidCc() ?
                    CcNotificationTemplateCode.CC_PAYMENT_RECEIVED_FROM_ONE_TO_X_MINUS_ONE_MONTHS_NOTIFICATION.toString() :
                    MvNotificationTemplateCode.MV_PAYMENT_RECEIVED_FROM_ONE_TO_X_MINUS_ONE_MONTHS_NOTIFICATION.toString();

        } else if (countOfPayment == first_X_Payments) {
            tName = contract.isMaidCc() ?
                    CcNotificationTemplateCode.CC_PAYMENT_RECEIVED_ON_X_MONTH_NOTIFICATION.toString() :
                    MvNotificationTemplateCode.MV_PAYMENT_RECEIVED_ON_X_MONTH_NOTIFICATION.toString();
        } else {
            tName = contract.isMaidCc() ?
                    CcNotificationTemplateCode.CC_PAYMENT_RECEIVED_FROM_X_PLUS_ONE_ONWARDS_MONTHS_NOTIFICATION.toString() :
                    MvNotificationTemplateCode.MV_PAYMENT_RECEIVED_FROM_X_PLUS_ONE_ONWARDS_MONTHS_NOTIFICATION.toString();
        }

        return TemplateUtil.getTemplate(tName);
    }

    private int getMonthlyPaymentCount(Contract contract){
         return paymentRepository.findTheCountOfMonthlyPaymentsReceived(contract);
    }
}
