package com.magnamedia.service.RiskDocumentsManagement;

import com.magnamedia.core.entity.Attachment;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagementTodo;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagementVisitHistory;
import com.magnamedia.repository.RiskDocumentsManagement.RiskDocumentsManagementVisitHistoryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RiskDocumentsManagementVisitHistoryService {

    @Autowired
    private RiskDocumentsManagementVisitHistoryRepository riskDocumentsManagementVisitHistoryRepository;

    public void addVisitHistoryForRelatedDocument(RiskDocumentsManagementTodo todo, List<Attachment> attachments) {

        RiskDocumentsManagementVisitHistory h = new RiskDocumentsManagementVisitHistory();

        h.setFrequencyOfVisit(todo.getRiskDocumentsManagement().getFrequencyOfVisit());
        h.setFrequencyType(todo.getRiskDocumentsManagement().getFrequencyType());
        h.setServiceReportName(todo.getRiskDocumentsManagement().getServiceReportName());
        h.setDateOfVisit(todo.getRiskDocumentsManagement().getNextVisitDate());
        h.setAttachments(attachments);
        h.setRiskDocumentsManagement(todo.getRiskDocumentsManagement());

        riskDocumentsManagementVisitHistoryRepository.save(h);
    }
}