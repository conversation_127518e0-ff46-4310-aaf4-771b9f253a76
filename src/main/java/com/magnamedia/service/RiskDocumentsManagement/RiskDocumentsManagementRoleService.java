package com.magnamedia.service.RiskDocumentsManagement;

import com.magnamedia.core.entity.User;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagementRole;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class RiskDocumentsManagementRoleService {

    public static List<User> getUsersFromRoles(List<RiskDocumentsManagementRole> roles) {
        // Get all users from roles
        List<User> allUsers = new ArrayList<>();
        roles.forEach(r -> allUsers.addAll(r.getResponsibleUser()));

        // Use a map to ensure uniqueness by ID
        Map<Long, User> uniqueUsers = new HashMap<>();
        allUsers.forEach(user -> uniqueUsers.putIfAbsent(user.getId(), user));

        // Return the list of unique users
        return new ArrayList<>(uniqueUsers.values());
    }

    public static String getUsersFullNameFromRoles(List<RiskDocumentsManagementRole> roles) {
        // Get unique users from roles
        List<User> uniqueUsers = getUsersFromRoles(roles);

        // Filter out users with null or empty full names and join them
        return uniqueUsers.stream()
                .filter(u -> u.getFullName() != null && !u.getFullName().isEmpty())
                .map(User::getFullName)
                .collect(Collectors.joining(", "));
    }

    public static String getEmailsFromUsers(List<RiskDocumentsManagementRole> roles) {
        // Get unique users from roles
        List<User> uniqueUsers = getUsersFromRoles(roles);

        // Filter out users with null or empty emails and join them
        return uniqueUsers.stream()
                .filter(u -> u.getEmail() != null && !u.getEmail().isEmpty())
                .map(User::getEmail)
                .collect(Collectors.joining(","));
    }
}