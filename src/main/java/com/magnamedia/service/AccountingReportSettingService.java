package com.magnamedia.service;


import com.magnamedia.entity.AccountingReportSetting;
import com.magnamedia.repository.AccountingReportSettingRepository;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.logging.Logger;


@Service
public class AccountingReportSettingService {

    protected static final Logger logger = Logger.getLogger(AccountingReportSettingService.class.getName());

    @Autowired
    private AccountingReportSettingRepository accountingReportSettingRepository;

    public void updateQueries(
            MultipartFile file, AccountingReportSetting.ReportCategory reportCategory) throws IOException {

        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        XSSFSheet sheet = workbook.getSheetAt(0);

        if (sheet == null) {
            logger.warning("Sheet not found");
            return;
        }

        for (Row row : sheet) {

            if (row.getRowNum() == 0) continue;
            try {
                logger.info("Row Num: " + row.getRowNum());

                AccountingReportSetting.QueryCategory queryCategory = AccountingReportSetting.QueryCategory.valueOf(row.getCell(2).getStringCellValue());
                logger.info("queryCategory: " + queryCategory);
                AccountingReportSetting a = accountingReportSettingRepository
                        .findFirstByReportCategoryAndQueryCategory(reportCategory, queryCategory);
                if (a == null) {
                    a = new AccountingReportSetting();
                    a.setReportCategory(reportCategory);
                    a.setQueryCategory(queryCategory);
                }

                a.setQuery(row.getCell(3) == null ? null : row.getCell(3).getStringCellValue());
                accountingReportSettingRepository.save(a);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    public void saveNewQuery(
            AccountingReportSetting.ReportCategory reportCategory, AccountingReportSetting.QueryCategory queryCategory, String query) {

        AccountingReportSetting accountingReportSetting = accountingReportSettingRepository
                .findFirstByReportCategoryAndQueryCategory(reportCategory, queryCategory);
        if (accountingReportSetting == null) {
            accountingReportSetting = new AccountingReportSetting();
            accountingReportSetting.setReportCategory(reportCategory);
            accountingReportSetting.setQueryCategory(queryCategory);
        }
        accountingReportSetting.setQuery(query);
        accountingReportSettingRepository.save(accountingReportSetting);
    }
}
