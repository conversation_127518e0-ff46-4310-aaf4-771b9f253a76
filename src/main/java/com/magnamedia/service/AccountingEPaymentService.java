package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.entity.epayment.EPaymentTransaction;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.epayment.ClientFormModeType;
import com.magnamedia.core.helper.epayment.EPaymentService;
import com.magnamedia.core.helper.epayment.ETransactionStatus;
import com.magnamedia.core.helper.epayment.PaymentSuccessApplyService;
import com.magnamedia.core.repository.epayment.EPaymentTransactionRepository;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.entity.ContractPayment;
import com.magnamedia.entity.ContractPaymentConfirmationToDo;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.helper.PayTabsHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.repository.ClientRefundTodoRepository;
import com.magnamedia.repository.ContractPaymentConfirmationToDoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.logging.Logger;

import static com.magnamedia.entity.ContractPaymentConfirmationToDo.Source.DD_FLOWS_DYNAMIC_WEB_PAGE;

/**
 * <AUTHOR> Mahfoud
 * Created on Oct 14, 2023
 * ACC-6513
 */

@Service
public class AccountingEPaymentService extends PaymentSuccessApplyService {
    private static final Logger logger = Logger.getLogger(AccountingEPaymentService.class.getName());

    @Autowired
    private ContractPaymentConfirmationToDoRepository contractPaymentConfirmationToDoRepository;
    @Autowired
    private BackgroundTaskService backgroundTaskService;
    @Autowired
    private PayTabsHelper payTabsHelper;
    @Autowired
    private ContractPaymentConfirmationToDoService contractPaymentConfirmationToDoService;
    @Autowired
    private EPaymentService ePaymentService;
    @Autowired
    private EPaymentTransactionRepository ePaymentTransactionRepository;
    @Autowired
    private ClientRefundService clientRefundService;

    @Override
    @Transactional
    public EPaymentTransaction initializeTransaction(Map<String, Object> context) {

        try {
            Map<String, String> customParams = new HashMap<>();
            ContractPaymentConfirmationToDo toDo = contractPaymentConfirmationToDoRepository.findByUuid((String) context.get("todoUuid"));
            if (toDo == null) {
                logger.info("todo uuid: " + context.get("todoUuid") + " not found");
                return null;
            }
            logger.info("start create transaction for todo uuid: " + context.get("todoUuid"));

            customParams.put("todoUuid", toDo.getUuid());

            String identifier = getToDoIdentifier(toDo);
            EPaymentTransaction ePaymentTransactions = Setup.getRepository(EPaymentTransactionRepository.class)
                    .findTop1WithLockingByLocalReferenceAndTransactionStatusAndTransactionMode(identifier,
                                    ETransactionStatus.CREATED,
                                    ClientFormModeType.valueOf((String) context.get("hostedPageMode"))); // CORE-1645

            if (ePaymentTransactions != null) {
                logger.info("transaction id: " + ePaymentTransactions.getId());
                return ePaymentTransactions;
            }

            logger.info("create new a transaction");
            EPaymentTransaction ePaymentTransaction = Setup.getApplicationContext()
                    .getBean(EPaymentService.class)
                    .createTransaction(toDo.getId(),
                            toDo.getEntityType(),
                            identifier,
                            toDo.getTotalAmount(),
                            toDo.getDescription(),
                            "accountingEPaymentService",
                            customParams);

            String transactionString = payTabsHelper.convertToString(ePaymentTransaction);
            logger.info("transactionString Object: " + transactionString);

            toDo.setCreditCardInfo(transactionString);
            contractPaymentConfirmationToDoRepository.save(toDo);

            return ePaymentTransaction;

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Transactional
    public void applyBusinessLogic(Long transactionId) {
        applyBusinessLogic(ePaymentTransactionRepository.findOne(transactionId));
    }

    @Override
    @Transactional
    protected Map<String, Object> applyBusinessLogic(EPaymentTransaction transaction) {
        logger.info("transaction id: " + transaction.getId() + "; related to todo id: " + transaction.getRelatedEntityId());

        ContractPaymentConfirmationToDo todo = contractPaymentConfirmationToDoRepository.findOne(transaction.getRelatedEntityId());
        paidViaPayTabs(todo, null, true);
        return new HashMap<>();
    }

    @Override
    @Transactional
    protected Map<String, Object> applyOnFailBusinessLogic(EPaymentTransaction transaction) {
        logger.info("transaction id: " + transaction.getId() + "; related to todo id: " + transaction.getRelatedEntityId());

        ContractPaymentConfirmationToDo todo = contractPaymentConfirmationToDoRepository.findOne(transaction.getRelatedEntityId());
        paidViaPayTabs(todo, null,false);
        return new HashMap<>();
    }

    @Override
    @Transactional
    public Boolean validatePaymentAlreadyCreated(Map<String, Object> context) {
        ContractPaymentConfirmationToDo toDo = contractPaymentConfirmationToDoRepository.findByUuid((String) context.get("todoUuid"));
        if (toDo == null) {
            logger.info("todo uuid: " + context.get("todoUuid") + " not found");
            return false;
        }

        return isValid(toDo);
    }

    @Override
    @Transactional
    public Boolean validatePaymentAlreadyPaid(
            Long transactionId, Long relatedEntityId, String RelatedEntityType,
            String localReference, String remoteReference) {

        ContractPaymentConfirmationToDo toDo = contractPaymentConfirmationToDoRepository.findOne(relatedEntityId);
        if (toDo == null) {
            logger.info("todo id: " + relatedEntityId + " not found");
            return false;
        }

        return isValid(toDo);
    }

    private boolean isValid(ContractPaymentConfirmationToDo toDo) {
        boolean isValid = !toDo.isShowOnERP() && !contractPaymentConfirmationToDoService.isTodoExpired(toDo);

        // ACC-8851 After Web payment page
        if (isValid && toDo.getSource().equals(DD_FLOWS_DYNAMIC_WEB_PAGE)) {
            // Check if ContractPaymentConfirmationToDo contains any payments related to canceled DDs.
            boolean requiredPaymentsChanged = toDo.getContractPaymentList()
                    .stream()
                    .noneMatch(w ->
                            QueryService.existsEntity(ContractPayment.class,
                                    "join e.directDebit dd",
                                "e.contractPaymentTerm.id = :p0 and e.paymentMethod = :p1 and e.paymentType.code = :p2 and " +
                                        "e.date = :p3 and e.amount = :p4 and " +
                                        "((dd.category = 'A' and dd.MStatus not in :p5) or (dd.category = 'B' and dd.status not in :p5))",
                                new Object[] {
                                        toDo.getContractPaymentTerm().getId(), PaymentMethod.DIRECT_DEBIT,
                                        w.getPaymentType().getCode(), w.getPaymentDate(), w.getAmount(),
                                        DirectDebitService.notAllowedStatusesWithConfirmed
                                }));
            logger.info("requiredPaymentsChanged: " + requiredPaymentsChanged);
            isValid = !requiredPaymentsChanged;
        }
        logger.info("todo id: " + toDo.getId() + "; isValid: " + isValid);

        return isValid;
    }

    @Transactional
    public String paidViaPayTabs(ContractPaymentConfirmationToDo toDo, String respMessage, boolean paidSuccess) {
        String redirectPageAction;
        //5156
        if (toDo == null) redirectPageAction = "3"; // Not Found
        else if (toDo.isShowOnERP()) redirectPageAction = "2"; // Already done
        else {
            logger.info("PAID Success: " + paidSuccess);

            backgroundTaskService.create(new BackgroundTask.builder(
                    "paidViaPayTabs_" + toDo.getId(),
                    "accounting",
                    "contractPaymentConfirmationToDoService",
                    "paidViaPayTabs")
                    .withRelatedEntity("ContractPaymentConfirmationToDo", toDo.getId())
                    .withParameters(
                            new Class[] { Long.class, Boolean.class, String.class, String.class },
                            new Object[] { toDo.getId(), paidSuccess, respMessage, paidSuccess ? "paidSuccess" : "paidFailure" })
                    .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                    .withDelay(toDo.getSource() != null && toDo.getSource()
                            .equals(ContractPaymentConfirmationToDo.Source.SWITCH_NATIONALITY) ? (3 * 60 * 1000L) : 0L)
                    .withRetryDelay(10 * 60 * 1000L) // ACC-7243
                    // CORE-1488
                    // AS per Ibrahim AdDandan
                    // if true then core will prevent add a new background task (throws exception)
                    // if there is already one in DB with same name and parameters triggered by the same user
                    // and its status not finished and not failed
                    .withPreventDuplicateExecution()
                    .build());

            redirectPageAction = paidSuccess ? "1" : "4";
        }

        return "/" + Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_PAYTAB_PAYMENT_REDIRECT_AFTER_ACTION_PAGE) +
                "?action=" + redirectPageAction;
    }

    public static String getToDoIdentifier(ContractPaymentConfirmationToDo toDo) {

        return toDo.getSource().toString().toLowerCase() + "_" +
                ((toDo.getContractPaymentList().stream().anyMatch(w -> w.getReplacedBouncedPaymentId() != null)) ?
                        "bounced_" : "") +
                (toDo.getContractPaymentList().stream().anyMatch(w -> w.getReplacedBouncedPaymentId() == null) ?
                        "regular_" : "") +
                toDo.getId();
    }

    public void applyOnRefundedBusinessLogic(Long transactionId) {

        applyOnRefundedBusinessLogic(ePaymentTransactionRepository.findOne(transactionId));
    }

    @Transactional
    @Override
    protected Map<String, Object> applyOnRefundedBusinessLogic(EPaymentTransaction transaction) {
        logger.info("transaction id: " + transaction.getId() +
                "; transReference: " + transaction.getTransReference() +
                "; payment confirmation id: " + transaction.getRelatedEntityId());

        Map<String, Object> m = getRefundConfirmationToDo(transaction);
        if(m.get("clientRefundToDo") == null || m.get("refundConfirmationToDo") == null) return new HashMap<>();

        ClientRefundToDo clientRefundToDo = (ClientRefundToDo) m.get("clientRefundToDo");

        logger.info("clientRefundToDo id: " + clientRefundToDo.getId() +
                "; confirmation id: " + clientRefundToDo.getContractPaymentConfirmationToDo().getId());

        // If refund approval process is not finished  => retry call ApplyOnRefundedBusinessLogic after finished (recursive call using BGT)
        if (!clientRefundService.checkFinishedBGTRefundOrRetryRefundedBusinessLogic(transaction, clientRefundToDo)) {
            logger.info("Refund approval process is not finished -> retry later");
            return new HashMap<>();
        }

        switch (transaction.getProvider()) {
            case PAYTABS:
                clientRefundToDo.getContractPaymentConfirmationToDo().setPayTabsResponseMessage(transaction.getHasError());
                break;
            case CHECKOUT:
                clientRefundToDo.getContractPaymentConfirmationToDo().setCheckoutResponseMessage(transaction.getHasError());
                break;
        }

        if (transaction.getTransactionStatus().equals(ETransactionStatus.REFUNDED)) {

            clientRefundService.closeClientRefundTodo(clientRefundToDo, "APPROVED");
            contractPaymentConfirmationToDoService.linkRefundPaymentWithConfirmationToDo(clientRefundToDo);
            logger.info("refund done");
        }
        contractPaymentConfirmationToDoRepository.save(clientRefundToDo.getContractPaymentConfirmationToDo());

        return new HashMap<>();
    }

    public Map<String, Object> getRefundConfirmationToDo(EPaymentTransaction transaction){
        ClientRefundTodoRepository clientRefundTodoRepository = Setup.getRepository(ClientRefundTodoRepository.class);
        Map<String, Object> m = new HashMap<String, Object>(){{
            put("clientRefundToDo", null);
            put("refundConfirmationToDo", null);
        }};
        if (!transaction.getRelatedEntityType().equals("ContractPaymentConfirmationToDo")) return m;
        ContractPaymentConfirmationToDo t = contractPaymentConfirmationToDoRepository.findOne(transaction.getRelatedEntityId());
        if (t == null) return m;

        logger.info("ContractPaymentConfirmationToDo id: " + t.getId());
        m.put("refundConfirmationToDo", t);

        List<ClientRefundToDo> l = clientRefundTodoRepository.findRefundByConfirmationToDo(t.getId());
        if (!l.isEmpty()){
            m.put("clientRefundToDo", l.get(0));
            return m;
        }

        l = clientRefundTodoRepository.findByContractAndTransferReferenceAndAmountAndPending(t.getContractPaymentTerm().getContract(),
                        transaction.getTransReference(), transaction.getAmount());
        if (l.isEmpty()) return m;

        m.put("clientRefundToDo", l.get(0));
        return m;
    }

    public EPaymentTransaction captureRecurringPayment(String sourceId, ContractPaymentConfirmationToDo toDo) {
        logger.info("sourceId: " + sourceId + "; todoUuid" + toDo.getUuid() + "; todo id: " + toDo.getId());
        EPaymentTransaction newEPaymentTransaction = new EPaymentTransaction();

        newEPaymentTransaction.setRelatedEntityId(toDo.getId());
        newEPaymentTransaction.setRelatedEntityType(toDo.getEntityType());
        newEPaymentTransaction.setLocalReference(getToDoIdentifier(toDo));
        newEPaymentTransaction.setAmount(toDo.getTotalAmount());
        newEPaymentTransaction.setCallbackServiceName("accountingEPaymentService");

        EPaymentTransaction captureTransaction = ePaymentService.captureRecurringPayment(newEPaymentTransaction, sourceId);

        logger.info("Status: " + (captureTransaction != null ? captureTransaction.getTransactionStatus() : "NULL") +
                "; Has error: " + (captureTransaction != null ? captureTransaction.getHasError() : "NULL"));

        return captureTransaction;
    }
}