package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.entity.template.ChannelSpecificSetting;
import com.magnamedia.core.helper.Shortener;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.notification.AppAction;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.core.repository.SmsRepository;
import com.magnamedia.core.type.*;
import com.magnamedia.core.type.template.ChannelSpecificSettingType;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.entity.workflow.PayrollAccountantTodo;
import com.magnamedia.extra.CcNotificationTemplateCode;
import com.magnamedia.extra.PaymentHelper;
import com.magnamedia.extra.MvNotificationTemplateCode;
import com.magnamedia.extra.StringUtils;
import com.magnamedia.extra.Utils;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PushNotificationHelper;
import com.magnamedia.helper.StringHelper;
import com.magnamedia.helper.UaePhoneNormlizer;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.LocalTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Time;
import java.sql.Timestamp;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class FlowProcessorMessagingService {
    private static final Logger logger = Logger.getLogger(FlowProcessorMessagingService.class.getName());

    @Autowired
    private DDMessagingRepository ddMessagingRepository;
    @Autowired
    private Shortener shortener;
    @Autowired
    private ContractPaymentConfirmationToDoRepository contractPaymentConfirmationToDoRepository;
    @Autowired
    private Utils utils;
    @Autowired
    private MessagingService messagingService;
    @Autowired
    private PushNotificationHelper pushNotificationHelper;
    @Autowired
    private PicklistItemRepository itemRepository;
    @Autowired
    private ClientRefundTodoRepository clientRefundTodoRepository;
    @Autowired
    private PayrollAccountantTodoRepository payrollAccountantTodoRepository;
    @Autowired
    private ClientRepository clientRepository;
    @Autowired
    private DisablePushNotificationRepository disablePushNotificationRepository;
    @Autowired
    private OneMonthAgreementFlowService oneMonthAgreementFlowService;
    @Autowired
    private CreditCardOfferService creditCardOfferService;
    @Autowired
    private PaymentTypeDescriptionService paymentTypeDescriptionService;

    public void sendDdMessage(
            DDMessagingContract ddMessagingContract,
            ContractPaymentTerm contractPaymentTerm,
            Map<String, String> parameters,
            Map<String, AppAction> cta,
            DDMessagingToDo ddMessagingToDo) {

        Client client = contractPaymentTerm.getContract().getClient();
        Contract contract = contractPaymentTerm.getContract();
        Housemaid maid = contractPaymentTerm.getContract().getHousemaid();

        Long ownerId = Long.valueOf(parameters.get("ownerId"));
        String ownerType = parameters.get("ownerType");

        if (client != null) {
            if (ddMessagingContract.getSendToClient()) {
                disableOldNotificationAfterSend(ddMessagingContract.getDdMessaging(), contract);

                processClientMessage(ddMessagingContract, contractPaymentTerm,
                        ddMessagingContract.getClientTemplate(), parameters,  cta, ddMessagingToDo);
                updateLastMessageDate(parameters);
            }
        }

        if (maid != null) {
            if (ddMessagingContract.getSendToMaid()) {
                // acc-2677 close notifications related to this payment
                List<PushNotification> maidNotifications = pushNotificationHelper.getByRecepientTypeAndIdOrderByCreationDesc(
                        "Housemaid", maid.getId());

                pushNotificationHelper.stopDisplaying(maidNotifications);

                logger.info("SendToMaid");
                processMaidMessage(ddMessagingContract.getMaidTemplate(), parameters,
                        maid.getId(), ownerId, ownerType, contract);
            }

            // ACC-2445
            if (ddMessagingContract.getSendToMaidWhenRetractCancellation()) {
                logger.info("SendToMaidWhenRetractCancellation");
                processMaidMessage(ddMessagingContract.getMaidWhenRetractCancellationTemplate(), parameters,
                        maid.getId(), ownerId, ownerType, contract);
            }
        }
    }

    private void updateLastMessageDate(Map<String, String> parameters) {
        if (!parameters.containsKey("relatedEntityType2") ||
                parameters.get("relatedEntityType2") == null ||
                !parameters.containsKey("relatedEntityId2") ||
                parameters.get("relatedEntityId2") == null) {
            return;
        }

        switch (parameters.get("relatedEntityType2")) {
            case "DirectDebitRejectionToDo":
                DirectDebitRejectionToDoRepository directDebitRejectionToDoRepository =
                        Setup.getRepository(DirectDebitRejectionToDoRepository.class);
                DirectDebitRejectionToDo todo  =
                        directDebitRejectionToDoRepository.findOne(Long.valueOf(parameters.get("relatedEntityId2")));
                todo.setLastMessageDate(new Date());
                directDebitRejectionToDoRepository.silentSave(todo);
                break;
            case "Payment":
                PaymentRepository paymentRepository = Setup.getRepository(PaymentRepository.class);
                Payment payment = paymentRepository.findOne(Long.valueOf(parameters.get("relatedEntityId2")));
                payment.setLastMessageDate(new Date());
                Setup.getApplicationContext()
                        .getBean(PaymentService.class)
                        .updatePaymentDeepSilentSave(payment);
                break;
            case "FlowProcessorEntity":
                FlowProcessorEntityRepository flowProcessorEntityRepository =
                        Setup.getRepository(FlowProcessorEntityRepository.class);
                FlowProcessorEntity flow = flowProcessorEntityRepository
                        .findOne(Long.valueOf(parameters.get("relatedEntityId2")));
                flow.setLastMessageDate(new Date());
                flowProcessorEntityRepository.silentSave(flow);
                break;
            case "DirectDebit":
                DirectDebitRepository directDebitRepository = Setup.getRepository(DirectDebitRepository.class);
                DirectDebit dd = directDebitRepository.findOne(Long.valueOf(parameters.get("relatedEntityId2")));
                dd.setLastMessageDate(new Date());
                directDebitRepository.silentSave(dd);
                break;
        }
    }
    /*public void createDdMessagingHumanSms(
            DDMessaging ddMessaging,
            ContractPaymentTerm contractPaymentTerm,
            Map<String, String> parameters) {

        logger.info("execute human_sms ");

        Client client = contractPaymentTerm.getContract().getClient();
        Housemaid maid = contractPaymentTerm.getContract().getHousemaid();

        PicklistItemRepository picklistItemRepository = Setup.getRepository(PicklistItemRepository.class);

        Picklist Picklist = Setup.getRepository(PicklistRepository.class)
                .findByCode(AccountingModule.PICKLIST_CALL_REASONS_SMS);
        PicklistItem picklistItem = null;

        switch (ddMessaging.getEvent()) {
            case BouncedPayment: {
                picklistItem = picklistItemRepository.findByListAndCodeIgnoreCase(Picklist,
                        AccountingModule.PICKLIST_ITEM_CALL_REASONS_SMS_BOUNCED_PAYMENT
                );
                break;
            }
            case DirectDebitRejected: {
                picklistItem = picklistItemRepository.findByListAndCodeIgnoreCase(Picklist,
                        AccountingModule.PICKLIST_ITEM_CALL_REASONS_SMS_DD_REJECTED);
                break;
            }
            case ClientPaidCashAndNoSignatureProvided: {
                picklistItem = picklistItemRepository.findByListAndCodeIgnoreCase(Picklist,
                        AccountingModule.PICKLIST_ITEM_CALL_REASONS_SMS_DD_MISSING);
                break;
            }
            case IncompleteDDRejectedByDataEntry: {
                picklistItem = picklistItemRepository.findByListAndCodeIgnoreCase(Picklist,
                        AccountingModule.PICKLIST_ITEM_CALL_REASONS_SMS_DD_MISSING_WITHOUT_CASH_PAYMENT);
                break;
            }
        }

        Map<String, Object> body = new HashMap<>();

        Map<String, Object> pi = new HashMap<>();
        if (picklistItem != null) pi.put("id", picklistItem.getId());

        body.put("reasonToCall", pi);

        if (maid != null) {
            Map<String, Object> h = new HashMap<>();
            h.put("id", maid.getId());
            body.put("housemaid", h);
        }

        Map<String, Object> c = new HashMap<>();
        if (client != null) c.put("id", client.getId());

        body.put("client", c);
        body.put("taskName", "accounting_human_sms");
        body.put("reasonToCallLabel", ddMessaging.getHumanSmsTitle());
        body.put("actionStage", ActionStage.ZIWO_CALL_CLIENT);
        body.put("completed", false);
        body.put("stopped", false);

        Map map = Setup.getApplicationContext().getBean(InterModuleConnector.class)
                .postJson("/staffmgmt/resolverToDo/create", body, Map.class);
        try {
            logger.log(Level.SEVERE, "human sms body is: {0}", body.toString());
            logger.log(Level.SEVERE, "human sms response {0}", map.get("id"));
            logger.log(Level.SEVERE, "human sms response{0}", map.toString());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "human sms response not valid");
        }

        if (map != null && !map.isEmpty() && map.containsKey("id") &&  parameters.get("bouncedPaymentId") != null) {
            Payment payment = Setup.getRepository(PaymentRepository.class).findOne(Long.valueOf(parameters.get("bouncedPaymentId")));
            payment.setHumanSmsId((Long) map.get("id"));
            Setup.getApplicationContext()
                    .getBean(PaymentService.class)
                    .forceUpdatePayment(payment);
        }
    }*/

    /*public void createDdMessagingExpertTodo(
            DDMessaging ddMessaging,
            ContractPaymentTerm contractPaymentTerm) {

        logger.info("execute expert_todo ");

        switch (ddMessaging.getEvent()) {
            case BouncedPayment:
                Setup.getApplicationContext().getBean(BouncingFlowService.class)
                        .createBouncedExpertTodo(contractPaymentTerm.getContract(), ddMessaging);
                break;
            case OnlineCreditCardPaymentReminders:
                Setup.getApplicationContext().getBean(ContractPaymentConfirmationToDoService.class)
                        .createPaymentReminderTodo(contractPaymentTerm.getContract());
                break;
            case ClientPaidCashAndNoSignatureProvided:
                Setup.getApplicationContext().getBean(AfterCashFlowService.class)
                        .createAfterCashFlowExpertTodo(contractPaymentTerm.getContract(), ddMessaging);
                break;
        }
    }*/

    public void createDdMessagingClientTodo(
            DDMessagingContract ddMessagingContract,
            FlowProcessorEntity entity,
            ContractPaymentTerm contractPaymentTerm,
            Map<String, String> parameters) {

        logger.info("execute client_todo ");

        Setup.getApplicationContext()
                .getBean(ClientToDoService.class)
                .createClientTodo(ddMessagingContract, entity, contractPaymentTerm, parameters);
    }

    public void processClientMessage(
            DDMessagingContract ddMessagingContract,
            ContractPaymentTerm contractPaymentTerm,
            Template template,
            Map<String, String> parameters,
            Map<String, AppAction> cta,
            DDMessagingToDo ddMessagingToDo) {

        logger.info("processClientMessage");

        Client client = contractPaymentTerm.getContract().getClient();
        Contract contract = contractPaymentTerm.getContract();

        parameters.put("dear_receiver_name", client != null ?
                "Dear " + client.getFirstName(true) : "");
        parameters.put("client_nickname_or_first_name", StringUtils.getClientNicknameOrFirstName(client));

        logger.log(Level.INFO, "sending Notification dd id {0}", ddMessagingContract.getId());
        fillContext(ddMessagingContract, contractPaymentTerm, parameters, cta, template);

        messagingService.sendMessageToClient(contract,
                parameters,
                cta,
                Long.valueOf(parameters.get("ownerId")),
                parameters.get("ownerType"),
                template,
                ddMessagingContract);

        sendAlertUponVipReceiveMessage(client, ddMessagingContract.getDdMessaging(), contract.getId(),
                TemplateUtil.compileTemplate(template,
                        template.isChannelExist(ChannelSpecificSettingType.SMS) ?
                                template.getChannelSetting(ChannelSpecificSettingType.SMS) :
                                template.getChannelSetting(ChannelSpecificSettingType.Notification),
                        "en", new HashMap<>(), parameters));
    }

    public void fillParameters(
            DDMessagingContract ddMessagingContract,
            FlowProcessorEntity entity,
            ContractPaymentTerm cpt,
            Map<String, String> parameters, boolean sendToClient) {

        fillPublicParameters(ddMessagingContract.getDdMessaging(), cpt, parameters, entity);

        if (sendToClient) {
            fillCreditCardOfferParameters(ddMessagingContract.getDdMessaging(), cpt, parameters);
        }

        switch (ddMessagingContract.getDdMessaging().getEvent()) {
            case ClientPaidCashAndNoSignatureProvided:
                fillAfterCashFlowMessageParameter(ddMessagingContract.getDdMessaging(), cpt, parameters);
                break;
            case BouncedPayment:
                fillBouncedPaymentParameter(ddMessagingContract.getDdMessaging(), cpt, parameters);
                break;
            case OnlineCreditCardPaymentReminders:
                fillOnlineCreditCardPaymentRemindersParameter(entity, cpt, parameters, ddMessagingContract.getClientTemplate());
                break;
            case ExpiryPayment:
                fillExpiryPaymentParameter(cpt, parameters);
                break;
            case ClientsPayingViaCreditCard:
                fillClientPayingViaCreditCardMessageParameter(ddMessagingContract.getDdMessaging(), cpt, parameters, ddMessagingContract.getClientTemplate());
                break;
            case ExtensionFlow:
                fillExtensionMessageParameter(entity, parameters);
                break;
//            case OneMonthAgreement:
//                fillOneMonthAgreementFlowMessageParameter(
//                    ddMessaging, contractPaymentTerm, parameters);
//                break;
        }
    }

    public void fillContext(
            DDMessagingContract ddMessagingContract,
            ContractPaymentTerm cpt,
            Map<String, String> parameters,
            Map<String, AppAction> cta,
            Template notificationTemplate) {

        fillPublicContext(ddMessagingContract.getDdMessaging(), cpt.getContract(), parameters, cta, notificationTemplate);

        switch (ddMessagingContract.getDdMessaging().getEvent()) {
            case BouncedPayment:
                fillBouncedPaymentContext(ddMessagingContract.getClientTemplate(), cpt, parameters, cta);
                break;
            case DirectDebitRejected:
                // fillDirectDebitRejectedContext(ddMessaging, contractPaymentTerm, context);
                // no more CTA context after removing UBER CTA
                break;
            case ClientPaidCashAndNoSignatureProvided:
                fillAfterCashFlowMessageContext(cpt, parameters, cta);
                break;
            case IncompleteDDRejectedByDataEntry:
                // fillIncompleteDDRejectedByDataEntryContext(ddMessaging, contractPaymentTerm, context);
                // no more CTA context after removing UBER CTA
                break;
            case OnlineCreditCardPaymentReminders:
                fillOnlineCreditCardPaymentRemindersContext(cpt, parameters, cta);
                break;
            case ExpiryPayment:
                fillExpiryPaymentContext(cpt, parameters, cta);
                break;
            case ClientsPayingViaCreditCard:
                fillClientPayingViaCreditCardMessageContext(ddMessagingContract.getClientTemplate(), cpt, cta, parameters);
                break;
//            case OneMonthAgreement:
//                fillOneMonthAgreementFlowMessageContext(ddMessaging, contractPaymentTerm, parameters, cta);
//                break;
        }

        fillCreditCardOfferContext(cpt, parameters, cta);
    }

    public void fillPublicContext(
            DDMessaging ddMessaging,
            Contract contract,
            Map<String, String> parameters,
            Map<String, AppAction> context,
            Template t) {

        ChannelSpecificSetting n = t.getChannelSetting(ChannelSpecificSettingType.Notification);
        if (n == null || !n.isActive()) return;

        String link_send_dd_details_value = parameters.getOrDefault("link_send_dd_details", "");

        parameters.put("link_send_dd_details_click_here", "@link6@");
        parameters.put("link_send_dd_details_here", "@link7@");
        parameters.put("link_send_dd_details_clicking_here", "@link8@");
        parameters.put("link_send_dd_details_payment_link", "@link9@");
        parameters.put("bounced_payment_clicking_here", "@bounced_payment_clicking_here@");
        parameters.put("payment_bounced_sign_now_clicking_here", "@payment_bounced_sign_now_clicking_here@");
        parameters.put("pay_using_different_bank_account_click_here", "@pay_using_different_bank_account_click_here@");

        if (!link_send_dd_details_value.isEmpty()) {
            for (int index = 6; index < 10; index++) {
                AppAction a = new AppAction();
                switch (index) {
                    case 6:
                        if (n.getText().contains("@link_send_dd_details_click_here@"))
                            a.setText("click here");
                        break;
                    case 7:
                        if (n.getText().contains("@link_send_dd_details_here@"))
                            a.setText("here");
                        break;
                    case 8:
                        if (n.getText().contains("@link_send_dd_details_clicking_here@"))
                            a.setText("clicking here");
                        break;
                    case 9:
                        if (n.getText().contains("@link_send_dd_details_payment_link@"))
                            a.setText("payment link");
                        break;
                }

                if (a.getText() == null) continue;

                a.setType(AppActionType.LINK);
                a.setFunctionType(FunctionType.WEB_SERVICE);
                a.setNavigationType(NavigationType.WEB);
                a.setHyperlink(link_send_dd_details_value);
                a.setAppRouteName("");
                a.setAppRouteArguments(new HashMap<String, String>() {{
                    put("contractId", contract.getId().toString());
                    put("contractUuid", contract.getUuid());
                }});

                context.put("link" + index, a);
            }
        }

        AppAction a = new AppAction();
        a.setFunctionType(FunctionType.NAVIGATE);
        a.setNavigationType(NavigationType.INAPP);
            a.setAppRouteName("/my_monthly_payments_different_bank");

        a.setAppRouteArguments(new HashMap<String, String>() {{
            put("isSwitch", Boolean.TRUE.toString());
            put("contractId", contract.getId().toString());
            put("contractUuid", contract.getUuid());
        }});

        if (n.getText().contains("@pay_using_different_bank_account_click_here@")) {
            a.setType(AppActionType.LINK);
            a.setText("click here");
            context.put("pay_using_different_bank_account_click_here", a);
        } else if (ddMessaging.getClientTemplate().isChannelExist(ChannelSpecificSettingType.SMS) &&
                ddMessaging.getClientTemplate().getChannelSetting(ChannelSpecificSettingType.SMS)
                        .getParameters()
                        .stream()
                        .anyMatch(p -> p.getName().equals("pay_using_different_bank_account_sms"))){

            a.setType(AppActionType.BUTTON);
            a.setText("Add New Bank Account Details");
            context.put("pay_using_different_bank_account_click_here", a);
        }

        if (showSignNowButton(t)) {
            switch (ddMessaging.getEvent()) {
                case ClientPaidCashAndNoSignatureProvided:
                case OneMonthAgreement:
                    context.put("sign_now", getSignNowButton(getSignButtonName(ddMessaging), contract));
                    break;
                default:
                    context.put("sign_now",
                            getSignNowButtonWebLink(getSignButtonName(ddMessaging),
                                    contract, parameters.get("link_send_dd_details")));
                    break;
            }
        }

        if (ddMessaging.getEvent().equals(DDMessagingType.Termination) &&
                t.getChannelSetting(ChannelSpecificSettingType.SMS) != null) {

            if (t.getChannelSetting(ChannelSpecificSettingType.SMS)
                    .getText()
                    .contains("@accommodation_location@")) {

                AppAction a1 = new AppAction();
                a1.setType(AppActionType.BUTTON);
                a1.setText("Get Accommodation Directions");
                a1.setFunctionType(FunctionType.WEB_SERVICE);
                a1.setNavigationType(NavigationType.WEB);
                a1.setAppRouteName("");
                a1.setHyperlink(parameters.get("accommodation_location"));
                a1.setAppRouteArguments(new HashMap<String, String>() {{
                    put("contractId", contract.getId().toString());
                    put("contractUuid", contract.getUuid());
                }});

                context.put("accommodation_location", a1);
            }

            if (ddMessaging.getSendPayTabMessage() &&
                    t.getChannelSetting(ChannelSpecificSettingType.SMS)
                            .getText()
                            .contains("payment_link_termination_message")) {

                AppAction a1 = new AppAction();
                a1.setType(AppActionType.BUTTON);
                // TODO Should be change button name?
                a1.setText("Pay Now");
                a1.setFunctionType(FunctionType.WEB_SERVICE);
                a1.setNavigationType(NavigationType.WEB);
                a1.setAppRouteName("");
                a1.setHyperlink(parameters.get("payment_link_termination_message"));
                a1.setAppRouteArguments(new HashMap<String, String>() {{
                    put("contractId", contract.getId().toString());
                    put("contractUuid", contract.getUuid());
                }});
                context.put("payment_link_termination_message", a1);
            }
        }
    }

    public AppAction getSignNowButtonWebLink(String text, Contract contract, String link) {
        AppAction a = new AppAction();
        a.setType(AppActionType.BUTTON);
        a.setText(text);
        a.setFunctionType(FunctionType.WEB_SERVICE);
        a.setNavigationType(NavigationType.WEB);
        a.setAppRouteName("");
        a.setHyperlink(link);
        a.setAppRouteArguments(new HashMap<String, String>() {{
            put("previousAppBarTitle", "Back");
            put("isSwitch", Boolean.FALSE.toString());
            put("contractId", contract.getId().toString());
            put("contractUuid", contract.getUuid());
        }});
        return a;
    }

    public void fillCreditCardOfferContext(ContractPaymentTerm cpt, Map<String, String> parameters, Map<String, AppAction> cta) {
        if (!parameters.containsKey("credit_card_offer_link")) return;

        AppAction a = new AppAction();
        a.setType(AppActionType.BUTTON);
        a.setText(parameters.get("cc_offer_cta_label"));
        a.setFunctionType(FunctionType.NAVIGATE);
        a.setNavigationType(NavigationType.INAPP);
        a.setAppRouteName("/my_monthly_payments_pay_with_cc");
        a.setAppRouteArguments(new HashMap<String, String>() {{
            put("todoUuid", parameters.get("todoUuid"));
            put("amount", parameters.get("todoTotalAmount").replaceAll(",", ""));
            put("contractId", cpt.getContract().getId().toString()); // ACC-6067
            put("contractUuid", cpt.getContract().getUuid());
        }});
        cta.put("credit_card_offer_cta" , a);
    }

    public void fillPublicParameters(
            DDMessaging ddMessaging,
            ContractPaymentTerm contractPaymentTerm,
            Map<String, String> parameters,
            FlowProcessorEntity entity) {

        Client client = contractPaymentTerm.getContract().getClient();
        Housemaid maid = contractPaymentTerm.getContract().getHousemaid();
        Contract contract = contractPaymentTerm.getContract();

        Date adjustedEndDate = contract.getAdjustedEndDate();
        LocalDate adjustedEndDateMinus1 = adjustedEndDate == null ?
                null : new LocalDate(adjustedEndDate).minusDays(1);

        String centerLocation = Setup.getParameter(Setup.getModule("clientmgmt"),
                AccountingModule.PARAMETER_CENTER_LOCATION_MESSAGES);

        String accommodationLocation = Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_ACCOMMODATION_LOCATION_MESSAGES);

        String recruitmentWhatsappNumber = Setup.getParameter(Setup.getModule("sales"),
                AccountingModule.PARAMETER_Local_Recruitment_Whatsapp_Number);

        if (isTemplateContainsParameters(ddMessaging.getClientTemplate(),
                Arrays.asList("link_send_dd_details",
                        "link_send_dd_details_click_here",
                        "link_send_dd_details_here",
                        "link_send_dd_details_clicking_here",
                        "link_send_dd_details_payment_link"))) {

            parameters.put("link_send_dd_details", getLinkSendDdDetails(ddMessaging, contract, true));
            parameters.put("link_send_dd_details_click_here",  parameters.get("link_send_dd_details"));
            parameters.put("link_send_dd_details_here", parameters.get("link_send_dd_details"));
            parameters.put("link_send_dd_details_clicking_here", parameters.get("link_send_dd_details"));
            parameters.put("link_send_dd_details_payment_link", parameters.get("link_send_dd_details"));
        }

        parameters.put("greetings", contract.getContractProspectType().getCode()
                .equals(PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE)) ?
                        Setup.getCoreParameter(CoreParameter.SMS_GREETINGS_MAIDSVISA) :
                        Setup.getCoreParameter(CoreParameter.SMS_GREETINGS_MAIDSCC));
        parameters.put("client_first_name", client == null || client.getName() == null ?
                "" : client.getName());
        parameters.put("client_nickname_or_first_name", StringUtils.getClientNicknameOrFirstName(client));
        parameters.put("maid_name", maid == null || maid.getName() == null || maid.getName().toLowerCase().contains("unknown") ?
                "your maid" : maid.getName());
        parameters.put("maid_first_name", maid == null || maid.getFirstName() == null || maid.getFirstName().toLowerCase().contains("unknown") ?
                "your maid" : maid.getFirstName());
        parameters.put("Account_holder_name", contractPaymentTerm.getAccountName() == null ?
                "" : contractPaymentTerm.getAccountName());
        parameters.put("adjusted_end_date", adjustedEndDate == null ?
                "" : DateUtil.formatClientFullDate(adjustedEndDate));
        parameters.put("adjusted_end_date - 1 day", adjustedEndDateMinus1 == null ?
                "" : DateUtil.formatClientFullDate(adjustedEndDateMinus1.toDate()));
        parameters.put("center_location", centerLocation);
        parameters.put("accommodation_location", accommodationLocation);

        //todo this empty value should be replaced
        parameters.put("min_amount_in_bank", "");
        parameters.put("Local_Recruitment_Whatsapp_Number", recruitmentWhatsappNumber);

        if ((parameters.get("scheduled_termination_date") == null
                || parameters.get("scheduled_termination_date - 1 day") == null) &&
                contract.getScheduledDateOfTermination() != null) {

            parameters.put("scheduled_termination_date",
                    DateUtil.formatClientFullDate(contract.getScheduledDateOfTermination()));
            parameters.put("scheduled_termination_date - 1 day",
                    DateUtil.formatClientFullDate(new LocalDate(contract.getScheduledDateOfTermination()).minusDays(1).toDate()));
        }

        // acc-2357
        if (contract.getClient() != null) {
            if (contractPaymentTerm.getSpouseWillSignDD()) {
                parameters.put("spouse_phone_number", contract.getClient().getMobileNumber());
            } else {
                parameters.put("spouse_phone_number", contract.getClient().getSpouseMobileNumber());
            }
        }

        parameters.putIfAbsent("monthly_payment", "");
        parameters.putIfAbsent("scheduled_termination_date", "");
        parameters.putIfAbsent("scheduled_termination_date - 1 day", "");
        parameters.putIfAbsent("spouse_phone_number", "");

        if (ddMessaging.getScheduleTermCategory() == DirectDebitMessagingScheduleTermCategory.None) {
            parameters.putIfAbsent("ownerType", "Contract");
            parameters.putIfAbsent("ownerId", contract.getId().toString());
        } else {
            parameters.putIfAbsent("ownerType", "OnCloseTaxiWorkOrder");
            parameters.putIfAbsent("ownerId", contract.getId().toString());
        }

        if (maid != null && ddMessaging.getMaidTemplate() != null && ddMessaging.getSendToMaid() && contract.isMaidVisa()) {
            String signCancellationLink = Setup.getApplicationContext().getBean(InterModuleConnector.class)
                    .get("/staffmgmt/remoteCancellation/generateRemoteCancellationPageLink/" + maid.getId(), String.class);

            signCancellationLink = utils.shorteningUrl(signCancellationLink);
            parameters.put("visa_cancellation_paper_link", signCancellationLink);
        } else {
            parameters.put("visa_cancellation_paper_link", "");
        }

        if (isTemplateContainsParameter(ddMessaging.getClientTemplate(), "payment_options_link")) {
            parameters.put("payment_options_link", shortener.shorten("https://ccapp.page.link?amv=0&apn=cc.maids.app&link=https%3A%2F%2Fccapp.page.link%2Fpayment_bounced" +
                    (parameters.get("bounced_payment_amount") == null ? "" : "%3Famount%3D" + parameters.get("bounced_payment_amount")) +
                    (parameters.get("bouncedPaymentId") == null ? "" : "%26bouncedPaymentId%3D" + parameters.get("bouncedPaymentId"))));
        }

        if (isTemplateContainsParameter(ddMessaging.getClientTemplate(), "pay_using_different_bank_account_sms")) {
            parameters.put("pay_using_different_bank_account_sms",
                    shortener.shorten("https://ccapp.page.link?amv=0&apn=cc.maids.app&link=https%3A%2F%2Fccapp.page.link%2Fpayment_different_bank%3FisSwitch%3Dtrue"));
        }

        if (ddMessaging.getEvent().equals(DDMessagingType.Termination) &&
                ddMessaging.getSendPayTabMessage()) {

            String link;
            if (entity != null) {
                ContractPaymentConfirmationToDo toDo = entity.getContractPaymentConfirmationToDo();
                boolean recurring = ContractPaymentConfirmationToDoService.isEligibleForTokenizationViaConfirmationToDO(toDo);

                String payLink = Setup.getCoreParameter(CoreParameter.PAYMENT_PUBLIC_PAGE) +
                        "/accounting/accountingEPaymentService?context=%7B%22todoUuid%22:%22" + toDo.getUuid() + "%22%7D" +
                        (recurring ? "&recurring=true" : "");

                AccountingLink a = AccountingLinkService.getOrCreateNewLink(
                        new AccountingLink.AccountingLinkBuilder()
                                .AccountingLink(
                                        toDo.getId(), toDo.getEntityType(),
                                        AccountingLink.AccountingLinkType.ONLINE_CARD, payLink)
                                .setContractId(contractPaymentTerm.getContract().getId())
                                .setCptId(contractPaymentTerm.getId())
                                .setRelatedFlowId(entity.getId())
                                .setRelatedFlowEntityType(entity.getEntityType())
                                .setIgnoreDuplication(true)
                                .setAdditionalInfo(new HashMap<String, Object>() {{
                                    put("sendFromFlowTerminationMessage", true);
                                }}));

                // redirect to paytabs Main page
                link = shortener.shorten(Setup.getCoreParameter(CoreParameter.PUBLIC_LINK_BASE) + "/modules/accounting/paytabs/#!/main?uid=" + a.getUuid());
            } else {
                link = AccountingLinkService.getDynamicPayTabsLink(contractPaymentTerm, "payment_link_termination_message", parameters);
            }

            parameters.put("payment_link_termination_message", link);
        }

        if (isTemplateContainsParameter(ddMessaging.getClientTemplate(), "dynamic_payment_link")) {
            parameters.put("dynamic_payment_link", AccountingLinkService.getDynamicPayTabsLink(
                    contractPaymentTerm, "dynamic_payment_link", parameters));
        }

        // ACC-9286
        LocalDate paidEndDate = new LocalDate(contract.getPaidEndDate());
        parameters.put("paid_end_date_or_tomorrow", paidEndDate.isBefore(new LocalDate()) ?
                "tomorrow" :
                paidEndDate.toString("MMM dd'" + DateUtil.getOrdinalSuffix(paidEndDate.getDayOfMonth()) + "' yyyy"));

        // Check if Template text contains @by_paid_end_date_or_within_X_days@
        if (ddMessaging.getClientTemplate() != null &&
                ddMessaging.getClientTemplate().isChannelExist(ChannelSpecificSettingType.SMS)) {

            List<Map.Entry<String, Integer>> withinXDaysParams = extractWithinXDaysParameterAndValue(
                    ddMessaging.getClientTemplate().getChannelSetting(ChannelSpecificSettingType.SMS).getText());

            if (!withinXDaysParams.isEmpty()) {
                // by_paid_end_date_or_within_X_days
                boolean isBeforePED = paidEndDate.isBefore(new LocalDate());

                String valueWhenEqualOrAfterPED = "";
                if (!isBeforePED) {
                    valueWhenEqualOrAfterPED = "by " + paidEndDate.toString("MMM dd'" + DateUtil.getOrdinalSuffix(paidEndDate.getDayOfMonth()) + "' yyyy");
                }
                for (Map.Entry<String, Integer> withinXDaysParam : withinXDaysParams) {
                    parameters.put(withinXDaysParam.getKey(), isBeforePED ?
                            "within " + withinXDaysParam.getValue() + " days" :
                            valueWhenEqualOrAfterPED);
                }
            }
        }
    }

    public void fillCreditCardOfferParameters(DDMessaging d, ContractPaymentTerm cpt, Map<String, String> parameters) {

        parameters.put("sms_cc_offer_sentence", "");
        parameters.put("cc_offer_sentence", "");

        if (isDdMessagingContainsCreditCardLink(d) || !isDdMessagingContainsCreditCardOfferLink(d)) return;

        try {
            creditCardOfferService.createTodoIfNotExists(cpt, d, parameters);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void fillDescriptionAndBreakdownDescription(Template t, ContractPaymentConfirmationToDo toDo, Map<String, String> parameters) {
        if (t.getChannelSetting(ChannelSpecificSettingType.SMS) != null &&
                t.getChannelSetting(ChannelSpecificSettingType.SMS).getText() != null) {
            if (t.getChannelSetting(ChannelSpecificSettingType.SMS).getText().contains("@description@")) {
                parameters.put("description", paymentTypeDescriptionService.getPaymentTypeDescriptionTextFromWrapper(
                        toDo.getContractPaymentList().get(0)));
            }

            if (t.getChannelSetting(ChannelSpecificSettingType.SMS).getText().contains("@breakdown_description@")) {
                parameters.put("breakdown_description", getBreakdownDescription(toDo));

            }
        }
    }

    public String getLinkSendDdDetails(DDMessaging ddMessaging, Contract contract, boolean withShortLink){
        Map<String, Object> signDDMap = new HashMap<>();
        signDDMap.put("cpt", contract.getActiveContractPaymentTerm());
        signDDMap.put("ddMessaging", ddMessaging);
        signDDMap.put("additionalInfo", new HashMap<String, Object>() {{
            put("sentFrom", "ddMessaging");
            put("event", ddMessaging.getEvent());
            put("templateName", ddMessaging.getClientTemplate() != null ? ddMessaging.getClientTemplate().getName() : "");
        }});

        return ddMessaging.getRejectCategory() == DirectDebitRejectCategory.Signature ?
                utils.getSingDDLinkWithWrongSignatureTrue(signDDMap, withShortLink) :
                ddMessaging.getEvent() == DDMessagingType.IncompleteDDClientHasNoApprovedSignature ?
                        utils.getSingDDLinkWithWrongSignatureTrueAndHideRejectionMessageTrue(signDDMap, withShortLink) :
                        utils.getSingDDLink(signDDMap, withShortLink);
    }

    public void fillAfterCashFlowMessageParameter(
            DDMessaging ddMessaging,
            ContractPaymentTerm contractPaymentTerm,
            Map<String, String> parameters) {

        logger.log(Level.INFO, "contractPaymentTerm id: {0}", contractPaymentTerm.getId());
        LocalDate paidEndDate = new LocalDate(contractPaymentTerm.getContract().getPaidEndDate());

        parameters.put("paid_end_date", paidEndDate.toString("yyyy-MM-dd"));
        parameters.put("paid_end_date - 1", paidEndDate.minusDays(1).toString("yyyy-MM-dd"));
        parameters.putIfAbsent("scheduled_termination_date",
                DateUtil.formatClientFullDate(paidEndDate.isAfter(new LocalDate()) ?
                        paidEndDate.toDate() : new LocalDate().toDate()));

        if (!ddMessaging.getSendPayTabMessage()) return;

        ContractPaymentConfirmationToDo todo = Setup.getApplicationContext()
                .getBean(AfterCashFlowService.class).getPaymentTodo(contractPaymentTerm);
        if (todo == null) return;
        parameters.put("ownerType", "ContractPaymentConfirmationToDo");
        parameters.put("ownerId", todo.getId().toString());

        logger.log(Level.INFO, "toDo id: {0}", todo.getId());

        parameters.put("amount", String.valueOf(todo.getTotalAmount().intValue()));
        parameters.put("paytabs_link", Setup.getApplicationContext()
                .getBean(ContractPaymentConfirmationToDoService.class)
                .getPayingViaCreditCardLink(todo));
        parameters.put("todoUuid", todo.getUuid());
        parameters.put("todoTotalAmount", todo.getTotalAmount().toString());
    }

    public void fillAfterCashFlowMessageContext(
            ContractPaymentTerm cpt,
            Map<String, String> parameters,
            Map<String, AppAction> cta) {

        logger.log(Level.INFO, "contractPaymentTerm id: {0}", cpt.getId());

        if (parameters.get("paytabs_link") != null)
            cta.put("payment_credit_card", getPaytabsButton(parameters, cpt.getContract()));
    }

    //ACC-4715
    public void fillClientPayingViaCreditCardMessageParameter(
            DDMessaging ddMessaging,
            ContractPaymentTerm contractPaymentTerm,
            Map<String, String> parameters,
            Template t) {

        logger.log(Level.INFO, "contractPaymentTerm id: {0}", contractPaymentTerm.getId());
        parameters.put("amount", "");
        parameters.put("description", "");
        parameters.put("breakdown_description", "");
        parameters.put("paying_via_credit_card_sms", "");
        parameters.put("paying_via_credit_card_clicking_here", "@paying_via_credit_card_clicking_here@");
        parameters.put("paying_via_credit_card_click_here", "@paying_via_credit_card_click_here@");
        parameters.put("paying_via_credit_card_sign_now_click_here", "@paying_via_credit_card_sign_now_click_here@");
        parameters.put("signing_offer_clicking_here", "@signing_offer_clicking_here@");
        parameters.put("cc_app_download_url",
                Setup.getParameter(Setup.getModule(AccountingModule.CLIENT_MGMT_MODULE_CODE),
                AccountingModule.PARAMETER_CLIENT_MGMT_CC_APP_DOWNLOAD_URL));

        DateTime paidEndDate = new DateTime(contractPaymentTerm.getContract().getPaidEndDate());
        parameters.put("paid_end_date", paidEndDate.toString("yyyy-MM-dd"));
        parameters.put("paid_end_date - 1", paidEndDate.minusDays(1).toString("yyyy-MM-dd"));

        ContractPaymentConfirmationToDo toDo = contractPaymentConfirmationToDoRepository
                .findFirstByContractPaymentTerm_ContractAndSourceInAndShowOnERPFalseAndDisabledFalseOrderByCreationDateDesc(
                        contractPaymentTerm.getContract(),
                        Arrays.asList(
                                ContractPaymentConfirmationToDo.Source.CLIENT_PAYING_VIA_Credit_Card,
                                ContractPaymentConfirmationToDo.Source.ONE_MONTH_AGREEMENT));
        if (toDo == null) return;
        parameters.put("ownerType", "ContractPaymentConfirmationToDo");
        parameters.put("ownerId", toDo.getId().toString());
        checkPaidEndDate(contractPaymentTerm.getContract(), ddMessaging, parameters, paidEndDate);

        parameters.putIfAbsent("scheduled_termination_date", DateUtil.formatClientFullDate(
                paidEndDate.isAfter(new DateTime()) ? paidEndDate.toDate() : new LocalDate().toDate()));

        logger.log(Level.INFO, "toDo id: {0}", toDo.getId());
         String link  = shortener.shorten(Setup.getCoreParameter(CoreParameter.PUBLIC_LINK_BASE)
                + "/modules/accounting/paytabs/#!/home?uid=" + contractPaymentTerm.getContract().getUuid());
        parameters.put("amount", String.valueOf(toDo.getTotalAmount().intValue()));
        parameters.put("paying_via_credit_card_sms", link);
        fillDescriptionAndBreakdownDescription(t, toDo, parameters);
    }

    public void fillExtensionMessageParameter(
            FlowProcessorEntity entity,
            Map<String, String> parameters) {

        logger.info("entity id: " + entity.getId());
        parameters.put("amount", "");
        parameters.put("payment_link", "");

        if (entity.getContractPaymentConfirmationToDo() == null) return;
        parameters.put("ownerType", "ContractPaymentConfirmationToDo");
        parameters.put("ownerId", entity.getContractPaymentConfirmationToDo().getId().toString());

        parameters.put("amount", String.valueOf(entity.getContractPaymentConfirmationToDo().getTotalAmount().intValue()));
        parameters.put("payment_link", Setup.getApplicationContext()
                .getBean(ContractPaymentConfirmationToDoService.class)
                .getPayingViaCreditCardLink(entity.getContractPaymentConfirmationToDo()));
    }

    //ACC-4715
    public void fillClientPayingViaCreditCardMessageContext(
            Template t,
            ContractPaymentTerm contractPaymentTerm,
            Map<String, AppAction> cta,
            Map<String, String> parameters) {

        logger.log(Level.INFO, "contractPaymentTerm id: {0}", contractPaymentTerm.getId());

        if (t == null ||
                !t.isChannelExist(ChannelSpecificSettingType.Notification)) return;

        if (t.getChannelSetting(ChannelSpecificSettingType.Notification)
                .getText()
                .contains("paying_via_credit_card_click")) {
            AppAction a = new AppAction();
            a.setType(AppActionType.LINK);
            a.setText(t.getChannelSetting(ChannelSpecificSettingType.Notification).getText().contains("clicking_here") ?
                    "clicking here" : "click here");
            a.setFunctionType(FunctionType.WEB_SERVICE);
            a.setNavigationType(NavigationType.WEB);
            a.setHyperlink(parameters.get("paying_via_credit_card_sms"));
            a.setAppRouteName("");
            a.setAppRouteArguments(new HashMap<String, String>() {{
                put("contractId", contractPaymentTerm.getContract().getId().toString());
                put("contractUuid", contractPaymentTerm.getContract().getUuid());
            }});
            cta.put(t.getChannelSetting(ChannelSpecificSettingType.Notification).getText().contains("clicking_here") ?
                "paying_via_credit_card_clicking_here" : "paying_via_credit_card_click_here", a);
        } else if (t.getChannelSetting(ChannelSpecificSettingType.SMS)
                .getText()
                .contains("paying_via_credit_card_sms")) { // ACC-6840
            AppAction a = new AppAction();
            a.setType(AppActionType.BUTTON);
            a.setText("Pay Now");
            a.setFunctionType(FunctionType.WEB_SERVICE);
            a.setNavigationType(NavigationType.WEB);
            a.setHyperlink(parameters.get("paying_via_credit_card_sms"));
            a.setAppRouteName("");
            a.setAppRouteArguments(new HashMap<String, String>() {{
                put("contractId", contractPaymentTerm.getContract().getId().toString());
                put("contractUuid", contractPaymentTerm.getContract().getUuid());
            }});
            cta.put("paying_via_credit_card", a);
        }

        if (t.getChannelSetting(ChannelSpecificSettingType.Notification)
                .getText()
                .contains("paying_via_credit_card_sign_now_click_here")) {
            AppAction a = new AppAction();
            a.setType(AppActionType.LINK);
            a.setText("click here");
            a.setFunctionType(FunctionType.NAVIGATE);
            a.setNavigationType(NavigationType.INAPP);
            a.setAppRouteName("/payment_different_bank");
            a.setAppRouteArguments(new HashMap<String, String>() {{
                put("previousAppBarTitle", "Back");
                put("isSwitch", Boolean.FALSE.toString());
                put("contractId", contractPaymentTerm.getContract().getId().toString());
                put("contractUuid", contractPaymentTerm.getContract().getUuid());
            }});
            cta.put("paying_via_credit_card_sign_now_click_here", a);
        }

        if (t.getChannelSetting(ChannelSpecificSettingType.Notification)
                .getText()
                .contains("signing_offer_clicking_here")) {
            AppAction a = new AppAction();
            a.setType(AppActionType.LINK);
            a.setText("clicking here");
            a.setFunctionType(FunctionType.NAVIGATE);
            a.setNavigationType(NavigationType.INAPP);
            a.setAppRouteName("/payment_different_bank");
            a.setAppRouteArguments(new HashMap<String, String>() {{
                put("previousAppBarTitle", "Back");
                put("isSwitch", Boolean.FALSE.toString());
                put("contractId", contractPaymentTerm.getContract().getId().toString());
                put("contractUuid", contractPaymentTerm.getContract().getUuid());
            }});
            cta.put("signing_offer_clicking_here", a);
        }
    }

    //ACC-4905
//    public void fillOneMonthAgreementFlowMessageParameter(
//            DDMessaging ddMessaging,
//            ContractPaymentTerm contractPaymentTerm,
//            Map<String, String> parameters) {
//
//        logger.log(Level.INFO, "contractPaymentTerm id: {0}", contractPaymentTerm.getId());
//        DateTime paidEndDate = new DateTime(contractPaymentTerm.getContract().getPaidEndDate());
//        parameters.put("paid_end_date", paidEndDate.toString("yyyy-MM-dd"));
//
//        if (ddMessaging.getSendPayTabMessage()) {
//
//            ContractPaymentConfirmationToDo toDo = Setup.getApplicationContext().getBean(OneMonthAgreementFlowService.class)
//                    .createTodoIfNotExists(contractPaymentTerm);
//
//            logger.log(Level.INFO, "toDo id: {0}", toDo.getId());
//            checkPaidEndDate(contractPaymentTerm.getContract(), ddMessaging, parameters, paidEndDate);
//
//            String link  = shortener.shorten(Setup.getCoreParameter(CoreParameter.PUBLIC_LINK_BASE)
//                + "/modules/accounting/paytabs/#!/home?uid=" + contractPaymentTerm.getContract().getUuid());
//            parameters.put("amount", String.valueOf(toDo.getTotalAmount().intValue()));
//            parameters.put("todoUuid", toDo.getUuid());
//            parameters.put("paying_via_credit_card_sms", link);
//            parameters.put("ownerType", "ContractPaymentConfirmationToDo");
//            parameters.put("ownerId", toDo.getId().toString());
//        }
//        parameters.putIfAbsent("scheduled_termination_date",
//                DateUtil.formatClientFullDate(paidEndDate.isAfter(new DateTime()) ?
//                        paidEndDate.toDate() : new LocalDate().toDate()));
//    }

    //ACC-4905
//    public void fillOneMonthAgreementFlowMessageContext(
//            DDMessaging ddMessaging,
//            ContractPaymentTerm cpt,
//            Map<String, String> parameters,
//            Map<String, AppAction> context) {
//
//        logger.log(Level.INFO, "contractPaymentTerm id: {0}", cpt.getId());
//
//        if (oneMonthAgreementFlowService.showSignNowButton(ddMessaging))
//            context.put("sign_now", getSignNowButton(cpt.getContract()));
//
//        if (ddMessaging.getSendPayTabMessage()) {
//            context.put("payment_credit_card", getPaytabsButton(parameters, cpt.getContract()));
//        }
//    }

    public void fillBouncedPaymentParameter(
            DDMessaging ddMessaging,
            ContractPaymentTerm contractPaymentTerm,
            Map<String, String> parameters) {

        parameters.put("bounced_payment_amount", "");
        parameters.put("cheque_number", "");
        parameters.put("bank_name", "");
        parameters.put("cheque_name", "");

        logger.info("parameters bouncedPaymentId:" + parameters.get("bouncedPaymentId"));
        Payment payment = Setup.getRepository(PaymentRepository.class).findOne(Long.valueOf(parameters.get("bouncedPaymentId")));
        if (payment == null) {
            logger.info("no bounced payment found");
            return;
        }

        parameters.put("bouncedPaymentId", payment.getId().toString());
        parameters.put("cheque_number", payment.getChequeNumber());
        parameters.put("bank_name", payment.getBankName() != null ? payment.getBankName().getName() : "");
        parameters.put("cheque_name", payment.getChequeName());

        logger.log(Level.INFO, "contractPaymentTerm id: {0}", contractPaymentTerm.getId());


        if (ddMessaging.getBouncedPaymentStatus() != null &&
                ddMessaging.getBouncedPaymentStatus().getCode().equals("bounced_payment_received")) {

            Date soonestDate = utils.getSoonestPayrollDate();
            parameters.put("soonest_payroll", soonestDate != null ?
                    DateUtil.formatDateDashedV2(soonestDate) : "");
        }

        parameters.put("ownerType", "Payment");
        parameters.put("ownerId", parameters.get("bouncedPaymentId"));

        String days = Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_ACCOUNTANT_EMAILS_CHEQUE_DAYS);
        parameters.put("number_of_days", days);

        // ACC-6513
        ContractPaymentConfirmationToDo toDo = Setup.getApplicationContext()
                .getBean(BouncingFlowService.class)
                .createToDoIfNotExists(Long.valueOf(parameters.get("bouncedPaymentId")), contractPaymentTerm);

        parameters.put("bounced_payment_amount", String.valueOf(toDo.getTotalAmount().intValue()));
        parameters.put("latest_bounced_amount", parameters.get("bounced_payment_amount"));

        String bouncedPaymentLink = "https://ccapp.page.link?amv=0&apn=cc.maids.app&link=https%3A%2F%2Fccapp.page.link%2Fpayment_bounced" +
                (parameters.get("bounced_payment_amount") == null ? "" : "%3Famount%3D" + parameters.get("bounced_payment_amount")) +
                (parameters.get("bouncedPaymentId") == null ? "" : "%26bouncedPaymentId%3D" + parameters.get("bouncedPaymentId")) +
                (toDo == null ? "" : "%26todoUuid%3D" + toDo.getUuid());
        logger.log(Level.INFO, "bouncedPaymentLink : {0}", bouncedPaymentLink);

        parameters.put("bounced_payment_sms_link",  shortener.shorten(bouncedPaymentLink));
        logger.log(Level.INFO, "bouncedPaymentLink  after shorten: {0}", parameters.get("bounced_payment_sms_link"));

        if (toDo != null) parameters.put("todoUuid", toDo.getUuid());
    }

    public void fillBouncedPaymentContext(
            Template t,
            ContractPaymentTerm contractPaymentTerm,
            Map<String, String> parameters,
            Map<String, AppAction> cta) {

        logger.log(Level.INFO, "contractPaymentTerm id: {0}", contractPaymentTerm.getId());

        if (t == null || !t.isChannelExist(ChannelSpecificSettingType.Notification)) return;

        {
            AppAction a = new AppAction();
            a.setFunctionType(FunctionType.NAVIGATE);
            a.setNavigationType(NavigationType.INAPP);
            a.setAppRouteArguments(new HashMap<String, String>() {{
                if (parameters.containsKey("todoUuid")) {
                    put("todoUuid", parameters.get("todoUuid"));
                }
                put("bouncedPaymentId", parameters.get("bouncedPaymentId"));
                put("amount", parameters.get("bounced_payment_amount"));
                put("notificationType", t.getName());
                put("contractId", contractPaymentTerm.getContract().getId().toString());
                put("contractUuid", contractPaymentTerm.getContract().getUuid());
            }});
            a.setAppRouteName("/payment_bounced");
            // ACC-4706
            if (t.getChannelSetting(ChannelSpecificSettingType.Notification)
                    .getText()
                    .contains("bounced_payment_clicking_here")) {
                a.setType(AppActionType.LINK);
                a.setText("clicking here");
                cta.put("bounced_payment_clicking_here", a);
            } else if (t.isChannelExist(ChannelSpecificSettingType.SMS) &&
                   t.getChannelSetting(ChannelSpecificSettingType.SMS)
                            .getParameters()
                            .stream()
                            .anyMatch(p -> p.getName().equals("bounced_payment_sms_link"))){

                a.setType(AppActionType.BUTTON);
                a.setText("Pay Now");
                cta.put("bounced_payment_clicking_here", a);
            }
        }

        // ACC-4706 #2
        if (t.getChannelSetting(ChannelSpecificSettingType.Notification)
                .getText()
                .contains("payment_bounced_sign_now_clicking_here")) {
            AppAction a = new AppAction();
            a.setType(AppActionType.LINK);
            a.setText("clicking here");
            a.setFunctionType(FunctionType.NAVIGATE);
            a.setNavigationType(NavigationType.INAPP);
            a.setAppRouteArguments(new HashMap<String, String>() {{
                put("previousAppBarTitle", "Back");
                put("isSwitch", Boolean.FALSE.toString());
                put("contractId", contractPaymentTerm.getContract().getId().toString());
                put("contractUuid", contractPaymentTerm.getContract().getUuid());
            }});
            a.setAppRouteName("/payment_different_bank");
            cta.put("payment_bounced_sign_now_clicking_here", a);
        }
    }

    public void fillDirectDebitRejectedContext(
            DDMessaging ddMessaging,
            ContractPaymentTerm contractPaymentTerm,
            Map<String, Object> context) {

    }

    public void fillIncompleteDDRejectedByDataEntryContext(
            DDMessaging ddMessaging,
            ContractPaymentTerm contractPaymentTerm,
            Map<String, Object> context) {

    }

    public void fillOnlineCreditCardPaymentRemindersParameter(
            FlowProcessorEntity entity,
            ContractPaymentTerm cpt,
            Map<String, String> parameters,
            Template t) {

        parameters.put("paytabs_link", "");
        parameters.put("todoUuid", "");
        parameters.put("amount", "");
        parameters.put("description", "");
        parameters.put("breakdown_description", "");
        parameters.put("paid_end_date", new LocalDate(cpt.getContract().getPaidEndDate()).toString("yyyy-MM-dd"));
        parameters.put("paid_end_date - 1", new LocalDate(cpt.getContract().getPaidEndDate()).minusDays(1).toString("yyyy-MM-dd"));

        logger.log(Level.INFO, "contractPaymentTerm id: {0}", cpt.getId());

        if (entity == null) {
            entity =  Setup.getRepository(FlowProcessorEntityRepository.class)
                    .findFirstByFlowEventConfigAndContractPaymentTermAndStoppedFalseAndCompletedFalseOrderByCreationDateDesc(
                            Setup.getRepository(FlowEventConfigRepository.class).findByName(
                                    FlowEventConfig.FlowEventName.ONLINE_CREDIT_CARD_PAYMENT_REMINDERS),
                            cpt);
        }

        if (entity == null) {
            logger.info("null entity -> existing");
            return;
        }

        ContractPaymentConfirmationToDo toDo = entity.getContractPaymentConfirmationToDo();
        if (toDo == null || toDo.isDisabled()) return;

        logger.log(Level.INFO, "ToDo id: {0}", toDo.getId());

        parameters.put("paytabs_link", Setup.getApplicationContext()
                .getBean(ContractPaymentConfirmationToDoService.class)
                .getPayingViaCreditCardLink(toDo));
        parameters.put("todoUuid", toDo.getUuid());
        parameters.put("amount", PaymentHelper.df.format(toDo.getTotalAmount().intValue()));
        parameters.put("ownerType", "ContractPaymentConfirmationToDo");
        parameters.put("ownerId", toDo.getId().toString());
        fillDescriptionAndBreakdownDescription(t, toDo, parameters);

    }

    public void fillOnlineCreditCardPaymentRemindersContext(
            ContractPaymentTerm cpt,
            Map<String, String> parameters,
            Map<String, AppAction> cta) {

        logger.log(Level.INFO, "contractPaymentTerm id: {0}", cpt.getId());

        if (parameters.get("paytabs_link") != null) {
            cta.put("payment_credit_card", getPaytabsButton(parameters, cpt.getContract()));
        }
    }

    public void fillExpiryPaymentParameter(ContractPaymentTerm contractPaymentTerm, Map<String, String> parameters) {
        parameters.put("monthly_fee_of_nationality", String.valueOf(contractPaymentTerm.getLastConfirmedDD() != null
                ? contractPaymentTerm.getLastConfirmedDD().getAmount() : ""));
        parameters.put("ExpiryDate", new LocalDate().plusMonths(1).toString("MM/yyyy"));
        parameters.put("receiver_name", contractPaymentTerm.getContract().getClient().getName());

        String link = contractPaymentTerm.getContract().isMaidCc() ?
                "https://ccapp.page.link?amv=0&apn=cc.maids.app&link=https%3A%2F%2Fccapp.page.link%chat_with_us" :
                "https://wa.me/" + StringHelper.NormalizePhoneNumber(
                        Setup.getParameter(Setup.getCurrentModule(),
                                AccountingModule.PARAMETER_MAIDS_CC_CLIENT_CALL));

        Map<String, Object> signDDMap = new HashMap<>();
        signDDMap.put("cpt", contractPaymentTerm);
        signDDMap.put("additionalInfo", new HashMap<String, Object>() {{
            put("sentFrom", "PaymentExpiryFlow");
        }});

        parameters.put("chat_with_us_link", utils.shorteningUrl(link));
        parameters.put("link_send_dd_details", utils.getSingDDLink(signDDMap));
        parameters.put("greetings", contractPaymentTerm.getContract().isMaidCc() ?
                Setup.getCoreParameter(CoreParameter.SMS_GREETINGS_MAIDSCC) :
                Setup.getCoreParameter(CoreParameter.SMS_GREETINGS_MAIDSVISA));
    }

    public void fillExpiryPaymentContext(
            ContractPaymentTerm cpt,
            Map<String, String> parameters,
            Map<String, AppAction> cta) {

        AppAction a = new AppAction();
        a.setType(AppActionType.LINK);
        if (cpt.getContract().isMaidCc()) {
            a.setText("here");
            a.setFunctionType(FunctionType.NAVIGATE);
            a.setNavigationType(NavigationType.INAPP);
            a.setHyperlink("");
            a.setAppRouteName("/medical_care/chat_screen");
        } else {
            a.setText("click here");
            a.setFunctionType(FunctionType.WEB_SERVICE);
            a.setNavigationType(NavigationType.WEB);
            a.setHyperlink(parameters.get("chat_with_us_link"));
            a.setAppRouteName("");
        }

        a.setAppRouteArguments(new HashMap<String, String>() {{
            put("contractId", cpt.getContract().getId().toString());
            put("contractUuid", cpt.getContract().getUuid());
        }});
        cta.put("chat_with_us_path", a);
        parameters.put("chat_with_us", "@chat_with_us_path@");
    }

    public AppAction getSignNowButton(String text, Contract contract) {
        AppAction a = new AppAction();
        a.setType(AppActionType.BUTTON);
        a.setText(text);
        a.setFunctionType(FunctionType.NAVIGATE);
        a.setNavigationType(NavigationType.INAPP);
        a.setAppRouteName("/payment_different_bank");
        a.setAppRouteArguments(new HashMap<String, String>() {{
            put("previousAppBarTitle", "Back");
            put("isSwitch", Boolean.FALSE.toString());
            put("contractId", contract.getId().toString());
            put("contractUuid", contract.getUuid());
        }});
        return a;
    }

    public AppAction getPaytabsButton(Map<String, String> parameters, Contract contract) {
        return getPaytabsAction(parameters, contract, AppActionType.BUTTON,
                "Pay Now", null, "/my_monthly_payments_pay_with_cc");
    }

    public AppAction getPaytabsAction(
            Map<String, String> parameters, Contract contract,
            AppActionType type, String text, String cmsText, String routeName) {

        AppAction a = new AppAction();
        a.setType(type);
        a.setText(text);
        a.setFunctionType(FunctionType.NAVIGATE);
        a.setNavigationType(NavigationType.INAPP);
        a.setAppRouteName(routeName);
        a.setAppRouteArguments(new HashMap<String, String>() {{
            put("todoUuid", parameters.get("todoUuid"));
            put("amount", (parameters.containsKey("todoTotalAmount") ?
                    parameters.get("todoTotalAmount") : parameters.get("amount"))
                    .replaceAll(",", ""));
            put("contractId", contract.getId().toString()); // ACC-6067
            put("contractUuid", contract.getUuid());
            if (cmsText != null) put("cmsText", cmsText);
        }});

        return a;
    }

    // ACC-3280
    private void processMaidMessage(
            Template template,
            Map<String, String> parameters,
            Long housemaidId,
            Long ownerId,
            String ownerType,
            Contract contract) {

        if (housemaidId == null) {
            logger.warning("Maid ID is NULL -> do nothing");
            return;
        }

        Housemaid housemaid = Setup.getRepository(HousemaidRepository.class)
                .findOne(housemaidId);

        if (parameters.containsKey("maid_name") && parameters.get("maid_name").equalsIgnoreCase("your maid")) {
            parameters.put("maid_name", "");
        }

        if (parameters.containsKey("maid_first_name") && parameters.get("maid_first_name").equalsIgnoreCase("your maid")) {
            parameters.put("maid_first_name", "");
        }

        if (!contract.isMaidCc() &&
                template.isChannelExist(ChannelSpecificSettingType.SMS) &&
                 template.getChannelSetting(ChannelSpecificSettingType.SMS)
                         .getText().contains("visa_cancellation_paper_link")) { // ACC-3280

             insertMessageLog(template.getName(), template, parameters, housemaid, contract);
             return;
         }

        messagingService.sendMessageToMaid(contract, housemaid,
                template, parameters, ownerId, ownerType);
    }

    private void insertMessageLog(
            String messageTemplateName,
            Template template,
            Map paramValues,
            Housemaid housemaid,
            Contract contract) {

        if (template == null) template = TemplateUtil.getTemplate(messageTemplateName);

        logger.info("Insert Log for Maid: " + housemaid.getId() + "; Message Template Name = " + template.getName());

        if (housemaid.getNormalizedPhoneNumber() == null || housemaid.getNormalizedPhoneNumber().isEmpty()) {
            logger.info("maid#" + housemaid.getId() + " has no normalized mobile number");
            return;
        }

        String msg = TemplateUtil.compileTemplateText(
                template, template.getChannelSetting(ChannelSpecificSettingType.SMS),
                template.getChannelSetting(ChannelSpecificSettingType.SMS).getText(), contract, paramValues);

        Sms sms = new Sms();
        sms.setType(getType(messageTemplateName));
        sms.setMobileNumber(UaePhoneNormlizer.NormalizePhoneNumber(housemaid.getNormalizedPhoneNumber()));
        sms.setSentDate(new Timestamp(new Date().getTime()));
        sms.setText(msg);
        sms.setReceiverType(SmsReceiverType.Housemaid);
        sms.setReceiverId(housemaid.getId());
        sms.setReceiverName(housemaid.getName());
        sms.setUnicode(false);
        sms.setTemplate(template);
        sms.setRelatedEntityId1(contract.getId());
        sms.setRelatedEntityType1(contract.getEntityType());

        sms.addBaseAdditionalInfo("maidId", housemaid.getId().toString());
        Setup.getRepository(SmsRepository.class).save(sms);
    }

    private PicklistItem getType(String type) {
        Picklist typesList = Setup.getRepository(PicklistRepository.class)
                .findByCode(Picklist.SMS_TYPES);
        PicklistItem item;

        if (typesList != null) {
            try {
                List<PicklistItem> items = itemRepository.findAllByListAndCode(
                        typesList, PicklistItem.getCode(type));

                if (items == null || items.isEmpty()) {
                    item = new PicklistItem(typesList, type);
                    itemRepository.save(item);
                } else {
                    item = items.get(0);
                }
                return item;
            } catch (Exception ex) {
                logger.log(Level.SEVERE,
                        ex.getMessage(),
                        ex);
                return null;
            }
        }
        return null;
    }

    public boolean validateContractData(Contract contract, DDMessaging ddMessaging) {
        if(ddMessaging == null) return true;

        switch(ddMessaging.getEvent()) {
            case DirectDebitRejected:
                return Setup.getRepository(DirectDebitRejectionToDoRepository.class)
                        .existsActiveByContract(contract);
        }

        return true;
    }

    public void sendForceSms(
            DDMessagingToDo ddMessagingToDo,
            Template template,
            Map<String, String> parameters,
            ContractPaymentTerm cpt) {

        //CMA-2440 ClientMessagePriority.SEND_SMS_NEXT_DAY
        logger.log(Level.SEVERE, "ddMessagingToDo " + ddMessagingToDo.getId());
        logger.log(Level.SEVERE, "client template name  " + ddMessagingToDo.getClientTemplateName());

        DDMessaging ddMessaging = null;
        if (ddMessagingToDo.getDdMessagingConfigId() != null)
            ddMessaging = ddMessagingRepository.findOne(ddMessagingToDo.getDdMessagingConfigId());

        if (ddMessaging == null) {
            if (ddMessagingToDo.getClientTemplateName().contains("CLIENT_REFUND_BANK_TRANSFER_DETAILS")) {
                PayrollAccountantTodo payrollAccountantTodo = payrollAccountantTodoRepository.findOne
                (ddMessagingToDo.getPayrollAccountantTodoId());
                if (payrollAccountantTodo != null) {
                    if (payrollAccountantTodo.getClientRefundToDo() != null) {
                        parameters.put("amount", String.valueOf(payrollAccountantTodo
                                .getClientRefundToDo().getAmount().intValue()));
                    }
                    parameters.putAll(Setup.getApplicationContext().getBean(ClientRefundService.class)
                            .getProofTransferLink(ddMessagingToDo.getPayrollAccountantTodoId()));
                }
            }

            // ACC-5183 ACC-5214
            if (ddMessagingToDo.getDirectDebitGenerationPlanId() != null) {
                DirectDebitGenerationPlan plan = Setup.getRepository(DirectDebitGenerationPlanRepository.class)
                        .findOne(ddMessagingToDo.getDirectDebitGenerationPlanId());
                parameters.put("Client_Name", ddMessagingToDo.getClientName());
                parameters.put("client_nickname_or_name", StringUtils.getClientNicknameOrName(cpt.getContract().getClient()));

                switch (plan.getContractPaymentType().getType().getCode()) {
                    case AbstractPaymentTypeConfig.INSURANCE_TYPE_CODE:
                        parameters.put("insurance_DD_amount", String.valueOf(plan.getAmount().intValue()));
                        parameters.put("payment_date", plan.getDDSendDate().toString());
                        break;
                    case AbstractPaymentTypeConfig.AGENCY_FEE_TYPE_CODE:
                        parameters.put("SDR_DD_Amount", String.valueOf(plan.getAmount().intValue()));
                        parameters.put("SDR_Payment_date", plan.getDDSendDate().toString());
                        break;
                    default:
                        parameters.put("payment_amount", String.valueOf(plan.getAmount().intValue()));
                        parameters.put("DD_TYPE", plan.getContractPaymentType().getDescription());
                        parameters.put("payment_date", plan.getDDSendDate().toString());
                        break;
                }
            }
            // ACC-5235
            if (Arrays.asList(CcNotificationTemplateCode.CC_ACCOUNTING_NOT_OWED_MONEY_FROM_CLIENT_8_1_2_NOTIFICATION.toString(),
                CcNotificationTemplateCode.CC_ACCOUNTING_WRONGLY_CHARGED_MONEY_ON_CLIENT_8_1_3_NOTIFICATION.toString())
                .contains(ddMessagingToDo.getClientTemplateName()))
                parameters.put("amount", String.valueOf(ddMessagingToDo.getAmount().intValue()));

            if (ddMessagingToDo.getClientTemplateName().equals(CcNotificationTemplateCode.CC_ACCOUNTING_OWE_MONEY_TO_CLIENT_8_1_1_NOTIFICATION.toString()))
                parameters.put("remaining_balance", String.valueOf(ddMessagingToDo.getAmount().intValue()));

            if (ddMessagingToDo.getEvent()!= null && ddMessagingToDo.getEvent().equals(DDMessagingType.ExpiryPayment))
                fillExpiryPaymentParameter(cpt, parameters);
        }

        Map<String, Object> signDDMap = new HashMap<>();
        signDDMap.put("cpt", cpt);
        signDDMap.put("additionalInfo", new HashMap<String, Object>() {{
            put("sentFrom", "sendForceSms");
        }});

        parameters.put("link_send_dd_details", utils.getSingDDLink(signDDMap));

        Client client = clientRepository.findOne(ddMessagingToDo.getClientId());
        List<PushNotification> clientNotifications = pushNotificationHelper.getByRecepientTypeAndIdOrderByCreationDesc(
                "Client", client.getId());

        PushNotification pushNotification = clientNotifications.stream().filter(o -> o.getType() != null
            && o.getType().getCode().equalsIgnoreCase(ddMessagingToDo.getClientTemplateName())
            && new LocalDate(o.getCreationDate()).toString("yyyy-MM-dd")
            .equals(new LocalDate().minusDays(1).toString("yyyy-MM-dd"))).findFirst().orElse(null);

        if (pushNotification != null) {
            if (pushNotification.isReceived()) return;
            pushNotification.setSmsSendDate(new Date());
            ChannelSpecificSetting channelSpecificSetting = template.getChannelSetting(ChannelSpecificSettingType.SMS.toString());
            pushNotification.setSmsText(TemplateUtil.compileTemplateText(template, channelSpecificSetting,
                    channelSpecificSetting.getText(), new ArrayList<>(), parameters));
            pushNotification.setSmsSent(true);
            disablePushNotificationRepository.save(pushNotification);
        }

        logger.log(Level.SEVERE, "SEND_SMS_NEXT_DAY sms template name " + template.getName());
        messagingService.sendClientSms(cpt.getContract(),
                template,
                parameters,
                new HashMap<>(),
                client.getId(),
                client.getEntityType());

        ddMessagingToDo.setSent(true);
    }

    public void disableOldNotificationAfterSend(DDMessaging ddMessaging, Contract c) {

        switch (ddMessaging.getEvent()) {
            case ClientPaidCashAndNoSignatureProvided:
            case ClientsPayingViaCreditCard:
            // case OneMonthAgreement:

                if (ddMessaging.getEvent().equals(DDMessagingType.ClientsPayingViaCreditCard)){

                    switch (ddMessaging.getSubType()) {
                        case EXCEEDING_DAILY_LIMITS:
                        case EXPIRED_CARD:
                        case INSUFFICIENT_FUNDS:
                        case ACCOUNT_ISSUE:
                        case OTHER_ISSUES:
                        case TOKEN_DELETED:
                            List<PushNotification> clientNotifications = ddMessaging.getEvent().equals(DDMessagingType.ClientsPayingViaCreditCard) ?
                                    disablePushNotificationRepository
                                            .findActiveHomeNotificationsByDDMessagingType(
                                                    c.getClient().getId().toString(),
                                                    Arrays.asList(DDMessagingType.ClientsPayingViaCreditCard,
                                                            DDMessagingType.OneMonthAgreement),
                                                    c.getId()) :
                                    disablePushNotificationRepository
                                            .findActiveHomeNotificationsByDDMessagingType(
                                                    c.getClient().getId().toString(), ddMessaging.getEvent(), c.getId());
                            messagingService.createMoveToInboxBgt(
                                clientNotifications.stream().map(PushNotification::getId).collect(Collectors.toList()),
                                "move to inbox once next reminder sent");
                            return;
                    }
                }
                // ACC-7172
                if (!ddMessaging.getScheduleTermCategory()
                        .equals(DirectDebitMessagingScheduleTermCategory.None)) return;


                List<PushNotification> clientNotifications = ddMessaging.getEvent().equals(DDMessagingType.ClientsPayingViaCreditCard) ?
                        disablePushNotificationRepository
                                .findActiveNotificationsByDDMessagingType(
                                        c.getClient().getId().toString(),
                                        Arrays.asList(DDMessagingType.ClientsPayingViaCreditCard,
                                                DDMessagingType.OneMonthAgreement),
                                        c.getId()) :
                        disablePushNotificationRepository
                                .findActiveNotificationsByDDMessagingType(
                                        c.getClient().getId().toString(), ddMessaging.getEvent(), c.getId());

                pushNotificationHelper.stopDisplaying(clientNotifications);

                if (ddMessaging.getEvent().equals(DDMessagingType.ClientPaidCashAndNoSignatureProvided))
                    // ACC-5214
                    Setup.getApplicationContext().getBean(AfterCashFlowService.class)
                            .disableThankYouMessage(c);
                break;
            case DirectDebitRejected:
            case BouncedPayment:
            case IncompleteDDRejectedByDataEntry:
            case IncompleteDDClientHasNoApprovedSignature:
                clientNotifications = disablePushNotificationRepository
                        .findActiveHomeNotificationsByDDMessagingType(
                                c.getClient().getId().toString(), ddMessaging.getEvent(), c.getId());
                messagingService.createMoveToInboxBgt(
                        clientNotifications.stream().map(BaseEntity::getId).collect(Collectors.toList()),
                        "Next reminder is sent");
                break;
            case Termination:
                clientNotifications = disablePushNotificationRepository
                        .findActiveHomeNotificationsByDDMessagingType(
                                c.getClient().getId().toString(),
                                Arrays.asList(
                                        DDMessagingType.ClientPaidCashAndNoSignatureProvided,
                                        DDMessagingType.DirectDebitRejected,
                                        DDMessagingType.BouncedPayment,
                                        DDMessagingType.OnlineCreditCardPaymentReminders,
                                        DDMessagingType.IncompleteDDClientHasNoApprovedSignature,
                                        DDMessagingType.IncompleteDDRejectedByDataEntry,
                                        DDMessagingType.ClientsPayingViaCreditCard,
                                        DDMessagingType.OneMonthAgreement),
                                c.getId());
                messagingService.createMoveToInboxBgt(
                        clientNotifications.stream().map(BaseEntity::getId).collect(Collectors.toList()),
                        "Next reminder is sent");
                break;
            case OnlineCreditCardPaymentReminders:
                clientNotifications = disablePushNotificationRepository
                        .findActiveHomeNotificationsByDDMessagingType(
                                c.getClient().getId().toString(), ddMessaging.getEvent(), c.getId());
                clientNotifications.addAll(disablePushNotificationRepository
                        .findActiveNotifications(c.getClient().getId().toString(), c.getId(),
                                Arrays.asList(
                                        CcNotificationTemplateCode.CC_PAYMENT_FOR_APPROVAL_REQUEST_FROM_ERP.toString(),
                                        MvNotificationTemplateCode.MV_PAYMENT_FOR_APPROVAL_REQUEST_FROM_ERP.toString())));
                messagingService.createMoveToInboxBgt(
                        clientNotifications.stream().map(BaseEntity::getId).collect(Collectors.toList()),
                        "Next reminder is sent");
                break;
        }
    }

    private void sendAlertUponVipReceiveMessage(
            Client client,
            DDMessaging ddMessaging,
            Long contractId,
            String messageSentText) {

        if (!client.isVip() || !ddMessaging.getAlertForVip()) return;
        logger.log(Level.INFO, "Client id : {0}; Contract id : {1}", new Object[] {client.getId(), contractId});

        Map<String, String> parameters = new HashMap();
        parameters.put("client_name", client.getName());
        parameters.put("contract_id", contractId.toString());
        String url = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_FRONT_END_URL)
                + "#!/client/details/" + client.getId();
        parameters.put("erp_profile_link", Setup.getApplicationContext().getBean(Shortener.class).shorten(url));
        parameters.put("event", ddMessaging.getEvent().getLabel());
        parameters.put("number_of_trials", ddMessaging.getTrials());
        parameters.put("number_of_reminder", ddMessaging.getReminders());
        parameters.put("message_sent_to_client", messageSentText);

        messagingService.sendEmailToOfficeStaff("alert_vip_has_collection_flow",
                        parameters, Setup.getParameter(Setup.getCurrentModule(),
                                AccountingModule.RECEIPTS_EMAIL_UPON_VIP_CLIENT_RECEIVE_MESSAGE),
                        "Alert : VIP client has a running collection flow");
    }

    //Acc-4684
    public void handleDdMessagingSendTime(DDMessaging ddMessaging, Client client) {

        LocalTime currentTime = new LocalTime();
        logger.log(Level.INFO, "ddMessaging id : {0}", ddMessaging.getId());
        if (ddMessaging.getSendTime() != null)
            currentTime = new LocalTime(ddMessaging.getSendTime().getTime());

        logger.log(Level.INFO, "currentTime: {0}", currentTime.toString("HH:mm:ss"));

        LocalTime startAllowingTime = LocalTime.parse(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.ACC_START_ALLOWING_MESSAGE_SENDING_TIME));
        LocalTime endAllowingTime = LocalTime.parse(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.ACC_STOP_ALLOWING_MESSAGE_SENDING_TIME));
        LocalTime firstMessageTime = LocalTime.parse(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.ACC_SHIFT_FIRST_MESSAGE_SENDING_TIME));

        LocalTime startShiftingSecondMessageTime = LocalTime.parse(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.ACC_START_SHIFTING_SECOND_MESSAGE_TIME));
        LocalTime endShiftingSecondMessageTime = LocalTime.parse(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.ACC_END_SHIFTING_SECOND_MESSAGE_TIME));
        LocalTime secondMessageSendingTime = LocalTime.parse(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.ACC_SHIFT_SECOND_MESSAGE_SENDING_TIME));

        int sendAfterDays = currentTime.isAfter(endAllowingTime) ?
                1 : 0;

        // check if current time between 8 PM and 8AM shift the message to next day at 9 AM
        boolean allowSending = currentTime.isAfter(startAllowingTime)
                && currentTime.isBefore(endAllowingTime) ;
        if (allowSending) {
            // check if current time between 8 AM and 1 PM
            boolean shiftNewMessage = currentTime.isAfter(startShiftingSecondMessageTime)
                    && currentTime.isBefore(endShiftingSecondMessageTime);
            logger.log(Level.INFO, "shiftNewMessage : {0}", shiftNewMessage);

            if (!shiftNewMessage) return;
            // check if old message was sent today the new message will shift to 5 PM
            boolean oldMessageSentToday = Setup.getRepository(DDMessagingToDoRepository.class).existsByClientIdAndSendTimeAndSendDateAndEvent(
                    client.getId(), Time.valueOf(firstMessageTime.toString("HH:mm:ss")),
                    new java.sql.Date(new LocalDate().toDate().getTime()), ddMessaging.getEvent());
            if (oldMessageSentToday) {
                sendAfterDays = 0;
            } else
                return;
        }
        logger.log(Level.INFO, "allowSending : {0}", allowSending);

        ddMessaging.setSendTime(Time.valueOf(sendAfterDays == 0 && allowSending ? secondMessageSendingTime.toString("HH:mm:ss")
                 : firstMessageTime.toString("HH:mm:ss")));
        ddMessaging.setSendDate(java.sql.Date.valueOf(new LocalDate().plusDays(sendAfterDays).toString("yyyy-MM-dd")));
    }

    // ACC-5553
    private void checkPaidEndDate(
            Contract contract, DDMessaging ddMessaging,
            Map<String, String> parameters, DateTime paidEndDate) {

        if (new DateTime(contract.getStartOfContract()).toString("yyyy-MM-dd")
                .equals(paidEndDate.toString("yyyy-MM-dd"))) {
            Integer lastReminderPeriod = Setup.getRepository(FlowProgressPeriodRepository.class)
                    .findPeriodInHoursByFlowSubEventConfig_NameAndTrialAndReminder(
                            FlowSubEventConfig.FlowSubEventName.valueOf(ddMessaging.getSubType().getValue()),
                            Integer.parseInt(ddMessaging.getTrials()) + 1,
                            Integer.parseInt(ddMessaging.getReminders()));
            paidEndDate = new DateTime().withHourOfDay(10).plusHours(lastReminderPeriod == null ? 0 : lastReminderPeriod);

            parameters.put("paid_end_date", paidEndDate.toString("yyyy-MM-dd"));
            parameters.put("paid_end_date - 1", paidEndDate.toString("yyyy-MM-dd"));
        }
    }

    public AppAction getWebLinkContext(Contract contract, String linkUrl, String label) {
        AppAction a = new AppAction();
        a.setType(AppActionType.LINK);
        a.setText(label);
        a.setFunctionType(FunctionType.WEB_SERVICE);
        a.setNavigationType(NavigationType.WEB);
        a.setHyperlink(linkUrl);
        a.setAppRouteName("");
        a.setAppRouteArguments(new HashMap<String, String>() {{
            put("contractId", contract.getId().toString());
            put("contractUuid", contract.getUuid());
        }});

        return a;
    }

    public boolean isDdMessagingContainsCreditCardLink(DDMessaging ddMessaging) {
        if (ddMessaging.getClientTemplate() == null ||
                ddMessaging.getClientTemplate().getChannelSetting(ChannelSpecificSettingType.SMS) == null ||
                ddMessaging.getClientTemplate().getChannelSetting(ChannelSpecificSettingType.SMS).getText() == null) return false;

        return ddMessaging.getClientTemplate().getChannelSetting(ChannelSpecificSettingType.SMS).getText().contains("@bounced_payment_sms_link@") || // bounced payment flow
                ddMessaging.getClientTemplate().getChannelSetting(ChannelSpecificSettingType.SMS).getText().contains("@paytabs_link@") || // IPAM and online reminder
                ddMessaging.getClientTemplate().getChannelSetting(ChannelSpecificSettingType.SMS).getText().contains("@paying_via_credit_card_sms@") || // paying via cc
                ddMessaging.getClientTemplate().getChannelSetting(ChannelSpecificSettingType.SMS).getText().contains("@payment_options_link@"); // DD Messaging parameter for bounced flow route
    }

    public boolean isDdMessagingContainsCreditCardOfferLink(DDMessaging ddMessaging) {
        if (ddMessaging.getClientTemplate() == null ||
                ddMessaging.getClientTemplate().getChannelSetting(ChannelSpecificSettingType.Notification) == null) return false;

        return ddMessaging.getClientTemplate().getChannelSetting(ChannelSpecificSettingType.Notification).getText().contains("@cc_offer_sentence@");
    }

    public static String getSignButtonName(DDMessaging d) {

        switch (d.getEvent()) {
            case IncompleteDDRejectedByDataEntry:
                switch (d.getDataEntryRejectCategory()){
                    case WrongEID:
                        return "Add Account Holder's EID Photo";
                    case WrongIBAN:
                        return "Add Account Holder's IBAN Photo";
                    case WrongAccountName:
                        return "Add Account Holder's Name Photo";
                    case WrongEIDAndIBAN:
                        return "Add EID and IBAN Photos";
                    case WrongEIDAndAccountName:
                        return "Add EID and Name Photos";
                    case WrongIBANAndAccountName:
                        return "Add IBAN and Name Photos";
                    case AllDataIncorrect:
                        return "Add Bank Account Details";
                }
            case IncompleteDDClientHasNoApprovedSignature:
                return "Complete Monthly Bank Payment Form";
            case BouncedPayment:
                return "Sign Now";
            case ClientPaidCashAndNoSignatureProvided:
                return "Add Bank Account Details";
            case DirectDebitRejected:
                switch (d.getRejectCategory()) {
                    case Signature:
                        if (d.getClientTemplate().getChannelSetting(ChannelSpecificSettingType.Notification)
                                .getText()
                                .contains("Add Signature Photo")) return "Add Signature Photo";
                        return "Sign Now";
                    case Account:
                        return "Add Account Holder's Name Photo";
                    case EID:
                        return "Add Account Holder's EID Photo";
                    case Authorization:
                    case Invalid_Account:
                        return "Add New Bank Account Details";
                }
            case OneMonthAgreement:
                return "Add Bank Details";
        }

        return "Sign Now";
    }

    public static boolean showSignNowButton(Template t) {

        boolean oldMessageWithLink = t.isChannelExist(ChannelSpecificSettingType.Notification) &&
                t.getChannelSetting(ChannelSpecificSettingType.Notification)
                        .getParameters()
                        .stream()
                        .anyMatch(p -> Arrays.asList(
                                        "link_send_dd_details_click_here",
                                        "link_send_dd_details_here",
                                        "link_send_dd_details_clicking_here",
                                        "link_send_dd_details_payment_link",
                                        "payment_bounced_sign_now_clicking_here")
                                .contains(p.getName()));

        return !oldMessageWithLink && t.isChannelExist(ChannelSpecificSettingType.SMS) &&
                t.getChannelSetting(ChannelSpecificSettingType.SMS)
                        .getParameters()
                        .stream()
                        .anyMatch(p -> p.getName().equals("link_send_dd_details"));

    }

    private boolean isTemplateContainsParameter(Template t, String parameter) {

        return isTemplateContainsParameters(t, Collections.singletonList(parameter));
    }

    private boolean isTemplateContainsParameters(Template t, List<String> parameters) {
        if (t == null) return false;

        Set<String> s = new HashSet<>();
        if (t.isChannelExist(ChannelSpecificSettingType.Notification)) {
            s.addAll(TemplateUtil.getParameters(t.getChannelSetting(ChannelSpecificSettingType.Notification).getText()));
        }

        if (t.isChannelExist(ChannelSpecificSettingType.SMS)) {
            s.addAll(TemplateUtil.getParameters(t.getChannelSetting(ChannelSpecificSettingType.SMS).getText()));
        }

        return s.stream().anyMatch(parameters::contains);
    }

    private String getBreakdownDescription(ContractPaymentConfirmationToDo toDo) {
        // ACC-9286
        StringBuilder s = new StringBuilder();
        for (int i = 0; i < toDo.getContractPaymentList().size(); i++) {
            ContractPaymentWrapper w = toDo.getContractPaymentList().get(i);
            String description = paymentTypeDescriptionService.getPaymentTypeDescriptionTextFromWrapper(w);
            s.append(("● AED @amount@ for @description@").trim()
                            .replace("@amount@", PaymentHelper.df.format(w.getAmount().intValue()))
                            .replace("@description@", description))
                    .append(description.endsWith(".") ? "" : ".")
                    .append((i != toDo.getContractPaymentList().size() - 1 ? "\n" : ""));
        }

        return s.toString();
    }

    private static List<Map.Entry<String, Integer>> extractWithinXDaysParameterAndValue(String text) {
        List<Map.Entry<String, Integer>> results = new ArrayList<>();
        if (text == null) return results;

        // Pattern to match parameters like @by_paid_end_date_or_within_X_days@
        Pattern pattern = Pattern.compile("@(by_paid_end_date_or_within_(\\d+)_days)@");
        Matcher matcher = pattern.matcher(text);

        while (matcher.find()) {
            try {
                String paramName = matcher.group(1);
                int days = Integer.parseInt(matcher.group(2));
                results.add(new AbstractMap.SimpleEntry<>(paramName, days));
            } catch (Exception e) {
                logger.severe("Failed to extract days parameter from text: " + e.getMessage());
                e.printStackTrace();
            }
        }

        return results;
    }
}