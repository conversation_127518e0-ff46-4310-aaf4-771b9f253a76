package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.notification.AppAction;
import com.magnamedia.core.type.CoreParameter;
import com.magnamedia.core.type.template.ChannelSpecificSettingType;
import com.magnamedia.entity.Contract;
import com.magnamedia.extra.CcNotificationTemplateCode;
import com.magnamedia.extra.CcSmsTemplateCode;
import com.magnamedia.extra.MvNotificationTemplateCode;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created at 4/9/2020
 */

@Service
public class MessageTemplateService {

    private static final Logger logger = Logger.getLogger(MessageTemplateService.class.getName());

    public static void messageTemplateSendNotification(Map<String, Object> map) {
        Contract contract = (Contract) map.get("contract");

        map.putIfAbsent("ownerId", contract.getId());
        map.putIfAbsent("ownerType", contract.getEntityType());

        Template notificationTemplate = getNotificationTemplate(map.get("templateOriginalName").toString(), contract.isMaidCc());

        logger.info("Roua checked: " + map.get("templateOriginalName").toString());
        if (notificationTemplate == null) return;

        Map<String, String> parameters = getParameters(map);
        Map<String, AppAction> cta = getContext(notificationTemplate.isChannelExist(ChannelSpecificSettingType.Notification) ?
                notificationTemplate.getChannelSetting(ChannelSpecificSettingType.Notification).getText() :
                notificationTemplate.getText(),
                parameters, contract);

        logger.log(Level.INFO, "parameters : {0}", parameters.entrySet());
        logger.log(Level.INFO, "context : {0}", cta.entrySet());
        logger.log(Level.INFO, "notificationTemplate name : {0}", notificationTemplate.getName());

        Setup.getApplicationContext().getBean(MessagingService.class)
                .sendMessageToClient(contract,
                        parameters,
                        cta,
                        (Long) map.get("ownerId"),
                        (String) map.get("ownerType"),
                        notificationTemplate,
                        null,
                        map);
    }

    public static Map<String, String> getParameters(
            Map<String, Object> map) {

        Contract contract = (Contract) map.get("contract");
        Map<String, String> parameters = new HashMap();
        switch (map.get("templateOriginalName").toString()) {
            case "ACCOUNTING_PAY_VIA_CREDIT_CARD":
                parameters.put("paytab_link", map.get("paytab_link").toString());
                parameters.put("amount", map.get("amount").toString());
                parameters.put("paytab_link_click_here", "@paytab_link_click_here_link@");
                break;
            case "DD_PENDING_INFO":
                parameters.put("link_send_dd_details", (String) map.get("link_send_dd_details"));
                parameters.put("link_send_dd_details_click_here", "@link_send_dd_details_click_here_link@");
                break;
            case "CLIENT_REFUND_BANK_TRANSFER_DETAILS":
                parameters.put("amount", map.get("amount").toString());
                parameters.putAll(Setup.getApplicationContext().getBean(ClientRefundService.class)
                    .getProofTransferLink((Long) map.get("payrollAccountantTodoId")));
                parameters.put("proof_of_transfer_sentence",
                        parameters.containsKey("proof_of_transfer_sentence_sms") &&
                                !parameters.get("proof_of_transfer_sentence_sms").isEmpty() ?
                                (" @proof_of_transfer_link_click_here_link@ " + "to view the proof of transfer.") : "");
                break;
            default:
                parameters.putAll(map.entrySet().stream()
                        .filter(e -> e.getValue() instanceof String)
                        .collect(Collectors.toMap(Map.Entry::getKey, e -> (String) e.getValue())));
                break;
        }

        parameters.put("greetings", contract.isMaidCc() ?
                Setup.getCoreParameter(CoreParameter.SMS_GREETINGS_MAIDSCC) :
                Setup.getCoreParameter(CoreParameter.SMS_GREETINGS_MAIDSVISA));
        return parameters;
    }

    public static Map<String, AppAction> getContext(
            String notificationTemplateText,
            Map<String, String> parameters,
            Contract contract) {

        FlowProcessorMessagingService flowProcessorMessagingService = Setup.getApplicationContext()
                .getBean(FlowProcessorMessagingService.class);

        Map<String, AppAction> cta = new HashMap();
        if (notificationTemplateText.contains("@paytab_link_click_here@")) {
            cta.put("paytab_link_click_here_link", Setup.getApplicationContext().getBean(FlowProcessorMessagingService.class).
                    getWebLinkContext(contract, parameters.get("paytab_link"), "click here"));

        } else if(parameters.containsKey("paytabs_link")) {
            cta.put("payment_credit_card", Setup.getApplicationContext().getBean(FlowProcessorMessagingService.class)
                    .getPaytabsButton(parameters, contract));
        }

        if (notificationTemplateText.contains("@spouse_sing_dd_Link_click_here@")) {
            cta.put("spouse_sing_dd_Link_click_here_link", Setup.getApplicationContext().getBean(FlowProcessorMessagingService.class)
                    .getWebLinkContext(contract, parameters.get("spouse_sing_dd_Link"), "click here"));
        }

        if (notificationTemplateText.contains("@proof_of_transfer_sentence@")) {
            cta.put("proof_of_transfer_link_click_here_link", Setup.getApplicationContext().getBean(FlowProcessorMessagingService.class)
                    .getWebLinkContext(contract, parameters.get("proof_of_transfer_link"), "click here"));
        }

        if (notificationTemplateText.contains("@link_send_dd_details_click_here@")) {
            cta.put("link_send_dd_details_click_here_link", Setup.getApplicationContext().getBean(FlowProcessorMessagingService.class)
                    .getWebLinkContext(contract, parameters.get("link_send_dd_details"), "click here"));
        }

        return cta;
    }

    public static Template getNotificationTemplate(
            String templateOriginalName,
            boolean isMaidCc) {

        String notificationTemplateName = "";
        switch (templateOriginalName) {
            case "ACCOUNTING_PAY_VIA_CREDIT_CARD":
                notificationTemplateName = CcNotificationTemplateCode.CC_ACCOUNTING_PAY_VIA_CREDIT_CARD_NOTIFICATION.toString();
                break;
            case "DD_PENDING_INFO":
                notificationTemplateName = isMaidCc ?
                        CcNotificationTemplateCode.CC_DD_PENDING_INFO_NOTIFICATION.toString() :
                        MvNotificationTemplateCode.MV_DD_PENDING_INFO_NOTIFICATION.toString();
                break;
            case "CLIENT_REFUND_BANK_TRANSFER_DETAILS":
                notificationTemplateName =
                    CcNotificationTemplateCode.CC_CLIENT_REFUND_BANK_TRANSFER_DETAILS_NOTIFICATION.toString();
                break;
//            case "ACCOUNTING_PAY_ACCOMMODATION_FEE":
//                notificationTemplateName = isMaidCc ?
//                    CcNotificationTemplateCode.CC_ACCOUNTING_PAY_ACCOMMODATION_FEE_NOTIFICATION.toString() :
//                    MvNotificationTemplateCode.MV_ACCOUNTING_PAY_ACCOMMODATION_FEE_NOTIFICATION.toString();
//                break;
//            case "ACCOUNTING_PAY_CC_TO_MV":
//                notificationTemplateName = isMaidCc ?
//                    CcNotificationTemplateCode.CC_ACCOUNTING_PAY_CC_TO_MV_NOTIFICATION.toString() :
//                    MvNotificationTemplateCode.MV_ACCOUNTING_PAY_CC_TO_MV_NOTIFICATION.toString();
//                break;
//            case "ACCOUNTING_PAY_MONTHLY_PAYMENT":
//                notificationTemplateName = isMaidCc ?
//                    CcNotificationTemplateCode.CC_ACCOUNTING_PAY_MONTHLY_PAYMENT_NOTIFICATION.toString() :
//                    MvNotificationTemplateCode.MV_ACCOUNTING_PAY_MONTHLY_PAYMENT_NOTIFICATION.toString();
//                break;
//            case "ACCOUNTING_PAY_OVERSTAY_FEES":
//                notificationTemplateName = isMaidCc ?
//                    CcNotificationTemplateCode.CC_ACCOUNTING_PAY_OVERSTAY_FEES_NOTIFICATION.toString() :
//                    MvNotificationTemplateCode.MV_ACCOUNTING_PAY_OVERSTAY_FEES_NOTIFICATION.toString();
//                break;
//            case "ACCOUNTING_PAY_PCR_TEST":
//                notificationTemplateName = isMaidCc ?
//                    CcNotificationTemplateCode.CC_ACCOUNTING_PAY_PCR_TEST_NOTIFICATION.toString() :
//                    MvNotificationTemplateCode.MV_ACCOUNTING_PAY_PCR_TEST_NOTIFICATION.toString();
//                break;
//            case "ACCOUNTING_PAY_URGENT_VISA_CHARGES":
//                notificationTemplateName = isMaidCc ?
//                    CcNotificationTemplateCode.CC_ACCOUNTING_PAY_URGENT_VISA_CHARGES_NOTIFICATION.toString() :
//                    MvNotificationTemplateCode.MV_ACCOUNTING_PAY_URGENT_VISA_CHARGES_NOTIFICATION.toString();
//                break;
//            case "ACCOUNTING_PAY_INSURANCE":
//                notificationTemplateName = isMaidCc ?
//                    CcNotificationTemplateCode.CC_ACCOUNTING_PAY_INSURANCE_NOTIFICATION.toString() :
//                    MvNotificationTemplateCode.MV_ACCOUNTING_PAY_INSURANCE_NOTIFICATION.toString();
//                break;
//            case "ACCOUNTING_PAY_OTHER_PAYMENTS_TYPES":
//                notificationTemplateName = isMaidCc ?
//                    CcNotificationTemplateCode.CC_ACCOUNTING_PAY_OTHER_PAYMENTS_TYPES_NOTIFICATION.toString() :
//                    MvNotificationTemplateCode.MV_ACCOUNTING_PAY_OTHER_PAYMENTS_TYPES_NOTIFICATION.toString();
//                break;
            default:
                notificationTemplateName = templateOriginalName;
                break;
        }

        return TemplateUtil.getTemplate(notificationTemplateName);
    }


    public ArrayList<Template> getMessageTemplates() {
        return new ArrayList() {
            {
                add(new Template("Accounting_housemaid_deduction_notification_sms", false, "Dear @maid_name@. You got a deduction of @amount@ because @reason_of_deduction@", "Notification SMS on Housemaid Deduction."));
                add(new Template("Accounting_Send_URL", false, "@url@", "send url message only"));
                add(new Template("Accounting_Send_DD_Files_To_Client", false, "@message_header@@receiptURL@@attachmentURLs@@agreementMVContractURL@", "send Direct Debit File To client message"));
                add(new Template("Accounting_Closing_VAT_Message", false, "@sms_greeting@. Please click here @url@ to view a photo of your torn cheques which are replaced by the new direct debit.", "send a close VAT message to client."));
                add(new Template("Accounting_Payment_Proof_Of_Transfer_To_Client", false, "@greetings@ Dear @client_name@, We just sent you @amount@ by Bank Transfer. Please expect to receive the amount to your bank account within 1 business day. Thank you", "send to the Client when the his refund is approved on Expensify."));
                add(new Template("Accounting_Payment_ProofWithLink_Of_Transfer_To_Client", false, "We've just sent you @amount@ by Bank transfer. Please expect to receive the transfer within 2 business days. You can view the proof of transfer here @link@. ", "send to the Client Whenever the transfer proof is uploaded to ERP."));
                add(new Template("Accounting_FIRST_TIME_WITH_DD_WITH_SIGNS", false, "Dear @client_name@, @greeting@. The government has informed us that we must add 5%% on all services we provide. As of next month, we plan on adjusting your existing Bank Direct Debit Form by adding 5%% to your monthly payments. Please click here @url@ to confirm this payment adjustment.If you need any help at all, please call us at @contact_phone@, at your convenience.", "send for the first time to the Client to confirm adding VAT"));
                add(new Template("Accounting_FIRST_TIME_WITH_DD_NO_SIGNS", false, "Dear @client_name@, @greeting@. The government has informed us that we have to add 5%% VAT on all payments. As of next payment, we'll have to add 5%% VAT to your monthly payment. For your convenience, please click here @url@ to sign the new Bank Direct Debit forms.If you need any help at all, please call us at @contact_phone@, at your convenience.", "send for te first time to the Client to sign the new Direct Debit to add VAT"));
                add(new Template("Accounting_FIRST_TIME_WITHOUT_DD", false, "Dear @client_name@, @greeting@. The government has informed us that we have to add 5%% VAT on all payments. As of next payment, we'll have to add 5%% VAT to your monthly payment. To make it easy on you, we will issue a Bank Direct Debit for you. For your convenience, please click here @url@ to generate and sign the new Bank Direct Debit forms.If you need any help at all, please call us at @contact_phone@, at your convenience.", "send for te first time to the Client to issue Direct Debit to add VAT"));

                add(new Template("Accounting_REMINDER_WITH_DD_WITH_SIGNS", false, "Dear @client_name@, @greeting@. We'd like to remind you that we have to add 5%% VAT on your monthly payments, as requested by the government, As of next payment, We MUST adjust your existing Bank Direct Debit form by adding 5%% on your monthly payments. Please take a moment right now, and click here @url@ to confirm the payment adjustment.If you need any help at all, please call us at @contact_phone@, at your convenience.", "send Reminder to the Client to confirm adding VAT"));
                add(new Template("Accounting_REMINDER_WITH_DD_NO_SIGNS", false, "Dear @client_name@, @greeting@. We'd like to remind you that we have to add 5%% VAT on your monthly payments, as requested by the government, As of next payment, We MUST add 5%% to your monthly payments. Please take a moment right now, and click here @url@ to sign the new Bank Direct Debit forms.If you need any help at all, please call us at @contact_phone@, at your convenience.", "send Reminder to the Client to sign the new Direct Debit to add VAT"));
                add(new Template("Accounting_REMINDER_WITHOUT_DD", false, "Dear @client_name@, @greeting@. We'd like to remind you that we have to add 5%% VAT on your monthly payments, as requested by the government. To make it easy on you, We MUST issue a Bank Direct Debit for you. Please take a moment right now, and click here @url@ to generate and sign the new Bank Direct Debit forms.If you need any help at all, please call us at @contact_phone@, at your convenience.", "send Reminder to the Client to issue Direct Debit to add VAT"));

                // PAY-47 Housemaid Payslips
                add(new Template("Filipino_Housemaid_Payslips", false, "Hello ate,\nYou can download this month’s payslip in English @payslip_en@ or in Tagalog @payslip_tl@", "Sent once each month on payroll generation and sending"));
                add(new Template("Ethiopian_Housemaid_Payslips", false, "Hello,\nYou can download this month’s payslip in English @payslip_en@, Amharic @payslip_amh@ or in Oromo @payslip_om@", "Sent once each month on payroll generation and sending"));
                add(new Template("Indian_Housemaid_Payslips", false, "Hello,\nYou can download this month’s payslip in English @payslip_en@, Hindi @payslip_hi@, or in Malayalm @payslip_ml@", "Sent once each month on payroll generation and sending"));

                add(new Template("MV_PAYMENTS_DETAILS_AGREEMENT_AND_RECEIPT", false, "@dear_receiver_name@ @greetings@. Please find your receipts and @postponed_passed@ agreement for your contract following those links: \nPayment Details: @receipt_attachment_url@ \n@postponed_passed@ Agreement: @agreement_MV_contract_url@", "sendDDFilestoClient with haveToSendMaidVisaAgreementFile true and receiptAttachment not null"));
                add(new Template("MV_PAYMENTS_DETAILS_AGREEMENT_ONLY", false, "@dear_receiver_name@ @greetings@. Please find your @postponed_passed@ agreement for your contract following this link: \n@postponed_passed@ Agreement: @agreement_MV_contract_url@", "sendDDFilestoClient with haveToSendMaidVisaAgreementFile true and receiptAttachment null"));
                add(new Template("MV_PAYMENTS_DETAILS_RECEIPT_ONLY", false, "@dear_receiver_name@ @greetings@. Please find your receipts for your contract following this link \nPayment Details: @receipt_attachment_url@", "sendDDFilestoClient with haveToSendMaidVisaAgreementFile false and receiptAttachment not null"));

                //ACC-2857
                add(new Template("CLIENT_REFUND_MONEY_TRANSFER_DETAILS", false, "We just sent you AED @amount@ by Ansari Premium. Please expect to receive an SMS from Ansari within an hour. Once you get the SMS, you can visit the nearest Al Ansari branch to collect your money. Thank you.", "sent when Client's Money Transfer is completed"));
                add(new Template("CLIENT_REFUND_BANK_TRANSFER_DETAILS", false, "We just sent you AED @amount@ by bank transfer. Please expect to receive the amount to your bank account within 2 business days.@proof_of_transfer_sentence_sms@ Thank you.", "sent when Client's Bank Transfer is completed"));

                add(new Template("CC_PAYMENTS_DETAILS", false, "@dear_receiver_name@ @greetings@. Please find your receipts for your contract following this link \nPayment Details: @receipt_attachment_url@", "sendDDFilestoClient with haveToSendMaidVisaAgreementFile false and receiptAttachment not null"));

                add(new Template("MAID.cc_CANCELLATION_RETRACTION", false, "@Client_Name@, ERP ID: @ERP_ID@ - Contract Id: @Contract_Id@ undid his cancellation from the app and the client's direct debit forms are sent to the bank for cancellation.", "Maid.cc, upon Cancellation Retraction"));

                // CMA-996
                add(new Template("@DD_amendment_to_filipina_after_first@", false, "Please click \"I Agree\" below to indicate your approval allowing us to submit the following Direct Debits:", "Permission to Amend DD Forms when Switching Nationality to Filipino"));
                add(new Template("@DD_amendment_to_filipina_before_first@", false, "Please click \"I Agree\" below to indicate your approval allowing us to submit the following Direct Debits:", "Permission to Amend DD Forms when Switching Nationality to Filipino"));

                // ACC-3368
                add(new Template("@DD_second_amendment_to_filipina_after_first@", false, "You’ve previously hired a maid from different nationality with new payment terms. We still haven’t amended your Bank Direct Debit according to these new payment terms. <br/>" +
                        "Please click \"I Agree\" below to indicate your approval allowing us to submit the following Direct Debits:", "Permission to Amend DD Forms when Switching Nationality to Filipino for the second time during trial period"));
                add(new Template("@DD_second_amendment_to_filipina_before_first@", false, "You’ve previously hired a maid from different nationality with new payment terms. We still haven’t amended your Bank Direct Debit according to these new payment terms. <br/>" +
                        "Please click \"I Agree\" below to indicate your approval allowing us to submit the following Direct Debits:", "Permission to Amend DD Forms when Switching Nationality to Filipino for the second time during trial period"));
                add(new Template("pay_via_credit_card", false, "To pay your overdue payment of AED @amount@ via credit card, please click on the following link: @link@", "SMS contains the Credit Card Paying link"));

                // ACC-4708
                add(new Template("DD_PENDING_INFO", false, "ACTION REQUIRED:\n@greetings@\nDear @client_name@, please click on the following link @link_send_dd_details@ using your phone, and complete your Bank Direct Debit application.", "DD status is Pending and sub-status is Pending to Receive DD Info/Signature from Client, At 10 AM"));
                add(new Template("pay_via_credit_card", false, "To pay your overdue payment of AED @amount@ via credit card, please click on the following link: @link@", "SMS contains the Credit Card Paying link"));
                add(new Template("SIGN_DD_MESSAGE", false, "Your spouse has decided to pay using a different bank account and has requested that we send you a link to sign for the monthly payments. Please click on the following link to sign: @sign_link@", "the message that will be sent to phone to sign"));
                add(new Template("PAYMENT_SECTION_RECEIVE_PAYMENT_NOTIFICATIONS_SUB_TITLE", false, "Enable this feature if you'd like to receive payment notifications whenever your payment is received. You can stop these notifications whenever you wish by turning this option off.", "Payment section receive payment notifications sub-title"));
                add(new Template("Postponed_MV_After_Signing", false,
                        "Dear @client_first_name_with_title@, Thank you for using our service. We still " +
                                "haven't applied for your maid's visa. Please press here when you're ready to proceed @link@",
                        "Postponed MaidVisa Agreement Right After Signing"));
            }
        };
    }


    public ArrayList<MessageTemplateWithExpression> getMessageTemplatesWithExpression() {
        return new ArrayList() {
            {
                add(new MessageTemplateWithExpression(
                        new Template("@DD_amendment_to_non_filipina@", true,
                                "Please click \"I Agree\" below to indicate your approval allowing us to submit a Bank Direct Debit payment of "
                                        + "AED @selected_nationality_price@ / month + VAT, with your previously obtained electronic signature. "
                                        + "We'll cancel all your other Bank Direct Debits.", "Permission to Amend DD Forms when Switching Nationality from Filipino"),
                                new HashMap()));
//                        new HashMap() {
//                            {
//                                put("selected_nationality_price", "T(com.magnamedia.core.Setup).getApplicationContext().getBean('ccAppContentHelper').getMaidNationalityPrice(id)");
//                            }
//                        }));

                add(new MessageTemplateWithExpression(
                        new Template("@DD_second_amendment_to_non_filipina@", true,
                                "You’ve previously hired a maid from different nationality with new payment terms. We still haven’t amended your Bank Direct Debit according to these new payment terms. <br/>" +
                                        "Please click \"I Agree\" below to indicate your approval allowing us to submit a Bank Direct Debit payment of AED @selected_nationality_price@ / month + VAT, with your previously obtained electronic signature. We'll cancel all your other Bank Direct Debits.", "Permission to Amend DD Forms when Switching Nationality from Filipino for the second time during trial period"),
                                new HashMap()));
//                        new HashMap() {
//                            {
//                                put("selected_nationality_price", "T(com.magnamedia.core.Setup).getApplicationContext().getBean('ccAppContentHelper').getMaidNationalityPrice(id)");
//                            }
//                        }));
            }
        };
    }


    public static class MessageTemplateWithExpression {
        private Template msgTemplate;
        private Map<String, String> expressions;

        public MessageTemplateWithExpression() {
        }

        public MessageTemplateWithExpression(Template msgTemplate, Map<String, String> expressions) {
            this.msgTemplate = msgTemplate;
            this.expressions = expressions;
        }

        public Template getMsgTemplate() {
            return msgTemplate;
        }

        public Map<String, String> getExpressions() {
            return expressions;
        }
    }
}
