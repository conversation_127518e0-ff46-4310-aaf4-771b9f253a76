package com.magnamedia.entity;

import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.module.type.PaymentMethod;
import java.sql.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Lob;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Dec 2, 2018
 * Jirra ACC-284
 */
@Entity
public class Deposit extends BaseEntity {

    @Label
    @Column
    private String name;
    
    @Column
    private Double amount;

    //Jirra ACC-325
    @Enumerated(EnumType.STRING)
    private PaymentMethod paymentMethod;
    
    @Column
    private Date paymentDate;
    
    @Lob
    private String notes;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public PaymentMethod getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(PaymentMethod paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public Date getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(Date paymentDate) {
        this.paymentDate = paymentDate;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    @BeforeInsert
    @BeforeUpdate
    public void validate() {
            if (getName() == null || getName().isEmpty())
                        throw new RuntimeException("Name should not be empty.");
            
            if (getAmount()== null)
                        throw new RuntimeException("Amount should not be empty.");
            
            //Jirra ACC-370
//            if (getPaymentMethod()== null)
//                        throw new RuntimeException("PaymentMethod should not be empty.");
            
            //Jirra ACC-388
//            if (getPaymentDate()== null)
//                        throw new RuntimeException("PaymentDate should not be empty.");
    }
}
