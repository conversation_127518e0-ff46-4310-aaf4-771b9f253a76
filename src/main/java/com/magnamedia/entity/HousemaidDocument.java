package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelSerializer;
import java.sql.Date;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import org.hibernate.envers.NotAudited;

/**
 * <AUTHOR> Jabr Created on 2017-10-11
 *
 */
@Entity
public class HousemaidDocument extends BaseEntity {

	private Date expiryDate;

	@ManyToOne(optional = false,
			   fetch = FetchType.LAZY)
	@JsonSerialize(using = IdLabelSerializer.class)
	private PicklistItem type;

	@ManyToOne(optional = false,
			   fetch = FetchType.LAZY)
	@JsonBackReference
        @NotAudited
	private Housemaid housemaid;

	public Date getExpiryDate() {
		return expiryDate;
	}

	public void setExpiryDate(Date expiryDate) {
		this.expiryDate = expiryDate;
	}

	public PicklistItem getType() {
		return type;
	}

	public void setType(PicklistItem type) {
		this.type = type;
	}

	public Housemaid getHousemaid() {
		return housemaid;
	}

	public void setHousemaid(Housemaid housemaid) {
		this.housemaid = housemaid;
	}
}
