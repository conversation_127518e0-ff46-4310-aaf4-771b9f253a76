package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.PaymentTermConfig;
import com.magnamedia.entity.serializer.HousemaidSerilizer;
import com.magnamedia.module.type.ContractPackageType;
import com.magnamedia.module.type.ContractPaymentTermReason;
import com.magnamedia.module.type.PaymentTermConfigType;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Mar 08, 2020
 *         Jirra ACC-1435
 */
public interface ContractPaymentTermSalesProjection {

    Long getId();

    List<Attachment> getAttachments();

    @JsonSerialize(using = HousemaidSerilizer.class)
    Housemaid getHousemaid();

    @JsonSerialize(using = IdLabelSerializer.class)
    Contract getContract();

    @JsonSerialize(using = IdLabelSerializer.class)
    PaymentTermConfig getPaymentTermConfig();

    double getFirstMonthPayment();

    boolean isIsProRated();

    String getEid();

    String getIbanNumber();

    String getBankName();

    String getAccountName();

    boolean isIsActive();

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    PicklistItem getBank();

    boolean isIsEOM();

    Double getAdditionalDiscount();

    String getAdditionalDiscountNotes();

    Integer getProRatedDays();

    boolean isDdMsgsDisabled();

    boolean isIsFixedByVAT();

    Boolean getSpouseWillSignDD();

    ContractPaymentTermReason getReason();

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    PicklistItem getNationality();

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    PicklistItem getContractProspectType();

    PaymentTermConfigType getType();

    double getAgencyFee();

    double getMonthlyPayment();

    double getDiscount();

    int getDiscountEffectiveAfter();

    ContractPackageType getPackageType();

    Boolean getIsRemote();

    String getUuid();

    long getVersion();

    Date getLastModificationDate();

    Date getCreationDate();

    @Value("#{target.getCreator() != null ? target.getCreator().getLoginName() : ''}")
    String getCreator();

    @Value("#{target.getCreatorModule() != null ? target.getCreatorModule().getName() : ''}")
    String getCreatorModule();

    @Value("#{target.getLastModifier() != null ? target.getLastModifier().getLoginName() : ''}")
    String getLastModifier();
    
    Boolean getIsEidRejected();
    Boolean getIsIBANRejected();
    Boolean getIsAccountHolderRejected();
}