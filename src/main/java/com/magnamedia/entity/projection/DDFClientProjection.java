package com.magnamedia.entity.projection;

import com.magnamedia.core.entity.Attachment;
import com.magnamedia.module.type.DirectDebitFileStatus;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.module.type.DirectDebitType;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Apr 3, 2019
 *         Jirra ACC-494
 */

public interface DDFClientProjection {

    Long getId();

    @Value("#{target.getSecuredAttachments()}")
    List<Attachment> getAttachments();

    DirectDebitStatus getDdStatus();

    DirectDebitFileStatus getStatus();

    String getApplicationId();

    @Value("#{target.getDdFrequency()}")
    DirectDebitType getType();

    String getDdaRefNo();

    Date getStartDate();

    Date getExpiryDate();

    Double getAmount();

    String getIbanNumber();

    String getBankName();

    String getAccountName();
}
