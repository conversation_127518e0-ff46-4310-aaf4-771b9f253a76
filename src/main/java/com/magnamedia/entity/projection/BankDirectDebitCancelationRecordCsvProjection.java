package com.magnamedia.entity.projection;

import java.util.Date;
import org.springframework.beans.factory.annotation.Value;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Jul 3, 2019
 * Jirra ACC-778
 */
public interface BankDirectDebitCancelationRecordCsvProjection {

    @Value("#{(target.getDirectDebitFile() != null && "
            + "target.getDirectDebitFile().getDirectDebit() != null && "
            + "target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm() != null && "
            + "target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract() != null && "
            + "target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract().getClient() != null) ? "
            + "(target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract().getClient().getName()) : ''}")
    String getClientName();
    
    @Value("#{target.getDirectDebitFile() != null ? "
            + "(target.getDirectDebitFile().getApplicationId()) : ''}")
    public String getContract();
    
    @Value("#{target.getDirectDebitFile() != null ? "
            + "(target.getDirectDebitFile().getAmount()) : ''}")
    public Double getAmount();
    
    @Value("#{target.getDirectDebitFile() != null ? "
            + "(target.getDirectDebitFile().getPresentmentDate()) : ''}")
    public Date getPresentmentDate();
    
    @Value("#{target.getDirectDebitFile() != null ? "
            + "(target.getDirectDebitFile().getStartDate()) : ''}")
    public Date getStartDate();
    
    @Value("#{target.getDirectDebitFile() != null ? "
            + "(target.getDirectDebitFile().getExpiryDate()) : ''}")
    public Date getExpiryDate();
    
    @Value("#{target.getDirectDebitFile() != null ? "
            + "(target.getDirectDebitFile().getDdStatus()) : ''}")
    public String getStatus();
    public String getDdaRefNoCsv();
    public String getCancelReason();
}
