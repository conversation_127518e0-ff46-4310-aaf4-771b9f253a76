package com.magnamedia.entity.projection;

import com.magnamedia.entity.Expense;
import com.magnamedia.entity.Revenue;
import org.springframework.beans.factory.annotation.Value;

import java.util.Map;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Aug 19, 2020
 *         Jirra ACC-2395
 */
public interface BasePlVariableBucketProjection {

    Long getId();

    Double getWieght();

    @Value("#{(target.getRevenue()!=null)?{id:target.getRevenue().getId(), name:target.getRevenue().getName(), label:target.getRevenue().getLabel(), code:target.getRevenue().getCode()}:null}")
    Map<?, ?> getRevenue();

    @Value("#{(target.getExpense()!=null)?{id:target.getExpense().getId(), name:target.getExpense().getNameLabel(), label:target.getExpense().getNameLabel(), code:target.getExpense().getCodeLabel()}:null}")
    Map<?, ?> getExpense();

    @Value("#{target.getpLVariable() != null ? "
            + "{id: target.getpLVariable().getId(), "
            + "label: target.getpLVariable().getLabel(), "
            + "name: target.getpLVariable().getName(), "
            + "type: target.getpLVariable().getType(), "
            + "parent: target.getpLVariable().getParent() != null ? "
            + "{id: target.getpLVariable().getParent().getId(), "
            + "label: target.getpLVariable().getParent().getLabel(), "
            + "name: target.getpLVariable().getParent().getName(), "
            + "type: target.getpLVariable().getParent().getType(), "
            + "parent: target.getpLVariable().getParent().getParent() != null ? "
            + "{id: target.getpLVariable().getParent().getParent().getId(), "
            + "label: target.getpLVariable().getParent().getParent().getLabel(), "
            + "name: target.getpLVariable().getParent().getParent().getName(), "
            + "type: target.getpLVariable().getParent().getParent().getType(), "
            + "pLCompany: target.getpLVariable().getParent().getParent().getPLCompany() != null ? "
            + "{id: target.getpLVariable().getParent().getParent().getPLCompany().getId(), "
            + "label: target.getpLVariable().getParent().getParent().getPLCompany().getLabel(), "
            + "name: target.getpLVariable().getParent().getParent().getPLCompany().getName() "
            + "}:null"
            + "}:null"
            + "}:null"
            + "}: null"
            + "}")
    Map<?, ?> getpLVariable();
}
