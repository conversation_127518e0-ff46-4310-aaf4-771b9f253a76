package com.magnamedia.entity.projection;

import java.util.Date;
import org.springframework.beans.factory.annotation.Value;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Oct 20, 2018
 */
public interface TelecomPhoneCsvProjection {
    
    String getName();
    
    String getNumber();
    
    @Value("#{((target.getPrimaryExpense()!=null)?target.getPrimaryExpense().getCodeLabel():\"\")}")
    String getPrimaryExpense();
    
    @Value("#{((target.getSecondryExpense()!=null)?target.getSecondryExpense().getCodeLabel():\"\")}")
    String getSecondryExpense();
    
    Date getDueEvery();
    
    String getHolders() ;
    
    @Value("#{((target.getLastTelecomPhoneBill()!=null)?target.getLastTelecomPhoneBill().getAmount():\"\")}")
    Integer getLastBill();
    
    @Value("#{((target.getLastTelecomPhoneBill()!=null)?target.getLastTelecomPhoneBill().getFormattedBillDate():\"\")}")
    String getLastBillDate();
    
    String getUsageText();
    
    @Value("#{((target.getServiceType()!=null)?target.getServiceType().getName():\"\")}")
    String getServiceType();
    
    @Value("#{((target.getPaymentMethod()!=null)?target.getPaymentMethod().getName():\"\")}")
    String getPaymentMethod();
    
    String getNotes();
}
