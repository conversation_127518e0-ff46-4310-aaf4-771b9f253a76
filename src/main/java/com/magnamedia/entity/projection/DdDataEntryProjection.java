package com.magnamedia.entity.projection;

import org.springframework.beans.factory.annotation.Value;

import java.util.Map;

public interface DdDataEntryProjection {
    Long getId();
    
    @Value("#{target.getCategory() != null ? target.getCategory().getLabel() : null}")
    String getCategory();
    
    @Value("#{target.getDirectDebitRejectionToDo() != null ? "
        + "{id: target.getDirectDebitRejectionToDo().getId()} : null}")
    Map<?, ?> getDirectDebitRejectionToDo();
    
    @Value("#{target.getDirectDebitBouncingRejectionToDo() != null ? "
        + "{id: target.getDirectDebitBouncingRejectionToDo().getId()} : null}")
    Map<?, ?> getDirectDebitBouncingRejectionToDo();
    
    @Value("#{{id: target.getContractPaymentTerm().getId()," +
               "contract: " +
               "{id: target.getContractPaymentTerm().getContract().getId()," +
               "client: " +
                    "{id: target.getContractPaymentTerm().getContract().getClient().getId(), " +
                     "name: target.getContractPaymentTerm().getContract().getClient().getName(), " +
                     "nationality: (target.getContractPaymentTerm().getContract().getClient().getNationality() !=  null ? " +
                         "{id:  target.getContractPaymentTerm().getContract().getClient().getNationality().getId(), " +
                         "name: target.getContractPaymentTerm().getContract().getClient().getNationality().getName()}" +
                        " : null)," +
                     "title: (target.getContractPaymentTerm().getContract().getClient().getTitle() != null ? " +
                            "{id: target.getContractPaymentTerm().getContract().getClient().getTitle().getId() , " +
                            "name: target.getContractPaymentTerm().getContract().getClient().getTitle().getName()} : null)" +
          "}}}}")
    Map<?, ?> getContractPaymentTerm();
}
