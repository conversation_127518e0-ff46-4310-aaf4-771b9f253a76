package com.magnamedia.entity.projection;

import com.magnamedia.module.type.ContractType;
import com.magnamedia.module.type.DirectDebitType;
import com.magnamedia.module.type.PaymentMethod;
import java.util.Date;
import org.springframework.beans.factory.annotation.Value;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Apr 3, 2019
 * Jirra ACC-494
 */
public interface DDContractPaymentSearchProjectionCsv {
    
    @Value("#{target.getClientName()}")
    String getClient();
    
    @Value("#{target.getHousemaidName()}")
    String getHousemaid();
    
    Long getContractId();
    
    ContractType getContractType();
    
    @Value("#{target.getNationalityName()}")
    String getNationality();
    
    @Value("#{target.getDate()}")
    Date getPaymentDate();
    @Value("#{target.getDirectDebitCreationDate()}")
    Date getCreationDate();
    @Value("#{target.getDirectDebitAmount()}")
    Double getAmount();
    @Value("#{target.getPaymentTypeName()}")
    String getPaymentType();
    PaymentMethod getPaymentMethod();
    @Value("#{target.getCount()}")
    long getNumberOfPayments();
    //Jirra ACC-786
    DirectDebitType getType();
}
