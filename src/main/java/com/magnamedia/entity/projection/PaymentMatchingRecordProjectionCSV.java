package com.magnamedia.entity.projection;

import com.magnamedia.core.entity.Attachment;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on May 13, 2020
 *         Jirra ACC-1870
 */
public interface PaymentMatchingRecordProjectionCSV {

    Long getId();

    List<Attachment> getAttachments();

    String getUuid();

    long getVersion();

    Date getLastModificationDate();

    Date getCreationDate();

    @Value("#{target.getCreator() != null ? target.getCreator().getLoginName() : ''}")
    String getCreator();

    @Value("#{target.getCreatorModule() != null ? target.getCreatorModule().getName() : ''}")
    String getCreatorModule();

    @Value("#{target.getLastModifier() != null ? target.getLastModifier().getLoginName() : ''}")
    String getLastModifier();

    @Value("#{target.getPaymentIdStr()}")
    String getPaymentId();

    @Value("#{target.getPayment() != null && target.getPayment().getContract() != null && target.getPayment().getContract().getClient() != null && " +
            "target.getPayment().getContract().getClient().getName() != null ? target.getPayment().getContract().getClient().getName() : ''}")
    String getClientName();

    @Value("#{target.getPayment() != null && target.getPayment().getContract() != null && " +
            "target.getPayment().getContract().getId() != null ? target.getPayment().getContract().getId() : '' }")
    String getContractId();

    @Value("#{target.getPayment() != null && target.getPayment().getAmountOfPayment() != null ? target.getPayment().getAmountOfPayment() : '' }")
    String getPaymentAmount();

    @Value("#{target.getPrevStatus() != null ? target.getPrevStatus().getLabel() : '' }")
    String getPrevStatus();

    @Value("#{target.getPayment() != null && target.getPayment().getStatus() != null ? target.getPayment().getStatus().getLabel() : '' }")
    String getPaymentStatus();

    @Value("#{target.getStatus()}")
    String getImportedStatus();

    String getReasonOfBouncedCheque();

    String getTransferringFailureReason();
}
