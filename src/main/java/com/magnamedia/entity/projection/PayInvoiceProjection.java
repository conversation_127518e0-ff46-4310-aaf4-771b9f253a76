package com.magnamedia.entity.projection;

import org.springframework.beans.factory.annotation.Value;

import java.util.Date;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 8/6/2022
 **/

public interface PayInvoiceProjection {

    @Value("#{target.getExpense() == null ? \"\" : (target.getExpense().getCaption() != null && !target.getExpense().getCaption().isEmpty() ? target.getExpense().getCaption() : target.getExpense().getName())}")
    String getExpense();
	
	@Value("#{target.getRelatedToInfo().isEmpty() ? \"\" : target.getRelatedToInfo().split(\"-\", 2)[0].trim()}")
	String getEmployeeType();
	
	@Value("#{target.getRelatedToInfo().isEmpty() ? \"\" : target.getRelatedToInfo().split(\"-\", 2)[1].trim()}")
	String getEmployeeName();

    Double getAmount();

    Date getCreationDate();
}