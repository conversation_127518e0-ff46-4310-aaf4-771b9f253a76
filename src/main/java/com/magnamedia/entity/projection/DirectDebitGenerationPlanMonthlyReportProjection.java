package com.magnamedia.entity.projection;

import org.springframework.beans.factory.annotation.Value;

import java.util.Date;

public interface DirectDebitGenerationPlanMonthlyReportProjection {

    @Value("#{target.getClient().getId()}")
    Long getClientId();

    @Value("#{target.getClient().getName()}")
    String getClientName();

    @Value("#{target.getContract().getId()}")
    Long getContractId();

    @Value("#{target.getContract().getStartOfContractCSV()}")
    String getContractStartDate();

    @Value("#{target.getAmount().intValue()}")
    int getAmount();

    @Value("#{target.getContractPaymentType().getType().getName()}")
    String getDDType();

    Date getDDSendDate();

    @Value("#{target.getContractPaymentType().getContractPaymentTerm().getPaymentTermConfig().getName()}")
    String getCPTName();
}