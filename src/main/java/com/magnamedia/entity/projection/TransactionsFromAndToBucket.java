/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.entity.projection;

import com.magnamedia.entity.Transaction;
import java.util.LinkedList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TransactionsFromAndToBucket {

    List<Transaction> fromBucket;
    List<Transaction> toBucket;

    public TransactionsFromAndToBucket() {
        this.toBucket = new LinkedList<>();
        this.fromBucket = new LinkedList<>();
    }

    public List<Transaction> getFromBucket() {
        return fromBucket;
    }

    public void setFromBucket(List<Transaction> fromBucket) {
        this.fromBucket = fromBucket;
    }

    public List<Transaction> getToBucket() {
        return toBucket;
    }

    public void setToBucket(List<Transaction> toBucket) {
        this.toBucket = toBucket;
    }
}
