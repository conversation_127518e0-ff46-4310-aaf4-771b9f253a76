package com.magnamedia.entity.projection;

import org.springframework.beans.factory.annotation.Value;

import java.util.Map;

public interface TelecomPhoneExpenseRequestProjection {

    String getUsageText();

    Long getId();

    @Value("#{{code: target.getServiceType().getCode(), label: target.getServiceType().getLabel(), id: target.getServiceType().getId()}}")
    Map<?,?> getServiceType();

    @Value("#{{code: target.getPrimaryExpense()?.getCode(),"
            + "label: target.getPrimaryExpense()?.getLabel(),"
            + "name: target.getPrimaryExpense()?.getName(),"
            + "id: target.getPrimaryExpense()?.getId(),"
            + "beneficiaryType: target.getPrimaryExpense()?.getBeneficiaryType(),"
            + "suppliers: target.getPrimaryExpense()?.getSuppliers(),"
            + "paymentMethods: target.getPrimaryExpense()?.getPaymentMethods()}}")
    Map<?,?> getExpense();



}
