package com.magnamedia.entity.projection;

import com.magnamedia.module.type.VatType;
import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on July 2, 2020
 * Jirra ACC-2154
 */
public interface ManuallyMatchedTransactionProjection {

    public Long getId();

    public Double getTransactionAmount();

    @Value("#{(target.getTransaction() !=null && target.getTransaction().getExpense()!=null)?(target.getTransaction().getExpense().getCodeLabel()):('')}")
    String getExpense();

    @Value("#{(target.getTransaction() !=null && target.getTransaction().getVatAmount()!=null)?(target.getTransaction().getVatAmount().longValue()):(0)}")
    Double getVatAmount();

    @Value("#{(target.getTransaction() !=null && target.getTransaction().getVatType()!=null)?(target.getTransaction().getVatType()):(null)}")
    VatType getVatType();

    @Value("#{(target.getTransaction() !=null && target.getTransaction().getDescription()!=null)?(target.getTransaction().getDescription()):('')}")
    String getDescription();

    String getPaymentDetail();
}
