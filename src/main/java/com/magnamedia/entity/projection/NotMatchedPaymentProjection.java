package com.magnamedia.entity.projection;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.module.type.PaymentStatus;
import java.sql.Date;
import java.util.Map;
import org.springframework.beans.factory.annotation.Value;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Dec 27, 2017
 */
public interface NotMatchedPaymentProjection {
    
    Long getId();
    
    @Value("#{{id:target.getContract().getId()}}")
    Map<?,?> getContract();
    
    PicklistItem getTypeOfPayment();
    PaymentMethod getMethodOfPayment();
    Double getAmountOfPayment();
    Date getDateOfPayment();
    String getChequeName();
    String getChequeNumber();
    PaymentStatus getStatus();
    PicklistItem getBankName();
    java.util.Date getCreationDate();
    String getNote();
    Boolean getChequeWithTheBank();
    Boolean getReplaced();
}
