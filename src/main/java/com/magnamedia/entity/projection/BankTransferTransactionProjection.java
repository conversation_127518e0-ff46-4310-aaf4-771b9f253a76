package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.entity.Transaction;
import com.magnamedia.entity.serializer.ExpensePaymentSerializer;
import com.magnamedia.entity.serializer.IdOnlySerializer;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.module.type.BankTransactionMatchType;
import com.magnamedia.module.type.BankTransactionType;
import com.magnamedia.module.type.ExpensePaymentMethod;
import com.magnamedia.module.type.VatType;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by Mamon.Masod on 2/23/2021.
 */
public interface BankTransferTransactionProjection {
    Long getId();

    String getDescription();

    String getFromBucket();

    String getToBucket();

    @Value("#{(target.getExpense() != null)? {id: target.getExpense().getId(), label: target.getExpense().getNameLabel(), code: target.getExpense().getCodeLabel() } : null}")
    Map<?, ?> getExpense();

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    PicklistItem getLicense();

    Date getDate();

    ExpensePaymentMethod getExpensePaymentType();

    @JsonSerialize(using = ExpensePaymentSerializer.class)
    ExpensePayment getExpensePayment();

    String getPaymentDetail();

    BankTransactionMatchType getBankTransactionMatchType();

    Double getVatAmount();

    VatType getVatType();

    @Value("#{(target.getPayment() != null && target.getPayment().getAmountOfPayment() != null)?"
            + "(target.getPayment().getAmountOfPayment()):(target.getTransactionAmount())}")
    Double getTransactionAmount();

    boolean isResolved();

    BankTransactionType getBankTransactionType();

    String getNote();

    List<Attachment> getVatAttachments();

    List<Attachment> getNoVatAttachments();

    boolean getMissingVatInvoice();

    @JsonSerialize(using = IdOnlySerializer.class)
    Transaction getTransaction();
}
