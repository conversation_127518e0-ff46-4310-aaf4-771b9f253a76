package com.magnamedia.entity.projection;

import com.magnamedia.module.type.SalaryCurrency;
import com.magnamedia.workflow.type.PayrollAccountantTodoManagerAction;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;

public interface AccountantToDoCsvProjection {
    Long getId();
    Date getCreationDate();
    String getTodoCategory();
    String getTaskName();
    String getLabel();

    // SendRequestForApprovalAction → just its ID (or blank)
    @Value("#{target.getSendRequestForApprovalAction() != null ? target.getSendRequestForApprovalAction().getId() : ''}")
    String getSendRequestForApprovalAction();

    /* ——— Secured amount masking exactly like the JSON projection ——— */
    @Value("#{target.shouldAmountBeSecured() ? '****' : (target.getAmount() != null ? target.getAmount().toString() : '')}")
    String getAmount();

    @Value("#{target.shouldAmountBeSecured() ? '****' : (target.getTotal() != null ? target.getTotal().toString() : '')}")
    String getTotal();

    @Value("#{target.shouldAmountBeSecured() ? '****' : (target.getAmountAED() != null ? target.getAmountAED().toString() : '')}")
    String getAmountAED();

    @Value("#{target.shouldAmountBeSecured() ? '****' : (target.getTotalInAED() != null ? target.getTotalInAED().toString() : '')}")
    String getTotalInAED();

    @Value("#{target.shouldAmountBeSecured() ? '****' : (target.getAmountInForeignCurrency() != null ? target.getAmountInForeignCurrency().toString() : '')}")
    String getAmountInForeignCurrency();

    String getForeignCurrency();
    SalaryCurrency getCurrency();
    String getAccountName();
    String getIban();
    String getAccountNumber();
    String getMobileNumber();
    String getSwift();
    String getAddress();
    Boolean getInternational();

    /* todoType / itemType label */
    @Value("#{target.getItemType() != null ? target.getItemType().getLabel() : ''}")
    String getTodoType();

    String getBeneficiary();
    String getDueSince();
    String getRequester();
    String getApprovalFlow();

    @Value("#{target.getApprover() != null ? target.getApprover().getLabel() : ''}")
    String getApprover();

    String getDescription();
    String getManagerUserName();
    PayrollAccountantTodoManagerAction getManagerAction();
    String getEntityType();

    /* Question flags (booleans) */
    boolean getOneQuestionAnswered();
    boolean getNoneQuestionAnswered();
    boolean getAllQuestionsAnswered();

    @Value("#{target.getContractProspectType() != null ? target.getContractProspectType().getLabel() : ''}")
    String getContractProspectType();

    /* Beneficiary type breakdown  */
    @Value("#{target.getBeneficiaryType() != null ? target.getBeneficiaryType().get('beneficiaryType') : ''}")
    String getBeneficiaryType();

    @Value("#{target.getBeneficiaryType() != null ? target.getBeneficiaryType().get('beneficiaryName') : ''}")
    String getBeneficiaryName();

    Long getBeneficiaryTypeId();

    /* Bank info injected after accountantTodoService.updateBankTransferInfo(...) */
    String getBankName();
    String getBankCountry();
    String getBankCity();
}
