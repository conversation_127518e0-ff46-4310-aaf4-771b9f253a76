package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.Bucket;
import com.magnamedia.entity.CooQuestion;
import com.magnamedia.entity.Transaction;
import com.magnamedia.entity.serializer.ExpensePaymentTransactionSerializer;
import com.magnamedia.entity.serializer.IdNameSerializer;
import com.magnamedia.module.type.ExpensePaymentMethod;
import com.magnamedia.module.type.ExpensePaymentStatus;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by Mamon.Masod on 4/10/2021.
 */
public interface ExpensePaymentNightReviewProjection {

    Long getId();

    @Value("#{{name: target.getExpenseToPost()?.getName(), id: target.getExpenseToPost()?.getId()}}")
    Map<?, ?> getExpense();

    @Value("#{target.getExpenseToPost()?.getCaption()}")
    String getExpenseCaption();

    Date getPaymentDate();

    String getBeneficiaryName();

    @JsonSerialize(using = IdLabelSerializer.class)
    PicklistItem getBeneficiaryNationality();

    Map<?, ?> getBeneficiaryExtraDetails();

    Date getBeneficiaryStartDate();

    @JsonSerialize(using = IdLabelSerializer.class)
    User getRequester();
    
    String getApprovedBy();

    Double getAmount();

    Double getAmountToPay();

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    PicklistItem getCurrency();

    ExpensePaymentMethod getMethod();

    ExpensePaymentStatus getStatus();

    @JsonSerialize(using = IdNameSerializer.class)
    Bucket getFromBucket();

    String getNotes();

    @JsonSerialize(using = ExpensePaymentTransactionSerializer.class)
    Transaction getTransaction();

    String getEntityType();
    
    Boolean getTaxable();

    Double getLoanAmount();

    List<CooQuestion> getCooQuestions();

    boolean isOneQuestionAnswered();

    boolean isNoneQuestionAnswered();

    boolean isAllQuestionsAnswered();
}
