package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.entity.CooQuestion;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.serializer.HousemaidSerilizer;
import com.magnamedia.module.type.ExpensePaymentMethod;
import com.magnamedia.workflow.type.ExpenseRequestStatus;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface ExpenseRequestRefundProjection {
    public Long getId();

    @Value("#{target.getExpense()?.getId()}")
    public Long getExpenseId();

    @Value("#{target.getExpense()?.getName()}")
    public String getExpenseName();

    Boolean getIsRefunded();
    public Boolean getRefundConfirmed();

    String getRelatedToInfo();

    Long getRelatedToId();

    Long getBeneficiaryId();


    String getSupplier();

    Double getAmount();
    public Double getAmountInLocalCurrency();
    Double getRefundAmount();

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    public PicklistItem getCurrency();

    Date getCreationDate();
    Date getRefundDate();

    Long getExpenseTransaction();

}
