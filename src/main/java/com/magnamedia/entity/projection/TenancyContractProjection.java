package com.magnamedia.entity.projection;

import org.springframework.beans.factory.annotation.Value;
import java.sql.Date;

public interface TenancyContractProjection {
    String getName();

    Date getStartDate();

    Date getEndDate();

    String getDescription();

    String getOwnerName();

    String getTenantName();

    @Value("#{target.getActive() != null && target.getActive() ? \"Yes\" : \"No\"}")
    String getActive();

    @Value("#{target.getTypeOfDocument() != null ? target.getTypeOfDocument().getName() : \"\"}")
    String getTypeOfDocument();
}
