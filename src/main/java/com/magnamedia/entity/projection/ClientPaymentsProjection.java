/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.entity.projection;

import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR> ALKASSAR
 * FOR CM-1336
 * Client Payments Tab - Page improvements
 * 
 */
public interface ClientPaymentsProjection {
    Long getId();

    @Value("#{{code: target.getTypeOfPayment().getCode(), label: target.getTypeOfPayment().getLabel()}}")
    Map<?,?> getTypeOfPayment();
    
    @Value("#{target.getMethodOfPayment().getValue()}")
    String getMethodOfPayment();

    Boolean getIsInitial();
    
    @Value("#{{applicationId: target.getDirectDebit()?.getApplicationId()}}")
    Map<?,?> getDirectDebit();

    @Value("#{{applicationId: target.getDirectDebitFile()?.getApplicationId()}}")
    Map<?,?> getDirectDebitFile();
    
    String getChequeNumber();
    
    String getOldApplicationId();
    
    String getChequeName();
    
    @Value("#{target.getCreationDate()}")
    Date getCreationDate();
    
    @Value("#{target.getDateChangedToPDP()}")
    Date getDateChangedToPDP();
    
    @Value("#{target.getDateChangedToReceived()}")
    Date getDateChangedToReceived();
    
    @Value("#{target.getDateOfPayment()}")
    Date getDateOfPayment();
    
    Double getAmountOfPayment();
    
    Double getVat();
    
    @Value("#{target.isPrepareToRefund()}")
    Boolean isPrepareToRefund();
    
    @Value("#{"
            + "{"
            + "id:target.getBankName()?.getId()"
            + ",code:target.getBankName()?.getCode()"
            + ",label:target.getBankName()?.getName()"
            + "}"
            + "}")
    Map<?,?> getBankName();
    
    @Value("#{{label: target.getStatus().getLabel(), value: target.getStatus().getValue()}}")
    Map<?,?> getStatus();
    
    String getNote();
    
    Boolean getVatPaidByClient();

    @Value("#{target.getFilteredAttachments()}")
    List<Attachment> getAttachments();
    // Related with Bounaced Payment
    @Value("#{target.getIsReplacement()}")
    Boolean getIsReplacement();

    @Value("#{target.getReplacedByPDP()}")
    Boolean getReplacedByPDP();

    @Value("#{target.isReplaced()}")
    Boolean isReplaced();
    
    PicklistItem getReasonOfBouncingCheque();
    
    @Value("#{target.getLastLogDate()}")
    Date getLastLogDate();
    
    @Value("#{target.getBouncingTrials()}")
    Integer getBouncingTrials();
    
    @Value("#{target.getTrials()}")
    Integer getTrials();

    @Value("#{target.getDateOfBouncing()}")
    Date getDateOfBouncing();
    
    @Value("#{{id: target.getReplacementFor()?.getId()}}")
    Map<?,?> getReplacementFor();
    
    @Value("#{{id: target.getContract().getId(), client: {id: target.getContract().getClient().getId()}}}")
    Map<?,?> getContract();

    Double getDiscount();

    Date getStatusLastModificationDate();

    @Value("#{"
            + "target.getCreator()?.getFullName()"
            + "}")
    String getCreator();

    @Value("#{"
            + "target.getLastModifier()?.getFullName()"
            + "}")
    String getLastModifier();

    //Boolean getNeedsPaymentReceipt();
}