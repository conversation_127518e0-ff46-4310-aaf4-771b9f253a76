package com.magnamedia.entity.projection;

import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on Apr 26, 2020
 */

public interface CreditCardTransactionProjection {

    public String getTransactionDate();

    @Value("#{target.getSalesAmount() != null ? target.getSalesAmount().longValue() : 0 }")
    public Double getSalesAmount();

    public String getTerminalId();

    public String getSequenceNumber();

    public String getAuthCode();

}