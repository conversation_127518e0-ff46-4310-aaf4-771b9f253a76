package com.magnamedia.entity.projection;

import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.module.type.DirectDebitType;

import java.util.Date;

import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR>
 *         <<EMAIL>>
 *         Created on Mar 4, 2019
 *         Jirra ACC-456
 */
public interface DDFCsvProjection {

    @Value("#{(target.getDirectDebit() != null && "
            + "target.getDirectDebit().getContractPaymentTerm() != null && "
            + "target.getDirectDebit().getContractPaymentTerm().getContract() != null && "
            + "target.getDirectDebit().getContractPaymentTerm().getContract().getClient() != null) ? "
            + "(target.getDirectDebit().getContractPaymentTerm().getContract().getClient().getName()) : ''}")
    String getClient();

    @Value("#{(target.getDirectDebit() != null && "
            + "target.getDirectDebit().getContractPaymentTerm() != null && "
            + "target.getDirectDebit().getContractPaymentTerm().getContract() != null) ? "
            + "(target.getDirectDebit().getContractPaymentTerm().getContract().getId()) : ''}")
    Long getContractId();

    @Value("#{(target.getApplicationId())}")
    String getReferenceNumber();

    @Value("#{(target.getDirectDebit() != null && "
            + "target.getDirectDebit().getContractPaymentTerm() != null) ? "
            + "target.getDirectDebit().getContractPaymentTerm().getBankName() : ''}")
    String getBankName();

    @Value("#{(target.getCreationDate())}")
    Date getSigningDate();

    //ACC-1051
    @Value("#{(target.getDirectDebit() != null && "
            + "target.getDirectDebit().getContractPaymentTerm() != null) ? "
            + "target.getDirectDebit().getContractPaymentTerm().isIsActive() : ''}")
    Boolean getIsActive();

    Date getStartDate();

    Date getExpiryDate();

    @Value("#{target.getDdStatus()}")
    DirectDebitStatus getStatus();

    @Value("#{target.getStatus().getValue().equals('SENT')}")
    Boolean getUploaded();

    Date getResultDate();

    String getNotes();

    //Jirra ACC-616
    Double getAmount();

    //Jirra ACC-606
    @Value("#{target.getDirectDebit() != null ? target.getDirectDebit().getAdditionalDiscount() : 0}")
    Double getAdditionalDiscount();

    //ACC-952
    @Value("#{target.getDirectDebit() != null && "
            + "target.getDirectDebit().getAdditionalDiscountNotes() != null && !target.getDirectDebit().getAdditionalDiscountNotes().isEmpty() ? "
            + "target.getDirectDebit().getAdditionalDiscountNotes() : "
            + "(target.getDirectDebit().getAdditionalDiscount() != null && target.getDirectDebit().getAdditionalDiscount() != 0 && "
            + "target.getDirectDebit().getContractPaymentTerm() != null && target.getDirectDebit().getContractPaymentTerm().getAdditionalDiscountNotes() != null "
            + "? target.getDirectDebit().getContractPaymentTerm().getAdditionalDiscountNotes() : '')}")
    String getAdditionalDiscountNotes();

    @Value("#{target.getDdFrequency()}")
    DirectDebitType getType();

    //Jirra ACC-2926
    @Value("'#{target.getDdaRefNo()}")
    String getDdaRefNo();
}
