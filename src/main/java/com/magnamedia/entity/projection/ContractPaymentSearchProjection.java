package com.magnamedia.entity.projection;

import com.magnamedia.core.entity.Attachment;
import com.magnamedia.entity.Payment;
import com.magnamedia.module.type.PaymentMethod;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Value;

/**
 *
 * <AUTHOR>
 * <<EMAIL>>
 * Created on Mar 18, 2019
 * Jirra ACC-506
 */
public interface ContractPaymentSearchProjection {
    
    Long getId();
    @Value("#{target.getContractPaymentTerm() != null ? "
                + "{id:target.getContractPaymentTerm().getId(), "
                + "isActive:target.getContractPaymentTerm().isIsActive(), "
                + "contract:(target.getContractPaymentTerm().getContract() != null ? "
                    + "{id: target.getContractPaymentTerm().getContract().getId(), "
                    + "contractType: target.getContractPaymentTerm().getContract().getContractType(), "
                    + "client: (target.getContractPaymentTerm().getContract().getClient() != null ? "
                        + " {id: target.getContractPaymentTerm().getContract().getClient().getId(), "
                        + "name: target.getContractPaymentTerm().getContract().getClient().getName()}"
                    + ": null), "
                    + "housemaid: (target.getContractPaymentTerm().getContract().getHousemaid() != null ? "
                        + " {id: target.getContractPaymentTerm().getContract().getHousemaid().getId(), "
                        + "name: target.getContractPaymentTerm().getContract().getHousemaid().getName()}"
                    + ": null)} "
                + ": null), "
                + "housemaid: (target.getContractPaymentTerm().getHousemaid() != null ? "
                    + " {id: target.getContractPaymentTerm().getHousemaid().getId(), "
                    + "name: target.getContractPaymentTerm().getHousemaid().getName(),"
                    + "nationality: (target.getContractPaymentTerm().getHousemaid().getNationality() != null ? "
                        + " {id: target.getContractPaymentTerm().getHousemaid().getNationality().getId(), "
                        + "name: target.getContractPaymentTerm().getHousemaid().getNationality().getName()}"
                        + ": null)} "
                + ": null)} "
            + ": null}")
    Map<?, ?> getContractPaymentTerm();
    
    PaymentMethod getPaymentMethod();
    
    @Value("#{target.getPaymentType() != null ? "
                + "{id:target.getPaymentType().getId(), "
                + "name:target.getPaymentType().getName()} "
                + ": null}")
    Map<?, ?> getPaymentType();
    Double getAmount();
    Date getDate();
    int getAttachmentsCount();
    List<Attachment> getAttachments();
    Payment getReplaceOf();
    Date getCreationDate();
    
    Double getVatPercent();
    Double getVat();
    Double getPaymentWithoutVAT();
    Double getVisaFees();
    Double getVisaFeesWithoutVAT();
    Boolean getIncludeWorkerSalary();
    //Jirra ACC-1421
    Boolean getIsProRated();
    
    //ACC-952
    Double getAdditionalDiscountAmount();
    @Value("#{target.getAdditionalDiscountAmount() != null && target.getAdditionalDiscountAmount() != 0 && target.getContractPaymentTerm() != null ? "
            + "target.getContractPaymentTerm().getAdditionalDiscountNotes() "
            + ": ''}")
    String getAdditionalDiscountNotes();
    @Value("#{target.getAdditionalDiscountAmount() != null && target.getAdditionalDiscountAmount() != 0 && target.getContractPaymentTerm() != null? "
            + "target.getContractPaymentTerm().getAttachment('ADDITIONAL_DISCOUNT_ATTACHMENT') "
            + ": null}")
    Attachment getAdditionalDiscountAttachment();
}
