/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.entity.projection;

import com.magnamedia.core.entity.PicklistItem;
import org.springframework.beans.factory.annotation.Value;

/**
 *
 * <AUTHOR>
 */
public interface ExpenseCSVProjection {

 

    String getName();

    String getCode();

    @Value("#{(target.getPnlExpenseType())}")
    PicklistItem getWithinPnLExpenseTypeCode();

    @Value("#{(target.getAdjustedExpense())}")
    Long getAnnualPayment();
}
