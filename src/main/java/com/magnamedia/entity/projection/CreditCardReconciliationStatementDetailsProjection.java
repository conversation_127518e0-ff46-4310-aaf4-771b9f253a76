package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.entity.CreditCardReconciliationStatementDetails;
import com.magnamedia.entity.ReconciliationTransaction;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by Mamon.Masod on 3/20/2021.
 */
public interface CreditCardReconciliationStatementDetailsProjection {

    Long getId();

    List<Attachment> getAttachments();

    String getRecordDescription();

    Date getRecordTransactionDate();

    Double getRecordAmount();

    CreditCardReconciliationStatementDetails.CRDRAction getCrdrAction();

    Double getRequestAmount();

    Double getRequestAmountInLocalCurrency();

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    PicklistItem getRequestCurrency();

    CreditCardReconciliationStatementDetails.MatchType getMatchType();

    String getStatus();

    Boolean getConfirmed();

    @Value("#{target.getMatchedAutoDeduct() != null ? " +
            "{id: target.getMatchedAutoDeduct().getId()} : null}")
    Map<?, ?> getMatchedAutoDeduct();

    @Value("#{target.getMatchedExpenseRequest() != null ? " +
            "{id: target.getMatchedExpenseRequest().getId(), " +
            "bucket: target.getMatchedExpenseRequest().getBucket(), " +
            "paymentDate: target.getMatchedExpenseRequest().getExpensePayment() != null ? target.getMatchedExpenseRequest().getExpensePayment().getCreationDate() : null," +
            "beneficiaryName: target.getMatchedExpenseRequest().getExpensePayment() != null ? target.getMatchedExpenseRequest().getExpensePayment().getBeneficiaryName() : null" +
            "} : null}")
    Map<?, ?> getMatchedExpenseRequest();

    @Value("#{target.getReplenishmentTodo() != null ? " +
            "{id: target.getReplenishmentTodo().getId(), " +
            "paymentDate: (target.getReplenishmentTodo().getExpensePayments() != null && target.getReplenishmentTodo().getExpensePayments().size() > 0) ?" +
            " target.getReplenishmentTodo().getExpensePayments().get(0).getCreationDate() : null," +
            "beneficiaryName: (target.getReplenishmentTodo().getExpensePayments() != null && target.getReplenishmentTodo().getExpensePayments().size() > 0) ?" +
            " target.getReplenishmentTodo().getExpensePayments().get(0).getBeneficiaryName() : null" +
            "} : null}")
    Map<?, ?> getReplenishmentTodo();


    @Value("#{target.getReconciliationTransaction()}")
    ReconciliationTransaction getTransaction();

    boolean isAbleToEditVatInfo();
}
