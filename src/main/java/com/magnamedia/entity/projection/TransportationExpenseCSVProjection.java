package com.magnamedia.entity.projection;

import org.springframework.beans.factory.annotation.Value;

import java.util.Date;

public interface TransportationExpenseCSVProjection {

    String getBookingId();

    String getPassenger();

    String getPurpose();

    Date getDate();

    @Value("#{target.getDate()}")
    String getDateString();

    Double getErpAmount();

    String getAttachmentLink();

    String getPickupLocation();

    String getDropOffLocation();

    Double getBillAmount();

}
