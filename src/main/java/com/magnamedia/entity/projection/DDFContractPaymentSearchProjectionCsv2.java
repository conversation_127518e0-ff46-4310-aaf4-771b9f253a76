package com.magnamedia.entity.projection;

import com.magnamedia.module.type.ContractType;
import com.magnamedia.module.type.DirectDebitType;
import com.magnamedia.module.type.PaymentMethod;

import java.util.Date;

import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Apr 3, 2019
 *         Jirra ACC-494
 */
public interface DDFContractPaymentSearchProjectionCsv2 {

    @Value("#{(target.getDirectDebit() != null && "
            + "target.getDirectDebit().getContractPaymentTerm() != null && "
            + "target.getDirectDebit().getContractPaymentTerm().getContract() != null && "
            + "target.getDirectDebit().getContractPaymentTerm().getContract().getClient() != null) ? "
            + "(target.getDirectDebit().getContractPaymentTerm().getContract().getClient().getName()) : ''}")
    String getClient();

    @Value("#{target.getApplicationId()}")
    String getReferenceNumber();

    @Value("#{(target.getDirectDebit() != null && "
            + "target.getDirectDebit().getContractPaymentTerm() != null && "
            + "target.getDirectDebit().getContractPaymentTerm().getContract() != null && "
            + "target.getDirectDebit().getContractPaymentTerm().getContract().getHousemaid() != null) ? "
            + "(target.getDirectDebit().getContractPaymentTerm().getContract().getHousemaid().getName()) : ''}")
    String getHousemaid();

    @Value("#{(target.getDirectDebit() != null && "
            + "target.getDirectDebit().getContractPaymentTerm() != null && "
            + "target.getDirectDebit().getContractPaymentTerm().getContract() != null) ? "
            + "(target.getDirectDebit().getContractPaymentTerm().getContract().getId()) : ''}")
    Long getContractId();

    @Value("#{(target.getDirectDebit() != null && "
            + "target.getDirectDebit().getContractPaymentTerm() != null && "
            + "target.getDirectDebit().getContractPaymentTerm().getContract() != null && "
            + "target.getDirectDebit().getContractPaymentTerm().getContract().getContractType() != null) ? "
            + "(target.getDirectDebit().getContractPaymentTerm().getContract().getContractType()) : ''}")
    ContractType getContractType();

    @Value("#{(target.getDirectDebit() != null && "
            + "target.getDirectDebit().getContractPaymentTerm() != null && "
            + "target.getDirectDebit().getContractPaymentTerm().getContract() != null && "
            + "target.getDirectDebit().getContractPaymentTerm().getContract().getHousemaid() != null && "
            + "target.getDirectDebit().getContractPaymentTerm().getContract().getHousemaid().getNationality() != null) ? "
            + "(target.getDirectDebit().getContractPaymentTerm().getContract().getHousemaid().getNationality().getName()) : ''}")
    String getNationality();

    @Value("#{target.getStartDate()}")
    Date getPaymentDate();

    Date getCreationDate();

    Double getAmount();

    @Value("#{(target.getDirectDebit() != null && "
            + "target.getDirectDebit().getPaymentType() != null) ? "
            + "target.getDirectDebit().getPaymentType().getName() : ''}")
    String getPaymentType();

    @Value("#{target.getDirectDebit() != null ? target.getDirectDebit().getPaymentMethod() : ''}")
    PaymentMethod getPaymentMethod();

    @Value("#{target.getDirectDebit() != null ? target.getDirectDebit().getPaymentsCount() : 0}")
    Long getNumberOfPayments();

    @Value("#{target.getDirectDebit() != null ? target.getDirectDebit().getAdditionalDiscount() : 0}")
    Double getAdditionalDiscount();

    //ACC-952
    @Value("#{target.getDirectDebit() != null && "
            + "target.getDirectDebit().getAdditionalDiscountNotes() != null && !target.getDirectDebit().getAdditionalDiscountNotes().isEmpty()? "
            + "target.getDirectDebit().getAdditionalDiscountNotes() : "
            + "(target.getDirectDebit().getAdditionalDiscount() != null && target.getDirectDebit().getAdditionalDiscount() != 0 && "
            + "target.getDirectDebit().getContractPaymentTerm() != null ? target.getDirectDebit().getContractPaymentTerm().getAdditionalDiscountNotes(): '')}")
    String getAdditionalDiscountNotes();

    //ACC-1051
    @Value("#{(target.getDirectDebit() != null && "
            + "target.getDirectDebit().getContractPaymentTerm() != null) ? "
            + "target.getDirectDebit().getContractPaymentTerm().isIsActive() : ''}")
    Boolean getIsActive();

    String getType();

    public String getBankName();
}
