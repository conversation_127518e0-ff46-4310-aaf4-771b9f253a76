package com.magnamedia.entity;

import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.extra.InvoiceStatementStatus;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.sql.Date;

@Entity
public class InvoiceStatement extends BaseEntity {

    @Column
    private String taxInvoiceNumber;

    @Column
    private Date taxInvoiceDate;

    @Column
    private Date dueDate;

    @Column
    @Enumerated(EnumType.STRING)
    private InvoiceStatementStatus status = InvoiceStatementStatus.PENDING;

    public Date getTaxInvoiceDate() {
        return taxInvoiceDate;
    }

    public void setTaxInvoiceDate(Date taxInvoiceDate) {
        this.taxInvoiceDate = taxInvoiceDate;
    }

    public String getTaxInvoiceNumber() {
        return taxInvoiceNumber;
    }

    public void setTaxInvoiceNumber(String taxInvoiceNumber) {
        this.taxInvoiceNumber = taxInvoiceNumber;
    }

    public Date getDueDate() {
        return dueDate;
    }

    public void setDueDate(Date dueDate) {
        this.dueDate = dueDate;
    }

    public InvoiceStatementStatus getStatus() {
        return status;
    }

    public void setStatus(InvoiceStatementStatus status) {
        this.status = status;
    }
}
