package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.AfterInsert;
import com.magnamedia.core.entity.workflow.WorkflowEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.workflow.FormField;
import com.magnamedia.entity.serializer.IdLabelListSerializer;
import com.magnamedia.module.type.PurchaseRequestStatus;
import com.magnamedia.service.ExpenseNotificationService;
import com.magnamedia.workflow.type.PurchasingToDoType;

import javax.persistence.*;
import java.util.List;

/**
 * <PERSON> (Jan 31, 2021)
 */
@Entity
public class PurchasingToDo extends WorkflowEntity {
    public PurchasingToDo(String startTaskName) {
        super(startTaskName);
    }

    public PurchasingToDo() {
        super(PurchasingToDoType.PA_CONFIRM_PURCHASING_REQUEST.toString());
    }

    @Override
    public String getFinishedTaskName() {
        return "";
    }

    @Override
    public List<FormField> getForm(String s) {
        return null;
    }

    @OneToMany(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelListSerializer.class)
    private List<PurchaseItem> items ;

    @Enumerated(EnumType.STRING)
    private PurchaseRequestStatus purchaseRequestStatus = PurchaseRequestStatus.PENDING;

    private String orderCycleName;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Category category;

    public Category getCategory() {
        return category;
    }

    public void setCategory(Category category) {
        this.category = category;
    }

    public String getOrderCycleName() {
        return orderCycleName;
    }

    public void setOrderCycleName(String orderCycleName) {
        this.orderCycleName = orderCycleName;
    }

    public List<PurchaseItem> getItems() {
        return items;
    }

    public void setItems(List<PurchaseItem> items) {
        this.items = items;
    }

    public PurchaseRequestStatus getPurchaseRequestStatus() {
        return purchaseRequestStatus;
    }

    public void setPurchaseRequestStatus(PurchaseRequestStatus purchaseRequestStatus) {
        this.purchaseRequestStatus = purchaseRequestStatus;
    }

    @AfterInsert
    private void afterInsert() {
        Setup.getApplicationContext()
                .getBean(ExpenseNotificationService.class)
                .expenseToDoCreatedEmail(getEntityType(), getTaskName());
    }

}
