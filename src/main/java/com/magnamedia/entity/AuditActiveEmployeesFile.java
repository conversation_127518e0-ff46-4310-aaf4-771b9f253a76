package com.magnamedia.entity;

import com.magnamedia.core.entity.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import java.sql.Date;

@Entity
public class AuditActiveEmployeesFile extends BaseEntity {

    @Column
    private Date asOfDate;

    @Column(columnDefinition = "boolean default false")
    private boolean isFinishParsing = false;

    public Date getAsOfDate() {
        return asOfDate;
    }

    public void setAsOfDate(Date asOfDate) {
        this.asOfDate = asOfDate;
    }

    public boolean isFinishParsing() {
        return isFinishParsing;
    }

    public void setFinishParsing(boolean finishParsing) {
        isFinishParsing = finishParsing;
    }
}
