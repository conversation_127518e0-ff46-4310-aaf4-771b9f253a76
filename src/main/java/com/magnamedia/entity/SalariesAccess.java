package com.magnamedia.entity;

import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.User;

import javax.persistence.Entity;
import javax.persistence.OneToOne;

/**
 * Created on 3/4/2021.
 * ACC-3200
 */

@Entity
public class SalariesAccess extends BaseEntity {

    @OneToOne
    private User user;

    public SalariesAccess() {
    }

    public SalariesAccess(User user) {
        this.user = user;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }
}

