package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.module.type.DDMessagingType;
import com.magnamedia.module.type.DirectDebitRejectCategory;

import javax.persistence.*;
import java.sql.Time;
import java.util.Date;

/**
 * <AUTHOR>
 * Created on Apr 13, 2020
 * Jirra ACC-1611
 */
@Entity
public class DDMessagingToDo extends BaseEntity {

    @Column(columnDefinition = "boolean default false")
    private Boolean isSent = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean sendToClient = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean sendToMaid = false;

    // Jirra ACC-2445
    @Column(columnDefinition = "boolean default false")
    private Boolean sendToMaidWhenRetractCancellation = false;

    @Column
    private String clientPhoneNumber;

    @Column
    private String spousePhoneNumber;

    @Column
    private Long clientId;

    @Column
    private String clientName;

    @Column
    private String spouseName;

    @Column
    private String maidPhoneNumber;

    @Column
    private Long maidId;

    @Column
    private String maidName;

    @Column
    private String maidFirstName;

    @Column
    private String clientTemplateName;

    @Column
    private String maidTemplateName;

    // Jirra-224
    @Column
    private String maidWhenRetractCancellationTemplateName;

    @Column
    private Time sendTime;

    @Column
    private String type; // sms ,human_sms

    @Column
    private String contractUuid;

    @Column
    private String accountName;

    @Column
    @Temporal(TemporalType.DATE)
    private Date adjustedEndDate;

    @Column
    @Temporal(TemporalType.DATE)
    private Date contractScheduleDateOfTermination;

    @Column
    private String humanSmsTitle;

    @Column
    private String humanSmsDesc;

    @Column
    private Double amount;

    @Enumerated(EnumType.STRING)
    private DDMessagingType event;

    @Column
    @Enumerated(EnumType.STRING)
    private DirectDebitRejectCategory rejectCategory;

    @Column
    private Long paymentId;

    @Column
    private Long ddMessagingConfigId;

    @Column
    private java.sql.Date sendDate;

    @Column(columnDefinition = "boolean default true")
    private boolean active = true;

    @Column
    @JsonIgnore
    private Long payrollAccountantTodoId;
    
    @Column
    @JsonIgnore
    private Long directDebitGenerationPlanId;
    
    @Column
    @JsonIgnore
    private Long directDebitId;

    @Column
    @JsonIgnore
    private Long flowProcessorEntityId;

    @Column
    private Long relatedEntityId;

    @Column
    private String relatedEntityType;


    @Column
    private boolean isTerminationMessage = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem cancellationReason;

    public Boolean getSent() {
        return isSent;
    }

    public void setSent(Boolean sent) {
        isSent = sent;
    }

    public Boolean getSendToClient() {
        return sendToClient;
    }

    public void setSendToClient(Boolean sendToClient) {
        this.sendToClient = sendToClient;
    }

    public Boolean getSendToMaid() {
        return sendToMaid;
    }

    public void setSendToMaid(Boolean sendToMaid) {
        this.sendToMaid = sendToMaid;
    }

    public Boolean getSendToMaidWhenRetractCancellation() {
        return sendToMaidWhenRetractCancellation;
    }

    public void setSendToMaidWhenRetractCancellation(Boolean sendToMaidWhenRetractCancellation) {
        this.sendToMaidWhenRetractCancellation = sendToMaidWhenRetractCancellation;
    }

    public String getClientPhoneNumber() {
        return clientPhoneNumber;
    }

    public void setClientPhoneNumber(String clientPhoneNumber) {
        this.clientPhoneNumber = clientPhoneNumber;
    }

    public String getSpousePhoneNumber() {
        return spousePhoneNumber;
    }

    public void setSpousePhoneNumber(String spousePhoneNumber) {
        this.spousePhoneNumber = spousePhoneNumber;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getSpouseName() {
        return spouseName;
    }

    public void setSpouseName(String spouseName) {
        this.spouseName = spouseName;
    }

    public String getMaidPhoneNumber() {
        return maidPhoneNumber;
    }

    public void setMaidPhoneNumber(String maidPhoneNumber) {
        this.maidPhoneNumber = maidPhoneNumber;
    }

    public String getMaidName() {
        return maidName;
    }

    public void setMaidName(String maidName) {
        this.maidName = maidName;
    }
    
    public String getMaidFirstName() {
        return maidFirstName;
    }

    public void setMaidFirstName(String maidFirstName) {
        this.maidFirstName = maidFirstName;
    }
    
    public String getClientTemplateName() {
        return clientTemplateName;
    }

    public void setClientTemplateName(String clientTemplateName) {
        this.clientTemplateName = clientTemplateName;
    }

    public String getMaidTemplateName() {
        return maidTemplateName;
    }

    public void setMaidTemplateName(String maidTemplateName) {
        this.maidTemplateName = maidTemplateName;
    }

    public String getMaidWhenRetractCancellationTemplateName() {
        return maidWhenRetractCancellationTemplateName;
    }

    public void setMaidWhenRetractCancellationTemplateName(String maidWhenRetractCancellationTemplateName) {
        this.maidWhenRetractCancellationTemplateName = maidWhenRetractCancellationTemplateName;
    }

    public Time getSendTime() {
        return sendTime;
    }

    public void setSendTime(Time sendTime) {
        this.sendTime = sendTime;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public Long getMaidId() {
        return maidId;
    }

    public void setMaidId(Long maidId) {
        this.maidId = maidId;
    }

    public String getContractUuid() {
        return contractUuid;
    }

    public void setContractUuid(String contractUuid) {
        this.contractUuid = contractUuid;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public Date getAdjustedEndDate() {
        return adjustedEndDate;
    }

    public void setAdjustedEndDate(Date adjustedEndDate) {
        this.adjustedEndDate = adjustedEndDate;
    }

    public void setEvent(DDMessagingType event) {
        this.event = event;
    }

    public DDMessagingType getEvent() {
        return event;
    }

    public void setHumanSmsTitle(String humanSmsTitle) {
        this.humanSmsTitle = humanSmsTitle;
    }

    public String getHumanSmsTitle() {
        return humanSmsTitle;
    }

    public void setHumanSmsDesc(String humanSmsDesc) {
        this.humanSmsDesc = humanSmsDesc;
    }

    public String getHumanSmsDesc() {
        return humanSmsDesc;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Double getAmount() {
        return amount;
    }

    public void setContractScheduleDateOfTermination(Date contractScheduleDateOfTermination) {
        this.contractScheduleDateOfTermination = contractScheduleDateOfTermination;
    }

    public Date getContractScheduleDateOfTermination() {
        return contractScheduleDateOfTermination;
    }

    public void setRejectCategory(DirectDebitRejectCategory rejectCategory) {
        this.rejectCategory = rejectCategory;
    }

    public DirectDebitRejectCategory getRejectCategory() {
        return rejectCategory;
    }

    public Long getPaymentId() {
        return paymentId;
    }

    public void setPaymentId(Long paymentId) {
        this.paymentId = paymentId;
    }

    public Long getDdMessagingConfigId() {
        return ddMessagingConfigId;
    }

    public void setDdMessagingConfigId(Long ddMessagingConfigId) {
        this.ddMessagingConfigId = ddMessagingConfigId;
    }

    public Boolean getIsSent() {
        return isSent;
    }

    public void setIsSent(Boolean isSent) {
        this.isSent = isSent;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public java.sql.Date getSendDate() { return sendDate; }

    public void setSendDate(java.sql.Date sendDate) { this.sendDate = sendDate; }

    public Long getPayrollAccountantTodoId() { return payrollAccountantTodoId; }

    public void setPayrollAccountantTodoId(Long payrollAccountantTodoId) {
        this.payrollAccountantTodoId = payrollAccountantTodoId;
    }

    public Long getDirectDebitGenerationPlanId() { return directDebitGenerationPlanId; }
    
    public void setDirectDebitGenerationPlanId(Long directDebitGenerationPlanId) {
        this.directDebitGenerationPlanId = directDebitGenerationPlanId;
    }
    
    public Long getDirectDebitId() { return directDebitId; }
    
    public void setDirectDebitId(Long directDebitId) { this.directDebitId = directDebitId; }

    public Long getFlowProcessorEntityId() { return flowProcessorEntityId; }

    public void setFlowProcessorEntityId(Long flowProcessorEntityId) {
        this.flowProcessorEntityId = flowProcessorEntityId;
    }

    public Long getRelatedEntityId() { return relatedEntityId; }

    public void setRelatedEntityId(Long relatedEntityId) { this.relatedEntityId = relatedEntityId; }

    public String getRelatedEntityType() { return relatedEntityType; }

    public void setRelatedEntityType(String relatedEntityType) { this.relatedEntityType = relatedEntityType; }

    public boolean isTerminationMessage() { return isTerminationMessage; }

    public void setTerminationMessage(boolean terminationMessage) { isTerminationMessage = terminationMessage; }

    public PicklistItem getCancellationReason() { return cancellationReason; }

    public void setCancellationReason(PicklistItem cancellationReason) { this.cancellationReason = cancellationReason; }
}