package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.UserIdNameSerializer;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import java.util.Date;

/**
 * <PERSON> (Jan 27, 2021)
 */
@Entity
public class CashBox extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = UserIdNameSerializer.class)
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Bucket bucket;

    @Column(columnDefinition = "double default 0")
    private Double balance;

    @Column(columnDefinition = "double default 0")
    private Double bucketBalance;

    private Date lastCloseDate;

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Double getBalance() {
        return balance;
    }

    public void setBalance(Double balance) {
        this.balance = balance;
    }

    public Date getLastCloseDate() {
        return lastCloseDate;
    }

    public void setLastCloseDate(Date lastCloseDate) {
        this.lastCloseDate = lastCloseDate;
    }

    public Double getBucketBalance() {
        return bucketBalance;
    }

    public void setBucketBalance(Double bucketBalance) {
        this.bucketBalance = bucketBalance;
    }

    public Bucket getBucket() {
        return bucket;
    }

    public void setBucket(Bucket bucket) {
        this.bucket = bucket;
    }
}
