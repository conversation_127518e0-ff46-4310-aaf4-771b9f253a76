package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.BucketIdLabelCodeSerializer;
import com.magnamedia.entity.serializer.IdOnlySerializer;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.module.type.VatType;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2/7/2021
 */
@Entity
public class ReconciliationTransaction extends BaseEntity {

    @ManyToOne
    @JsonSerialize(using = BucketIdLabelCodeSerializer.class)
    private Bucket fromBucket;

    @ManyToOne
    @JsonSerialize(using = BucketIdLabelCodeSerializer.class)
    private Bucket toBucket;
    @ManyToOne
    @JsonSerialize(using = IdLabelSerializer.class)
    private Expense expense;

    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem license;

    @Column
    private Double amount = 0D;

    @Column
    @Enumerated(EnumType.STRING)
    private VatType vatType;

    @Column
    private Double vatAmount = 0D;

    @Column
    private Date transactionDate;

    @Column
    private String description;

    @Column
    @Enumerated(EnumType.STRING)
    private PaymentMethod paymentType;

    @Column
    private Boolean missingTaxInvoice;

    @Column
    private Date pnlValueDate;

    @ManyToOne
    @JsonSerialize(using = IdOnlySerializer.class)
    private Transaction transaction;

    public Date getPnlValueDate() {
        return pnlValueDate;
    }

    public void setPnlValueDate(Date pnlValueDate) {
        this.pnlValueDate = pnlValueDate;
    }

    public Bucket getFromBucket() {
        return fromBucket;
    }

    public void setFromBucket(Bucket fromBucket) {
        this.fromBucket = fromBucket;
    }

    public Expense getExpense() {
        return expense;
    }

    public void setExpense(Expense expense) {
        this.expense = expense;
    }

    public PicklistItem getLicense() {
        return license;
    }

    public void setLicense(PicklistItem license) {
        this.license = license;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public VatType getVatType() {
        return vatType;
    }

    public void setVatType(VatType vatType) {
        this.vatType = vatType;
    }

    public Double getVatAmount() {
        return vatAmount;
    }

    public void setVatAmount(Double vatAmount) {
        this.vatAmount = vatAmount;
    }

    public Date getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(Date transactionDate) {
        this.transactionDate = transactionDate;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public PaymentMethod getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(PaymentMethod paymentType) {
        this.paymentType = paymentType;
    }

    public Boolean getMissingTaxInvoice() {
        return missingTaxInvoice;
    }

    public void setMissingTaxInvoice(Boolean missingTaxInvoice) {
        this.missingTaxInvoice = missingTaxInvoice;
    }

    public Bucket getToBucket() {
        return toBucket;
    }

    public void setToBucket(Bucket toBucket) {
        this.toBucket = toBucket;
    }

    public Transaction getTransaction() {
        return transaction;
    }

    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
    }
}
