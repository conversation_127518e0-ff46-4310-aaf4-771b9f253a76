package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdSerializer;
import com.magnamedia.entity.serializer.IdOnlySerializer;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on Apr 19, 2020
 * Jira ACC-1728
 */

@Entity
public class BankStatementRecord extends BaseEntity {

    @ManyToOne
    @JsonSerialize(using = IdSerializer.class)
    private BankStatementFile bankStatementFile;

    @OneToMany(mappedBy = "bankStatementRecord", fetch = FetchType.LAZY)
    @JsonSerialize(using = IdOnlySerializer.class)
    private List<BankStatementTransaction> bankStatementTransactions;
    @ManyToOne
    @JsonSerialize(using = IdOnlySerializer.class)
    private DirectDebitFile directDebitFile;
    @Column
    private Integer srNo;
    @Column
    private Date date;
    @Column
    private Date valueDate;
    @Column
    private String BankReferenceNo;
    @Column
    private String customerReferenceNo;
    @Column
    private String description;
    @Column
    private Double debitAmount;
    @Column
    private Double creditAmount;
    @Column
    private Double runningBalance;
    @Column
    private String swiftRefNo;
    @Column
    private String beneficiaryInfo1;
    @Column
    private String beneficiaryInfo2;
    @Column
    private String beneficiaryInfo3;
    @Column
    private String remitterInfo1;
    @Column
    private String remitterInfo2;
    @Column
    private String remitterInfo3;
    @Column
    private String paymentDetail;

    @JsonIgnore
    @Transient
    private String uniqueNo;

    @JsonIgnore
    @Transient
    private String DDRefNo;

    @Column
    private Date uploadDate;

    @Column
    private String uploadFileName;

    @Column
    private String errorDesc;

    @Column(columnDefinition = "boolean default false")
    private Boolean processed = false;

    public DirectDebitFile getDirectDebitFile() {
        return directDebitFile;
    }

    public void setDirectDebitFile(DirectDebitFile directDebitFile) {
        this.directDebitFile = directDebitFile;
    }

    public List<BankStatementTransaction> getBankStatementTransactions() {
        return bankStatementTransactions;
    }

    public void setBankStatementTransactions(List<BankStatementTransaction> bankStatementTransactions) {
        this.bankStatementTransactions = bankStatementTransactions;
    }

    public BankStatementFile getBankStatementFile() {
        return bankStatementFile;
    }

    public void setBankStatementFile(BankStatementFile bankStatementFile) {
        this.bankStatementFile = bankStatementFile;
    }

    public Integer getSrNo() {
        return srNo;
    }

    public void setSrNo(Integer srNo) {
        this.srNo = srNo;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Date getValueDate() {
        return valueDate;
    }

    public void setValueDate(Date valueDate) {
        this.valueDate = valueDate;
    }

    public String getBankReferenceNo() {
        return BankReferenceNo;
    }

    public void setBankReferenceNo(String bankReferenceNo) {
        BankReferenceNo = bankReferenceNo;
    }

    public String getCustomerReferenceNo() {
        return customerReferenceNo;
    }

    public void setCustomerReferenceNo(String customerReferenceNo) {
        this.customerReferenceNo = customerReferenceNo;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Double getDebitAmount() {
        return debitAmount;
    }

    public void setDebitAmount(Double debitAmount) {
        this.debitAmount = debitAmount;
    }

    public Double getCreditAmount() {
        return creditAmount;
    }

    public void setCreditAmount(Double creditAmount) {
        this.creditAmount = creditAmount;
    }

    public Double getRunningBalance() {
        return runningBalance;
    }

    public void setRunningBalance(Double runningBalance) {
        this.runningBalance = runningBalance;
    }

    public String getSwiftRefNo() {
        return swiftRefNo;
    }

    public void setSwiftRefNo(String swiftRefNo) {
        this.swiftRefNo = swiftRefNo;
    }

    public String getBeneficiaryInfo1() {
        return beneficiaryInfo1;
    }

    public void setBeneficiaryInfo1(String beneficiaryInfo1) {
        this.beneficiaryInfo1 = beneficiaryInfo1;
    }

    public String getBeneficiaryInfo2() {
        return beneficiaryInfo2;
    }

    public void setBeneficiaryInfo2(String beneficiaryInfo2) {
        this.beneficiaryInfo2 = beneficiaryInfo2;
    }

    public String getBeneficiaryInfo3() {
        return beneficiaryInfo3;
    }

    public void setBeneficiaryInfo3(String beneficiaryInfo3) {
        this.beneficiaryInfo3 = beneficiaryInfo3;
    }

    public String getRemitterInfo1() {
        return remitterInfo1;
    }

    public void setRemitterInfo1(String remitterInfo1) {
        this.remitterInfo1 = remitterInfo1;
    }

    public String getRemitterInfo2() {
        return remitterInfo2;
    }

    public void setRemitterInfo2(String remitterInfo2) {
        this.remitterInfo2 = remitterInfo2;
    }

    public String getRemitterInfo3() {
        return remitterInfo3;
    }

    public void setRemitterInfo3(String remitterInfo3) {
        this.remitterInfo3 = remitterInfo3;
    }

    public String getPaymentDetail() {
        return paymentDetail;
    }

    public void setPaymentDetail(String paymentDetail) {
        this.paymentDetail = paymentDetail;
    }

    public Date getUploadDate() {
        return uploadDate;
    }

    public void setUploadDate(Date uploadDate) {
        this.uploadDate = uploadDate;
    }

    public String getUploadFileName() {
        return uploadFileName;
    }

    public void setUploadFileName(String uploadFileName) {
        this.uploadFileName = uploadFileName;
    }

    public String getUniqueNo() {
        return paymentDetail != null && paymentDetail.length() > 20 ? paymentDetail.substring(0,paymentDetail.indexOf("_")).trim() : null;
    }

    public void setUniqueNo(String uniqueNo) {
        this.uniqueNo = uniqueNo;
    }

    public String getDDRefNo() {
        return paymentDetail != null && paymentDetail.length() > 20 ? paymentDetail.substring(paymentDetail.lastIndexOf("_") + 1).trim() : null;
    }

    public void setDDRefNo(String DDRefNo) {
        this.DDRefNo = DDRefNo;
    }

    public String getErrorDesc() {
        return errorDesc;
    }

    public void setErrorDesc(String errorDesc) {
        this.errorDesc = errorDesc;
    }

    public Boolean getProcessed() {
        return processed;
    }

    public void setProcessed(Boolean processed) {
        this.processed = processed;
    }
}