package com.magnamedia.entity;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.magnamedia.core.entity.BaseEntity;


import javax.persistence.*;

/**
 * <AUTHOR>
 */
@Entity
public class AppsServiceDDApprovalTodo extends BaseEntity {

    public enum Result {
        CLOSED_WITH_NO_CONFIRMATION, CLOSED_WITH_CONFIRMATION;
    }
    //ACC-9426
    @JsonIgnore
    @Column(nullable = false)
    private Boolean clientPaidCash = false;

    public enum DdcTodoType {
        PROSPECT_APP,
        GET_CONFIRMATION,
        APPROVAL_TODO,
    }

    @Column
    @Lob
    private String ddDataEntryNote;

    @Column
    private String approvalChatId;

    @OneToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    private CcServiceApplication ccServiceApplication;

    @OneToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    private VisaServiceApplication visaServiceApplication;

    @Column(nullable = false)
    private Boolean isClosed = Boolean.FALSE;

    @Column
    @Enumerated(EnumType.STRING)
    private Result result;

    @Column
    @Enumerated(EnumType.STRING)
    private DdcTodoType todoType;

    @OneToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    private Contract contract;

    public CcServiceApplication getCcServiceApplication() {
        return ccServiceApplication;
    }

    public void setCcServiceApplication(CcServiceApplication ccServiceApplication) {
        this.ccServiceApplication = ccServiceApplication;
    }

    public VisaServiceApplication getVisaServiceApplication() {
        return visaServiceApplication;
    }

    public void setVisaServiceApplication(VisaServiceApplication visaServiceApplication) {
        this.visaServiceApplication = visaServiceApplication;
    }


    public Boolean getClientPaidCash() { return clientPaidCash; }

    public void setClientPaidCash(Boolean clientPaidCash) { this.clientPaidCash = clientPaidCash; }

    public DdcTodoType getDdcTodoType() { return todoType; }

    public void setDdcTodoType(DdcTodoType ddcTodoType) { this.todoType = ddcTodoType; }

    public Boolean getClosed() {
        return isClosed;
    }

    public void setClosed(Boolean closed) {
        isClosed = closed;
    }

    public Contract getContract() { return contract; }

    public void setContract(Contract contract) { this.contract = contract; }

    public String getDdDataEntryNote() { return ddDataEntryNote; }

    public void setDdDataEntryNote(String ddDataEntryNote) { this.ddDataEntryNote = ddDataEntryNote; }

    public String getApprovalChatId() { return approvalChatId; }

    public void setApprovalChatId(String approvalChatId) { this.approvalChatId = approvalChatId; }
}
