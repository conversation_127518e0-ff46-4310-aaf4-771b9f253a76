package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.CustomIdLabelSerializer;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
@Entity
public class ScheduledAnnualVacation extends BaseEntity{
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = CustomIdLabelSerializer.class)
    private Housemaid housemaid;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem type;
    
    @Column
    private Date vacationFrom;
    
    @Column
    private Date vacationTo;
    
    @Column
    private Date payrollDueDate;
    
    @Column
    private Double amount;
    
    @Column
    private String information;
    
    @Column
    private Long dueDays;

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public PicklistItem getType() {
        return type;
    }

    public void setType(PicklistItem type) {
        this.type = type;
    }

    public Date getVacationFrom() {
        return vacationFrom;
    }

    public void setVacationFrom(Date vacationFrom) {
        this.vacationFrom = vacationFrom;
    }

    public Date getVacationTo() {
        return vacationTo;
    }

    public void setVacationTo(Date vacationTo) {
        this.vacationTo = vacationTo;
    }

    public Date getPayrollDueDate() {
        return payrollDueDate;
    }

    public void setPayrollDueDate(Date payrollDueDate) {
        this.payrollDueDate = payrollDueDate;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public String getInformation() {
        return information;
    }

    public void setInformation(String information) {
        this.information = information;
    }

    public Long getDueDays() {
        return dueDays;
    }

    public void setDueDays(Long dueDays) {
        this.dueDays = dueDays;
    }
}
