package com.magnamedia.entity;

import com.magnamedia.workflow.visa.ExpensePurpose;
import javax.persistence.Entity;

/**
 *
 * <AUTHOR>
 */
@Entity
public class ContractModificationExpense extends VisaExpense<ContractModification> {
    public ContractModificationExpense() {
        super(null, null);
    }

    public ContractModificationExpense(ContractModification request, ExpensePurpose purpose) {
        super(request, purpose);
    }
}

