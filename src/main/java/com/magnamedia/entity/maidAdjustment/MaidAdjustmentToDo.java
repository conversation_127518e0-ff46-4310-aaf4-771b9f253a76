package com.magnamedia.entity.maidAdjustment;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.workflow.WorkflowEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.workflow.FormField;
import com.magnamedia.entity.Housemaid;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Sep 9, 2019
 */

@Entity
public class MaidAdjustmentToDo extends WorkflowEntity {
    
    public MaidAdjustmentToDo() {
        super("REVIEW_VIDEO_AND_MATCHING_TYPES");
    }
    
    public MaidAdjustmentToDo(String startTaskName) {
        super(startTaskName);
    }
   
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Housemaid housemaid;

    @Column
    private Double cashAdvanceAmount;
    
    @Column
    private Integer cashAdvanceDays;
    
    @Column
    private Boolean cashAdvanceAdded = false;

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public Double getCashAdvanceAmount() {
        return cashAdvanceAmount;
    }

    public void setCashAdvanceAmount(Double cashAdvanceAmount) {
        this.cashAdvanceAmount = cashAdvanceAmount;
    }

    public Integer getCashAdvanceDays() {
        return cashAdvanceDays;
    }

    public void setCashAdvanceDays(Integer cashAdvanceDays) {
        this.cashAdvanceDays = cashAdvanceDays;
    }

    public Boolean getCashAdvanceAdded() {
        return cashAdvanceAdded;
    }

    public void setCashAdvanceAdded(Boolean cashAdvanceAdded) {
        this.cashAdvanceAdded = cashAdvanceAdded;
    }

    @Override
    public String getFinishedTaskName() {
        return "";
    }

    @Override
    public List<FormField> getForm(String taskName) {
        return null;
    }
    
}
