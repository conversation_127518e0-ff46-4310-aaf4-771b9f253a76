package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.IdNameSerializer;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2/1/2021
 */
@Entity
public class CreditCardReconciliationStatement extends BaseEntity {

    public enum Status {
        PENDING,
        CONFIRMED
    }

    @ManyToOne
    @JsonSerialize(using = IdNameSerializer.class)
    private Bucket creditCard;

    @Column
    private Date uploadDate;

    @Column
    private Date parsingDate;

    @Column
    @Enumerated(EnumType.STRING)
    private Status status;

    @JsonIgnore
    @OneToMany(mappedBy = "creditCardReconciliationStatement", fetch = FetchType.LAZY)
    private List<CreditCardReconciliationStatementDetails> details;

    @Column
    private Double authorizedTransactions;

    @Column
    private Boolean isDeleted = false;

    public Boolean getDeleted() {
        return isDeleted;
    }

    public void setDeleted(Boolean deleted) {
        isDeleted = deleted;
    }

    public Bucket getCreditCard() {
        return creditCard;
    }

    public void setCreditCard(Bucket creditCard) {
        this.creditCard = creditCard;
    }

    public List<CreditCardReconciliationStatementDetails> getDetails() {
        return details;
    }

    public void setDetails(List<CreditCardReconciliationStatementDetails> details) {
        this.details = details;
    }

    public Date getParsingDate() {
        return parsingDate;
    }

    public void setParsingDate(Date parsingDate) {
        this.parsingDate = parsingDate;
    }

    public Double getAuthorizedTransactions() {
        return authorizedTransactions;
    }

    public void setAuthorizedTransactions(Double authorizedTransactions) {
        this.authorizedTransactions = authorizedTransactions;
    }

    public Date getUploadDate() {
        return uploadDate;
    }

    public void setUploadDate(Date uploadDate) {
        this.uploadDate = uploadDate;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }
}
