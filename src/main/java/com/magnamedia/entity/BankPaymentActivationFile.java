package com.magnamedia.entity;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.AfterInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.BankPaymentActivationFileRepository;
import com.magnamedia.repository.BankPaymentActivationRecordRepository;
import java.io.IOException;
import java.sql.Date;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.OneToMany;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.envers.NotAudited;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Dec 9, 2018
 * Jirra ACC-329
 */
@Entity
public class BankPaymentActivationFile extends BaseEntity {
    
    @Label
    private String bankName;
    
    @Column
    private java.util.Date date;
    
    @Column
    private boolean hidden = false;

    @NotAudited
    @OneToMany(mappedBy = "bankPaymentActivationFile",
                      fetch = FetchType.LAZY,
                      cascade = CascadeType.ALL)
    private List<BankPaymentActivationRecord> records;

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }
    
    public java.util.Date getDate() {
        return date;
    }

    public void setDate(java.util.Date date) {
        this.date = date;
    }

    public boolean isHidden() {
        return hidden;
    }

    public void setHidden(boolean hidden) {
        this.hidden = hidden;
    }

    public List<BankPaymentActivationRecord> getRecords() {
        return records;
    }

    public void setRecords(List<BankPaymentActivationRecord> records) {
        this.records = records;
    }
    
    @BeforeUpdate
    public void validateUpdate() {
        
        BankPaymentActivationFileRepository bankPaymentActivationFileRepository =
                Setup.getApplicationContext().getBean(BankPaymentActivationFileRepository.class);
        
        BankPaymentActivationFile old = bankPaymentActivationFileRepository.getOne(getId());
        List<Long> oldRecordsIDs = old.getRecords().stream().map(x->x.getId()).collect(Collectors.toList());
        if (getRecords().size()!=oldRecordsIDs.size())
                    throw new RuntimeException("Records is not updatable.");
        for (BankPaymentActivationRecord record : getRecords())
            if (record.getId() == null || !oldRecordsIDs.contains(record.getId()))
                    throw new RuntimeException("Records is not updatable.");
    }

    //Jirra ACC-332
    @AfterInsert
    public void parseRecords() throws IOException {
        if (getAttachments() == null || getAttachments().isEmpty())
            throw new RuntimeException("Attachment file is required.");
        
        Storage.updateAttachements(this);
        Attachment att = getAttachments().get(0);
        
        List<BankPaymentActivationRecord> notMatchedRecords = new ArrayList<>();
        Map<Long,BankPaymentActivationRecord> matchedRecordsMap = new HashMap<>();
            
        Workbook workbook = null;
        if (att.getExtension().equals("xlsx"))
            workbook = new XSSFWorkbook(Storage.getStream(att));
        else if (att.getExtension().equals("xls"))
            workbook = new HSSFWorkbook(Storage.getStream(att));
        
        if (workbook != null){
            Sheet sheet = workbook.getSheetAt(0);

            Iterator<Row> rowIterator = sheet.iterator();
            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();

                if (row.getRowNum()<16)
                    continue;
                BankPaymentActivationRecord activationRecord = new BankPaymentActivationRecord();
                try{
                    activationRecord.setRowIndex(Integer.parseInt(row.getCell(0).getStringCellValue()));
                    activationRecord.setDate(
                            (row.getCell(1).getStringCellValue() !=null && !row.getCell(1).getStringCellValue().isEmpty()) ?
                                    new Date(com.magnamedia.helper.DateUtil.parseDateDashedV2(row.getCell(1).getStringCellValue()).getTime()):
                                    null);
                    activationRecord.setValueDate(
                            (row.getCell(2).getStringCellValue() !=null && !row.getCell(2).getStringCellValue().isEmpty()) ?
                                    new Date(com.magnamedia.helper.DateUtil.parseDateDashedV2(row.getCell(2).getStringCellValue()).getTime()):
                                    null);
                    String creditAmount = row.getCell(7).getStringCellValue().replace(",", "");
                    String debitAmount = row.getCell(6).getStringCellValue().replace(",", "");
                    activationRecord.setAmount(
                            (creditAmount.equals("-"))?
                                    Double.parseDouble(debitAmount) :
                                    Double.parseDouble(creditAmount));
                    activationRecord.setStatus(
                            (creditAmount.equals("-"))?
                                    PaymentStatus.BOUNCED : 
                                    PaymentStatus.RECEIVED);
                    activationRecord.setRunningBalance(
                            Double.parseDouble(
                                    row.getCell(8).getStringCellValue()
                                            .replace(",", "")));
                    activationRecord.setDescription(row.getCell(5).getStringCellValue());
                    if (activationRecord.getDescription() !=null && !activationRecord.getDescription().isEmpty()){
                        String[] descStrings = activationRecord.getDescription().split("_");
                        activationRecord.setContract(descStrings[descStrings.length -1]);
                    }
                } catch (ParseException ex) {
                    Logger.getLogger(BankDirectDebitActivationFile.class.getName()).log(Level.SEVERE, null, ex);
                    activationRecord.setErrorMessage("Date Parsing Exception: " + ex.getMessage());
                } catch (NumberFormatException ex) {
                    Logger.getLogger(BankDirectDebitActivationFile.class.getName()).log(Level.SEVERE, null, ex);
                    activationRecord.setErrorMessage("Number Parsing Exception: " + ex.getMessage());
                }catch (Exception ex) {
                    Logger.getLogger(BankDirectDebitActivationFile.class.getName()).log(Level.SEVERE, null, ex);
                    activationRecord.setErrorMessage("Exception: " + ex.getMessage());
                }
                finally{
                    activationRecord.setBankPaymentActivationFile(this);
                    activationRecord.validateInsert();
                    if (activationRecord.getPayment()!=null){
                        matchedRecordsMap.put(
                                activationRecord.getPayment().getId(),
                                activationRecord);
                    }
                    else{
                        notMatchedRecords.add(activationRecord);
                    }
                }
            }

            BankPaymentActivationRecordRepository repo =
                    Setup.getRepository(BankPaymentActivationRecordRepository.class);
            repo.save(notMatchedRecords);
            repo.save(matchedRecordsMap.values());
        }
    }
}
