package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.serialize.IdSerializer;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import java.io.Serializable;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Jan 15, 2019
 */
@Entity
public class InterviewVisit extends BaseEntity implements Serializable {

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private InterviewPlace place;
    
    //Jirra ACC-1435
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem status;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdSerializer.class)
    private Client client;

    public InterviewPlace getPlace() {
        return place;
    }

    public void setPlace(InterviewPlace place) {
        this.place = place;
    }

    public PicklistItem getStatus() {
        return status;
    }

    public void setStatus(PicklistItem status) {
        this.status = status;
    }

    public Client getClient() {
        return client;
    }

    public void setClient(Client client) {
        this.client = client;
    }
    

    @JsonIgnore
    public boolean isRemoteSale() {
        return this.getPlace() != null
                && this.getPlace().getBranch() != null
                && this.getPlace().getBranch().getCode().equals("remote");
    }
}

