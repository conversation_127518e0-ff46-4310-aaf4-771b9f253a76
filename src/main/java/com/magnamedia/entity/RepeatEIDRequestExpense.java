package com.magnamedia.entity;

import com.magnamedia.workflow.visa.ExpensePurpose;
import javax.persistence.Entity;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Aug 1, 2019
 * Jirra ACC-841
 */
@Entity
public class RepeatEIDRequestExpense extends VisaExpense<RepeatEIDRequest> {

    public RepeatEIDRequestExpense() {
        super(null,
                null);
    }

    public RepeatEIDRequestExpense(RepeatEIDRequest request,
            ExpensePurpose purpose) {
        super(request,
                purpose);
    }
}

