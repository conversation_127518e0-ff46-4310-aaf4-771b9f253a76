package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdSerializer;

import javax.persistence.*;
import java.util.Date;

@Entity
public class NoqodiStatementRecord extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdSerializer.class)
    private VisaStatement statement;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdSerializer.class)
    private VisaStatementTransaction transaction;

    @Column
    private String srNumber;

    @Column
    private Date transactionDate;


    @Column
    private String transactionNumber;

    @Column
    @Lob
    private String description;


    @Column(columnDefinition = "double default 0")
    private Double debit = 0.0;

    @Column(columnDefinition = "double default 0")
    private Double credit = 0.0;

    @Column(columnDefinition = "double default 0")
    private Double balance = 0.0;

    @Column
    private Boolean isFinal = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdSerializer.class)
    private NoqodiStatementRecord master;

    @Column
    private String amounts;


    public VisaStatement getStatement() {
        return statement;
    }

    public void setStatement(VisaStatement statement) {
        this.statement = statement;
    }

    public Date getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(Date transactionDate) {
        this.transactionDate = transactionDate;
    }

    public String getTransactionNumber() {
        return transactionNumber;
    }

    public void setTransactionNumber(String transactionNumber) {
        this.transactionNumber = transactionNumber;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Double getDebit() {
        return debit;
    }

    public void setDebit(Double debit) {
        this.debit = debit;
    }

    public Double getCredit() {
        return credit;
    }

    public void setCredit(Double credit) {
        this.credit = credit;
    }

    public Double getBalance() {
        return balance;
    }

    public void setBalance(Double balance) {
        this.balance = balance;
    }

    public VisaStatementTransaction getTransaction() {
        return transaction;
    }

    public void setTransaction(VisaStatementTransaction transaction) {
        this.transaction = transaction;
    }

    public String getSrNumber() {
        return srNumber;
    }

    public void setSrNumber(String srNumber) {
        this.srNumber = srNumber;
    }

    public Boolean getFinal() {
        return isFinal;
    }

    public void setFinal(Boolean aFinal) {
        isFinal = aFinal;
    }

    public NoqodiStatementRecord getMaster() {
        return master;
    }

    public void setMaster(NoqodiStatementRecord master) {
        this.master = master;
    }

    public String getAmounts() {
        return amounts;
    }

    public void setAmounts(String amounts) {
        this.amounts = amounts;
    }
}
