package com.magnamedia.entity;

import com.magnamedia.core.entity.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Apr 11, 2020
 *         Jirra ACC-1604
 */

@Entity
public class DDFExportingConfig extends BaseEntity {
    @Column
    private String name;
    @Column
    private String oic;
    @Column
    private Long initialSeqNumber;
    @Column
    private String fileIndex;
    @Column
    private String customerType;
    @Column
    private String customerIdType;
    @Column
    private String ddaIssuedFor;
    @Column
    private String fundingAccountType;
    @Column
    private String ddAmountType;
    @Column
    private Long definedDays;
    @Column
    private String bulkFileFormat;
    @Column
    private String captureMode;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOic() {
        return oic;
    }

    public void setOic(String oic) {
        this.oic = oic;
    }

    public Long getInitialSeqNumber() {
        return initialSeqNumber;
    }

    public void setInitialSeqNumber(Long initialSeqNumber) {
        this.initialSeqNumber = initialSeqNumber;
    }

    public String getFileIndex() {
        return fileIndex;
    }

    public void setFileIndex(String fileIndex) {
        this.fileIndex = fileIndex;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getCustomerIdType() {
        return customerIdType;
    }

    public void setCustomerIdType(String customerIdType) {
        this.customerIdType = customerIdType;
    }

    public String getDdaIssuedFor() {
        return ddaIssuedFor;
    }

    public void setDdaIssuedFor(String ddaIssuedFor) {
        this.ddaIssuedFor = ddaIssuedFor;
    }

    public String getFundingAccountType() {
        return fundingAccountType;
    }

    public void setFundingAccountType(String fundingAccountType) {
        this.fundingAccountType = fundingAccountType;
    }

    public String getDdAmountType() {
        return ddAmountType;
    }

    public void setDdAmountType(String ddAmountType) {
        this.ddAmountType = ddAmountType;
    }

    public Long getDefinedDays() {
        return definedDays;
    }

    public void setDefinedDays(Long definedDays) {
        this.definedDays = definedDays;
    }

    public String getBulkFileFormat() {
        return bulkFileFormat;
    }

    public void setBulkFileFormat(String bulkFileFormat) {
        this.bulkFileFormat = bulkFileFormat;
    }

    public String getCaptureMode() {
        return captureMode;
    }

    public void setCaptureMode(String captureMode) {
        this.captureMode = captureMode;
    }
}
