package com.magnamedia.entity.workflow;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.entity.workflow.WorkflowEntity;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.serialize.IdSerializer;
import com.magnamedia.core.workflow.FormField;
import com.magnamedia.entity.DirectDebit;

import java.util.List;
import javax.persistence.*;

import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.module.type.DirectDebitCancellationToDoReason;
import com.magnamedia.repository.DirectDebitCancelationToDoRepository;
import com.magnamedia.service.QueryService;
import org.hibernate.annotations.Where;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Nov 19, 2019
 *         Jirra ACC-1134
 */
@Entity
@Where(clause = "(exists(select ddf.id from DIRECTDEBITFILES ddf where ddf.id = DIRECT_DEBIT_FILE_ID))")
public class DirectDebitCancelationToDo extends WorkflowEntity {
    private static final String DUPLICATION_ERR_MSG = "a record for the same direct debit already exists";

    public DirectDebitCancelationToDo() {
        super("Direct Debit Cancelation Prepare");
    }

    //Jirra ACC-1435
    public DirectDebitCancelationToDo(String string) {
        super(string);
    }

    //Jirra ACC-1811 #8
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdSerializer.class)
    private DirectDebit directDebit;

    //Jirra ACC-1587
    @ManyToOne(fetch = FetchType.LAZY)
    private DirectDebitFile directDebitFile;

    @Column(columnDefinition = "boolean default false")
    private Boolean hidden = false;

    @Column
    @Enumerated(EnumType.STRING)
    private DirectDebitCancellationToDoReason reason;

    @Transient
    private Boolean ignoreDDRejectionFlow = false;

    public DirectDebit getDirectDebit() {
        return directDebit;
    }

    public void setDirectDebit(DirectDebit directDebit) {
        this.directDebit = directDebit;
    }

    public DirectDebitFile getDirectDebitFile() {
        return directDebitFile;
    }

    public void setDirectDebitFile(DirectDebitFile directDebitFile) {
        this.directDebitFile = directDebitFile;
    }

    public Boolean getHidden() {
        return hidden;
    }
    
    public void setHidden(Boolean hidden) {
        this.hidden = hidden;
    }

    public DirectDebitCancellationToDoReason getReason() {
        return reason;
    }

    public void setReason(DirectDebitCancellationToDoReason reason) {
        this.reason = reason;
    }

    public Boolean getIgnoreDDRejectionFlow() {
        return ignoreDDRejectionFlow;
    }

    public void setIgnoreDDRejectionFlow(Boolean ignoreDDRejectionFlow) {
        this.ignoreDDRejectionFlow = ignoreDDRejectionFlow;
    }

    
    @BeforeUpdate
    public void checkIntegrity() {

        if (QueryService.existsEntity(DirectDebitCancelationToDo.class,
                "e.id <> :p0 and e.directDebitFile = :p1 and e.completed = false and e.stopped = false",
                new Object[]{getId(), getDirectDebitFile()})) {
            throw new BusinessException(DUPLICATION_ERR_MSG);
        }
    }

    //Assets
    @Override
    public List<FormField> getForm(String taskName) {
        return null;
    }

    @Override
    public String getFinishedTaskName() {
        return null;
    }
}
