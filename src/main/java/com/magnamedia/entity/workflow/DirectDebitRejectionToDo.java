package com.magnamedia.entity.workflow;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.workflow.WorkflowEntity;
import com.magnamedia.core.serialize.IdSerializer;
import com.magnamedia.core.workflow.FormField;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.DirectDebitRepository;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on 4-4-2020
 *         Jirra ACC-1595
 */
@Entity
@Audited
public class DirectDebitRejectionToDo extends WorkflowEntity {

    private static final String DUPLICATION_ERR_MSG = "a record for the same direct debit already exists";

    public DirectDebitRejectionToDo() {
        super("");
    }

    public DirectDebitRejectionToDo(String string) {
        super(string);
    }


    @Transient
    private List<DirectDebit> directDebits;

    @Transient
    private List<DirectDebit> directDebitsForBouncingFlow;

    @Column(columnDefinition = "int default 0")
    private int trials = 0;

    @Column(columnDefinition = "int default 0")
    private int reSignTrials = 0;

    @Column(columnDefinition = "int default 0")
    private int sameSignTrials = 0;

    @Column(columnDefinition = "int default 0")
    private int reminder = 0;

    @Column(columnDefinition = "int default 0")
    private int manualDDBTrials = 0;

    @Column(columnDefinition = "int default 0")
    private int autoDDBTrials = 0;

    @Column(columnDefinition = "int default 0")
    private int manualDDBTrialsPatch = 0;

    @Column(columnDefinition = "int default 0")
    private int autoDDBTrialsPatch = 0;

    @Temporal(TemporalType.TIMESTAMP)
    private Date reminderDate;

    @Column
    @Enumerated(EnumType.STRING)
    private DirectDebitRejectCategory lastRejectCategory;

    @Transient
    @JsonSerialize(using = IdSerializer.class)
    @NotAudited
    private DirectDebit lastDirectDebit;

    @Column
    @Enumerated(EnumType.STRING)
    private DirectDebitCategory ddCategory;

    @Column(columnDefinition = "boolean default false")
    private Boolean leadingRejectionFlow = false;
    
    @Transient
    private Boolean sendToClient;

    @Transient
    private String eid;

    @Transient
    private String accountName;

    @Transient
    private Boolean dontSendDdMessage; // false means send message , true means don't send message

    @Transient
    private java.util.Date contractScheduleDateOfTermination;

    @Column
    private Integer voiceResolverTodoId;

    @Temporal(TemporalType.TIMESTAMP)
    private Date lastTrialDate;

    @Column
    private String stopReason;
    
    //ACC-3597
    @Column(columnDefinition = "boolean default false")
    private boolean ddAddedByOecFlow = false;

    @Column
    private Date lastMessageDate;

    //ACC-8173
    @Column(columnDefinition = "boolean default false")
    private boolean causedTermination = false;

    @Transient
    private String cancellationReasonCode;

    public List<DirectDebit> getDirectDebits() {
        return Setup.getRepository(DirectDebitRepository.class).findByDirectDebitRejectionToDo(this);
    }

    public Date getLastMessageDate() {return lastMessageDate;}

    public void setLastMessageDate(Date lastMessageDate) {this.lastMessageDate = lastMessageDate;}

    public List<DirectDebit> getDirectDebitsForBouncingFlow() {
        return Setup.getRepository(DirectDebitRepository.class).findByDirectDebitBouncingRejectionToDo(this);
    }

    //Assets
    @Override
    public List<FormField> getForm(String taskName) {
        return null;
    }

    @Override
    public String getFinishedTaskName() {
        return null;
    }


    public int getTrials() {
        return trials;
    }

    public void setTrials(int trials) {
        this.trials = trials;
    }

    public int getReminder() {
        return reminder;
    }

    public void setReminder(int reminder) {
        this.reminder = reminder;
    }

    public void setSameSignTrials(int sameSignTrials) {
        this.sameSignTrials = sameSignTrials;
    }

    public int getSameSignTrials() {
        return sameSignTrials;
    }

    public void setLastRejectCategory(DirectDebitRejectCategory lastRejectCategory) {
        this.lastRejectCategory = lastRejectCategory;
    }

    public DirectDebitRejectCategory getLastRejectCategory() {
        return lastRejectCategory;
    }

    public Boolean getLeadingRejectionFlow() {
        return leadingRejectionFlow;
    }

    public void setLeadingRejectionFlow(Boolean leadingRejectionFlow) {
        if(getLeadingRejectionFlow() != null && getLeadingRejectionFlow()) {
            return;
        }
        this.leadingRejectionFlow = leadingRejectionFlow;
    }
    
    public void setLeadingRejectionFlowForced(Boolean leadingRejectionFlow) {
        this.leadingRejectionFlow = leadingRejectionFlow;
    }

    public void setDdCategory(DirectDebitCategory ddCategory) {
        this.ddCategory = ddCategory;
    }

    public DirectDebitCategory getDdCategory() {
        return ddCategory;
    }

    public int getManualDDBTrials() {
        return manualDDBTrials;
    }

    public void setManualDDBTrials(int manualDDBTrials) {
        this.manualDDBTrials = manualDDBTrials;
    }

    public int getAutoDDBTrials() {
        return autoDDBTrials;
    }

    public void setAutoDDBTrials(int autoDDBTrials) {
        this.autoDDBTrials = autoDDBTrials;
    }

    public DirectDebit getLastDirectDebit() {
        if (lastDirectDebit != null)
            return lastDirectDebit;
        else if (this.getDirectDebits() != null && !this.getDirectDebits().isEmpty())
            return this.getDirectDebits().get(this.getDirectDebits().size() - 1);
        else if (this.getDirectDebitsForBouncingFlow() != null && !this.getDirectDebitsForBouncingFlow().isEmpty())
            return this.getDirectDebitsForBouncingFlow().get(this.getDirectDebitsForBouncingFlow().size() - 1);
        return null;
    }

    public void setLastDirectDebit(DirectDebit lastDirectDebit) {
        this.lastDirectDebit = lastDirectDebit;
    }

    public Boolean getSendToClient() {
        return sendToClient;
    }

    public void setSendToClient(Boolean sendToClient) {
        this.sendToClient = sendToClient;
    }

    public Boolean getDontSendDdMessage() {
        if(dontSendDdMessage == null) {
            return false;
        }
        return dontSendDdMessage;
    }

    public void setDontSendDdMessage(Boolean dontSendDdMessage) {
        this.dontSendDdMessage = dontSendDdMessage;
    }

    public String getEid() {
        return eid;
    }

    public void setEid(String eid) {
        this.eid = eid;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public java.util.Date getContractScheduleDateOfTermination() {
        if (this.getLastDirectDebit() != null && this.getLastDirectDebit().getContractPaymentTerm() != null
                && this.getLastDirectDebit().getContractPaymentTerm().getContract() != null
                && this.getLastDirectDebit().getContractPaymentTerm().getContract().getScheduledDateOfTermination() != null) {
            return this.getLastDirectDebit().getContractPaymentTerm().getContract().getScheduledDateOfTermination();
        }
        return contractScheduleDateOfTermination;
    }

    public void setContractScheduleDateOfTermination(java.util.Date contractScheduleDateOfTermination) {
        this.contractScheduleDateOfTermination = contractScheduleDateOfTermination;
    }

    public boolean isCausedTermination() { return causedTermination; }

    public void setCausedTermination(boolean causedTermination) { this.causedTermination = causedTermination; }

    public int getReSignTrials() {
        return reSignTrials;
    }

    public void setReSignTrials(int reSignTrials) {
        this.reSignTrials = reSignTrials;
    }

    public void setReminderDate(Date reminderDate) {
        this.reminderDate = reminderDate;
    }

    public Date getReminderDate() {
        return reminderDate;
    }

    public Integer getVoiceResolverTodoId() {
        return voiceResolverTodoId;
    }

    public void setVoiceResolverTodoId(Integer voiceResolverTodoId) {
        this.voiceResolverTodoId = voiceResolverTodoId;
    }

    public void setLastTrialDate(Date lastTrialDate) {
        this.lastTrialDate = lastTrialDate;
    }

    public Date getLastTrialDate() {
        return lastTrialDate;
    }

    public int getAutoDDBTrialsPatch() {
        return autoDDBTrialsPatch;
    }

    public void setAutoDDBTrialsPatch(int autoDDBTrialsPatch) {
        this.autoDDBTrialsPatch = autoDDBTrialsPatch;
    }

    public int getManualDDBTrialsPatch() {
        return manualDDBTrialsPatch;
    }

    public void setManualDDBTrialsPatch(int manualDDBTrialsPatch) {
        this.manualDDBTrialsPatch = manualDDBTrialsPatch;
    }
    
    public String getStopReason() {
        return stopReason;
    }
    
    public void setStopReason(String stopReason) {
        this.stopReason = stopReason;
    }

    public boolean isDdAddedByOecFlow() {
        return ddAddedByOecFlow;
    }
    
    public void setDdAddedByOecFlow(boolean ddAddedByOecFlow) {
        this.ddAddedByOecFlow = ddAddedByOecFlow;
    }
    
    public DirectDebitRejectionToDo clone(DirectDebitRejectionToDoType step, DirectDebitCategory ddCategory) {
        DirectDebitRejectionToDo rejectionToDo = new DirectDebitRejectionToDo(step.toString());
        if (ddCategory != null) {
            rejectionToDo.setDdCategory(ddCategory);
        } else {
            rejectionToDo.setDdCategory(this.ddCategory);
        }

        rejectionToDo.setLastRejectCategory(this.lastRejectCategory);
        rejectionToDo.setTrials(this.trials);
        rejectionToDo.setReminder(this.reminder);
        rejectionToDo.setReSignTrials(this.reSignTrials);
        rejectionToDo.setSameSignTrials(this.sameSignTrials);
        rejectionToDo.setLastDirectDebit(this.lastDirectDebit);
        rejectionToDo.setReminderDate(this.reminderDate);
        rejectionToDo.setVoiceResolverTodoId(this.voiceResolverTodoId);
        rejectionToDo.setLastTrialDate(this.lastTrialDate);

        rejectionToDo.setEid(this.eid);
        rejectionToDo.setAccountName(this.accountName);
        rejectionToDo.setDdAddedByOecFlow(this.isDdAddedByOecFlow());
        rejectionToDo.setCausedTermination(this.isCausedTermination());
        rejectionToDo.setContractScheduleDateOfTermination(this.getContractScheduleDateOfTermination());
        rejectionToDo.setAutoDDBTrials(this.getAutoDDBTrials());
        rejectionToDo.setManualDDBTrials(this.getManualDDBTrials());
        return rejectionToDo;
    }

    public String getCancellationReasonCode() { return cancellationReasonCode; }

    public void setCancellationReasonCode(String cancellationReasonCode) {
        this.cancellationReasonCode = cancellationReasonCode;
    }
}
