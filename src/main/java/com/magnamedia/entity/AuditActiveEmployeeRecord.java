package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.serialize.IdSerializer;
import com.magnamedia.extra.AuditActiveEmployeeRecordType;

import javax.persistence.*;
import java.sql.Date;

@Entity
public class AuditActiveEmployeeRecord extends BaseEntity {

    @ManyToOne
    @JsonSerialize(using = IdSerializer.class)
    private AuditActiveEmployeesFile file;

    @Column
    @Enumerated(EnumType.STRING)
    private AuditActiveEmployeeRecordType type;

    @Column
    private String fullName;

    @Column
    private String passportNumber;

    @Column
    private java.util.Date insuranceStartDate;

    @Column
    private Date dateOfBirth;

    @ManyToOne(fetch = FetchType.EAGER)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem nationality;

    @Column
    private Long employeeID;

    @Column
    private String employeeType;

    public AuditActiveEmployeeRecord() {
        super();
    }

    public AuditActiveEmployeeRecord(AuditActiveEmployeesFile file, AuditActiveEmployeeRecordType type, Housemaid housemaid) {
        super();
        this.file = file;
        this.type = type;

        this.employeeType = Housemaid.class.getSimpleName();
        this.employeeID = housemaid.getId();

        this.fullName = housemaid.getName();
        this.passportNumber = housemaid.getPassportNumber();
        this.dateOfBirth = housemaid.getBirthdate() != null ? new Date(housemaid.getBirthdate().getTime()) : null;
        this.insuranceStartDate = housemaid.getVisaNewRequest() != null ? housemaid.getVisaNewRequest().findTaskMoveOutDate("Prepare insurance application") : null;
        this.nationality = housemaid.getNationality();
    }

    public AuditActiveEmployeeRecord(AuditActiveEmployeesFile file, AuditActiveEmployeeRecordType type, OfficeStaff officeStaff) {
        this.file = file;
        this.type = type;

        this.employeeType = OfficeStaff.class.getSimpleName();
        this.employeeID = officeStaff.getId();

        this.fullName = officeStaff.getName();

        if (officeStaff.getVisaNewRequest() != null){
            this.passportNumber = officeStaff.getVisaNewRequest().getPassportId();
            this.dateOfBirth = officeStaff.getVisaNewRequest().getBirthdate();
            this.insuranceStartDate = officeStaff.getVisaNewRequest().findTaskMoveOutDate("Prepare insurance application");
        }
        this.nationality = officeStaff.getNationality();
    }

    public AuditActiveEmployeesFile getFile() {
        return file;
    }

    public void setFile(AuditActiveEmployeesFile file) {
        this.file = file;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getPassportNumber() {
        return passportNumber;
    }

    public void setPassportNumber(String passportNumber) {
        this.passportNumber = passportNumber;
    }

    public java.util.Date getInsuranceStartDate() {
        return insuranceStartDate;
    }

    public void setInsuranceStartDate(java.util.Date insuranceStartDate) {
        this.insuranceStartDate = insuranceStartDate;
    }

    public Date getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(Date dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public Long getEmployeeID() {
        return employeeID;
    }

    public void setEmployeeID(Long employeeID) {
        this.employeeID = employeeID;
    }

    public String getEmployeeType() {
        return employeeType;
    }

    public void setEmployeeType(String employeeType) {
        this.employeeType = employeeType;
    }

    public AuditActiveEmployeeRecordType getType() {
        return type;
    }

    public void setType(AuditActiveEmployeeRecordType type) {
        this.type = type;
    }

    public PicklistItem getNationality() {
        return nationality;
    }

    public void setNationality(PicklistItem nationality) {
        this.nationality = nationality;
    }
}
