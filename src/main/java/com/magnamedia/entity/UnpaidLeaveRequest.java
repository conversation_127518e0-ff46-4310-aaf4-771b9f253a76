package com.magnamedia.entity;

import com.magnamedia.core.workflow.FormField;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.List;

@Entity
public class UnpaidLeaveRequest extends VisaRequest<UnpaidLeaveRequest, UnpaidLeaveNote, UnpaidLeaveExpense> implements Serializable {

    public UnpaidLeaveRequest() { super(null);}

    public UnpaidLeaveRequest(String startTaskName) {
        super(startTaskName);
    }

    @Override
    public String getFinishedTaskName() {
        return null;
    }

    @Override
    public List<FormField> getForm(String taskName) {
        return null;
    }
}