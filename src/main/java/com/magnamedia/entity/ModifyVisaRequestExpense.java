package com.magnamedia.entity;

import com.magnamedia.workflow.visa.ExpensePurpose;

import javax.persistence.Entity;


@Entity
public class ModifyVisaRequestExpense extends VisaExpense<ModifyVisaRequest> {
    public ModifyVisaRequestExpense() {
        super(null, null);
    }

    public ModifyVisaRequestExpense(ModifyVisaRequest request, ExpensePurpose purpose) {
        super(request, purpose);
    }
}

