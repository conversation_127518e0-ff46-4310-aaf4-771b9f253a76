/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.entity;

import com.magnamedia.core.entity.BaseEntity;
import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Entity;

/**
 *
 * <AUTHOR>
 */
@Entity
public class BaseRuleLog extends BaseEntity implements Serializable {

    @Column
    private String baseRuleName;

    @Column
    private String baseRuleCode;
    
    @Column
    private String baseRuleCreator;
    
    @Column
    private String baseRuleDescription;
    
    @Column
    private Boolean withUpdate;
    
    @Column
    private Boolean subRulesApplied;
    
    @Column
    private Integer remainingSize;
    
    @Column(columnDefinition = "TEXT")
    private String remainingIds;
    
    @Column
    private String baseRuleEntityType;
    
     public String getBaseRuleName() {
        return baseRuleName;
    }

    public void setBaseRuleName(String baseRuleName) {
        this.baseRuleName = baseRuleName;
    }

    public String getBaseRuleCode() {
        return baseRuleCode;
    }

    public void setBaseRuleCode(String baseRuleCode) {
        this.baseRuleCode = baseRuleCode;
    }

    public String getBaseRuleCreator() {
        return baseRuleCreator;
    }

    public void setBaseRuleCreator(String baseRuleCreator) {
        this.baseRuleCreator = baseRuleCreator;
    }

    public String getBaseRuleDescription() {
        return baseRuleDescription;
    }

    public void setBaseRuleDescription(String baseRuleDescription) {
        this.baseRuleDescription = baseRuleDescription;
    }

    public Boolean getWithUpdate() {
        return withUpdate;
    }

    public void setWithUpdate(Boolean withUpdate) {
        this.withUpdate = withUpdate;
    }

    public Boolean getSubRulesApplied() {
        return subRulesApplied;
    }

    public void setSubRulesApplied(Boolean subRulesApplied) {
        this.subRulesApplied = subRulesApplied;
    }

    public Integer getRemainingSize() {
        return remainingSize;
    }

    public void setRemainingSize(Integer remainingSize) {
        this.remainingSize = remainingSize;
    }

    public String getRemainingIds() {
        return remainingIds;
    }

    public void setRemainingIds(String remainingIds) {
        this.remainingIds = remainingIds;
    }

    public String getBaseRuleEntityType() {
        return baseRuleEntityType;
    }

    public void setBaseRuleEntityType(String entityType) {
        this.baseRuleEntityType = entityType;
    }

}
