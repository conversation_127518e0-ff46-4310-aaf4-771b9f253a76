package com.magnamedia.entity;


import javax.persistence.Entity;
import javax.persistence.Column;

import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.IdLabelCodeOwner;
import com.magnamedia.mastersearch.Searchable;
import com.magnamedia.mastersearch.SearchableField;
import java.io.Serializable;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 */

@Entity
@Searchable(showunName = "Revenues", order = 3, permissionCode = "Revenues")
public class Revenue extends BaseEntity implements Serializable, IdLabelCodeOwner{

//	private static final long serialVersionUID = 1L;
//	@Column(nullable = false, unique = true)
    @Column
    @SearchableField(headerName = "Code", order = 1)
    private String code;

//	@Column(nullable = false, unique = true)
    @Label
    @SearchableField(headerName = "Name", order = 2)
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
