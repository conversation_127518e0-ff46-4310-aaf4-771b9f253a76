package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdSerializer;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import java.util.Date;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 4/21/2020
 **/

@Entity
public class CreditCardStatementRecord extends BaseEntity {

    @ManyToOne
    @JsonSerialize(using = IdSerializer.class)
    private BankStatementFile bankStatementFile;

    @Column
    private String srlNbr;

    @Column
    private String merchantId;

    @Column
    private String chainId;

    @Column
    private String merchantName;

    @Column
    private String location;

    @Column
    private String telephon;

    @Column
    private String terminalId;

    @Column
    private String sequenceNumber;

    @Column
    private Double transactionCurr;

    @Column
    private Date transactionDate;

    @Column
    private String cardType;

    @Column
    private String cardNumber;

    @Column
    private String authCode;

    @Column
    private String transactionType;

    @Column
    private Double salesAmount;

    @Column
    private Double commission;

    @Column
    private Double netAmount;

    @Column
    private String acquirerData;

    @Column
    private String referenceNbr;

    @Column
    private String ty;

    @Column
    private String bankAccount;

    @Column
    private String transactionTime;

    @Column
    private String ibanAcctNbr;

    @Column
    private String discType;

    @Column
    private String masterChainId1;

    @Column
    private String masterChainId2;

    @Column
    private Double tipAmount;

    @Column
    private String tagId;

    @Column
    private String driverId;

    @Column
    private String pwcbCashBack;

    @Column
    private String txnInd;

    @Column
    private String commInd;

    @Column
    private String source;

    @Column
    private Double transactionFee;

    @Column
    private Double vatOnFee;

    @Column
    private Double vatOnCommission;

    public BankStatementFile getBankStatementFile() {
        return bankStatementFile;
    }

    public void setBankStatementFile(BankStatementFile bankStatementFile) {
        this.bankStatementFile = bankStatementFile;
    }

    public String getSrlNbr() {
        return srlNbr;
    }

    public void setSrlNbr(String srlNbr) {
        this.srlNbr = srlNbr;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getChainId() {
        return chainId;
    }

    public void setChainId(String chainId) {
        this.chainId = chainId;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getTelephon() {
        return telephon;
    }

    public void setTelephon(String telephon) {
        this.telephon = telephon;
    }

    public String getTerminalId() {
        return terminalId;
    }

    public void setTerminalId(String terminalId) {
        this.terminalId = terminalId;
    }

    public String getSequenceNumber() {
        return sequenceNumber;
    }

    public void setSequenceNumber(String sequenceNumber) {
        this.sequenceNumber = sequenceNumber;
    }

    public Double getTransactionCurr() {
        return transactionCurr;
    }

    public void setTransactionCurr(Double transactionCurr) {
        this.transactionCurr = transactionCurr;
    }

    public Date getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(Date transactionDate) {
        this.transactionDate = transactionDate;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public Double getSalesAmount() {
        return salesAmount;
    }

    public void setSalesAmount(Double salesAmount) {
        this.salesAmount = salesAmount;
    }

    public Double getCommission() {
        return commission;
    }

    public void setCommission(Double commission) {
        this.commission = commission;
    }

    public Double getNetAmount() {
        return netAmount;
    }

    public void setNetAmount(Double netAmount) {
        this.netAmount = netAmount;
    }

    public String getAcquirerData() {
        return acquirerData;
    }

    public void setAcquirerData(String acquirerData) {
        this.acquirerData = acquirerData;
    }

    public String getReferenceNbr() {
        return referenceNbr;
    }

    public void setReferenceNbr(String referenceNbr) {
        this.referenceNbr = referenceNbr;
    }

    public String getTy() {
        return ty;
    }

    public void setTy(String ty) {
        this.ty = ty;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getTransactionTime() {
        return transactionTime;
    }

    public void setTransactionTime(String transactionTime) {
        this.transactionTime = transactionTime;
    }

    public String getIbanAcctNbr() {
        return ibanAcctNbr;
    }

    public void setIbanAcctNbr(String ibanAcctNbr) {
        this.ibanAcctNbr = ibanAcctNbr;
    }

    public String getDiscType() {
        return discType;
    }

    public void setDiscType(String discType) {
        this.discType = discType;
    }

    public String getMasterChainId1() {
        return masterChainId1;
    }

    public void setMasterChainId1(String masterChainId1) {
        this.masterChainId1 = masterChainId1;
    }

    public String getMasterChainId2() {
        return masterChainId2;
    }

    public void setMasterChainId2(String masterChainId2) {
        this.masterChainId2 = masterChainId2;
    }

    public Double getTipAmount() {
        return tipAmount;
    }

    public void setTipAmount(Double tipAmount) {
        this.tipAmount = tipAmount;
    }

    public String getTagId() {
        return tagId;
    }

    public void setTagId(String tagId) {
        this.tagId = tagId;
    }

    public String getDriverId() {
        return driverId;
    }

    public void setDriverId(String driverId) {
        this.driverId = driverId;
    }

    public String getPwcbCashBack() {
        return pwcbCashBack;
    }

    public void setPwcbCashBack(String pwcbCashBack) {
        this.pwcbCashBack = pwcbCashBack;
    }

    public String getTxnInd() {
        return txnInd;
    }

    public void setTxnInd(String txnInd) {
        this.txnInd = txnInd;
    }

    public String getCommInd() {
        return commInd;
    }

    public void setCommInd(String commInd) {
        this.commInd = commInd;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Double getTransactionFee() {
        return transactionFee;
    }

    public void setTransactionFee(Double transactionFee) {
        this.transactionFee = transactionFee;
    }

    public Double getVatOnFee() {
        return vatOnFee;
    }

    public void setVatOnFee(Double vatOnFee) {
        this.vatOnFee = vatOnFee;
    }

    public Double getVatOnCommission() {
        return vatOnCommission;
    }

    public void setVatOnCommission(Double vatOnCommission) {
        this.vatOnCommission = vatOnCommission;
    }
}
