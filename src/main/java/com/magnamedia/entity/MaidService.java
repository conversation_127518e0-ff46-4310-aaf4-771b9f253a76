package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.module.type.MaidServiceType;
import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Apr 27, 2019
 * Jirra ACC-580
 */
@Entity
public class MaidService extends BaseEntity implements Serializable {
    
    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Housemaid housemaid;
    

    @Column
    private Boolean repeatEID;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private MaidServiceType type;

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public Boolean getRepeatEID() {
        return repeatEID;
    }

    public void setRepeatEID(Boolean repeatEID) {
        this.repeatEID = repeatEID;
    }

    public MaidServiceType getType() {
        return type;
    }

    public void setType(MaidServiceType type) {
        this.type = type;
    }
    
    
}