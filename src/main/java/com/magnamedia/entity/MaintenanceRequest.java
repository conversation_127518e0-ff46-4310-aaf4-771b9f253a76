package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.workflow.WorkflowEntity;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.workflow.FormField;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.module.type.ExpensePaymentMethod;
import com.magnamedia.module.type.MaintenanceAuditorDecision;
import com.magnamedia.module.type.MaintenanceRequestStatus;
import com.magnamedia.module.type.MaintenanceRequestType;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <PERSON> (Feb 07, 2021)
 */
@Entity
public class MaintenanceRequest extends WorkflowEntity {
    public MaintenanceRequest() {
        super(MaintenanceRequestType.PURCHASE_MANAGER_GET_PRICE.toString());
    }

    public MaintenanceRequest(String startTaskName) {
        super(startTaskName);
    }


    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private ExpenseRequestTodo expenseRequestTodo;


    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Expense expense;

    private String description;

    @Enumerated(EnumType.STRING)
    private MaintenanceRequestStatus status = MaintenanceRequestStatus.PENDING;
    private Boolean quotationReady;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Supplier supplier;

    private BigDecimal cost;

    private Boolean invoiceAttached;
    private BigDecimal vatAmount;

    private Boolean attachedValidVatInvoice;

    private Boolean taxable;
    
    @Enumerated(EnumType.STRING)
    private MaintenanceAuditorDecision auditorDecision;
    
    private String sendToGetBetterPriceNote;
    
    @Transient
    private String getPriceNote;
    
    @ElementCollection
    @CollectionTable(name = "MAINTENANCE_REQUEST_NOTE",
            joinColumns = @JoinColumn(name = "MAINTENANCE_REQUEST_ID"))
    private List<MaintenanceRequestNote> maintenanceRequestNotes;

    private Long bucketId;


    @Enumerated(EnumType.STRING)
    private ExpensePaymentMethod paymentMethod;

    public Boolean getTaxable() {
        return taxable;
    }

    public void setTaxable(Boolean taxable) {
        this.taxable = taxable;
    }

    public MaintenanceRequestStatus getStatus() {
        return status;
    }

    public void setStatus(MaintenanceRequestStatus status) {
        this.status = status;
    }
    
    public String getSendToGetBetterPriceNote() {
        return sendToGetBetterPriceNote;
    }

    public ExpenseRequestTodo getExpenseRequestTodo() {
        return expenseRequestTodo;
    }

    public void setExpenseRequestTodo(ExpenseRequestTodo expenseRequestTodo) {
        this.expenseRequestTodo = expenseRequestTodo;
    }

    public void setSendToGetBetterPriceNote(String sendToGetBetterPriceNote) {
        this.sendToGetBetterPriceNote = sendToGetBetterPriceNote;
    }
    
    public String getGetPriceNote() {
        return getPriceNote;
    }
    
    public void setGetPriceNote(String getPriceNote) {
        this.getPriceNote = getPriceNote;
    }
    
    public List<MaintenanceRequestNote> getMaintenanceRequestNotes() {
        return maintenanceRequestNotes;
    }
    
    public void setMaintenanceRequestNotes(List<MaintenanceRequestNote> maintenanceRequestNotes) {
        this.maintenanceRequestNotes = maintenanceRequestNotes;
    }
    
    public void addMaintenanceRequestNote(String note){
        
        if (this.maintenanceRequestNotes == null) this.maintenanceRequestNotes = new ArrayList<>();
        this.maintenanceRequestNotes.add(new MaintenanceRequestNote(note, CurrentRequest.getUser()));
    }
    
    @Override
    public String getFinishedTaskName() {
        return "";
    }

    public MaintenanceAuditorDecision getAuditorDecision() {
        return auditorDecision;
    }

    public void setAuditorDecision(MaintenanceAuditorDecision auditorDecision) {
        this.auditorDecision = auditorDecision;
    }

    @Override
    public List<FormField> getForm(String s) {
        return null;
    }

    public Expense getExpense() {
        return expense;
    }

    public void setExpense(Expense expense) {
        this.expense = expense;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getQuotationReady() {
        return quotationReady;
    }

    public boolean isQuotationReady() {
        if (quotationReady == null || quotationReady.equals(Boolean.FALSE))
            return false;
        return quotationReady;
    }

    public void setQuotationReady(Boolean quotationReady) {
        this.quotationReady = quotationReady;
    }

    public Supplier getSupplier() {
        return supplier;
    }

    public void setSupplier(Supplier supplier) {
        this.supplier = supplier;
    }

    public BigDecimal getCost() {
        return cost;
    }

    public void setCost(BigDecimal cost) {
        this.cost = cost;
    }

    public Boolean getInvoiceAttached() {
        return invoiceAttached;
    }

    public void setInvoiceAttached(Boolean invoiceAttached) {
        this.invoiceAttached = invoiceAttached;
    }

    public BigDecimal getVatAmount() {
        return vatAmount;
    }

    public void setVatAmount(BigDecimal vatAmount) {
        this.vatAmount = vatAmount;
    }

    public Boolean getAttachedValidVatInvoice() {
        return attachedValidVatInvoice;
    }

    public void setAttachedValidVatInvoice(Boolean attachedValidVatInvoice) {
        this.attachedValidVatInvoice = attachedValidVatInvoice;
    }

    public String getSupplierName() {
        return supplier != null ? supplier.getName() : null;
    }

    public Long getBucketId() { return bucketId; }

    public void setBucketId(Long bucketId) { this.bucketId = bucketId; }

    public ExpensePaymentMethod getPaymentMethod() { return paymentMethod; }

    public void setPaymentMethod(ExpensePaymentMethod paymentMethod) { this.paymentMethod = paymentMethod; }
}
