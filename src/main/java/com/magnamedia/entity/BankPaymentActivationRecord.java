package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.PaymentJsonSerializer;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.BankPaymentActivationRecordRepository;
import com.magnamedia.repository.PaymentRepository;
import java.sql.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import org.hibernate.envers.NotAudited;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Dec 9, 2018
 * Jirra ACC-329
 * updated Jirra ACC-332
 */
@Entity
public class BankPaymentActivationRecord  extends BaseEntity{
    
    @Column
    private Date Date;
    
    @Column
    private Date valueDate;

    @Column
    private Double amount;
    
    @Enumerated(EnumType.STRING)
    private PaymentStatus status;
    
//    @Column
//    private Double debitAmount;
    
    @Column
    private Double runningBalance;

    @Column
    private String contract;
    
    @Column
    private String description;
    
    @Column
    private Integer rowIndex;
    
    @Column
    private String errorMessage;
    
    @Column
    private boolean confirmed = false;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    private BankPaymentActivationFile bankPaymentActivationFile;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = PaymentJsonSerializer.class)
    private Payment payment;

    public Date getDate() {
        return Date;
    }

    public void setDate(Date Date) {
        this.Date = Date;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getValueDate() {
        return valueDate;
    }

    public void setValueDate(Date valueDate) {
        this.valueDate = valueDate;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public PaymentStatus getStatus() {
        return status;
    }

    public void setStatus(PaymentStatus status) {
        this.status = status;
    }

//    public Double getDebitAmount() {
//        return debitAmount;
//    }
//
//    public void setDebitAmount(Double debitAmount) {
//        this.debitAmount = debitAmount;
//    }

    public Double getRunningBalance() {
        return runningBalance;
    }

    public void setRunningBalance(Double runningBalance) {
        this.runningBalance = runningBalance;
    }

    public String getContract() {
        return contract;
    }

    public void setContract(String contract) {
        this.contract = contract;
    }

    public Integer getRowIndex() {
        return rowIndex;
    }

    public void setRowIndex(Integer rowIndex) {
        this.rowIndex = rowIndex;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public boolean isConfirmed() {
        return confirmed;
    }

    public void setConfirmed(boolean confirmed) {
        this.confirmed = confirmed;
    }

    public BankPaymentActivationFile getBankPaymentActivationFile() {
        return bankPaymentActivationFile;
    }

    public void setBankPaymentActivationFile(BankPaymentActivationFile bankPaymentActivationFile) {
        this.bankPaymentActivationFile = bankPaymentActivationFile;
    }

    public Payment getPayment() {
        return payment;
    }

    public void setPayment(Payment payment) {
        this.payment = payment;
    }
    
    public void validate() {
            if (getBankPaymentActivationFile()== null)
                        throw new RuntimeException("File is mandatory.");

//                BankPaymentActivationRecordRepository bankPaymentActivationRecordRepository =
//                        Setup.getApplicationContext().getBean(BankPaymentActivationRecordRepository.class);
    }
    
    @BeforeInsert
    public void validateInsert() {
        PaymentRepository paymentRepository =
                Setup.getApplicationContext().getBean(PaymentRepository.class);
        List<Payment> payments;
        if (getContract()!= null && !getContract().isEmpty() &&
                getContract().contains("Contr-") &&
                getValueDate()!= null){
                Long contractID = Long.valueOf(getContract().split("-")[1]);
                //jirra 405
                payments =
                        paymentRepository
                                .findByContract_IdAndDateOfPaymentAndAmountOfPayment(
                                        contractID, getValueDate(), getAmount());
                if (payments.size() > 0)
                    setPayment(payments.get(0));
        }
        validate();
    }
    
    @BeforeUpdate
    public void validateUpdate() {
        validate();
        BankPaymentActivationRecordRepository bankPaymentActivationRecordRepository =
                Setup.getApplicationContext().getBean(BankPaymentActivationRecordRepository.class);
        
        BankPaymentActivationRecord old = bankPaymentActivationRecordRepository.getOne(getId());
        
        if ((getDate()!= null && old.getDate()== null)
                ||(getDate()== null && old.getDate()!= null)
                ||(getDate()!= old.getDate()))
                    throw new RuntimeException("Value Date is not updatable.");
        if ((getValueDate()!= null && old.getValueDate()== null)
                ||(getValueDate()== null && old.getValueDate()!= null)
                ||(getValueDate()!= old.getValueDate()))
                    throw new RuntimeException("Value Date is not updatable.");
        if ((getContract()!= null && old.getContract()== null)
                ||(getContract()== null && old.getContract()!= null)
                ||(!getContract().equals(old.getContract())))
                    throw new RuntimeException("Contract is not updatable.");
        if ((getAmount()!= null && old.getAmount()== null)
                ||(getAmount()== null && old.getAmount()!= null)
                ||(!getAmount().equals(old.getAmount())))
                    throw new RuntimeException("Amount is not updatable.");
//        if ((getDebitAmount()!= null && old.getDebitAmount()== null)
//                ||(getDebitAmount()== null && old.getDebitAmount()!= null)
//                ||(!getDebitAmount().equals(old.getDebitAmount())))
//                    throw new RuntimeException("Amount is not updatable.");
        if ((getRunningBalance()!= null && old.getRunningBalance()== null)
                ||(getRunningBalance()== null && old.getRunningBalance()!= null)
                ||(!getRunningBalance().equals(old.getRunningBalance())))
                    throw new RuntimeException("Amount is not updatable.");
        if (getPayment().getId() != old.getPayment().getId())
                    throw new RuntimeException("Payment is not updatable.");
    }
    
}
