package com.magnamedia.entity;

import com.magnamedia.core.entity.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Lob;

@Entity
public class DDSignatureTemp extends BaseEntity {

    private Long cptId;
    private String eid;
    private Long directDebitFileId;
    private Long oldAttachmentId;
    private Long newAttachmentId;

    public Long getCptId() { return cptId; }

    public void setCptId(Long cptId) { this.cptId = cptId; }

    public String getEid() { return eid; }

    public void setEid(String eid) { this.eid = eid; }

    public Long getDirectDebitFileId() { return directDebitFileId; }

    public void setDirectDebitFileId(Long directDebitFileId) { this.directDebitFileId = directDebitFileId; }

    public Long getOldAttachmentId() { return oldAttachmentId; }

    public void setOldAttachmentId(Long oldAttachmentId) { this.oldAttachmentId = oldAttachmentId; }

    public Long getNewAttachmentId() { return newAttachmentId; }

    public void setNewAttachmentId(Long newAttachmentId) { this.newAttachmentId = newAttachmentId; }
}
