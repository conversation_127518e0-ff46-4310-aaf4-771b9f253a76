package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.User;
import com.magnamedia.entity.serializer.IdLabelListSerializer;
import com.magnamedia.module.type.CompareOperations;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Entity
public class ExpenseNotification extends BaseEntity {

    public enum PercentageComparedTo {
        PAYMENT, MONTH
    }

    public enum NumberOfRquestComparedTo {
        YEAR, DAY, WEEK, MONTH
    }

    @OneToOne(mappedBy = "notification")
    @JsonIgnore
    private Expense expense;

    public Expense getExpense() {
        return expense;
    }

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "EXPENSE_NOTIFICATION_NOTIFIERS",
            joinColumns = @JoinColumn(
                    name = "NOTIFICATION_ID",
                    referencedColumnName = "ID"),
            inverseJoinColumns = @JoinColumn(
                    name = "USER_ID",
                    referencedColumnName = "ID")
    )
    @JsonSerialize(using = IdLabelListSerializer.class)
    private List<User> notifyUsers = new ArrayList<>();


    //by amount
    @Column
    @ColumnDefault("0")
    private boolean amountEnabled;

    @Column
    @Enumerated(EnumType.STRING)
    private CompareOperations amountOperation;

    @Column
    private Double amount;

    //by percentage
    @Column
    @ColumnDefault("0")
    private boolean percentageEnabled;

    @Column
    @Enumerated(EnumType.STRING)
    private CompareOperations percentageOperation;

    @Column
    private Double percentage;

    @Column
    private Integer PercentageNOCompared;

    @Column
    @Enumerated(EnumType.STRING)
    private PercentageComparedTo percentageComparedTo;

    //Number of Request
    @JsonProperty
    @ColumnDefault("0")
    private boolean numberOfRequestsEnabled;

    @Column
    @Enumerated(EnumType.STRING)
    private NumberOfRquestComparedTo numberOfRquestComparedTo;


    public List<User> getNotifyUsers() {
        return notifyUsers;
    }

    public void setNotifyUsers(List<User> notifyUsers) {
        this.notifyUsers = notifyUsers;
    }

    public boolean isAmountEnabled() {
        return amountEnabled;
    }

    public void setAmountEnabled(boolean amountEnabled) {
        this.amountEnabled = amountEnabled;
    }

    public void setExpense(Expense expense) {
        this.expense = expense;
    }

    public CompareOperations getAmountOperation() {
        return amountOperation;
    }

    public void setAmountOperation(CompareOperations amountOperation) {
        this.amountOperation = amountOperation;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public boolean isPercentageEnabled() {
        return percentageEnabled;
    }

    public void setPercentageEnabled(boolean percentageEnabled) {
        this.percentageEnabled = percentageEnabled;
    }

    public CompareOperations getPercentageOperation() {
        return percentageOperation;
    }

    public void setPercentageOperation(CompareOperations percentageOperation) {
        this.percentageOperation = percentageOperation;
    }

    public Double getPercentage() {
        return percentage;
    }

    public void setPercentage(Double percentage) {
        this.percentage = percentage;
    }

    public Integer getPercentageNOCompared() {
        return PercentageNOCompared;
    }

    public void setPercentageNOCompared(Integer percentageNOCompared) {
        PercentageNOCompared = percentageNOCompared;
    }

    public PercentageComparedTo getPercentageComparedTo() {
        return percentageComparedTo;
    }

    public void setPercentageComparedTo(PercentageComparedTo percentageComparedTo) {
        this.percentageComparedTo = percentageComparedTo;
    }

    public boolean isNumberOfRequestsEnabled() {
        return numberOfRequestsEnabled;
    }

    public void setNumberOfRequestsEnabled(boolean numberOfRequestsEnabled) {
        this.numberOfRequestsEnabled = numberOfRequestsEnabled;
    }

    public NumberOfRquestComparedTo getNumberOfRquestComparedTo() {
        return numberOfRquestComparedTo;
    }

    public void setNumberOfRquestComparedTo(NumberOfRquestComparedTo numberOfRquestComparedTo) {
        this.numberOfRquestComparedTo = numberOfRquestComparedTo;
    }
}
