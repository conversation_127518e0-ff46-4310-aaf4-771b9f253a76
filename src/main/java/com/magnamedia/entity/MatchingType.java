package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * SAL-2777
 * Created on Jan 19, 2021
 */
@Entity
public class MatchingType extends BaseEntity implements Serializable {

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private PicklistItem matchingType;

    @JsonSerialize(using = IdLabelSerializer.class)
    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    private Client client;

    @JsonSerialize(using = IdLabelSerializer.class)
    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    private Housemaid housemaid;

    @Column
    private Integer matchingTypeOrder;

    @Column
    private Boolean disqualifying = false;

    public PicklistItem getMatchingType() {
        return matchingType;
    }

    public void setMatchingType(PicklistItem matchingType) {
        this.matchingType = matchingType;
    }

    public Client getClient() {
        return client;
    }

    public void setClient(Client client) {
        this.client = client;
    }

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public Integer getMatchingTypeOrder() {
        return matchingTypeOrder;
    }

    public void setMatchingTypeOrder(Integer matchingTypeOrder) {
        this.matchingTypeOrder = matchingTypeOrder;
    }

    public Boolean getDisqualifying() {
        return disqualifying;
    }

    public void setDisqualifying(Boolean disqualifying) {
        this.disqualifying = disqualifying;
    }
    
}
