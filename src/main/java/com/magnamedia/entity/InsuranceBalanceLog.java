package com.magnamedia.entity;

import com.magnamedia.core.entity.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2/14/2021
 */
@Entity
public class InsuranceBalanceLog extends BaseEntity {

    public enum InsuranceBalanceLogType{
        OPENING,PAYMENT,STATEMENT
    }
    @Column
    private Date logDate;

    @Column
    private String Description;

    @Column
    private Double amount;

    @Column
    private Double balance;

    @Column
    private Long refId;

    @Column
    @Enumerated(EnumType.STRING)
    private InsuranceBalanceLogType type;

    public Date getLogDate() {
        return logDate;
    }

    public void setLogDate(Date logDate) {
        this.logDate = logDate;
    }

    public String getDescription() {
        return Description;
    }

    public void setDescription(String description) {
        Description = description;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Double getBalance() {
        return balance;
    }

    public void setBalance(Double balance) {
        this.balance = balance;
    }

    public Long getRefId() {
        return refId;
    }

    public void setRefId(Long refId) {
        this.refId = refId;
    }

    public InsuranceBalanceLogType getType() {
        return type;
    }

    public void setType(InsuranceBalanceLogType type) {
        this.type = type;
    }
}
