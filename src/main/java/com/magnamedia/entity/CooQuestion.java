package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.serializer.UserSerializer;
import com.magnamedia.extra.StringUtils;

import javax.persistence.*;

/**
 * Created by <PERSON><PERSON>.Masod on 4/4/2021.
 */

@Entity
public class CooQuestion extends BaseEntity {
    public enum QuestionFor {
        REQUESTER, APPROVER
    }

    public enum QuestionedPage {
        COO_APPROVAL, BANK_CONFIRMATION, NIGHT_REVIEW
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = UserSerializer.class)
    private User targetUser;

    @Column
    @Enumerated(EnumType.STRING)
    private QuestionFor questionFor;

    @Column
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String relatedEntityType;

    @Column
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long relatedEntityId;

    @JsonIgnore
    @Transient
    private BaseEntity relatedEntity;

    @Lob
    private String question;

    @Lob
    private String answer;

    @Column
    @Enumerated(EnumType.STRING)
    private QuestionedPage questionedPage;

    @Column
    private String questionedRecordLabel;

    @Lob
    private String questionedRecordData;

    public User getTargetUser() {
        return targetUser;
    }

    public void setTargetUser(User targetUser) {
        this.targetUser = targetUser;
    }

    public QuestionFor getQuestionFor() {
        return questionFor;
    }

    public void setQuestionFor(QuestionFor questionFor) {
        this.questionFor = questionFor;
    }

    public String getRelatedEntityType() {
        return relatedEntityType;
    }

    public void setRelatedEntityType(String relatedEntityType) {
        this.relatedEntityType = relatedEntityType;
    }

    public Long getRelatedEntityId() {
        return relatedEntityId;
    }

    public void setRelatedEntityId(Long relatedEntityId) {
        this.relatedEntityId = relatedEntityId;
    }

    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = question;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public QuestionedPage getQuestionedPage() {
        return questionedPage;
    }

    public void setQuestionedPage(QuestionedPage questionedPage) {
        this.questionedPage = questionedPage;
    }

    public String getQuestionedRecordLabel() {
        return questionedRecordLabel;
    }

    public void setQuestionedRecordLabel(String questionedRecordLabel) {
        this.questionedRecordLabel = questionedRecordLabel;
    }

    public String getQuestionedRecordData() {
        return questionedRecordData;
    }

    public void setQuestionedRecordData(String questionedRecordData) {
        this.questionedRecordData = questionedRecordData;
    }

    public boolean isAnswered() {
        return !StringUtils.isEmpty(answer);
    }

    public BaseEntity getRelatedEntity() {
        if (relatedEntity != null)
            return relatedEntity;

        String repositoryName = relatedEntityType.toLowerCase().charAt(0) + relatedEntityType.substring(1) + "Repository";
        relatedEntity = ((BaseRepository<? extends BaseEntity>) Setup.getApplicationContext().getBean(repositoryName))
                .findOne(relatedEntityId);

        return relatedEntity;
    }
}
