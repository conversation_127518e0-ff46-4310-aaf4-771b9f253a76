package com.magnamedia.entity.dto;

/*
 * <AUTHOR>
 * @created 21/04/2024 - 10:03 AM
 * ACC-7306
 */

import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.module.type.DirectDebitType;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DirectDebitFileSearchDto {

    private  Long id;
    private Map<String, Object> directDebit;
    private String applicationId;
    private Double amount;
    private String ddFrequency;
    private Date creationDate;
    private Date startDate;
    private Date expiryDate;
    private DirectDebitStatus ddStatus;
    private String rejectionReason;
    private Boolean uploaded;
    private Date resultDate;
    private String notes;
    private List<Map> Attachments;
    private  Long ddId;
    private  Long cptId;
    private String accountName;

    public DirectDebitFileSearchDto(
            Long clientId, String clientName, Long contractId, Boolean isActive,
            String bankName, Double additionalDiscount, String additionalDiscountNotesDD,
            String additionalDiscountNotesCPT, Long id, String applicationId,
            Double amount, DirectDebitType ddFrequency, Date creationDate, Date startDate, Date expiryDate,
            DirectDebitStatus ddStatus, String rejectionReason, Date resultDate,
            String notes, Long ddId, Long cptId, String accountName, String paymentTypeName) {

        this.directDebit = new HashMap<String, Object>(){{
            put("additionalDiscount", additionalDiscount);
            put("additionalDiscountNotes", additionalDiscountNotesDD);
            put("contractPaymentTerm", new HashMap<String, Object>(){{
                put("isActive", isActive);
                put("bankName", bankName);
                put("additionalDiscountNotes", additionalDiscountNotesCPT);
                put("contract", new HashMap<String, Object>(){{
                    put("id", contractId);
                    put("client", new HashMap<String, Object>(){{
                        put("id", clientId);
                        put("name", clientName);
                    }});
                }});

            }});
            put("paymentType", paymentTypeName == null ? null : new HashMap<String, Object>(){{
                put("name", paymentTypeName);
            }});
        }};
        this.id = id;
        this.applicationId = applicationId;
        this.amount = amount;
        this.ddFrequency = ddFrequency != null ? ddFrequency.getValue() : "";
        this.creationDate = creationDate;
        this.startDate = startDate;
        this.expiryDate = expiryDate;
        this.ddStatus = ddStatus;
        this.rejectionReason = rejectionReason;
        this.uploaded = ddStatus.getValue().equals("SENT");
        this.resultDate = resultDate;
        this.notes = notes;
        this.ddId = ddId;
        this.cptId = cptId;
        this.accountName = accountName;
    }

    public Long getId() { return id; }

    public void setId(Long id) { this.id = id; }

    public Map<String, Object> getDirectDebit() { return directDebit; }

    public void setDirectDebit(Map<String, Object> directDebit) { this.directDebit = directDebit; }

    public String getApplicationId() { return applicationId; }

    public void setApplicationId(String applicationId) { this.applicationId = applicationId; }

    public Double getAmount() { return amount; }

    public void setAmount(Double amount) { this.amount = amount; }

    public String getDdFrequency() { return ddFrequency; }

    public void setDdFrequency(String ddFrequency) { this.ddFrequency = ddFrequency; }

    public Date getCreationDate() { return creationDate; }

    public void setCreationDate(Date creationDate) { this.creationDate = creationDate; }

    public Date getStartDate() { return startDate; }

    public void setStartDate(Date startDate) { this.startDate = startDate; }

    public Date getExpiryDate() { return expiryDate; }

    public void setExpiryDate(Date expiryDate) { this.expiryDate = expiryDate; }

    public DirectDebitStatus getDdStatus() { return ddStatus; }

    public void setDdStatus(DirectDebitStatus ddStatus) { this.ddStatus = ddStatus; }

    public String getRejectionReason() { return rejectionReason; }

    public void setRejectionReason(String rejectionReason) { this.rejectionReason = rejectionReason; }

    public Boolean getUploaded() { return uploaded; }

    public void setUploaded(Boolean uploaded) { this.uploaded = uploaded; }

    public Date getResultDate() { return resultDate; }

    public void setResultDate(Date resultDate) { this.resultDate = resultDate; }

    public String getNotes() { return notes; }

    public void setNotes(String notes) { this.notes = notes; }

    public List<Map> getAttachments() { return Attachments; }

    public void setAttachments(List<Map> attachments) { Attachments = attachments; }

    public Long getDdId() { return ddId; }

    public void setDdId(Long ddId) { this.ddId = ddId; }

    public Long getCptId() { return cptId; }

    public void setCptId(Long ddId) { this.cptId = cptId; }

    public String getAccountName() { return accountName; }

    public void setAccountName(String accountName) { this.accountName = accountName; }
}