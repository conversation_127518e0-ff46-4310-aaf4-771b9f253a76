package com.magnamedia.entity.dto;

import com.magnamedia.entity.Transaction;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.module.type.ExpenseRequestType;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <PERSON> (Jan 27, 2021)
 */
public class ReconciliatorDto {
    public ReconciliatorDto(String action, ExpensePayment ep) {
        this.id = ep.getId();
        this.action = action;
        this.transactionId = ep.getTransaction() != null ? ep.getTransaction().getId() : null;
        //Jirra ACC-3558
        List<Transaction> transactions = ep.getTransactions();
        if (transactions != null && ! transactions.isEmpty())
            this.transactionIds = transactions.stream().map(x -> x.getId()).collect(Collectors.toList());
        this.fromBucket = ep.getFromBucket() != null ? ep.getFromBucket().getName() : null;
        this.beneficiary = ep.getBeneficiaryName();
        this.description = ep.getDescription();
        Optional<ExpenseRequestTodo> expenseTypeTemp = ep.getExpenseRequestTodos().stream().findAny();
        if (expenseTypeTemp.isPresent()) {
            this.expenseType = expenseTypeTemp.get().getExpenseRequestType().getLabel();
        } else {
            if (ep.getReplenishmentTodo() != null) {
                this.expenseType = "Replenishment";
            }
            this.expenseType = "";
        }
        this.amount = ep.getLocalCurrencyAmount();
        this.date = ep.getCreationDate();
        this.approvedBy = ep.getApprovedBy();
        this.relatedToName = ep.getRelatedToName();
        this.paymentMethod = ep.getMethod().getLabel();
    }

    private Long id;
    private String action;
    private Long transactionId;
    private List<Long> transactionIds;
    private String fromBucket;
    private String beneficiary;
    private String description;
    private String expenseType;
    private Double amount;
    private Date date;
    private String approvedBy;
    private String relatedToName;
    private String paymentMethod;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public Long getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(Long transactionId) {
        this.transactionId = transactionId;
    }
    
    public List<Long> getTransactionIds() {
        return transactionIds;
    }
    
    public void setTransactionIds(List<Long> transactionIds) {
        this.transactionIds = transactionIds;
    }
    
    public String getFromBucket() {
        return fromBucket;
    }

    public void setFromBucket(String fromBucket) {
        this.fromBucket = fromBucket;
    }

    public String getBeneficiary() {
        return beneficiary;
    }

    public void setBeneficiary(String beneficiary) {
        this.beneficiary = beneficiary;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getExpenseType() {
        return expenseType;
    }

    public void setExpenseType(String expenseType) {
        this.expenseType = expenseType;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(String approvedBy) {
        this.approvedBy = approvedBy;
    }

    public String getRelatedToName() {
        return relatedToName;
    }

    public void setRelatedToName(String relatedToName) {
        this.relatedToName = relatedToName;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }
}
