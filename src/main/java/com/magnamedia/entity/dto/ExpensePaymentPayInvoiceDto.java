package com.magnamedia.entity.dto;

import com.magnamedia.core.entity.Attachment;
import com.magnamedia.entity.Supplier;
import com.magnamedia.module.type.ExpensePaymentMethod;
import org.springframework.data.domain.Page;

import java.util.ArrayList;
import java.util.List;

/**
 * <PERSON> (Jan 21, 2021)
 */
public class ExpensePaymentPayInvoiceDto {
    private List<ExpenseRequestPayInvoiceDto> expenseRequestPayInvoiceDtos = new ArrayList<>();
    private Page<ExpenseRequestPayInvoiceDto> expenseRequestPayInvoiceDtosPage;
    private Long beneficiaryId;
    private Long expenseId;
    private String beneficiary;
    private Double balance;
    private Double amountToPay;
    private Double vatAmount;
    private Boolean taxable;
    private ExpensePaymentMethod paymentMethod;
    private List<Attachment> attachments;
    private Boolean isStatement = false;

    public ExpensePaymentPayInvoiceDto() {
    }

    public ExpensePaymentPayInvoiceDto(
        Page<ExpenseRequestPayInvoiceDto> expenseRequestTodos, Supplier supplier)  {
        if (expenseRequestTodos.isEmpty()) return;

        beneficiaryId = supplier.getId();
        beneficiary = supplier.getName();
        taxable = supplier.getVatRegistered();
        // todo add null check in the front-end
        paymentMethod = supplier.getPaymentMethods().stream().filter(method -> method == ExpensePaymentMethod.BANK_TRANSFER)
                .findFirst()
                .orElse(null);
        balance = expenseRequestTodos.stream().mapToDouble(ExpenseRequestPayInvoiceDto::getAmount).sum();
        expenseRequestPayInvoiceDtosPage = expenseRequestTodos;

    }

    public ExpensePaymentPayInvoiceDto(Long beneficiaryId, Long expenseId, String beneficiaryName, Double balance) {
        this.beneficiaryId=beneficiaryId;
        this.expenseId=expenseId;
        this.beneficiary=beneficiaryName;
        this.balance=balance;
    }

    // Constructor for invoice statements
    public ExpensePaymentPayInvoiceDto(Long statementId, String supplierName, Double balance, Boolean isStatement) {
        this.beneficiaryId = statementId; // Using beneficiaryId to store statement ID for statements
        this.beneficiary = supplierName;
        this.balance = balance;
        this.isStatement = isStatement;
    }

    public List<ExpenseRequestPayInvoiceDto> getExpenseRequestPayInvoiceDtos() {
        return expenseRequestPayInvoiceDtos;
    }

    public void setExpenseRequestPayInvoiceDtos(List<ExpenseRequestPayInvoiceDto> expenseRequestPayInvoiceDtos) {
        this.expenseRequestPayInvoiceDtos = expenseRequestPayInvoiceDtos;
    }

    public String getBeneficiary() {
        return beneficiary;
    }

    public void setBeneficiary(String beneficiary) {
        this.beneficiary = beneficiary;
    }

    public Double getBalance() {
        return balance;
    }

    public void setBalance(Double balance) {
        this.balance = balance;
    }

    public Double getAmountToPay() {
        return amountToPay;
    }

    public void setAmountToPay(Double amountToPay) {
        this.amountToPay = amountToPay;
    }

    public Double getVatAmount() {
        return vatAmount;
    }

    public void setVatAmount(Double vatAmount) {
        this.vatAmount = vatAmount;
    }

    public Boolean getTaxable() {
        return taxable;
    }

    public void setTaxable(Boolean taxable) {
        this.taxable = taxable;
    }

    public List<Attachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<Attachment> attachments) {
        this.attachments = attachments;
    }

    public Long getBeneficiaryId() {
        return beneficiaryId;
    }

    public void setBeneficiaryId(Long beneficiaryId) {
        this.beneficiaryId = beneficiaryId;
    }

    public Long getExpenseId() {
        return expenseId;
    }

    public void setExpenseId(Long expenseId) {
        this.expenseId = expenseId;
    }

    public ExpensePaymentMethod getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(ExpensePaymentMethod paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public Page<ExpenseRequestPayInvoiceDto> getExpenseRequestPayInvoiceDtosPage() { return expenseRequestPayInvoiceDtosPage; }

    public void setExpenseRequestPayInvoiceDtosPage(Page<ExpenseRequestPayInvoiceDto> expenseRequestPayInvoiceDtosPage) {
        this.expenseRequestPayInvoiceDtosPage = expenseRequestPayInvoiceDtosPage;
    }

    public Boolean getIsStatement() {
        return isStatement;
    }

    public void setIsStatement(Boolean isStatement) {
        this.isStatement = isStatement;
    }
}
