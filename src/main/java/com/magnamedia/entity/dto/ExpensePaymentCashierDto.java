package com.magnamedia.entity.dto;

import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.module.type.ExpensePaymentType;

import java.util.Date;

/**
 * <PERSON> (Jan 21, 2021)
 */
public class ExpensePaymentCashierDto {
    public static ExpensePaymentCashierDto createExpensePaymentCashierDtoForCashier(ExpensePayment expensePayment) {
        ExpensePaymentCashierDto dto = new ExpensePaymentCashierDto();
        dto.id = expensePayment.getId();
        dto.description = expensePayment.getDescription();
        dto.amount = expensePayment.getLocalCurrencyAmount();
        dto.beneficiary = expensePayment.getBeneficiaryName();
        dto.action = ((expensePayment.getType().equals(ExpensePaymentType.PAY) || expensePayment.getType()
                .equals(ExpensePaymentType.PAY_TO_BUCKET)) ? "Pay" : "Collect");
        dto.navigation = (expensePayment.getType().equals(ExpensePaymentType.PAY) ?
                "PAY-MONEY-1" : "PAY-MONEY-3");
        dto.setRequester(expensePayment.getRequester() == null ? "" : expensePayment.getRequester().getFullName());
        dto.setApprovedBy(expensePayment.getApprovedBy());
        dto.setCreationDate(expensePayment.getCreationDate());
        return dto;
    }


    public static ExpensePaymentCashierDto createExpensePaymentDtoForPendingInvoice(ExpensePayment expensePayment) {
        ExpensePaymentCashierDto dto = new ExpensePaymentCashierDto();
        dto.id = expensePayment.getId();
        dto.description = expensePayment.getDescription();
        dto.amount = expensePayment.getLocalCurrencyAmount();
        dto.beneficiary = expensePayment.getBeneficiaryName();
        dto.action = "Collect Invoice";
        dto.setRequester(expensePayment.getRequester() == null ? "" : expensePayment.getRequester().getFullName());
        dto.setApprovedBy(expensePayment.getApprovedBy());
        dto.setCreationDate(expensePayment.getCreationDate());
        return dto;
    }
//
//    public ExpensePaymentCashierDto(ExpensePayment expensePayment) {
//        this.id = expensePayment.getId();
//        this.description = expensePayment.getDescription();
//        this.amount = expensePayment.getAmount();
//        this.beneficiary = expensePayment.getBeneficiaryName();
//        if (expensePayment.getStatus().equals(ExpensePaymentStatus.PENDING)) {
//            this.action = ((expensePayment.getType().equals(ExpensePaymentType.PAY)
//                    || expensePayment.getType().equals(ExpensePaymentType.PAY_TO_BUCKET)) ?
//                    "Pay" : "Collect");
//
//            this.navigation = (expensePayment.getType().equals(ExpensePaymentType.PAY) ?
//                    "PAY-MONEY-1" : "PAY-MONEY-3");
//        } else if (expensePayment.getStatus().equals(ExpensePaymentStatus.PAID_PENDING_INVOICE)
//                && expensePayment.getConfirmed().equals(Boolean.FALSE)) {
//        }
//    }

    private Long id;
    private String description;
    private String beneficiary;
    private Double amount;
    private String action;
    private String navigation;
    private String requester;
    private String approvedBy;
    private Date creationDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNavigation() {
        return navigation;
    }

    public void setNavigation(String navigation) {
        this.navigation = navigation;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getBeneficiary() {
        return beneficiary;
    }

    public void setBeneficiary(String beneficiary) {
        this.beneficiary = beneficiary;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getRequester() {
        return requester;
    }

    public void setRequester(String requester) {
        this.requester = requester;
    }
    
    public String getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(String approvedBy) {
        this.approvedBy = approvedBy;
    }
    
    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }
}
