package com.magnamedia.entity.dto;

import com.magnamedia.entity.PurchaseItem;
import com.magnamedia.entity.PurchaseOrder;
import com.magnamedia.module.type.PurchaseItemInOrderStatus;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <PERSON> (Feb 04, 2021)
 */
public class PurchaseOrderDto {
    public PurchaseOrderDto(PurchaseOrder purchaseOrder) {
        this.id = purchaseOrder.getId();
        this.supplierName = purchaseOrder.getSupplier().getName();

        this.items = new ArrayList<>();
        purchaseOrder.getPurchaseItems().forEach(t -> {
            if (t.getItemInOrderStatus().equals(PurchaseItemInOrderStatus.INVOLVED))
                items.add(new PurchaseOrderItemDto(t));
        });

    }

    private Long id;
    private List<PurchaseOrderItemDto> items;
    private String supplierName;

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public List<PurchaseOrderItemDto> getItems() {
        return items;
    }

    public void setItems(List<PurchaseOrderItemDto> items) {
        this.items = items;
    }
}

class PurchaseOrderItemDto {
    public PurchaseOrderItemDto(PurchaseItem purchaseItem) {
        this.id = purchaseItem.getId();
        this.name = purchaseItem.getItem().getName();
        this.quantity = purchaseItem.getQuantity();
        this.unitOfMeasure = purchaseItem.getItem().getUnitOfMeasureShortName();
        this.price = purchaseItem.getUnitPrice();
    }

    private Long id;
    private String name;
    private BigDecimal quantity;
    private String unitOfMeasure;
    private BigDecimal price;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    public void setUnitOfMeasure(String unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }
}
