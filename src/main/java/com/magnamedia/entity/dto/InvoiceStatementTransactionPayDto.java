package com.magnamedia.entity.dto;

import com.magnamedia.core.entity.Attachment;
import com.magnamedia.entity.Supplier;
import com.magnamedia.module.type.ExpensePaymentMethod;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


public class InvoiceStatementTransactionPayDto {

    private List<InvoiceStatementTransactionPayItemDto> transactionItems = new ArrayList<>();
    private Boolean taxable;
    private List<Attachment> attachments = new ArrayList<>();


    private String maidName;
    private String passportNumber;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date fromDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date toDate;

    public InvoiceStatementTransactionPayDto() {
    }

    public InvoiceStatementTransactionPayDto(List<InvoiceStatementTransactionPayItemDto> transactionItems, Boolean taxable) {
        this.transactionItems = transactionItems;
        this.taxable = taxable;
    }

    public List<InvoiceStatementTransactionPayItemDto> getTransactionItems() {
        return transactionItems;
    }

    public void setTransactionItems(List<InvoiceStatementTransactionPayItemDto> transactionItems) {
        this.transactionItems = transactionItems;
    }

    public Boolean getTaxable() {
        return taxable;
    }

    public void setTaxable(Boolean taxable) {
        this.taxable = taxable;
    }

    public List<Attachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<Attachment> attachments) {
        this.attachments = attachments;
    }

    public String getMaidName() {
        return maidName;
    }

    public void setMaidName(String maidName) {
        this.maidName = maidName;
    }

    public String getPassportNumber() {
        return passportNumber;
    }

    public void setPassportNumber(String passportNumber) {
        this.passportNumber = passportNumber;
    }

    public Date getFromDate() {
        return fromDate;
    }

    public void setFromDate(Date fromDate) {
        this.fromDate = fromDate;
    }

    public Date getToDate() {
        return toDate;
    }

    public void setToDate(Date toDate) {
        this.toDate = toDate;
    }
}
