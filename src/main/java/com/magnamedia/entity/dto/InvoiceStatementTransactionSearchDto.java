package com.magnamedia.entity.dto;

import com.magnamedia.extra.InvoiceStatementTransactionType;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

public class InvoiceStatementTransactionSearchDto {

    private String maidName;
    private String passportNumber;
    
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date fromDate;
    
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date toDate;
    
    private InvoiceStatementTransactionType type;

    public InvoiceStatementTransactionSearchDto() {
    }

    public InvoiceStatementTransactionSearchDto(String maidName, String passportNumber,
                                              Date fromDate, Date toDate, InvoiceStatementTransactionType type) {
        this.maidName = maidName;
        this.passportNumber = passportNumber;
        this.fromDate = fromDate;
        this.toDate = toDate;
        this.type = type;
    }

    public String getMaidName() {
        return maidName;
    }

    public void setMaidName(String maidName) {
        this.maidName = maidName;
    }

    public String getPassportNumber() {
        return passportNumber;
    }

    public void setPassportNumber(String passportNumber) {
        this.passportNumber = passportNumber;
    }

    public Date getFromDate() {
        return fromDate;
    }

    public void setFromDate(Date fromDate) {
        this.fromDate = fromDate;
    }

    public Date getToDate() {
        return toDate;
    }

    public void setToDate(Date toDate) {
        this.toDate = toDate;
    }

    public InvoiceStatementTransactionType getType() {
        return type;
    }

    public void setType(InvoiceStatementTransactionType type) {
        this.type = type;
    }
}
