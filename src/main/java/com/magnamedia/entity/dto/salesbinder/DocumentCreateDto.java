package com.magnamedia.entity.dto.salesbinder;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.magnamedia.entity.PurchaseOrder;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <PERSON> (Feb 06, 2021)
 */
public class DocumentCreateDto {
    @JsonProperty("context_id")
    private int contextId;
    @JsonProperty("customer_id")
    private String customerId;
    @JsonProperty("issue_date")
    private String issueDate;

    @JsonProperty("document_items")
    private List<DocumentItemCreateDto> documentItems = new ArrayList<>();

    public DocumentCreateDto(PurchaseOrder o) {
        this.contextId=11;
        this.customerId=o.getSupplier().getSupplierId();
        this.issueDate= Instant.now().atZone(ZoneId.systemDefault()).format( DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        o.getPurchaseItems().forEach(item->{
            documentItems.add(new DocumentItemCreateDto(item));
        });
    }

    public int getContextId() {
        return contextId;
    }

    public void setContextId(int contextId) {
        this.contextId = contextId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(String issueDate) {
        this.issueDate = issueDate;
    }

    public List<DocumentItemCreateDto> getDocumentItems() {
        return documentItems;
    }

    public void setDocumentItems(List<DocumentItemCreateDto> documentItems) {
        this.documentItems = documentItems;
    }
}
