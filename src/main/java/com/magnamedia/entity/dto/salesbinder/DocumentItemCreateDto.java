package com.magnamedia.entity.dto.salesbinder;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.magnamedia.entity.PurchaseItem;

import java.math.BigDecimal;

/**
 * <PERSON> (Feb 06, 2021)
 */
public class DocumentItemCreateDto {

    private BigDecimal quantity;
    private BigDecimal tax=new BigDecimal(0);
    private BigDecimal price;
    private BigDecimal weight;

    @JsonProperty("item_id")
    private String itemId;

    public DocumentItemCreateDto(PurchaseItem item) {
        this.quantity=item.getQuantity();
//        this.tax=item.get
        this.price=item.getUnitPrice();
//        this.weight=item.get
        this.itemId=item.getItem().getItemId();
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public String getItemId() {
        return itemId;
    }

    public void setItemId(String itemId) {
        this.itemId = itemId;
    }
}
