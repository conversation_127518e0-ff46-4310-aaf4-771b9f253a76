package com.magnamedia.entity.dto;

import com.magnamedia.core.entity.Attachment;

public class InvoiceStatementCalculationsDto {
    
    private Double balance;
    private Double totalAmountToPay;
    private Attachment annexureFile;
    private Long supplierId;
    private String supplierName;


    public InvoiceStatementCalculationsDto() {
    }
    
    public InvoiceStatementCalculationsDto(Double balance, Double totalAmountToPay, Attachment annexureFile, Long supplierId, String supplierName) {
        this.balance = balance;
        this.totalAmountToPay = totalAmountToPay;
        this.annexureFile = annexureFile;
        this.supplierId = supplierId;
        this.supplierName = supplierName;
    }



    public Double getBalance() {
        return balance;
    }
    
    public void setBalance(Double balance) {
        this.balance = balance;
    }
    
    public Double getTotalAmountToPay() {
        return totalAmountToPay;
    }
    
    public void setTotalAmountToPay(Double totalAmountToPay) {
        this.totalAmountToPay = totalAmountToPay;
    }
    
    public Attachment getAnnexureFile() {
        return annexureFile;
    }
    
    public void setAnnexureFile(Attachment annexureFile) {
        this.annexureFile = annexureFile;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }
}
