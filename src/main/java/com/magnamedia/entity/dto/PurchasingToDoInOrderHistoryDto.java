package com.magnamedia.entity.dto;

import com.magnamedia.core.Setup;
import com.magnamedia.entity.PurchaseItem;
import com.magnamedia.entity.PurchasingToDo;
import com.magnamedia.module.type.PurchaseItemSupplierStatus;
import com.magnamedia.repository.PurchaseItemRepository;

import java.util.Date;
import java.util.List;

/**
 * <PERSON> (Feb 15, 2021)
 */
public class PurchasingToDoInOrderHistoryDto {
    public PurchasingToDoInOrderHistoryDto(PurchasingToDo purchasingToDo) {
        this.id = purchasingToDo.getId();
        this.category = purchasingToDo.getCategory().getName();
        this.cycle = purchasingToDo.getCategory().getOrderCycle().getName();
        this.date = purchasingToDo.getCreationDate();
        List<PurchaseItem> items = Setup.getRepository(PurchaseItemRepository.class).findByPurchasingToDo(purchasingToDo);
        this.itemsNumber = items.size();
    }

    private long id;
    private String category;
    private String cycle;
    private Date date;
    private int itemsNumber;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCycle() {
        return cycle;
    }

    public void setCycle(String cycle) {
        this.cycle = cycle;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public int getItemsNumber() {
        return itemsNumber;
    }

    public void setItemsNumber(int itemsNumber) {
        this.itemsNumber = itemsNumber;
    }
}
