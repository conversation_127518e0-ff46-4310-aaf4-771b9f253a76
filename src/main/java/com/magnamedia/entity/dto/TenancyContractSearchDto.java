package com.magnamedia.entity.dto;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.entity.ExpenseRelatedTo;
import com.magnamedia.module.type.ExpenseBeneficiaryType;
import com.magnamedia.module.type.ExpensePaymentMethod;
import com.magnamedia.workflow.type.ExpenseRequestStatus;

import java.util.Date;
import java.util.List;

public class TenancyContractSearchDto {

    private String startDateOperator;
    private Date startDate1;
    private Date startDate2;

    private String expiryDateOperator;
    private Date expiryDate1;
    private Date expiryDate2;

    private String firstPartyNameOperation;
    private String firstPartyName;

    private String secondPartyNameOperation;
    private String secondPartyName;

    private String typeOfDocumentOperation;
    private PicklistItem typeOfDocument;

    private Boolean active;

    public String getStartDateOperator() {
        return startDateOperator;
    }

    public void setStartDateOperator(String startDateOperator) {
        this.startDateOperator = startDateOperator;
    }

    public Date getStartDate1() {
        return startDate1;
    }

    public void setStartDate1(Date startDate1) {
        this.startDate1 = startDate1;
    }

    public Date getStartDate2() {
        return startDate2;
    }

    public void setStartDate2(Date startDate2) {
        this.startDate2 = startDate2;
    }

    public String getExpiryDateOperator() {
        return expiryDateOperator;
    }

    public void setExpiryDateOperator(String expiryDateOperator) {
        this.expiryDateOperator = expiryDateOperator;
    }

    public Date getExpiryDate1() {
        return expiryDate1;
    }

    public void setExpiryDate1(Date expiryDate1) {
        this.expiryDate1 = expiryDate1;
    }

    public Date getExpiryDate2() {
        return expiryDate2;
    }

    public void setExpiryDate2(Date expiryDate2) {
        this.expiryDate2 = expiryDate2;
    }

    public String getSecondPartyNameOperation() {
        return secondPartyNameOperation;
    }

    public void setSecondPartyNameOperation(String secondPartyNameOperation) {
        this.secondPartyNameOperation = secondPartyNameOperation;
    }

    public String getFirstPartyNameOperation() {
        return firstPartyNameOperation;
    }

    public void setFirstPartyNameOperation(String firstPartyNameOperation) {
        this.firstPartyNameOperation = firstPartyNameOperation;
    }

    public String getFirstPartyName() {
        return firstPartyName;
    }

    public void setFirstPartyName(String firstPartyName) {
        this.firstPartyName = firstPartyName;
    }

    public String getSecondPartyName() {
        return secondPartyName;
    }

    public void setSecondPartyName(String secondPartyName) {
        this.secondPartyName = secondPartyName;
    }

    public String getTypeOfDocumentOperation() {
        return typeOfDocumentOperation;
    }

    public void setTypeOfDocumentOperation(String typeOfDocumentOperation) {
        this.typeOfDocumentOperation = typeOfDocumentOperation;
    }

    public PicklistItem getTypeOfDocument() {
        return typeOfDocument;
    }

    public void setTypeOfDocument(PicklistItem typeOfDocument) {
        this.typeOfDocument = typeOfDocument;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }
}
