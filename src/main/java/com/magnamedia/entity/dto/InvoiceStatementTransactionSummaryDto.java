package com.magnamedia.entity.dto;

import com.magnamedia.entity.InvoiceStatementTransaction;
import com.magnamedia.extra.InvoiceStatementTransactionType;
import org.springframework.data.domain.Page;

import java.util.List;


public class InvoiceStatementTransactionSummaryDto {
    
    private Page<InvoiceStatementTransaction> matchedTransactions;
    private Page<InvoiceStatementTransaction> mismatchedTransactions;
    private Page<InvoiceStatementTransaction> pendingMatchedTransactions;
    private Double balance;
    
    public InvoiceStatementTransactionSummaryDto() {
    }
    
    public InvoiceStatementTransactionSummaryDto(
            Page<InvoiceStatementTransaction> matchedTransactions,
            Page<InvoiceStatementTransaction> mismatchedTransactions,
            Page<InvoiceStatementTransaction> pendingMatchedTransactions,
            Double balance) {
        this.matchedTransactions = matchedTransactions;
        this.mismatchedTransactions = mismatchedTransactions;
        this.pendingMatchedTransactions = pendingMatchedTransactions;
        this.balance = balance;
    }
    
    public Page<InvoiceStatementTransaction> getMatchedTransactions() {
        return matchedTransactions;
    }
    
    public void setMatchedTransactions(Page<InvoiceStatementTransaction> matchedTransactions) {
        this.matchedTransactions = matchedTransactions;
    }
    
    public Page<InvoiceStatementTransaction> getMismatchedTransactions() {
        return mismatchedTransactions;
    }
    
    public void setMismatchedTransactions(Page<InvoiceStatementTransaction> mismatchedTransactions) {
        this.mismatchedTransactions = mismatchedTransactions;
    }
    
    public Page<InvoiceStatementTransaction> getPendingMatchedTransactions() {
        return pendingMatchedTransactions;
    }
    
    public void setPendingMatchedTransactions(Page<InvoiceStatementTransaction> pendingMatchedTransactions) {
        this.pendingMatchedTransactions = pendingMatchedTransactions;
    }
    
    public Double getBalance() {
        return balance;
    }
    
    public void setBalance(Double balance) {
        this.balance = balance;
    }
}
