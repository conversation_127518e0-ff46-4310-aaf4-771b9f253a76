package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.workflow.FlowSubEventConfig;

import javax.persistence.*;

@Entity
public class RecurringCreditCardPaymentsIssue extends BaseEntity {

    @OneToOne( fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem errorCode;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private FlowSubEventConfig subEvent;

    public PicklistItem getErrorCode() { return errorCode; }

    public void setErrorCode(PicklistItem errorCode) { this.errorCode = errorCode; }

    public FlowSubEventConfig getSubEvent() {
        return subEvent;
    }

    public void setSubEvent(FlowSubEventConfig subEvent) {
        this.subEvent = subEvent;
    }
}
