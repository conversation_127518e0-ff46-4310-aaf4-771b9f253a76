package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.entity.ExpenseRelatedTo;
import com.magnamedia.entity.ExpenseRelatedToTeam;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 1/29/2021
 */
public class ExpenseRelatedToSerializer extends JsonSerializer<List<ExpenseRelatedTo>> {
    @Override
    public void serialize(List<ExpenseRelatedTo> expenseRelatedTos, JsonGenerator gen, SerializerProvider serializerProvider) throws IOException, JsonProcessingException {
        if (expenseRelatedTos == null || expenseRelatedTos.isEmpty()) {
            gen.writeStartArray();
            gen.writeEndArray();
            return;
        }
        gen.writeStartArray();
        for (ExpenseRelatedTo relatedTo : expenseRelatedTos) {

            if (relatedTo.getRelatedToType() == ExpenseRelatedTo.ExpenseRelatedToType.APPLICANT
                    || relatedTo.getRelatedToType() == ExpenseRelatedTo.ExpenseRelatedToType.OFFICE_STAFF) {
                gen.writeStartObject();
                //relatedToType enum
                gen.writeObjectFieldStart("relatedToType");
                gen.writeStringField("label",
                        relatedTo.getRelatedToType().name());
                gen.writeStringField("value",
                        relatedTo.getRelatedToType().toString());
                gen.writeEndObject();

                //relatedExpense
                if (relatedTo.getRelatedExpense() != null) {
                    gen.writeObjectFieldStart("relatedExpense");
                    gen.writeNumberField("id",
                            relatedTo.getRelatedExpense().getId());
                    gen.writeStringField("label",
                            relatedTo.getRelatedExpense().getLabel());
                    gen.writeEndObject();
                } else {
                    gen.writeNullField("relatedExpense");
                }

                gen.writeEndObject();
            } else if (relatedTo.getRelatedToType() == ExpenseRelatedTo.ExpenseRelatedToType.MAID) {
                gen.writeStartObject();
                //relatedToType enum
                gen.writeObjectFieldStart("relatedToType");
                gen.writeStringField("label",
                        relatedTo.getRelatedToType().name());
                gen.writeStringField("value",
                        relatedTo.getRelatedToType().toString());
                gen.writeEndObject();

                //maid expense cc

                if (relatedTo.getExpenseCC() != null) {
                    gen.writeObjectFieldStart("expenseCC");
                    gen.writeNumberField("id",
                            relatedTo.getExpenseCC().getId());
                    gen.writeStringField("label",
                            relatedTo.getExpenseCC().getLabel());

                    gen.writeEndObject();
                }
                //maid expense visa

                if (relatedTo.getExpenseVisa() != null) {
                    gen.writeObjectFieldStart("expenseVisa");
                    gen.writeNumberField("id",
                            relatedTo.getExpenseVisa().getId());
                    gen.writeStringField("label",
                            relatedTo.getExpenseVisa().getLabel());
                    gen.writeEndObject();
                }

                gen.writeEndObject();
            } else if (relatedTo.getRelatedToType() == ExpenseRelatedTo.ExpenseRelatedToType.TEAM) {
                gen.writeStartObject();

                //relatedToType enum
                gen.writeObjectFieldStart("relatedToType");
                gen.writeStringField("label",
                        relatedTo.getRelatedToType().name());
                gen.writeStringField("value",
                        relatedTo.getRelatedToType().toString());
                gen.writeEndObject();

                gen.writeArrayFieldStart("teams");
                if (!relatedTo.getTeams().isEmpty()) {
                    for (ExpenseRelatedToTeam toTeam : relatedTo.getTeams()) {
                        gen.writeStartObject();

                        //team object
                        if (toTeam.getTeam() == null || toTeam.getRelatedExpense() == null)
                            throw new RuntimeException("relatedToTeam with id " + toTeam.getId() + " has team or related Expense = null");
                        gen.writeObjectFieldStart("team");
                        gen.writeNumberField("id", toTeam.getTeam().getId());
                        gen.writeStringField("label", toTeam.getTeam().getLabel());
                        gen.writeEndObject();

                        //expense object
                        gen.writeObjectFieldStart("relatedExpense");
                        gen.writeNumberField("id", toTeam.getRelatedExpense().getId());
                        gen.writeStringField("label", toTeam.getRelatedExpense().getLabel());
                        gen.writeEndObject();

                        gen.writeEndObject();
                    }
                }

                gen.writeEndArray();

                gen.writeEndObject();
            }
        }
        gen.writeEndArray();
    }
}
