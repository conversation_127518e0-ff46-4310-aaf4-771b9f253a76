package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.entity.Payment;
import java.io.IOException;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Apr 18, 2019
 * ACC-599
 */
public class PaymentJsonSerializer extends JsonSerializer<Payment>  {

    @Override
    public void serialize(
            Payment value,
            JsonGenerator gen,
            SerializerProvider serializers)
            throws IOException, JsonProcessingException {
        if (value == null) {
            gen.writeNull();
            return;
        }
        gen.writeStartObject();
        gen.writeNumberField("id", value.getId());
        gen.writeNumberField("amountOfPayment", value.getAmountOfPayment());
        gen.writeStringField("status", value.getStatus().getLabel());
        gen.writeStringField("chequeNumber", value.getChequeNumber());
        gen.writeStringField("chequeName", value.getChequeName());
        if (value.getContract() != null) {
            gen.writeFieldName("contract");
            gen.writeStartObject();
            gen.writeNumberField("id", value.getContract().getId());
            if (value.getContract().getClient() != null) {
                gen.writeFieldName("client");
                gen.writeStartObject();
                gen.writeNumberField("id", value.getContract().getClient().getId());
                gen.writeStringField("name", value.getContract().getClient().getName());
                gen.writeEndObject();
            } else {
                gen.writeObjectField("client", null);
            }
            gen.writeEndObject();
        } else {
            gen.writeObjectField("contract", null);
        }
        gen.writeEndObject();
    }
}
