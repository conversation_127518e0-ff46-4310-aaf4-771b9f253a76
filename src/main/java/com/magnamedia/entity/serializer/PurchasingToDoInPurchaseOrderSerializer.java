package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.core.entity.Template;
import com.magnamedia.entity.PurchasingToDo;

import java.io.IOException;
import java.time.LocalDate;

/**
 * <PERSON> (Feb 15, 2021)
 */
public class PurchasingToDoInPurchaseOrderSerializer extends JsonSerializer<PurchasingToDo> {
    @Override
    public void serialize(PurchasingToDo value,
                          JsonGenerator gen,
                          SerializerProvider serializers)
            throws IOException, JsonProcessingException {
        if (value == null) {
            gen.writeNull();
            return;
        }
        gen.writeStartObject();
        gen.writeNumberField("id", value.getId());
        gen.writeStringField("category", value.getCategory() != null ? value.getCategory().getName() : null);
        gen.writeStringField("orderCycle", value.getCategory() != null ? value.getCategory().getOrderCycle().getName() : null);
        gen.writeStringField("creationDate", value.getCreationDate() != null ? value.getCreationDate().toString() : null);
        gen.writeEndObject();
    }

}
