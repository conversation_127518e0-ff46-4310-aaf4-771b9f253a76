package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.entity.maidArrival.MaidArrivalToDo;

import java.io.IOException;

/**
 *
 * <AUTHOR> Hachem
 */

public class MaidArrivalSerializer extends JsonSerializer<MaidArrivalToDo> {

    @Override
    public void serialize(MaidArrivalToDo value, JsonGenerator gen, SerializerProvider sp) throws IOException, JsonProcessingException {
        
        if (value == null) {
            gen.writeNull();
            return;
        }
        gen.writeStartObject();
        gen.writeNumberField("id",
                value.getId());
        gen.writeStringField("label",
                value.getLabel());
        gen.writeString<PERSON>ield("taskName", value.getTaskName());
        
        gen.writeEndObject();
        
    }
    
}
