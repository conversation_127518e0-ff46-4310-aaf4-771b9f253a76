package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.Payment;

import java.io.IOException;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on May 5, 2019
 */
public class ContractJsonSerializer extends JsonSerializer<Contract> {

    @Override
    public void serialize(
            Contract value,
            JsonGenerator gen,
            SerializerProvider serializers)
            throws IOException, JsonProcessingException {
        if (value == null) {
            gen.writeNull();
            return;
        }

        gen.writeStartObject();
        gen.writeNumberField("id", value.getId());
        //Jirra ACC-1435
        gen.writeStringField("label", value.getLabel());
        if (value.getClient() != null) {
            gen.writeFieldName("client");
            gen.writeStartObject();
            gen.writeNumberField("id", value.getClient().getId());
            gen.writeStringField("name", value.getClient().getName());
            gen.writeStringField("mobileNumber", value.getClient().getMobileNumber());
            gen.writeEndObject();
        } else {
            gen.writeObjectField("client", null);
        }
        gen.writeEndObject();

    }
}
