package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import java.io.IOException;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Feb 13, 2020
 * ACC-1333
 */
public class ClientRefundToDoSerializer extends JsonSerializer<ClientRefundToDo> {
    
    @Override
    public void serialize(ClientRefundToDo value,
            JsonGenerator gen,
            SerializerProvider serializers)
            throws IOException, JsonProcessingException {
        if (value == null) {
            gen.writeNull();
            return;
        }
        gen.writeStartObject();
        gen.writeNumberField("id",
                value.getId());
        gen.writeNumberField("amount",
                value.getAmount());
        gen.writeEndObject();
    }

}
