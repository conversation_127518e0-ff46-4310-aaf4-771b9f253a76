package com.magnamedia.entity.serializer;


import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.entity.BasePLNode;
import com.magnamedia.entity.BasePLVariableBucket;
import com.magnamedia.entity.PLVariableBucket;
import com.magnamedia.entity.interfaces.BasePLVariableNode;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Swp 09, 2020
 *         Jirra ACC-2407
 */

public class BasePNLNodeSerializer extends JsonSerializer<List<BasePLNode>> {

    @Override
    public void serialize(List<BasePLNode> values, JsonGenerator gen, SerializerProvider serializers)
            throws IOException {
        gen.writeStartArray();

        if (values != null) {
            for (BasePLNode value : values) {
                writeNode(value, gen);
            }
        }
        gen.writeEndArray();
    }

    private void writeNode(BasePLNode value, JsonGenerator gen) throws IOException {
        gen.writeStartObject();
        gen.writeNumberField("id",
                value.getId());
        gen.writeStringField("label",
                value.getLabel());
        gen.writeStringField("name",
                value.getName());
        gen.writeStringField("type",
                value.getType());
        gen.writeNumberField("nodeOrder",
                value.getNodeOrder());
        gen.writeObjectField("pLNodeType",
                value.getpLNodeType());
        gen.writeStringField("color",
                value.getColor());

        /*write company*/
        if (value.getPLCompany() != null) {
            gen.writeObjectFieldStart("pLCompany");
            gen.writeNumberField("id",
                    value.getPLCompany().getId());
            gen.writeStringField("label",
                    value.getPLCompany().getLabel());
            gen.writeEndObject();
        }

        /*write company*/
        if (value.getParent() != null) {
            gen.writeObjectFieldStart("parent");
            gen.writeStringField("type", value.getParent().getType());
            gen.writeNumberField("id", value.getParent().getId());
            gen.writeStringField("label", value.getParent().getLabel());
            gen.writeEndObject();
        }

        /*write pLVariableBuckets*/
        if (value instanceof BasePLVariableNode) {
            if ((List<PLVariableBucket>) ((BasePLVariableNode) value).getpLVariableBuckets() != null) {
                gen.writeArrayFieldStart("pLVariableBuckets");
                for (PLVariableBucket plVariableBucket : (List<PLVariableBucket>) ((BasePLVariableNode) value).getpLVariableBuckets()) {
                    writePLVariableBucket(plVariableBucket, gen);
                }
                gen.writeEndArray();
            }
        }

        /*write children*/
        if (value.getSortedChildren() != null) {
            gen.writeArrayFieldStart("sortedChildren");
            for (BasePLNode child : (List<BasePLNode>) value.getSortedChildren()) {
                writeNode(child, gen);
            }
            gen.writeEndArray();
        }

        gen.writeEndObject();
    }

    private void writePLVariableBucket(BasePLVariableBucket value, JsonGenerator gen) throws IOException {
        gen.writeStartObject();
        gen.writeNumberField("id",
                value.getId());
        gen.writeStringField("label",
                value.getLabel());
        gen.writeNumberField("wieght",
                value.getWieght());

        /*write pLVariable*/
        if (value.getpLVariable() != null) {
            gen.writeObjectFieldStart("pLVariable");
            gen.writeNumberField("id", value.getpLVariable().getId());
            gen.writeStringField("label", ((BasePLNode) value.getpLVariable()).getLabel());
            gen.writeStringField("type", ((BasePLNode) value.getpLVariable()).getType());
            gen.writeEndObject();
        }

        /*write Revenue*/
        if (value.getRevenue() != null) {
            gen.writeObjectFieldStart("revenue");
            gen.writeNumberField("id", value.getRevenue().getId());
            gen.writeStringField("label", value.getRevenue().getLabel());
            gen.writeEndObject();
        }

        /*write Expense*/
        if (value.getExpense() != null) {
            gen.writeObjectFieldStart("expense");
            gen.writeNumberField("id", value.getExpense().getId());
            gen.writeStringField("label", value.getExpense().getLabel());
            gen.writeEndObject();
        }

        gen.writeEndObject();
    }
}
