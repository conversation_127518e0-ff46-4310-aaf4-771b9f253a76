package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.entity.Bucket;
import java.io.IOException;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Feb 13, 2020
 * ACC-1333
 */
public class IdNameSerializer extends JsonSerializer<Bucket> {
    
    @Override
    public void serialize(Bucket value,
            JsonGenerator gen,
            SerializerProvider serializers)
            throws IOException, JsonProcessingException {
        if (value == null) {
            gen.writeNull();
            return;
        }
        gen.writeStartObject();
        gen.writeNumberField("id",
                value.getId());
        gen.writeString<PERSON>ield("name",
                value.getName());
        gen.writeStringField("label",
                value.getName());
        gen.writeStringField("code",
                value.getCode());
        gen.writeEndObject();
    }

}
