package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.jsontype.TypeSerializer;
import com.magnamedia.entity.BasePLNode;
import java.io.IOException;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Oct 15, 2018
 */
public class TypeIdLabelSerializer extends JsonSerializer<BasePLNode> {

	@Override
	public void serialize(BasePLNode value, JsonGenerator gen, SerializerProvider serializers)
		throws IOException, JsonProcessingException {
		if (value == null) {
			gen.writeNull();
			return;
		}
		gen.writeStartObject();
		gen.writeNumberField("id",
							 value.getId());
		gen.writeStringField("label",
							 value.getLabel());
		gen.writeEndObject();
	}
        
	@Override
	public void serializeWithType(BasePLNode value, JsonGenerator gen,
                SerializerProvider serializers, TypeSerializer typeSer)
		throws IOException, JsonProcessingException {
		if (value == null) {
			gen.writeNull();
			return;
		}
		gen.writeStartObject();
		gen.writeStringField("type", value.getType());
		gen.writeNumberField("id", value.getId());
		gen.writeStringField("label",  value.getLabel());
		gen.writeEndObject();
	}

}
