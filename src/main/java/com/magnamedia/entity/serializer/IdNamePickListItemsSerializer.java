package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.core.entity.PicklistItem;

import java.io.IOException;
import java.util.List;

public class IdNamePickListItemsSerializer extends JsonSerializer<List<PicklistItem>> {
    
    @Override
    public void serialize(List<PicklistItem> values,
            JsonGenerator gen,
            SerializerProvider serializers)
            throws IOException {

        if (values == null || values.isEmpty()) {
            gen.writeNull();
            return;
        }
        gen.writeStartArray();
        for (PicklistItem value : values){
            gen.writeStartObject();
            gen.writeNumberField("id",
                    value.getId());
            gen.writeStringField("name",
                    value.getName());
            gen.writeString<PERSON>ield("label",
                    value.getName());
            gen.writeStringField("code",
                    value.getCode());
            gen.writeEndObject();
        }
        gen.writeEndArray();
    }
}
