package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.core.entity.BaseEntity;
import java.io.IOException;

/**
 * <AUTHOR> on 2017-07-20
 *
 */
public class CustomIdLabelSerializer extends JsonSerializer<BaseEntity> {

    /* (non-Javadoc)
	 * @see com.fasterxml.jackson.databind.JsonSerializer#serialize(java.lang.Object, com.fasterxml.jackson.core.JsonGenerator, com.fasterxml.jackson.databind.SerializerProvider)
     */
    @Override
    public void serialize(BaseEntity value,
            JsonGenerator gen,
            SerializerProvider serializers)
            throws IOException, JsonProcessingException {
        if (value == null) {
            gen.writeNull();
            return;
        }
        gen.writeStartObject();
        gen.writeNumberField("id",
                value.getId());
        gen.writeStringField("label",
                value.getLabel());
        if (value.getCreationDate() != null) {
            gen.writeStringField("creationDate",
                    value.getCreationDate()
                            .toString());
        }
        gen.writeEndObject();
    }

}
