package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.entity.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Apr 28, 2020
 *         Jirra ACC-1810
 */
public class DirectDebitJsonSerializer extends JsonSerializer<DirectDebit> {

    @Override
    public void serialize(
            DirectDebit value,
            JsonGenerator gen,
            SerializerProvider serializers)
            throws IOException, JsonProcessingException {
        if (value == null) {
            gen.writeNull();
            return;
        }

        gen.writeStartObject();
        gen.writeNumberField("id", value.getId());
        if (value.getCategory() != null) {
            gen.writeStringField("category", value.getCategory().toString());
        } else {
            gen.writeObjectField("category", null);
        }
        if (value.getDirectDebitRejectionToDo() != null) {
            gen.writeFieldName("directDebitRejectionToDo");
            gen.writeStartObject();
            gen.writeNumberField("id", value.getDirectDebitRejectionToDo().getId());
            gen.writeEndObject();
        } else {
            gen.writeObjectField("directDebitRejectionToDo", null);
        }
        if (value.getDirectDebitBouncingRejectionToDo() != null) {
            gen.writeFieldName("directDebitBouncingRejectionToDo");
            gen.writeStartObject();
            gen.writeNumberField("id", value.getDirectDebitBouncingRejectionToDo().getId());
            gen.writeEndObject();
        } else {
            gen.writeObjectField("directDebitBouncingRejectionToDo", null);
        }

        //SAL-2034
        if (value.getContractPaymentTerm() != null) {
            gen.writeFieldName("contractPaymentTerm");
            gen.writeStartObject();
            gen.writeNumberField("id", value.getContractPaymentTerm().getId());
            if (value.getContractPaymentTerm().getContract() != null) {
                gen.writeFieldName("contract");
                gen.writeStartObject();
                gen.writeNumberField("id", value.getContractPaymentTerm().getContract().getId());
                if (value.getContractPaymentTerm().getContract().getClient() != null) {
                    gen.writeFieldName("client");
                    gen.writeStartObject();
                    gen.writeNumberField("id", value.getContractPaymentTerm().getContract().getClient().getId());
                    gen.writeStringField("name", value.getContractPaymentTerm().getContract().getClient().getName());
                    if (value.getContractPaymentTerm().getContract().getClient().getNationality() != null) {
                        gen.writeFieldName("nationality");
                        gen.writeStartObject();
                        gen.writeNumberField("id", value.getContractPaymentTerm().getContract().getClient().getNationality().getId());
                        gen.writeStringField("name", value.getContractPaymentTerm().getContract().getClient().getNationality().getName());
                        gen.writeEndObject();
                    } else {
                        gen.writeObjectField("nationality", null);
                    }
                    if (value.getContractPaymentTerm().getContract().getClient().getTitle() != null) {
                        gen.writeFieldName("title");
                        gen.writeStartObject();
                        gen.writeNumberField("id", value.getContractPaymentTerm().getContract().getClient().getTitle().getId());
                        gen.writeStringField("name", value.getContractPaymentTerm().getContract().getClient().getTitle().getName());
                        gen.writeEndObject();
                    } else {
                        gen.writeObjectField("title", null);
                    }
                    gen.writeEndObject();
                } else {
                    gen.writeObjectField("client", null);
                }
                gen.writeEndObject();
            } else {
                gen.writeObjectField("contract", null);
            }
            gen.writeEndObject();
        } else {
            gen.writeObjectField("contractPaymentTerm", null);
        }

        gen.writeEndObject();

    }
}
