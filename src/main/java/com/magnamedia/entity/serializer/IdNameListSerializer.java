package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.entity.Bucket;

import java.io.IOException;
import java.util.List;

public class IdNameListSerializer extends JsonSerializer<List<Bucket>> {
    
    @Override
    public void serialize(List<Bucket> values,
            JsonGenerator gen,
            SerializerProvider serializers)
            throws IOException {

        if (values == null || values.isEmpty()) {
            gen.writeNull();
            return;
        }
        gen.writeStartArray();
        for (Bucket value : values){
            gen.writeStartObject();
            gen.writeNumberField("id",
                    value.getId());
            gen.writeStringField("name",
                    value.getName());
            gen.writeStringField("label",
                    value.getName());
            gen.writeString<PERSON>ield("code",
                    value.getCode());
            gen.writeEndObject();
        }
        gen.writeEndArray();
    }
}
