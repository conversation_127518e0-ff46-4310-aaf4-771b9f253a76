package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.helper.DateUtil;

import java.io.IOException;

/**
 * Created by Mamon.Masod on 2/10/2021.
 */

public class ExpensePaymentSerializer extends JsonSerializer<ExpensePayment> {
    @Override
    public void serialize(
            ExpensePayment value,
            JsonGenerator gen,
            SerializerProvider serializers)
            throws IOException {
        if (value == null) {
            gen.writeNull();
            return;
        }

        gen.writeStartObject();

        gen.writeNumberField("id", value.getId());
        gen.writeStringField("label", value.getLabel());
        gen.writeStringField("expense", value.getExpenseToPost() != null ? value.getExpenseToPost().getCaption() : "");

        gen.writeObjectField("amount", value.getAmount());
        gen.writeObjectField("vatAmount", value.getVatAmount() != null ? value.getVatAmount() : 0D);

        gen.writeObjectField("date", value.getPaymentDate() != null ? DateUtil.formatDateDashed(value.getPaymentDate()) : null);
        gen.writeObjectField("dateTime", value.getPaymentDate());

        gen.writeStringField("requester", value.getRequester() != null ? value.getRequester().getFullName() : "");
        gen.writeStringField("description", value.getDescription());
        gen.writeStringField("notes", value.getNotes());
        gen.writeStringField("invoiceNumber", value.getInvoiceNumber());
        gen.writeStringField("beneficiary", value.getBeneficiaryName());

        gen.writeObjectField("relatedTo", value.getRelatedToType() != null ? value.getRelatedToType() : null);

        gen.writeArrayFieldStart("attachments");
        if (value.getAttachments() != null) {
            for (Attachment attachment : value.getAttachments()) {
                gen.writeObject(attachment);
            }
        }
        gen.writeEndArray();

        gen.writeEndObject();
    }
}
