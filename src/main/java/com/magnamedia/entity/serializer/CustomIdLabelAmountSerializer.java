package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.entity.PayrollManagerNote;
import java.io.IOException;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Jul 24, 2019
 * Jirra ACC-737
 */
public class CustomIdLabelAmountSerializer extends JsonSerializer<PayrollManagerNote> {

    @Override
	public void serialize(PayrollManagerNote value, JsonGenerator gen, SerializerProvider serializers)
		throws IOException, JsonProcessingException {
		if (value == null) {
			gen.writeNull();
			return;
		}
		gen.writeStartObject();
		gen.writeNumberField("id", value.getId());
		gen.writeStringField("label", value.getLabel());
                gen.writeObjectField("amount", value.getAmount());
		gen.writeEndObject();
	}

}
