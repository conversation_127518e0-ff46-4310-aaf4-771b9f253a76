package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.entity.Replacement;
import java.io.IOException;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Jul 24, 2019
 * Jirra ACC-737
 */
public class CustomIdReplacementSerializer extends JsonSerializer<Replacement> {

	/* (non-Javadoc)
	 * @see com.fasterxml.jackson.databind.JsonSerializer#serialize(java.lang.Object, com.fasterxml.jackson.core.JsonGenerator, com.fasterxml.jackson.databind.SerializerProvider)
	 */
	@Override
	public void serialize(Replacement value, JsonGenerator gen, SerializerProvider serializers)
		throws IOException, JsonProcessingException {
		if (value == null) {
			gen.writeNull();
			return;
		}
		gen.writeStartObject();
		gen.writeNumberField("id", value.getId());
		gen.writeStringField("label", value.getLabel());
                gen.writeObjectField("reasonOfReplacement", value.getReasonOfReplacement());
                gen.writeObjectField("complaintTypeCode", 
                        (value.getComplaint() == null || value.getComplaint().getPrimaryType() == null ? "" : value.getComplaint().getPrimaryType().getCode()));
		gen.writeEndObject();
	}

}

