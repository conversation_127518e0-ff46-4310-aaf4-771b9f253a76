package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.entity.serializer.CustomIdLabelCodeSerializer;

import javax.persistence.*;

@Entity
public class ContractInfo extends BaseEntity {

    @OneToOne(fetch = FetchType.LAZY)
    private Contract contract;


    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    @JsonSerialize(using = CustomIdLabelCodeSerializer.class)
    PicklistItem georgePaymentPlan;

    @Column(columnDefinition = "TEXT")
    String georgePaymentNotes;

    @Column
    Double GeorgeDiscountAmount;

    @Column(columnDefinition = "TEXT")
    String georgeDiscountNote;

    public Contract getContract() {
        return contract;
    }

    public void setContract(Contract contract) {
        this.contract = contract;
    }

    public PicklistItem getGeorgePaymentPlan() {
        return georgePaymentPlan;
    }

    public void setGeorgePaymentPlan(PicklistItem georgePaymentPlan) {
        this.georgePaymentPlan = georgePaymentPlan;
    }

    public String getGeorgePaymentNotes() {
        return georgePaymentNotes;
    }

    public void setGeorgePaymentNotes(String georgePaymentNotes) {
        this.georgePaymentNotes = georgePaymentNotes;
    }

    public Double getGeorgeDiscountAmount() {
        return GeorgeDiscountAmount;
    }

    public void setGeorgeDiscountAmount(Double georgeDiscountAmount) {
        GeorgeDiscountAmount = georgeDiscountAmount;
    }

    public String getGeorgeDiscountNote() {
        return georgeDiscountNote;
    }

    public void setGeorgeDiscountNote(String georgeDiscountNote) {
        this.georgeDiscountNote = georgeDiscountNote;
    }
}
