package com.magnamedia.entity;

import org.hibernate.annotations.ColumnDefault;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.util.Date;

/**
 * <PERSON> (Feb 09, 2021)
 */
@Embeddable
public class SupplierPaymentDetail {
    private Date changeDate;
    //store multiPaymentMethods as a string join by ','
    private String paymentMethods;
    private String iban;
    private String accountName;
    private String accountNumber;
    private String mobileNumber;
    private String swift;
    private String address;
    @Column
    @ColumnDefault("0")
    private Boolean hasNoIban = false;

    public Date getChangeDate() {
        return changeDate;
    }

    public void setChangeDate(Date changeDate) {
        this.changeDate = changeDate;
    }

    public String getPaymentMethods() { return paymentMethods; }

    public void setPaymentMethods(String paymentMethods) {
        this.paymentMethods = paymentMethods;
    }

    public String getIban() {
        return iban;
    }

    public void setIban(String iban) {
        this.iban = iban;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getSwift() {
        return swift;
    }

    public void setSwift(String swift) {
        this.swift = swift;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
    
    public Boolean getHasNoIban() {
        return hasNoIban;
    }

    public void setHasNoIban(Boolean hasNoIban) {
        this.hasNoIban = hasNoIban;
    }

    public boolean equals(SupplierPaymentDetail detail) {
        if (!compare(this.paymentMethods, detail.paymentMethods)) return false;
        if (!compare(this.accountName, detail.accountName)) return false;
        if (!compare(this.mobileNumber, detail.mobileNumber)) return false;
        if (!compare(this.iban, detail.iban)) return false;
        if (!compare(this.address, detail.address)) return false;
        if (!compare(this.swift, detail.swift)) return false;
        if (!compare(this.accountNumber, detail.accountNumber)) return false;

        return true;
    }

    public static boolean compare(String str1, String str2) {
        return (str1 == null ? str2 == null : str1.equals(str2));
    }
}
