package com.magnamedia.entity;

import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.extra.InsuranceDetailsType;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR> Kadoora
 * Created on 2024-07-20
 */
@Entity
public class InsuranceDetails extends BaseEntity {

    @Column
    @Enumerated(EnumType.STRING)
    private InsuranceDetailsType type;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn
    private NewRequest newRequest;

    @Temporal(TemporalType.DATE)
    private Date eventDate = new Date();

    public InsuranceDetails() {}

    public InsuranceDetailsType getType() { return type; }

    public void setType(InsuranceDetailsType type) { this.type = type; }

    public NewRequest getNewRequest() { return newRequest; }

    public void setNewRequest(NewRequest newRequest) { this.newRequest = newRequest; }

    public void setEventDate(Date eventDate) {this.eventDate = eventDate;}

    public Date getEventDate() {return eventDate != null ? eventDate : getCreationDate();}
}