package com.magnamedia.entity.RiskDocumentsManagement;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.IdLabelListSerializer;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.AuditOverride;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Getter
@Setter
@AuditOverride(isAudited = false)
@Embeddable
public class RiskDocumentsManagementHistory extends BaseEntity {

    @Column(nullable = false)
    private String name;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem type;

    @ElementCollection(fetch = FetchType.LAZY)
    @Enumerated(EnumType.STRING)
    private List<RiskDocumentsManagement.DocumentRelatedTo> relatesTo = new ArrayList<>();

    @JsonSerialize(using = IdLabelListSerializer.class)
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "RISK_DOCUMENTS_MANAGEMENT_HISTORY_ACCOUNTABLE_PERSON",
            joinColumns = @JoinColumn(
                    name = "DOCUMENT_ID",
                    referencedColumnName = "ID"),
            inverseJoinColumns = @JoinColumn(
                    name = "ROLE_ID",
                    referencedColumnName = "ID")
    )
    private List<RiskDocumentsManagementRole> accountablePerson = new ArrayList<>();

    @Column
    private Date issuanceDate;

    @Column
    private Date expiryDate;

    @Column
    private java.sql.Date registrationDate;

    @Column
    private java.sql.Date insuranceExpirationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    private RiskDocumentsManagement riskDocumentsManagement;
}