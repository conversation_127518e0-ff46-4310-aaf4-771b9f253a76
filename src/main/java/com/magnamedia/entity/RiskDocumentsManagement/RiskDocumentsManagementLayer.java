package com.magnamedia.entity.RiskDocumentsManagement;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.entity.serializer.IdLabelListSerializer;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.sql.Date;
import java.util.List;

@Entity
@Getter
@Setter
public class RiskDocumentsManagementLayer extends BaseEntity {

    public enum Type {
        LAYER_ONE,
        LAYER_TWO,
    }

    @Column
    private int daysBeforeExpiry;

    @Column
    private Date creationRenewalToDoDate;

    @Column
    private Date sendLayerTwoEmailDate;

    @Enumerated(EnumType.STRING)
    private Type type;

    @JsonSerialize(using = IdLabelListSerializer.class)
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "RISK_DOCUMENTS_MANAGEMENT_LAYER_ROLE_ASSIGNEE",
            joinColumns = @JoinColumn(
                    name = "LAYER_ID",
                    referencedColumnName = "ID"),
            inverseJoinColumns = @JoinColumn(
                    name = "ROLE_ID",
                    referencedColumnName = "ID"))
    private List<RiskDocumentsManagementRole> roleAssignee;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    private RiskDocumentsManagement riskDocumentsManagement;
}