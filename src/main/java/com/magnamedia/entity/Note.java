package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.magnamedia.core.entity.BaseEntity;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Oct 11, 2017
 */
@MappedSuperclass
public class Note<T extends VisaRequest> extends BaseEntity {

	@ManyToOne
	@JoinColumn
	@JsonIgnore
	private T request;

	@Column(name = "TEXT",
			length = 1024)
	private String text;

	public String getText() {
		return text;
	}

	public void setText(String text) {
		this.text = text;
	}

	public T getRequest() {
		return request;
	}

	public void setRequest(T request) {
		this.request = request;
	}

	public String getUser() {
		return super.getCreator()
			.getFullName();
	}

	public Date getCreateDate() {
		return getCreationDate();
	}

}
