package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.controller.DirectDebitController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.extra.Utils;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.BankDirectDebitActivationRecordRepository;
import com.magnamedia.repository.DirectDebitFileRepository;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.service.DirectDebitRejectionFlowService;
import com.magnamedia.service.DirectDebitSignatureService;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Formula;
import org.hibernate.annotations.Where;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import java.sql.Date;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Dec 9, 2018
 *         Jirra ACC-329 ACC-984
 */
@Entity
@Where(clause = "((DIRECT_DEBIT_FILE_ID IS NULL) OR exists(select ddf.id from DIRECTDEBITFILES ddf where ddf.id = DIRECT_DEBIT_FILE_ID))")
public class BankDirectDebitActivationRecord extends BaseEntity {

    @Column
    private Integer rowIndex;

    @Column
    private Date presentmentDate;

    @Column
    private String bank;

    @Column
    private String iban;

    @Column
    private String contract;

    @Column
    private String account;

    @Column
    private String ddaRefNo;

    @Column
    private String status;

    @Column
    private String rejectionReason;

    @Column
    private String rejectionReasonEdited;

    @Transient
    private String nextAction;

    @Column
    @Enumerated(EnumType.STRING)
    private DirectDebitRejectCategory rejectCategory;

    @Column
    private Double amount;

    @Column
    private Date startDate;

    @Column
    private Date expiryDate;

    @Column
    private String errorMessage;

    @Column
    private boolean confirmed = false;

    @Column
    @ColumnDefault("0")
    private boolean confirmedByRPA = false;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    private BankDirectDebitActivationFile bankDirectDebitActivationFile;

//    @NotAudited
//    @ManyToOne(fetch = FetchType.LAZY)
//    //@JsonSerialize(using = IdLabelSerializer.class)
//    private DirectDebit directDebit;

    @Transient
    @JsonSerialize(using = IdLabelSerializer.class)
    private DirectDebitFile directDebitFile;

    @NotAudited
    @Formula("(SELECT cnt.NAME " +
            "FROM DIRECTDEBITFILES ddf " +
            "INNER JOIN DIRECTDEBITS ddm on ddf.DIRECT_DEBIT_ID = ddm.id " +
            "INNER JOIN CONTRACTPAYMENTTERMS cpt on cpt.id = ddm.CONTRACT_PAYMENT_TERM_ID " +
            "INNER JOIN CONTRACTS con on con.id = cpt.CONTRACT_ID " +
            "INNER JOIN CLIENTS cnt on cnt.ID = con.CLIENT_ID " +
            "WHERE ddf.id = DIRECT_DEBIT_FILE_ID)")
    @Basic(fetch = FetchType.LAZY)
    @JsonIgnore
    private String clientName;

    @Column
    private Long directDebitFileId;

    @NotAudited
    @Formula("(SELECT cpt.CONTRACT_ID FROM DIRECTDEBITFILES ddf inner join DIRECTDEBITS ddm " +
            "on ddf.DIRECT_DEBIT_ID = ddm.id inner join CONTRACTPAYMENTTERMS cpt on cpt.id = ddm.CONTRACT_PAYMENT_TERM_ID " +
            "WHERE ddf.id = DIRECT_DEBIT_FILE_ID)")
    @Basic(fetch = FetchType.LAZY)
    @JsonIgnore
    private Long contractId;

    @NotAudited
    @Formula("(SELECT d.DD_METHOD FROM DIRECTDEBITFILES d WHERE d.id = DIRECT_DEBIT_FILE_ID)")
    @Basic(fetch = FetchType.LAZY)
    @JsonIgnore
    @Enumerated(EnumType.STRING)
    private DirectDebitMethod ddMethod;

    @NotAudited
    @Formula("(SELECT d.DD_STATUS FROM DIRECTDEBITFILES d WHERE d.id = DIRECT_DEBIT_FILE_ID)")
    @Basic(fetch = FetchType.LAZY)
    @JsonIgnore
    @Enumerated(EnumType.STRING)
    private DirectDebitStatus ddStatus;

    @Column(columnDefinition = "boolean default false")
    private boolean processing = false;

    @Column
    private Long processingTimeStamp;

    @Column
    private String details;


    public Integer getRowIndex() {
        return rowIndex;
    }

    public void setRowIndex(Integer rowIndex) {
        this.rowIndex = rowIndex;
    }

    public Date getPresentmentDate() {
        return presentmentDate;
    }

    public void setPresentmentDate(Date presentmentDate) {
        this.presentmentDate = presentmentDate;
    }

    public String getBank() {
        return bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }

    public String getContract() {
        return contract;
    }

    public void setContract(String contract) {
        this.contract = contract;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getDdaRefNo() {
        return ddaRefNo;
    }

    public void setDdaRefNo(String ddaRefNo) {
        this.ddaRefNo = ddaRefNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRejectionReason() {
        return rejectionReason;
    }

    public void setRejectionReason(String rejectionReason) {
        this.rejectionReason = rejectionReason;
    }

    public String getRejectionReasonEdited() {
        return rejectionReasonEdited;
    }

    public void setRejectionReasonEdited(String rejectionReasonEdited) {
        this.rejectionReasonEdited = rejectionReasonEdited;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public boolean isConfirmed() {
        return confirmed;
    }

    public void setConfirmed(boolean confirmed) {
        this.confirmed = confirmed;
    }

    public boolean isConfirmedByRPA() {
        return confirmedByRPA;
    }

    public void setConfirmedByRPA(boolean confirmedByRPA) {
        this.confirmedByRPA = confirmedByRPA;
    }

    public String getIban() {
        return iban;
    }

    public void setIban(String iban) {
        this.iban = iban;
    }

    public BankDirectDebitActivationFile getBankDirectDebitActivationFile() {
        return bankDirectDebitActivationFile;
    }

    public void setBankDirectDebitActivationFile(BankDirectDebitActivationFile bankDirectDebitActivationFile) {
        this.bankDirectDebitActivationFile = bankDirectDebitActivationFile;
    }

    public String getNextAction() {
        try {
            if (getDirectDebitFile() == null)
                return "";

            DirectDebit directDebit = getDirectDebitFile().getDirectDebit();
            DirectDebitRejectionFlowService directDebitRejectionFlowService = Setup.getApplicationContext().getBean(DirectDebitRejectionFlowService.class);

            switch (this.status) {
                case "DISCARDED":
                case "REJECTED": {

                    if (directDebit.getCategory() == DirectDebitCategory.A) {

                        if (directDebit.getMStatus() == DirectDebitStatus.CONFIRMED || directDebit.getMStatus() == DirectDebitStatus.REJECTED)
                            return "no action";

                        boolean allRejected = directDebitRejectionFlowService.allFilesRejected(directDebit.getDirectDebitFiles().stream().filter(
                                ddf -> !ddf.getId().equals(getDirectDebitFile().getId())).collect(Collectors.toList()), Arrays.asList(DirectDebitMethod.AUTOMATIC, DirectDebitMethod.MANUAL));

                        if (!allRejected) {
                            return "no action";
                        } else {
                            if (this.rejectCategory == DirectDebitRejectCategory.Compliance
                                    || this.rejectCategory == DirectDebitRejectCategory.Other) {
                                return "the dd will be sent again to bank";
                            } else if (this.rejectCategory == DirectDebitRejectCategory.Signature) {

                                DirectDebitSignatureService directDebitSignatureService = Setup.getApplicationContext()
                                        .getBean(DirectDebitSignatureService.class);
                                Map<String, Object> lastSignatureType = directDebitSignatureService
                                        .getLastSignatureType(directDebit.getContractPaymentTerm(), false, true);

                                if (!(Boolean) lastSignatureType.get("useApprovedSignature")) {
                                    return "Sms will be send to client to re-sign dd";
                                } else {
                                    return "the dd will be sent again to bank";
                                }
                            } else if (this.rejectCategory == DirectDebitRejectCategory.Account) {
                                return "Account will be reviewed by accountant";
                            } else if (this.rejectCategory == DirectDebitRejectCategory.EID) {
                                return "Eid will be reviewed by accountant";
                            } else if (this.rejectCategory == DirectDebitRejectCategory.Authorization) {
                                return "Sms will be send to client to provide new info";
                            } else if (this.rejectCategory == DirectDebitRejectCategory.Invalid_Account) {
                                return "Sms will be send to client to provide new info";
                            }
                        }
                    } else if (directDebit.getCategory() == DirectDebitCategory.B) {
                        if (getDirectDebitFile().getDdMethod() == DirectDebitMethod.AUTOMATIC) {

                            if (directDebit.getStatus() == DirectDebitStatus.CONFIRMED || directDebit.getStatus() == DirectDebitStatus.REJECTED)
                                return "no action";

                            if (directDebit.getMStatus() == DirectDebitStatus.PENDING)
                                return "no action";

                            boolean allAutoRejected = directDebitRejectionFlowService.allAutoFilesRejected(directDebit.getDirectDebitFiles().stream().filter(
                                    ddf -> !ddf.getId().equals(getDirectDebitFile().getId())).collect(Collectors.toList()));

                            if (allAutoRejected && directDebit.getMStatus() == DirectDebitStatus.CONFIRMED) {
                                return "automatic dd will be sent to bank again";
                            } else if (allAutoRejected && directDebit.getMStatus() == DirectDebitStatus.REJECTED) {
                                if (this.rejectCategory == DirectDebitRejectCategory.Compliance
                                        || this.rejectCategory == DirectDebitRejectCategory.Other) {
                                    return "the dd will be sent again to bank";
                                } else if (this.rejectCategory == DirectDebitRejectCategory.Signature) {

                                    DirectDebitSignatureService directDebitSignatureService = Setup.getApplicationContext()
                                            .getBean(DirectDebitSignatureService.class);
                                    Map<String, Object> lastSignatureType = directDebitSignatureService
                                            .getLastSignatureType(directDebit.getContractPaymentTerm(), false, true);

                                    if (!(Boolean) lastSignatureType.get("useApprovedSignature")) {
                                        return "Sms will be send to client to re-sign dd";
                                    } else {
                                        return "dd will be sent again to bank";
                                    }
                                } else if (this.rejectCategory == DirectDebitRejectCategory.Account) {
                                    return "Account will be reviewed by accountant";
                                } else if (this.rejectCategory == DirectDebitRejectCategory.EID) {
                                    return "Eid will be reviewed by accountant";
                                } else if (this.rejectCategory == DirectDebitRejectCategory.Authorization) {
                                    return "Sms will be send to client to provide new info";
                                } else if (this.rejectCategory == DirectDebitRejectCategory.Invalid_Account) {
                                    return "Sms will be send to client to provide new info";
                                }
                            } else if (!allAutoRejected) {
                                return "No action";
                            }

                        } else if (getDirectDebitFile().getDdMethod() == DirectDebitMethod.MANUAL) {

                            if (directDebit.getMStatus() == DirectDebitStatus.CONFIRMED || directDebit.getMStatus() == DirectDebitStatus.REJECTED)
                                return "no action";

                            if (directDebit.getStatus() == DirectDebitStatus.PENDING)
                                return "no action";

                            boolean allManualRejected = directDebitRejectionFlowService.allManualFilesRejected(directDebit.getDirectDebitFiles().stream().filter(
                                    ddf -> !ddf.getId().equals(getDirectDebitFile().getId())).collect(Collectors.toList()));

                            if (allManualRejected && directDebit.getStatus() == DirectDebitStatus.CONFIRMED) {
                                return "manual dd will be sent to bank again";
                            } else if (allManualRejected && directDebit.getStatus() == DirectDebitStatus.REJECTED) {

                                if (this.rejectCategory == DirectDebitRejectCategory.Compliance
                                        || this.rejectCategory == DirectDebitRejectCategory.Other) {
                                    return "the dd will be sent again to bank";
                                } else if (this.rejectCategory == DirectDebitRejectCategory.Signature) {

                                    DirectDebitSignatureService directDebitSignatureService = Setup.getApplicationContext()
                                            .getBean(DirectDebitSignatureService.class);
                                    Map<String, Object> lastSignatureType = directDebitSignatureService
                                            .getLastSignatureType(directDebit.getContractPaymentTerm(), false, true);

                                    if (!(Boolean) lastSignatureType.get("useApprovedSignature")) {
                                        return "Sms will be send to client to re-sign dd";
                                    } else {
                                        return "dd will be sent again to bank";
                                    }
                                } else if (this.rejectCategory == DirectDebitRejectCategory.Account) {
                                    return "Account will be reviewed by accountant";
                                } else if (this.rejectCategory == DirectDebitRejectCategory.EID) {
                                    return "Eid will be reviewed by accountant";
                                } else if (this.rejectCategory == DirectDebitRejectCategory.Authorization) {
                                    return "Sms will be send to client to provide new info";
                                } else if (this.rejectCategory == DirectDebitRejectCategory.Invalid_Account) {
                                    return "Sms will be send to client to provide new info";
                                }
                            } else if (!allManualRejected) {
                                return "No action";
                            }

                        }
                    }
                    break;
                }
                case "ACCEPTED": {

                    PaymentRepository paymentRepository = Setup.getRepository(PaymentRepository.class);

                    if (directDebit.getCategory() == DirectDebitCategory.A) {
                        if (directDebit.getStatus() == DirectDebitStatus.CONFIRMED) {
                            return "Sent for DD cancellation due to duplication";
                        } else if (directDebit.getStatus() == DirectDebitStatus.PENDING) {
                            List<PaymentStatus> statuses = new ArrayList<>();
                            statuses.add(PaymentStatus.PDC);
                            statuses.add(PaymentStatus.PRE_PDP);

                            List<Payment> payments = paymentRepository.findByDirectDebitIdAndStatusIn(directDebit.getId(), statuses);

                            StringBuilder response = new StringBuilder("Linked With payments: ");
                            for (Payment payment : payments) {
                                response.append(payment.getId()).append(",");
                            }
                            return response.toString();
                        }
                    } else if (directDebit.getCategory() == DirectDebitCategory.B) {
                        if (getDirectDebitFile().getDdMethod() == DirectDebitMethod.AUTOMATIC) {

                            if (directDebit.getStatus() == DirectDebitStatus.CONFIRMED) {
                                return "Sent for DD cancellation - due to duplication";
                            } else if (directDebit.getStatus() == DirectDebitStatus.PENDING) {
                                List<PaymentStatus> statuses = new ArrayList<>();
                                statuses.add(PaymentStatus.PDC);
                                statuses.add(PaymentStatus.PRE_PDP);

                                List<Payment> payments = paymentRepository.findByDirectDebitIdAndStatusIn(directDebit.getId(), statuses);

                                StringBuilder response = new StringBuilder("Linked With payments: ");
                                for (Payment payment : payments) {
                                    response.append(payment.getId()).append(",");
                                }
                                return response.toString();
                            }
                        } else if (getDirectDebitFile().getDdMethod() == DirectDebitMethod.MANUAL) {

                            if (directDebit.getMStatus() == DirectDebitStatus.CONFIRMED) {
                                return "Discarded - due to duplication";
                            } else if (directDebit.getMStatus() == DirectDebitStatus.PENDING) {

                                if (directDebit.getStatus() == DirectDebitStatus.CONFIRMED) {
                                    List<Payment> bouncedPayment = paymentRepository.findByDirectDebitIdAndStatusAndReplaced(directDebit.getId(), PaymentStatus.BOUNCED, false);
                                    if (bouncedPayment.isEmpty()) {

                                    } else {
                                        StringBuilder response = new StringBuilder("Linked With payment ");
                                        for (Payment payment : bouncedPayment) {
                                            response.append(payment.getId()).append(",");
                                        }
                                        response.append(" for bounced payment");
                                        return response.toString();
                                    }
                                } else {
                                    List<PaymentStatus> statuses = new ArrayList<>();
                                    statuses.add(PaymentStatus.PDC);
                                    statuses.add(PaymentStatus.PRE_PDP);

                                    List<Payment> payments = paymentRepository.findByDirectDebitIdAndStatusIn(directDebit.getId(), statuses);

                                    StringBuilder response = new StringBuilder("Linked With payment ");
                                    for (Payment payment : payments) {
                                        response.append(payment.getId()).append(",");
                                    }
                                    return response.toString();
                                }
                            }
                        }
                    }
                }
            }

            return nextAction;
        } catch (Exception e) {
            Logger logger = Logger.getLogger(BankDirectDebitActivationRecord.class.getName());
            logger.log(Level.SEVERE, "nextActionException " + e.getMessage(), e);
            return "no action";
        }
    }

    public void setNextAction(String nextAction) {
        this.nextAction = nextAction;
    }
    //    public DirectDebit getDirectDebit() {
//        return directDebit;
//    }
//
//    public void setDirectDebit(DirectDebit directDebit) {
//        this.directDebit = directDebit;
//    }


    public DirectDebitFile getDirectDebitFile() {
        if (directDebitFile == null && getDirectDebitFileId() != null)
            directDebitFile = Setup.getApplicationContext()
                    .getBean(DirectDebitFileRepository.class).findOne(getDirectDebitFileId());
        return directDebitFile;
    }

    public void setDirectDebitFile(DirectDebitFile directDebitFile) {
        setDirectDebitFileId(directDebitFile.getId());
        this.directDebitFile = directDebitFile;
    }

//    @BeforeInsert
//    public void validateInsert() {
//        DirectDebitFileRepository directDebitFileRepository =
//                Setup.getApplicationContext().getBean(DirectDebitFileRepository.class);
//        List<DirectDebitFile> ddfs;
//        if (!Utils.isEmpty(getContract()) && !Utils.isEmpty(getStartDate())
//                && !Utils.isEmpty(getExpiryDate()) && !Utils.isEmpty(getAccount())
//                && !Utils.isEmpty(getIban())) {
//
//            ddfs = directDebitFileRepository.findByApplicationIdAndStartDateAndExpiryDateAndAmountAndAccountNameAndIbanNumber(
//                    getContract(), getStartDate(), getExpiryDate(), getAmount(), getAccount(), getIban());
//            // Jirra ACC-817
//            for (DirectDebitFile ddf : ddfs) {
//                if (!ddf.isOldByApplicationId() ||
//                        (ddf.isOldByApplicationId()
//                                && ddf.getStatus().equals(DirectDebitFileStatus.SENT))) {
//                    setDirectDebitFile(ddf);
//
//                    break;
//                }
//            }
//        }
//        validate();
//    }

    @BeforeUpdate
    public void validateUpdate() {
        BankDirectDebitActivationRecordRepository bankDirectDebitActivationRecordRepository =
                Setup.getApplicationContext().getBean(BankDirectDebitActivationRecordRepository.class);

        BankDirectDebitActivationRecord old = bankDirectDebitActivationRecordRepository.getOne(getId());

        if (Utils.isPropertyUpdated(getPresentmentDate(), old.getPresentmentDate()))
            throw new RuntimeException("Presentment Date is not updatable.");

        if (Utils.isPropertyUpdated(getStartDate(), old.getStartDate()))
            throw new RuntimeException("Start Date is not updatable.");

        if (Utils.isPropertyUpdated(getExpiryDate(), old.getExpiryDate()))
            throw new RuntimeException("Expiry Date is not updatable.");

        if (Utils.isPropertyUpdated(getBank(), old.getBank()))
            throw new RuntimeException("Bank is not updatable.");

        if (Utils.isPropertyUpdated(getAccount(), old.getAccount()))
            throw new RuntimeException("Account is not updatable.");

        if (Utils.isPropertyUpdated(getStatus(), old.getStatus()))
            throw new RuntimeException("Status is not updatable.");

        if (Utils.isPropertyUpdated(getContract(), old.getContract()))
            throw new RuntimeException("Contract is not updatable.");

        if (Utils.isPropertyUpdated(getAmount(), old.getAmount()))
            throw new RuntimeException("Amount is not updatable.");

        if (Utils.isPropertyUpdated(getRejectionReason(), old.getRejectionReason()))
            throw new RuntimeException("Rejection Reason is not updatable.");

        if (Utils.isPropertyUpdated(getBankDirectDebitActivationFile().getId(), old.getBankDirectDebitActivationFile().getId()))
            throw new RuntimeException("File is not updatable.");
    }

    public DirectDebitRejectCategory getRejectCategory() {
        return rejectCategory;
    }

    public void setRejectCategory(DirectDebitRejectCategory rejectCategory) {
        this.rejectCategory = rejectCategory;
    }

    public boolean getProcessing() {

//        SelectQuery<BackgroundTask> query = new SelectQuery(BackgroundTask.class);
//        query.filterBy("relatedEntityType", "=", "BankDirectDebitActivationRecord");
//        query.filterBy("relatedEntityId", "=", this.getId());
//        query.filterBy("status", "NOT IN", Arrays.asList(BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed));
//        List<BackgroundTask> result = query.execute();
//        if (result != null && !result.isEmpty())
//            return true;
//        else
//            return false;
        return processing;
    }

    public void setProcessing(boolean processing) {
        this.processing = processing;
    }

    public Long getProcessingTimeStamp() {
        return processingTimeStamp;
    }

    public void setProcessingTimeStamp(Long processingTimeStamp) {
        this.processingTimeStamp = processingTimeStamp;
    }

    public String getClientName() {
        return clientName;
    }

    public Long getDirectDebitFileId() {
        return directDebitFileId;
    }

    public void setDirectDebitFileId(Long directDebitFileId) {
        this.directDebitFileId = directDebitFileId;
    }

    public Long getContractId() {
        return contractId;
    }

    public DirectDebitMethod getDdMethod() {
        return ddMethod;
    }

    public DirectDebitStatus getDdStatus() {
        return ddStatus;
    }

    public String getDetails() { return details; }

    public void setDetails(String details) { this.details = details; }
}
