package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import java.util.ArrayList;

import javax.persistence.*;
import java.util.List;
import java.util.Objects;

@Entity
public class ExpenseRelatedTo extends BaseEntity{

    public enum ExpenseRelatedToType {
        MAID, APPLICANT, OFFICE_STAFF, TEAM, NOT_DETERMINED
    }

    @Column
    @Enumerated(EnumType.STRING)
    private ExpenseRelatedToType relatedToType;

    @ManyToOne(fetch = FetchType.EAGER)
    @JsonSerialize(using = IdLabelSerializer.class)
    @JsonIgnore
    private Expense expense;

    //for Applicatn & OFFICE_STAFF
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Expense relatedExpense;

    //for Maid
    @ManyToOne(fetch = FetchType.EAGER)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Expense expenseCC;

    @ManyToOne(fetch = FetchType.EAGER)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Expense expenseVisa;

    //for team
    @OneToMany(mappedBy = "relatedTo")
    private List<ExpenseRelatedToTeam> teams = new ArrayList<>();

    public ExpenseRelatedToType getRelatedToType() {
        return relatedToType;
    }

    public void setRelatedToType(ExpenseRelatedToType relatedToType) {
        this.relatedToType = relatedToType;
    }

    public Expense getExpense() {
        return expense;
    }

    public void setExpense(Expense expense) {
        this.expense = expense;
    }

    public Expense getRelatedExpense() {
        return relatedExpense;
    }

    public void setRelatedExpense(Expense relatedExpense) {
        this.relatedExpense = relatedExpense;
    }

    public Expense getExpenseCC() {
        return expenseCC;
    }

    public void setExpenseCC(Expense expenseCC) {
        this.expenseCC = expenseCC;
    }

    public Expense getExpenseVisa() {
        return expenseVisa;
    }

    public void setExpenseVisa(Expense expenseVisa) {
        this.expenseVisa = expenseVisa;
    }

    public List<ExpenseRelatedToTeam> getTeams() {
        if (teams == null) teams = new ArrayList<>();
        return teams;
    }

    public void setTeams(List<ExpenseRelatedToTeam> teams) {
        this.teams = teams;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ExpenseRelatedTo relatedTo = (ExpenseRelatedTo) o;
        return relatedToType == relatedTo.relatedToType &&
                Objects.equals(expense.getId(), relatedTo.expense.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), relatedToType, expense);
    }
}
