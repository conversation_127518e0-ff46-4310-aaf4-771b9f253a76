package com.magnamedia.entity;

import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.extra.LabelValueEnum;
import com.magnamedia.module.type.PaymentType;
import com.magnamedia.workflow.visa.ExpensePurpose;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.math.BigDecimal;
import java.text.DecimalFormat;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on July 16, 2020
 * Jirra ACC-1970
 */

@Entity
public class VisaExpensePaymentTypeDetails extends BaseEntity {

    public enum ServiceChargeOfExpenseType implements LabelValueEnum {

        STATIC_AMOUNT("Static Amount"),
        PERCENTAGE("Percentage %");

        private final String label;

        ServiceChargeOfExpenseType(String label) {
            this.label = label;
        }

        @Override
        public String getLabel() {
            return label;
        }
    }

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private PaymentType paymentType;

    @Column
    private Double charge; // Charge Method Type

    @Column
    private Double vatCharge;

    @Column
    private Double vatChargePercentage; // VAT Charge Payment

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private ExpensePurpose expensePurpose; // Expense Type

    @Column
    private double serviceChargeOfExpense = 0.0;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ServiceChargeOfExpenseType typeOfServiceChargeOfExpense = ServiceChargeOfExpenseType.STATIC_AMOUNT;

    @Column
    private double vatChargeOfExpense = 0.0;

    public PaymentType getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(PaymentType paymentType) {
        this.paymentType = paymentType;
    }

    public Double getCharge() {
        return charge;
    }

    public void setCharge(Double charge) {
        this.charge = charge;
    }

    public Double getVatCharge() {
        return vatCharge;
    }

    public void setVatCharge(Double vatCharge) {
        this.vatCharge = vatCharge;
    }

    public Double getVatChargePercentage() {
        return vatChargePercentage;
    }

    public void setVatChargePercentage(Double vatChargePercentage) {
        this.vatChargePercentage = vatChargePercentage;
    }

    public ExpensePurpose getExpensePurpose() { return expensePurpose; }

    public void setExpensePurpose(ExpensePurpose expensePurpose) { this.expensePurpose = expensePurpose; }

    public double getServiceChargeOfExpense() { return serviceChargeOfExpense; }

    public void setServiceChargeOfExpense(double serviceChargeOfExpense) { this.serviceChargeOfExpense = serviceChargeOfExpense; }

    public double getVatChargeOfExpense() { return vatChargeOfExpense; }

    public void setVatChargeOfExpense(double vatChargeOfExpense) { this.vatChargeOfExpense = vatChargeOfExpense; }

    public ServiceChargeOfExpenseType getTypeOfServiceChargeOfExpense() {
        return typeOfServiceChargeOfExpense;
    }

    public void setTypeOfServiceChargeOfExpense(ServiceChargeOfExpenseType typeOfServiceChargeOfExpense) {
        this.typeOfServiceChargeOfExpense = typeOfServiceChargeOfExpense;
    }

    @BeforeInsert
    @BeforeUpdate
    public void calculateVatCharge() {

        this.vatCharge = Double.parseDouble(new DecimalFormat("############.##")
                .format((getCharge() != null ? getCharge() : 0.0) *
                        ((getVatChargePercentage() != null ? getVatChargePercentage() : 0.0) / 100)));
    }
}
