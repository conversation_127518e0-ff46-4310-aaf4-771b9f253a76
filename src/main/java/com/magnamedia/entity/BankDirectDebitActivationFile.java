package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.module.type.DDActivationFileStatus;
import com.magnamedia.repository.BankDirectDebitActivationFileRepository;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.persistence.*;

import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Formula;
import org.hibernate.envers.NotAudited;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Dec 9, 2018
 *         Jirra ACC-329
 */
@Entity
public class BankDirectDebitActivationFile extends BaseEntity {

    @Label
    private String bankName;

    @Column
    private java.util.Date date;

    @Column
    private boolean hidden = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean fileParsed = false;

    @Column
    @ColumnDefault("0")
    private boolean confirmedByRPA = false;

    @Column
    @ColumnDefault("0")
    private boolean addedByRPA = false;

    @Enumerated(EnumType.STRING)
    private DDActivationFileStatus status = DDActivationFileStatus.UNDER_PARSING;

    @JsonIgnore
    @NotAudited
    @OneToMany(mappedBy = "bankDirectDebitActivationFile",
            fetch = FetchType.LAZY,
            cascade = CascadeType.ALL)
    private List<BankDirectDebitActivationRecord> records;

    @Transient
    private List<BankDirectDebitActivationRecord> matchedRecords;

    @Transient
    private List<BankDirectDebitActivationRecord> notMatchedRecords;

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public java.util.Date getDate() {
        return date;
    }

    public void setDate(java.util.Date date) {
        this.date = date;
    }

    public boolean isHidden() {
        return hidden;
    }

    public void setHidden(boolean hidden) {
        this.hidden = hidden;
    }

    public Boolean getFileParsed() {
        return fileParsed;
    }

    public void setFileParsed(Boolean fileParsed) {
        this.fileParsed = fileParsed;
    }

    public boolean isConfirmedByRPA() {
        return confirmedByRPA;
    }

    public void setConfirmedByRPA(boolean confirmedByRPA) {
        this.confirmedByRPA = confirmedByRPA;
    }

    public boolean isAddedByRPA() { return addedByRPA; }

    public void setAddedByRPA(boolean addedByRPA) { this.addedByRPA = addedByRPA; }

    public List<BankDirectDebitActivationRecord> getRecords() {
        return records;
    }

    public void setRecords(List<BankDirectDebitActivationRecord> records) {
        this.records = records;
    }

    public DDActivationFileStatus getStatus() { return status; }

    public void setStatus(DDActivationFileStatus status) { this.status = status; }

    public List<BankDirectDebitActivationRecord> getMatchedRecords() {
        this.matchedRecords = new ArrayList<>();
        if (this.records != null && !this.records.isEmpty())
            this.matchedRecords = this.records.stream()
                    .filter(x -> (x.getDirectDebitFile() != null))
                    .collect(Collectors.toList());
        return matchedRecords;
    }

    public List<BankDirectDebitActivationRecord> getNotMatchedRecords() {
        this.notMatchedRecords = new ArrayList<>();
        if (this.records != null && !this.records.isEmpty())
            this.notMatchedRecords = this.records.stream()
                    .filter(x -> (x.getDirectDebitFile() == null))
                    .collect(Collectors.toList());
        return notMatchedRecords;
    }

    @BeforeUpdate
    public void validateUpdate() {

        BankDirectDebitActivationFileRepository bankDirectDebitActivationFileRepository =
                Setup.getApplicationContext().getBean(BankDirectDebitActivationFileRepository.class);

        List<Long> oldRecordsIDs = bankDirectDebitActivationFileRepository.findOne(getId())
                .getRecords().stream().map(x -> x.getId()).collect(Collectors.toList());
        if (getRecords().size() != oldRecordsIDs.size())
            throw new RuntimeException("Records is not updatable.");
        for (BankDirectDebitActivationRecord record : getRecords())
            if (record.getId() == null || !oldRecordsIDs.contains(record.getId()))
                throw new RuntimeException("Records is not updatable.");
    }
}