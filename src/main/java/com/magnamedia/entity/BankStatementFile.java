package com.magnamedia.entity;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.magnamedia.controller.BankStatementFileController;
import com.magnamedia.controller.TransactionsController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.helper.*;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.opencsv.CSVReader;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.annotations.Formula;
import org.hibernate.envers.NotAudited;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Transient;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static com.magnamedia.module.AccountingModule.*;

/**
 * <AUTHOR> Haj Hussein <<EMAIL>>
 *         Created At Apr 18, 2020
 **/

@Entity
public class BankStatementFile extends BaseEntity {


    private static final Logger logger = Logger.getLogger(BankStatementFile.class.getName());

    @Column
    private java.sql.Date reportDate;

    @Column(columnDefinition = "boolean default false")
    private boolean resolved = false;

    @Column(columnDefinition = "boolean default false")
    private boolean deleted = false;

    @Transient
    private Long totalTransactions;

    @NotAudited
    @Formula("(select count(b.ID) from BANKSTATEMENTTRANSACTIONS b " +
            "where b.FILE_ID = ID and b.RESOLVED = false)")
    private Long totalUnresolvedTransactions;

    @NotAudited
    @Formula("(select count(b.ID) from BANKSTATEMENTTRANSACTIONS b " +
            "where b.FILE_ID = ID and b.RESOLVED = true)")
    private Long totalResolvedTransactions;

    @Transient
    private Double totalPosSettlementAmount;

    @Transient
    private Double totalCreditCardAmount;

    @Transient
    private Double totalMaidsTransactionsAmount;

    @Transient
    private Double totalMaidsTransactionsCount;

    @Transient
    private Double totalErpCreditCardAmount;

    @Transient
    private Double totalCommission;

    @Transient
    private Long totalCreditCardTransactions;

    @Transient
    private Double totalVatOnCommission;

    @Transient
    private Double commissionPerTransaction;

    @Transient
    private Double tadbeerTransactionCount;

    @Transient
    private Double tadbeerTransactionAmount;

    @Transient
    private Double storageTransactionCount;

    @Transient
    private Double storageTransactionAmount;

    @Transient
    private List<Long> matchedPaymentsIds = new ArrayList<>();

    @Transient
    private List<Long> expensesMatchedPaymentsIds = new ArrayList<>();


//    @OneToMany(mappedBy = "file", fetch = FetchType.LAZY)
//    @JsonSerialize(using = IdLabelListSerializer.class)
//    private List<BankStatementTransaction> transactionList;

    public java.sql.Date getReportDate() {
        return reportDate;
    }

    public void setReportDate(java.sql.Date reportDate) {
        this.reportDate = reportDate;
    }

    public boolean isResolved() {
        return resolved;
    }

    public void setResolved(boolean resolved) {
        this.resolved = resolved;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    //    public List<BankStatementTransaction> getTransactionList() {
//        return transactionList;
//    }
//
//    public void setTransactionList(List<BankStatementTransaction> transactionList) {
//        this.transactionList = transactionList;
//    }

    public Long getTotalTransactions() {
        return this.getTotalResolvedTransactions() + this.getTotalUnresolvedTransactions();
    }

    public void setTotalTransactions(Long totalTransactions) {
        this.totalTransactions = totalTransactions;
    }

    public Long getTotalUnresolvedTransactions() {
        return totalUnresolvedTransactions;
    }

    public Long getTotalResolvedTransactions() {
        return totalResolvedTransactions;
    }

    public Double getTotalPosSettlementAmount() {
        Date date = new Date();
        try {
            date = new SimpleDateFormat("yyyy-MM-dd").parse(TransactionsController.TRANSACTION_MINIMUM_DATE);
        } catch (Exception e) {
            logger.log(Level.SEVERE, e.getMessage());
            e.printStackTrace();
        }
        SelectQuery<BankStatementRecord> bankStatementQuery = new SelectQuery<>(BankStatementRecord.class);
        bankStatementQuery.filterBy("description", "like", "%POS Settlement%");
        bankStatementQuery.filterBy("creditAmount", ">", 0.0);
        bankStatementQuery.filterBy("bankStatementFile", "=", this);
        bankStatementQuery.filterBy("date", ">=", date);
        AggregateQuery bankAggregateQuery = new AggregateQuery(bankStatementQuery, Aggregate.Sum, "creditAmount");
        return bankAggregateQuery.execute().doubleValue();
    }

    public void setTotalPosSettlementAmount(Double totalPosSettlementAmount) {
        this.totalPosSettlementAmount = totalPosSettlementAmount;
    }

    public Double getTotalCreditCardAmount() {
        Date date = new Date();
        try {
            date = new SimpleDateFormat("yyyy-MM-dd").parse(TransactionsController.TRANSACTION_MINIMUM_DATE);
        } catch (Exception e) {
            logger.log(Level.SEVERE, e.getMessage());
            e.printStackTrace();
        }

        SelectQuery<CreditCardStatementRecord> creditCardQuery = new SelectQuery<>(CreditCardStatementRecord.class);
        creditCardQuery.filterBy("bankStatementFile", "=", this);
        creditCardQuery.filterBy("transactionDate", ">=", date);
        AggregateQuery creditCardAggregateQuery = new AggregateQuery(creditCardQuery, Aggregate.Sum, "salesAmount");
        return creditCardAggregateQuery.execute().doubleValue();
    }

    public void setTotalCreditCardAmount(Double totalCreditCardAmount) {
        this.totalCreditCardAmount = totalCreditCardAmount;
    }

    public Double getTotalMaidsTransactionsAmount() {
        Date date = new Date();
        try {
            date = new SimpleDateFormat("yyyy-MM-dd").parse(TransactionsController.TRANSACTION_MINIMUM_DATE);
        } catch (Exception e) {
            logger.log(Level.SEVERE, e.getMessage());
            e.printStackTrace();
        }
        SelectQuery<CreditCardStatementRecord> creditCardQuery = new SelectQuery<>(CreditCardStatementRecord.class);
        List<String> terminalIds = Arrays.asList("********", "********", "********");
        creditCardQuery.filterBy("terminalId", "in", terminalIds);
        creditCardQuery.filterBy("bankStatementFile", "=", this);
        creditCardQuery.filterBy("transactionDate", ">=", date);
        AggregateQuery creditCardAggregateQuery = new AggregateQuery(creditCardQuery, Aggregate.Sum, "salesAmount");
        return creditCardAggregateQuery.execute().doubleValue();
    }

    public void setTotalMaidsTransactionsAmount(Double totalMaidsTransactionsAmount) {
        this.totalMaidsTransactionsAmount = totalMaidsTransactionsAmount;
    }

    public Double getTotalMaidsTransactionsCount() {
        Date date = new Date();
        try {
            date = new SimpleDateFormat("yyyy-MM-dd").parse(TransactionsController.TRANSACTION_MINIMUM_DATE);
        } catch (Exception e) {
            logger.log(Level.SEVERE, e.getMessage());
            e.printStackTrace();
        }
        SelectQuery<CreditCardStatementRecord> creditCardQuery = new SelectQuery<>(CreditCardStatementRecord.class);
        List<String> terminalIds = Arrays.asList("********", "********", "********");
        creditCardQuery.filterBy("terminalId", "in", terminalIds);
        creditCardQuery.filterBy("bankStatementFile", "=", this);
        creditCardQuery.filterBy("transactionDate", ">=", date);
        AggregateQuery creditCardAggregateQuery = new AggregateQuery(creditCardQuery, Aggregate.Count, "id");
        return creditCardAggregateQuery.execute().doubleValue();
    }

    public void setTotalMaidsTransactionsCount(Double totalMaidsTransactionsCount) {
        this.totalMaidsTransactionsCount = totalMaidsTransactionsCount;
    }

    public Double getTotalERPCreditCardAmount() {
        SelectQuery<Payment> paymentQuery = new SelectQuery<>(Payment.class);
        paymentQuery.filterBy("methodOfPayment", "=", PaymentMethod.CARD);

        // get dates of transactions in credit card statement file
        SelectQuery<CreditCardStatementRecord> creditCardQuery = new SelectQuery<>(CreditCardStatementRecord.class);
        creditCardQuery.filterBy("bankStatementFile", "=", this);
        List<CreditCardStatementRecord> creditCardRecords = creditCardQuery.execute();
        if (creditCardRecords != null && !creditCardRecords.isEmpty()) {
            Set<Date> dates = new HashSet<>();
            creditCardRecords.forEach(record -> {
                if (record.getTransactionDate() != null)
                    dates.add(new java.sql.Date(record.getTransactionDate().getTime()));

            });
            paymentQuery.filterBy("dateOfPayment", "in", dates);
            AggregateQuery paymentAggregateQuery = new AggregateQuery(paymentQuery, Aggregate.Sum, "amountOfPayment");

            // if there is no dates then no matching
            if (dates.isEmpty())
                return 0.0;

            return paymentAggregateQuery.execute().doubleValue();
        } else
            // if there is no credit card records then no matching
            return 0.0;
    }

    public void setTotalErpCreditCardAmount(Double totalErpCreditCardAmount) {
        this.totalErpCreditCardAmount = totalErpCreditCardAmount;
    }

    public Double getTotalCommission() {
        Date date = new Date();
        try {
            date = new SimpleDateFormat("yyyy-MM-dd").parse(TransactionsController.TRANSACTION_MINIMUM_DATE);
        } catch (Exception e) {
            logger.log(Level.SEVERE, e.getMessage());
            e.printStackTrace();
        }

        SelectQuery<CreditCardStatementRecord> query = new SelectQuery<>(CreditCardStatementRecord.class);
        query.filterBy("bankStatementFile", "=", this);
        query.filterBy("transactionDate", ">=", date);
        AggregateQuery commissionAggregateQuery = new AggregateQuery(query, Aggregate.Sum, "commission");
        totalCommission = Math.abs(commissionAggregateQuery.execute().doubleValue());
        totalCommission = Math.floor(totalCommission * 100) / 100;
        return totalCommission;
    }

    public void setTotalCommission(Double totalCommission) {
        this.totalCommission = totalCommission;
    }

    public Long getTotalCreditCardTransactions() {
        Date date = new Date();
        try {
            date = new SimpleDateFormat("yyyy-MM-dd").parse(TransactionsController.TRANSACTION_MINIMUM_DATE);
        } catch (Exception e) {
            logger.log(Level.SEVERE, e.getMessage());
            e.printStackTrace();
        }

        SelectQuery<CreditCardStatementRecord> query = new SelectQuery<>(CreditCardStatementRecord.class);
        query.filterBy("bankStatementFile", "=", this);
        query.filterBy("transactionDate", ">=", date);
        AggregateQuery commissionAggregateQuery = new AggregateQuery(query, Aggregate.Count, "id");
        totalCreditCardTransactions = commissionAggregateQuery.execute().longValue();
        return totalCreditCardTransactions;
    }

    public void setTotalCreditCardTransactions(Long totalCreditCardTransactions) {
        this.totalCreditCardTransactions = totalCreditCardTransactions;
    }

    public Double getTotalVatOnCommission() {
        Date date = new Date();
        try {
            date = new SimpleDateFormat("yyyy-MM-dd").parse(TransactionsController.TRANSACTION_MINIMUM_DATE);
        } catch (Exception e) {
            logger.log(Level.SEVERE, e.getMessage());
            e.printStackTrace();
        }

        SelectQuery<CreditCardStatementRecord> query = new SelectQuery<>(CreditCardStatementRecord.class);
        query.filterBy("bankStatementFile", "=", this);
        query.filterBy("transactionDate", ">=", date);
        AggregateQuery vatOnCommissionAggregateQuery = new AggregateQuery(query, Aggregate.Sum, "vatOnCommission");
        totalVatOnCommission = Math.abs(vatOnCommissionAggregateQuery.execute().doubleValue());
        totalVatOnCommission = Math.floor(totalVatOnCommission * 100) / 100;
        return totalVatOnCommission;
    }

    public void setTotalVatOnCommission(Double totalVatOnCommission) {
        this.totalVatOnCommission = totalVatOnCommission;
    }

    public Double getCommissionPerTransaction() {
        commissionPerTransaction = 0.0;
        Long totalCreditCardTransactions = getTotalCreditCardTransactions();
        if (totalCreditCardTransactions != null && !totalCreditCardTransactions.equals(0L)) {
            commissionPerTransaction = getTotalCommission() / getTotalCreditCardTransactions();
            commissionPerTransaction = Math.floor(commissionPerTransaction * 100) / 100;
        }
        return commissionPerTransaction;
    }

    public void setCommissionPerTransaction(Double commissionPerTransaction) {
        this.commissionPerTransaction = commissionPerTransaction;
    }

    public Double getTadbeerTransactionAmount() {
        Date date = new Date();
        try {
            date = new SimpleDateFormat("yyyy-MM-dd").parse(TransactionsController.TRANSACTION_MINIMUM_DATE);
        } catch (Exception e) {
            logger.log(Level.SEVERE, e.getMessage());
            e.printStackTrace();
        }
        SelectQuery<CreditCardStatementRecord> query = new SelectQuery<>(CreditCardStatementRecord.class);
        query.filterBy("bankStatementFile", "=", this);
        query.filterBy("transactionDate", ">=", date);
        query.filterBy("terminalId", "=", BankStatementFileController.tadbeerTransactionTerminalId);

        AggregateQuery totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "salesAmount");
        return totalAmountQuery.execute().doubleValue();
    }

    public void setTadbeerTransactionAmount(Double tadbeerTransactionAmount) {
        this.tadbeerTransactionAmount = tadbeerTransactionAmount;
    }

    public Double getTadbeerTransactionCount() {
        Date date = new Date();
        try {
            date = new SimpleDateFormat("yyyy-MM-dd").parse(TransactionsController.TRANSACTION_MINIMUM_DATE);
        } catch (Exception e) {
            logger.log(Level.SEVERE, e.getMessage());
            e.printStackTrace();
        }
        SelectQuery<CreditCardStatementRecord> query = new SelectQuery<>(CreditCardStatementRecord.class);
        query.filterBy("bankStatementFile", "=", this);
        query.filterBy("transactionDate", ">=", date);
        query.filterBy("terminalId", "=", BankStatementFileController.tadbeerTransactionTerminalId);

        AggregateQuery totalAmountQuery = new AggregateQuery(query, Aggregate.Count, "id");
        return totalAmountQuery.execute().doubleValue();
    }

    public void setTadbeerTransactionCount(Double tadbeerTransactionCount) {
        this.tadbeerTransactionCount = tadbeerTransactionCount;
    }

    public Double getStorageTransactionAmount() {
        Date date = new Date();
        try {
            date = new SimpleDateFormat("yyyy-MM-dd").parse(TransactionsController.TRANSACTION_MINIMUM_DATE);
        } catch (Exception e) {
            logger.log(Level.SEVERE, e.getMessage());
            e.printStackTrace();
        }
        SelectQuery<CreditCardStatementRecord> query = new SelectQuery<>(CreditCardStatementRecord.class);
        query.filterBy("bankStatementFile", "=", this);
        query.filterBy("transactionDate", ">=", date);
        query.filterBy("terminalId", "=", BankStatementFileController.storageTransactionTerminalId);

        AggregateQuery totalAmountQuery = new AggregateQuery(query, Aggregate.Sum, "salesAmount");
        return totalAmountQuery.execute().doubleValue();
    }

    public void setStorageTransactionAmount(Double storageTransactionAmount) {
        this.storageTransactionAmount = storageTransactionAmount;
    }

    public Double getStorageTransactionCount() {
        Date date = new Date();
        try {
            date = new SimpleDateFormat("yyyy-MM-dd").parse(TransactionsController.TRANSACTION_MINIMUM_DATE);
        } catch (Exception e) {
            logger.log(Level.SEVERE, e.getMessage());
            e.printStackTrace();
        }
        SelectQuery<CreditCardStatementRecord> query = new SelectQuery<>(CreditCardStatementRecord.class);
        query.filterBy("bankStatementFile", "=", this);
        query.filterBy("transactionDate", ">=", date);
        query.filterBy("terminalId", "=", BankStatementFileController.storageTransactionTerminalId);

        AggregateQuery totalAmountQuery = new AggregateQuery(query, Aggregate.Count, "id");
        return totalAmountQuery.execute().doubleValue();
    }

    public void setStorageTransactionCount(Double storageTransactionCount) {
        this.storageTransactionCount = storageTransactionCount;
    }

    public void filterCreditCard(List<CreditCardStatementRecord> tadbeerRecords, List<CreditCardStatementRecord> storageRecords, List<CreditCardStatementRecord> maidsRecords) {

        SelectQuery<CreditCardStatementRecord> query = new SelectQuery<>(CreditCardStatementRecord.class);
        query.filterBy("bankStatementFile", "=", this);
        List<CreditCardStatementRecord> allRecords = query.execute();


        tadbeerRecords = allRecords.stream().filter(r -> BankStatementFileController.tadbeerTransactionTerminalIdsList.contains(r.getTerminalId())).collect(Collectors.toList());
        storageRecords = allRecords.stream().filter(r -> r.getTerminalId().equals("********")).collect(Collectors.toList());
        maidsRecords = allRecords.stream().filter(r ->
                r.getTerminalId().equals("********") || r.getTerminalId().equals("********") || r.getTerminalId().equals("********")
        ).collect(Collectors.toList());
    }

    @JsonIgnore
    public void extractRecords() {

        if (getAttachments() == null || getAttachments().isEmpty())
            throw new RuntimeException("Attachment file is required.");

        Attachment bankStatementAttachment = null;
        Attachment creditCardStatementAttachment = null;
        for (Attachment att : getAttachments()) {
            switch (att.getTag()) {
                case ("bankStatement"): {
                    bankStatementAttachment = att;
                    break;
                }
                case ("creditCardStatement"): {
                    creditCardStatementAttachment = att;
                    break;
                }
            }
        }
        //extractBankStatementRecords
        try {
            DataFormatter formatter = new DataFormatter();
            Workbook workbook = null;
            if (bankStatementAttachment != null && bankStatementAttachment.getExtension().equals("xlsx"))
                workbook = new XSSFWorkbook(Storage.getStream(bankStatementAttachment));
            else if (bankStatementAttachment != null && bankStatementAttachment.getExtension().equals("xls"))
                workbook = new HSSFWorkbook(Storage.getStream(bankStatementAttachment));

            List<BankStatementRecord> bankStatementRecords = new ArrayList<>();
            if (workbook != null) {
                NumberFormat nf_in = NumberFormat.getNumberInstance(Locale.ENGLISH);
                Sheet sheet = workbook.getSheetAt(0);
                Iterator<Row> rowIterator = sheet.iterator();
                while (rowIterator.hasNext()) {
                    try {
                        Row row = rowIterator.next();
                        String srNoVal = formatter.formatCellValue(row.getCell(0)).trim();
                        if (!StringUtils.isNumeric(srNoVal))
                            continue;

                        BankStatementRecord bankStatementRecord = new BankStatementRecord();
                        bankStatementRecord.setBankStatementFile(this);
                        bankStatementRecord.setSrNo(Integer.parseInt(srNoVal));
                        logger.log(Level.SEVERE, "BankStatementFile: " + this.getId() + " bankStatementRecord.setDate: " + row.getCell(1).getStringCellValue().trim());
                        bankStatementRecord.setDate(new Date(DateUtil.parseDateDashedV2(row.getCell(1).getStringCellValue().trim()).getTime()));
                        bankStatementRecord.setValueDate(new Date(DateUtil.parseDateDashedV2(row.getCell(2).getStringCellValue().trim()).getTime()));
                        bankStatementRecord.setBankReferenceNo(formatter.formatCellValue(row.getCell(3)).trim());
                        bankStatementRecord.setCustomerReferenceNo(formatter.formatCellValue(row.getCell(4)).trim());
                        bankStatementRecord.setDescription(formatter.formatCellValue(row.getCell(5)).trim());

                        bankStatementRecord.setDebitAmount(!formatter.formatCellValue(row.getCell(6)).trim().equals("-") ?
                                nf_in.parse(formatter.formatCellValue(row.getCell(6)).trim()).doubleValue() : 0.0);
                        bankStatementRecord.setCreditAmount(!formatter.formatCellValue(row.getCell(7)).trim().equals("-") ?
                                nf_in.parse(formatter.formatCellValue(row.getCell(7)).trim()).doubleValue() : 0.0);
                        bankStatementRecord.setRunningBalance(!formatter.formatCellValue(row.getCell(8)).trim().equals("-") ?
                                nf_in.parse(formatter.formatCellValue(row.getCell(8)).trim()).doubleValue() : 0.0);
                        bankStatementRecord.setSwiftRefNo(formatter.formatCellValue(row.getCell(9)).trim());
                        bankStatementRecord.setBeneficiaryInfo1(formatter.formatCellValue(row.getCell(10)).trim());
                        bankStatementRecord.setBeneficiaryInfo2(formatter.formatCellValue(row.getCell(11)).trim());
                        bankStatementRecord.setBeneficiaryInfo3(formatter.formatCellValue(row.getCell(12)).trim());
                        bankStatementRecord.setRemitterInfo1(formatter.formatCellValue(row.getCell(13)).trim());
                        bankStatementRecord.setRemitterInfo2(formatter.formatCellValue(row.getCell(14)).trim());
                        bankStatementRecord.setRemitterInfo3(formatter.formatCellValue(row.getCell(15)).trim());
                        bankStatementRecord.setPaymentDetail(formatter.formatCellValue(row.getCell(16)).trim());
                        bankStatementRecord.setUploadDate(!formatter.formatCellValue(row.getCell(17)).trim().equals("") ? new Date(DateUtil.parseDateDashedV2(row.getCell(17).getStringCellValue().trim()).getTime()) : null);
                        bankStatementRecord.setUploadFileName(formatter.formatCellValue(row.getCell(18)).trim());
                        bankStatementRecords.add(bankStatementRecord);
                    } catch (ParseException ex) {
                        logger.log(Level.SEVERE, "Parsing Exception: " + ex.getMessage());
                        ex.printStackTrace();
                    } catch (NumberFormatException ex) {
                        logger.log(Level.SEVERE, "Number Parsing Exception: " + ex.getMessage());
                        ex.printStackTrace();
                    } catch (Exception ex) {
                        logger.log(Level.SEVERE, "Exception: " + ex.getMessage());
                        ex.printStackTrace();
                    }
                }
            }
            if (bankStatementRecords != null && !bankStatementRecords.isEmpty()) {
                BankStatementRecordRepository bankStatementRecordRepository = Setup.getRepository(BankStatementRecordRepository.class);
                bankStatementRecordRepository.save(bankStatementRecords);
            }
        } catch (IOException ex) {
            logger.log(Level.SEVERE, "Exception: " + ex.getMessage());
            ex.printStackTrace();
        }


        //extractCreditCardRecords
        InputStream attachmentInputStream = null;
        try {
            if (creditCardStatementAttachment != null && creditCardStatementAttachment.getExtension().equals("csv")) {
                List<CreditCardStatementRecord> creditCardRecords = new ArrayList<>();
                NumberFormat nf_in = NumberFormat.getNumberInstance(Locale.ENGLISH);

                attachmentInputStream = Storage.getStream(creditCardStatementAttachment);
                CSVReader reader = new CSVReader(new InputStreamReader(attachmentInputStream));
                String[] nextLine;

                //skip the header of the file
                reader.readNext();

                while ((nextLine = reader.readNext()) != null) {
                    try {
                        CreditCardStatementRecord creditCardStatementRecord = new CreditCardStatementRecord();
                        creditCardStatementRecord.setBankStatementFile(this);
                        creditCardStatementRecord.setSrlNbr(nextLine[0].trim());
                        creditCardStatementRecord.setMerchantId(nextLine[1].trim());
                        creditCardStatementRecord.setChainId(nextLine[2].trim());
                        creditCardStatementRecord.setMerchantName(nextLine[3].trim());
                        creditCardStatementRecord.setLocation(nextLine[4].trim());
                        creditCardStatementRecord.setTelephon(nextLine[5].trim());
                        creditCardStatementRecord.setTerminalId(nextLine[6].trim());
                        creditCardStatementRecord.setSequenceNumber(nextLine[7].trim());
                        creditCardStatementRecord.setTransactionCurr(nextLine[8] != null ? nf_in.parse(nextLine[8].trim()).doubleValue() : 0.0);
                        creditCardStatementRecord.setTransactionDate(nextLine[9] != null && !nextLine[9].trim().equals("") ?
                                new SimpleDateFormat("MM/dd/yyyy").parse(nextLine[9].trim()) : null);
                        creditCardStatementRecord.setCardType(nextLine[10].trim());
                        creditCardStatementRecord.setCardNumber(nextLine[11].trim());
                        creditCardStatementRecord.setAuthCode(nextLine[12].trim());
                        creditCardStatementRecord.setTransactionType(nextLine[13].trim());
                        creditCardStatementRecord.setSalesAmount(nextLine[14] != null ? nf_in.parse(nextLine[14].trim()).doubleValue() : 0.0);
                        creditCardStatementRecord.setCommission(nextLine[15] != null ? nf_in.parse(nextLine[15].trim()).doubleValue() : 0.0);
                        creditCardStatementRecord.setNetAmount(nextLine[16] != null ? nf_in.parse(nextLine[16].trim()).doubleValue() : 0.0);
                        creditCardStatementRecord.setAcquirerData(nextLine[17].trim());
                        creditCardStatementRecord.setReferenceNbr(nextLine[18].trim());
                        creditCardStatementRecord.setTy(nextLine[19].trim());
                        creditCardStatementRecord.setBankAccount(nextLine[20].trim());
                        creditCardStatementRecord.setTransactionTime(nextLine[21].trim());
                        creditCardStatementRecord.setIbanAcctNbr(nextLine[22].trim());
                        creditCardStatementRecord.setDiscType(nextLine[23].trim());
                        creditCardStatementRecord.setMasterChainId1(nextLine[24].trim());
                        creditCardStatementRecord.setMasterChainId2(nextLine[25].trim());
                        creditCardStatementRecord.setTipAmount(nextLine[26] != null ? nf_in.parse(nextLine[26].trim()).doubleValue() : 0.0);
                        creditCardStatementRecord.setTagId(nextLine[27].trim());
                        creditCardStatementRecord.setDriverId(nextLine[28].trim());
                        creditCardStatementRecord.setPwcbCashBack(nextLine[29].trim());
                        creditCardStatementRecord.setTxnInd(nextLine[30].trim());
                        creditCardStatementRecord.setCommInd(nextLine[31].trim());
                        creditCardStatementRecord.setSource(nextLine[32].trim());
                        creditCardStatementRecord.setTransactionFee(nextLine[33] != null ? nf_in.parse(nextLine[33].trim()).doubleValue() : 0.0);
                        creditCardStatementRecord.setVatOnFee(nextLine[34] != null ? nf_in.parse(nextLine[34].trim()).doubleValue() : 0.0);
                        creditCardStatementRecord.setVatOnCommission(nextLine[35] != null ? nf_in.parse(nextLine[35].trim()).doubleValue() : 0.0);

                        creditCardRecords.add(creditCardStatementRecord);
                    } catch (ParseException ex) {
                        logger.log(Level.SEVERE, "Parsing Exception: " + ex.getMessage());
                        ex.printStackTrace();
                    } catch (Exception ex) {
                        logger.log(Level.SEVERE, "Exception: " + ex.getMessage());
                        ex.printStackTrace();
                    }
                }
                CreditCardStatementRecordRepository creditCardStatementRecordRepository = Setup.getRepository(CreditCardStatementRecordRepository.class);
                creditCardStatementRecordRepository.save(creditCardRecords);
            }
        } catch (IOException ex) {
            logger.log(Level.SEVERE, "Exception: " + ex.getMessage());
            ex.printStackTrace();
        } finally {
            StreamsUtil.closeStream(attachmentInputStream);
        }
    }
}
