package com.magnamedia.entity;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Jun 27, 2018
 */
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.PickListItemSerializer;
import org.hibernate.envers.NotAudited;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import java.util.Set;
import javax.persistence.*;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * <AUTHOR> <PERSON>
 *
 */
@Entity
public class FreedomOperator extends BaseEntity implements Serializable {

    @Column
    @Label
    @NotNull(message = "freedom operator name cant't be null!")
    @NotAudited
    private String name;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    @NotAudited
    private Expense expense;

    @NotNull(message = "Type can't be null")
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    @NotAudited
    private PicklistItem freedomOperatorType;

    @NotNull(message = "Warranty duration can't be null")
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = PickListItemSerializer.class)
    private PicklistItem warrantyDuration;

    @OneToMany(mappedBy = "freedomOperator", cascade = CascadeType.ALL)
    @NotAudited
    private Set<Housemaid> housemaids;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    @NotAudited
    private PicklistItem location;

    @NotNull(message = "Operator charge can't be null")
    @Column
    private Double operatorCharge;

    @NotNull(message = "E-Vissa issued can't be null")
    @Column
    private Double eVisaIssuedCharge;

    @NotNull(message = "Landed in Dubai can't be null")
    @Column
    private Double landedInDubaiCharge;

    @NotNull(message = "Code can't be null")
    @Column
    @NotAudited
    private String code;

    @NotNull(message = "Code can't be null")
    @Column
    @NotAudited
    private Integer cap;

    @Column
    private Date effectiveStartingDate;

    private Date salaryCutOffDate;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Expense getExpense() {
        return expense;
    }

    public void setExpense(Expense expense) {
        this.expense = expense;
    }

    public PicklistItem getFreedomOperatorType() {
        return freedomOperatorType;
    }

    public void setFreedomOperatorType(PicklistItem freedomOperatorType) {
        this.freedomOperatorType = freedomOperatorType;
    }

    public PicklistItem getWarrantyDuration() {
        return warrantyDuration;
    }

    public void setWarrantyDuration(PicklistItem warrantyDuration) {
        this.warrantyDuration = warrantyDuration;
    }

    public PicklistItem getLocation() {
        return location;
    }

    public void setLocation(PicklistItem location) {
        this.location = location;
    }

    public Double getOperatorCharge() {
        return operatorCharge;
    }

    public void setOperatorCharge(Double operatorCharge) {
        this.operatorCharge = operatorCharge;
    }

    public Double geteVisaIssuedCharge() {
        return eVisaIssuedCharge;
    }

    public void seteVisaIssuedCharge(Double eVisaIssuedCharge) {
        this.eVisaIssuedCharge = eVisaIssuedCharge;
    }

    public Double getLandedInDubaiCharge() {
        return landedInDubaiCharge;
    }

    public void setLandedInDubaiCharge(Double landedInDubaiCharge) {
        this.landedInDubaiCharge = landedInDubaiCharge;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getCap() {
        return cap;
    }

    public void setCap(Integer cap) {
        this.cap = cap;
    }

    public Date getEffectiveStartingDate() {
        return effectiveStartingDate;
    }

    public void setEffectiveStartingDate(Date effectiveStartingDate) {
        this.effectiveStartingDate = effectiveStartingDate;
    }

    public Set<Housemaid> getHousemaids() {
        return housemaids;
    }

    public void setHousemaids(Set<Housemaid> housemaids) {
        this.housemaids = housemaids;
    }

    public Date getSalaryCutOffDate() {
        return salaryCutOffDate;
    }

    public void setSalaryCutOffDate(Date salaryCutOffDate) {
        this.salaryCutOffDate = salaryCutOffDate;
    }
}
