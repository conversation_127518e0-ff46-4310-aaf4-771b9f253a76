package com.magnamedia.entity.sms;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.helper.GreetingsHelper;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.repository.SmsTemplateRepository;
import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 *
 * <AUTHOR> E<PERSON>rawi <<EMAIL>>
 * Created at Jun 20, 2019
 * <AUTHOR> kanaan <<EMAIL>>
 * Jirra ACC-1092
 */
@Entity
@Table(name = "CM_SMS_TRANSLATIONS")
public class SmsTranslation extends BaseEntity implements Serializable {

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private SmsTemplate template;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem nationality;

    @Column
    @Lob
    private String text;

    @Column
    @Lob
    private String yayaText;
    @Column
    @Lob
    private String emailText;

    @Column
    @Lob
    private String emailSubject;
    
    public SmsTranslation() {
    }

    public SmsTranslation(String templateCode, String nationality, String text) {
        if (templateCode == null) {
            throw new RuntimeException("templateCode can not be null");
        }
        if (nationality == null) {
            throw new RuntimeException("nationality can not be null");
        }
        PicklistItem nat = PicklistHelper.getItem("nationalities", nationality);
        SmsTemplateRepository repo = Setup.getRepository(SmsTemplateRepository.class);
        SmsTemplate temp = repo.findFirst1ByCodeOrderByCreationDateDesc(templateCode);
        this.nationality = nat;
        this.template = temp; 
        this.text = text;
        this.yayaText = text;
        this.emailText = text;
    }

    public SmsTranslation(String templateCode, String nationality, String text, String yayaText) {
        if (templateCode == null) {
            throw new RuntimeException("templateCode can not be null");
        }
        if (nationality == null) {
            throw new RuntimeException("nationality can not be null");
        }
        PicklistItem nat = PicklistHelper.getItem("nationalities", nationality);
        SmsTemplateRepository repo = Setup.getRepository(SmsTemplateRepository.class);
        SmsTemplate temp = repo.findFirst1ByCodeOrderByCreationDateDesc(templateCode);
        this.nationality = nat;
        this.template = temp;
        this.text = text;
        this.yayaText = yayaText;
    }
    public SmsTranslation(String templateCode, String nationality, String text, String yayaText, String emailText,String emailSubject) {
        if (templateCode == null) {
            throw new RuntimeException("templateCode can not be null");
        }
        if (nationality == null) {
            throw new RuntimeException("nationality can not be null");
        }
        PicklistItem nat = PicklistHelper.getItem("nationalities", nationality);
        SmsTemplateRepository repo = Setup.getRepository(SmsTemplateRepository.class);
        SmsTemplate temp = repo.findFirst1ByCodeOrderByCreationDateDesc(templateCode);
        this.nationality = nat;
        this.template = temp;
        this.text = text;
        this.yayaText = yayaText;
        this.emailText = emailText;
        this.emailSubject=emailSubject;
    }

    public String getEmailSubject() {
        return emailSubject;
    }

    public void setEmailSubject(String emailSubject) {
        this.emailSubject = emailSubject;
    }

    
    public SmsTemplate getTemplate() {
        return template;
    }

    public void setTemplate(SmsTemplate template) {
        this.template = template;
    }

    public PicklistItem getNationality() {
        return nationality;
    }

    public void setNationality(PicklistItem nationality) {
        this.nationality = nationality;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getYayaText() {
        return yayaText;
    }

    public void setYayaText(String yayaText) {
        this.yayaText = yayaText;
    }

    public String getEmailText() {
        return emailText;
    }

    public void setEmailText(String emailText) {
        this.emailText = emailText;
    }

    public String getLanguageName() {
        return GreetingsHelper.getLanguageName(nationality);
    }
    public String getLanguageCode() {
        return GreetingsHelper.getLanguageCode(nationality);
    }

}
