package com.magnamedia.entity.sms;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.persistence.CollectionTable;
import javax.persistence.Column;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import org.hibernate.envers.NotAudited;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created at Jun 20, 2019
 * <AUTHOR> kanaan <<EMAIL>>
 * Jirra ACC-1092
 */
@Entity
@Table(name = "CM_SMS_TEMPLATES")
public class SmsTemplate extends BaseEntity implements Serializable {

    @Column(unique = true)
    @Label
    private String code;
    
    @Column
    private String type;
    
    @Column
    @Lob
    private String event;
    
    @Column(columnDefinition = "boolean default false")
    private Boolean enabled;
    
    @Column
    @Lob
    private String text;
    @Column
    @Lob
    private String yayaText;
    @Column
    @Lob
    private String emailText;
    @Column
    @Lob
    private String emailSubject;

    @ElementCollection(fetch = FetchType.LAZY)
    @NotAudited
    @CollectionTable(name="CM_SMS_TEMPLATE_PARAMS", joinColumns=@JoinColumn(name="CM_SMS_TEMPLATE_ID"))
    private List<String> params ;

    @OneToMany(mappedBy = "template")
    private List<SmsTranslation> translations;

    public SmsTemplate() {
    }

    public SmsTemplate(String code, String type, String event, String text, List<String> params) {
        this.code = code;
        this.type = type;
        this.event=event;
        this.text = text;
        this.yayaText = text;
        this.emailText = text;
        this.params = params;
    }

    public SmsTemplate(String code, String type, String event, String text, String yayaText, String emailText,String emailSubject, List<String> params) {
        this.code = code;
        this.type = type;
        this.event=event;
        this.text = text;
        this.yayaText = yayaText;
        this.emailText = emailText;
        this.params = params;
        this.emailSubject=emailSubject;
    }

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    
    
    

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    
    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getYayaText() {
        return yayaText;
    }

    public void setYayaText(String yayaText) {
        this.yayaText = yayaText;
    }

    public String getEmailText() {
        return emailText;
    }

    public void setEmailText(String emailText) {
        this.emailText = emailText;
    }
    

    public List<String> getParams() {
        return params;
    }

    public void setParams(List<String> params) {
        this.params = params;
    }

    public List<SmsTranslation> getTranslations() {
        return translations;
    }

    public void setTranslations(List<SmsTranslation> translations) {
        this.translations = translations;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getEmailSubject() {
        return emailSubject;
    }

    public void setEmailSubject(String emailSubject) {
        this.emailSubject = emailSubject;
    }
    
    

    @JsonIgnore
    public String execute(Map<String, String> context) {
        if (text == null) {
            return null;
        }
        return execute(context, text);
    }

    @JsonIgnore
    public String execute(Map<String, String> context, PicklistItem translation) {
        SmsTranslation tr = getTranslation(translation);
        if (tr == null) {
            return execute(context);
        }
        if (tr.getText() == null) {
            return null;
        }

        return execute(context, tr.getText());
    }
    @JsonIgnore
    public String executeEmail(Map<String, String> context) {
        if (emailText == null) {
            return null;
        }
        return execute(context, emailText);
    }

    @JsonIgnore
    public String executeEmail(Map<String, String> context, PicklistItem translation) {
        SmsTranslation tr = getTranslation(translation);
        if (tr == null) {
            return executeEmail(context);
        }
        if (tr.getEmailText()== null) {
            return null;
        }

        return execute(context, tr.getEmailText());
    }
    @JsonIgnore
    public String executeEmailSubject(Map<String, String> context) {
        if (emailSubject == null) {
            return null;
        }
        return execute(context, emailSubject);
    }

    @JsonIgnore
    public String executeEmailSubject(Map<String, String> context, PicklistItem translation) {
        SmsTranslation tr = getTranslation(translation);
        if (tr == null) {
            return executeEmailSubject(context);
        }
        if (tr.getEmailSubject()== null) {
            return null;
        }

        return execute(context, tr.getEmailSubject());
    }
    @JsonIgnore
    public String executeYaya(Map<String, String> context) {
        if (yayaText == null) {
            return null;
        }
        return execute(context, yayaText);
    }

    @JsonIgnore
    public String executeYaya(Map<String, String> context, PicklistItem translation) {
        SmsTranslation tr = getTranslation(translation);
        if (tr == null) {
            return executeYaya(context);
        }
        if (tr.getYayaText()== null) {
            return null;
        }

        return execute(context, tr.getYayaText());
    }

    @JsonIgnore
    private String execute(Map<String, String> context, String text) {
        String executed = text;
        for (Map.Entry<String, String> param : context.entrySet()) {
            if (param != null && param.getKey() != null && param.getValue() != null) {
                executed = executed.replace(param.getKey(), param.getValue());
            }

        } 
        return executed;
    }
 
    @JsonIgnore
    public SmsTranslation getTranslation(PicklistItem nationality) {
        try {
            Optional<SmsTranslation> tr = translations.stream().filter(t -> nationality.getId().equals(t.getNationality().getId())).findFirst();
            if (tr.isPresent()) {
                return tr.get();
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

}
