package com.magnamedia.entity;

import java.io.Serializable;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.Transient;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelSerializer;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Mar 25, 2020
 *         Jirra ACC-1435
 */
@Entity
public class ClientAgePreference extends BaseEntity implements Serializable {

	@ManyToOne(fetch = FetchType.LAZY)
	@JsonSerialize(using = IdLabelSerializer.class)
	private PicklistItem agePreference;

	@JsonSerialize(using = IdLabelSerializer.class)
	@ManyToOne(fetch = FetchType.LAZY)
	private Client client;

	@Transient
	private String ageCondition;

	public ClientAgePreference() {
	}

	public ClientAgePreference(PicklistItem agePreference,
							   Client client) {
		this.agePreference = agePreference;
		this.client = client;
	}

	public PicklistItem getAgePreference() {
		return agePreference;
	}

	public void setAgePreference(PicklistItem agePreference) {
		this.agePreference = agePreference;
	}

	public Client getClient() {
		return client;
	}

	public void setClient(Client client) {
		this.client = client;
	}
}
