package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import org.hibernate.envers.NotAudited;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Feb 8, 2020
 *         Jirra ACC-1227
 */

@Entity
public class OfficeStaffTransaction extends BaseEntity {
    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Transaction transaction;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private OfficeStaff officeStaff;


    @Override
    public boolean equals(Object object) {
        if (!(object instanceof OfficeStaffTransaction)) {
            return false;
        } else {
            OfficeStaffTransaction other = (OfficeStaffTransaction) object;
            return this.getId().equals(other.getId())
                    && this.officeStaff.getId().equals(other.officeStaff.getId()) && this.transaction.getId().equals(other.transaction.getId());
        }
    }

    public Transaction getTransaction() {
        return transaction;
    }

    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
    }

    public OfficeStaff getOfficeStaff() {
        return officeStaff;
    }

    public void setOfficeStaff(OfficeStaff officeStaff) {
        this.officeStaff = officeStaff;
    }
}
