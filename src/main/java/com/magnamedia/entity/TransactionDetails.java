package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;

import javax.persistence.*;
import java.sql.Date;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Feb 25, 2020
 *         Jirra ACC-1389
 */

@Entity
@Table(
        indexes = {
                @Index(columnList = "accrualDate", unique = false)
        })
public class TransactionDetails extends BaseEntity {

    @ManyToOne
    @JsonSerialize(using = IdLabelSerializer.class)
    private Transaction transaction;

    @Column
    private Double transactionAmount;

    @Column
    private Double averageAmount;

    @Column
    private Double profitAdjustment;

    @Column
    private Date accrualDate;

    public Transaction getTransaction() {
        return transaction;
    }

    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
    }

    public Double getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(Double transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    public Double getAverageAmount() {
        return averageAmount;
    }

    public void setAverageAmount(Double averageAmount) {
        this.averageAmount = averageAmount;
    }

    public Double getProfitAdjustment() {
        return profitAdjustment;
    }

    public void setProfitAdjustment(Double profitAdjustment) {
        this.profitAdjustment = profitAdjustment;
    }

    public Date getAccrualDate() {
        return accrualDate;
    }

    public void setAccrualDate(Date accrualDate) {
        this.accrualDate = accrualDate;
    }
}
