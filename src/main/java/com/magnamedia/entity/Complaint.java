package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.serialize.PascalCaseToSpaceSerializer;
import com.magnamedia.module.type.Caller;
import com.magnamedia.module.type.ComplaintStatus;
import com.magnamedia.module.type.Who;
import java.io.Serializable;
import java.util.Set;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.Lob;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;

/**
 * e
 *
 * <AUTHOR> Esrawi <<EMAIL>>
 * Created at Nov 5, 2017
 */
@Entity
public class Complaint extends BaseEntity implements Serializable {

    @ManyToOne(fetch = FetchType.LAZY)
    private Contract contract;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Housemaid housemaid;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private ComplaintType primaryType;
    
    @ManyToMany(fetch = FetchType.LAZY)
    @JsonSerialize(contentUsing = IdLabelSerializer.class)
    private Set<ComplaintType> otherTypes;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private ComplaintCategory category;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Client client;

    @Enumerated(EnumType.STRING)
    @Column
    private Who targetContact;

    @Enumerated(EnumType.STRING)
    @Column
    private Caller caller;
    
    @Column(length = 2048)
    private String oldType;
    
    @Column
    @Enumerated(EnumType.STRING)
    @JsonSerialize(using = PascalCaseToSpaceSerializer.class)
    private ComplaintStatus status = ComplaintStatus.NotResolved;
    
    @OneToOne
    private MatchingType relatedMatchingType;
    
    @Lob
    @Column
    private String initialDescription;

    @Column
    private Boolean needReplaceDeduction;

    public Boolean getNeedReplaceDeduction() {
        return needReplaceDeduction;
    }

    public void setNeedReplaceDeduction(Boolean needReplaceDeduction) {
        this.needReplaceDeduction = needReplaceDeduction;
    }
    
    
    public Contract getContract() {
        return contract;
    }

    public void setContract(Contract contract) {
        this.contract = contract;
    }

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public ComplaintType getPrimaryType() {
        return primaryType;
    }

    public void setPrimaryType(ComplaintType primaryType) {
        this.primaryType = primaryType;
    }

    public Set<ComplaintType> getOtherTypes() {
        return otherTypes;
    }

    public void setOtherTypes(Set<ComplaintType> otherTypes) {
        this.otherTypes = otherTypes;
    }

    public ComplaintCategory getCategory() {
        return category;
    }

    public void setCategory(ComplaintCategory category) {
        this.category = category;
    }

    public Client getClient() {
        return client;
    }

    public void setClient(Client client) {
        this.client = client;
    }

    public Who getTargetContact() {
        return targetContact;
    }

    public void setTargetContact(Who targetContact) {
        this.targetContact = targetContact;
    }

    public Caller getCaller() {
        return caller;
    }

    public void setCaller(Caller caller) {
        this.caller = caller;
    }

    public String getInitialDescription() {
        return initialDescription;
    }

    public void setInitialDescription(String initialDescription) {
        this.initialDescription = initialDescription;
    }

    public ComplaintStatus getStatus() {
        return status;
    }

    public void setStatus(ComplaintStatus status) {
        this.status = status;
    }

    public String getOldType() {
        return oldType;
    }

    public void setOldType(String oldType) {
        this.oldType = oldType;
    }
    
}
